/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.EntityTypes;
import java.io.File;
import com.untill.retailforce.model.FiscalClient;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.TseAnnouncementCreateResult;
import com.untill.retailforce.model.TseAnnouncementOverview;
import com.untill.retailforce.model.TseAnnouncementOverviewPageResultModel;
import com.untill.retailforce.model.TseAnnouncementStatusInfo;
import com.untill.retailforce.model.TseInformationOverview;
import com.untill.retailforce.model.TseInformationOverviewPageResultModel;
import com.untill.retailforce.model.TseType;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ImplementationControllerGermanyApi
 */
@Disabled
public class ImplementationControllerGermanyApiTest {

    private final ImplementationControllerGermanyApi api = new ImplementationControllerGermanyApi();

    /**
     * Store the given tse information
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeDistributorIdTseInformation2PostTest() throws ApiException {
        UUID distributorId = null;
        UUID organizationId = null;
        UUID terminalId = null;
        String serialNumber = null;
        OffsetDateTime certificateExpirationDate = null;
        String bsiCertificationId = null;
        OffsetDateTime initializationDate = null;
        TseType type = null;
        UUID tseId = null;
        api.apiV10ImplementationDeDistributorIdTseInformation2Post(distributorId, organizationId, terminalId, serialNumber, certificateExpirationDate, bsiCertificationId, initializationDate, type, tseId);
        // TODO: test validations
    }

    /**
     * Store the given tse information
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeDistributorIdTseInformationPostTest() throws ApiException {
        UUID distributorId = null;
        UUID organizationId = null;
        UUID terminalId = null;
        byte[] body = null;
        api.apiV10ImplementationDeDistributorIdTseInformationPost(distributorId, organizationId, terminalId, body);
        // TODO: test validations
    }

    /**
     * Exports germany tar data (tse) from cloud archive.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTerminalIdTarGetTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime tillDate = null;
        api.apiV10ImplementationDeTerminalIdTarGet(terminalId, fromDate, tillDate);
        // TODO: test validations
    }

    /**
     * Exports germany taxonomy data from cloud archive.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTerminalIdTaxonomyGetTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime tillDate = null;
        Integer type = null;
        api.apiV10ImplementationDeTerminalIdTaxonomyGet(terminalId, fromDate, tillDate, type);
        // TODO: test validations
    }

    /**
     * Starts tse provisioning for the given terminal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTerminalIdTseProvisioningPatchTest() throws ApiException {
        UUID terminalId = null;
        FiscalClient response = api.apiV10ImplementationDeTerminalIdTseProvisioningPatch(terminalId);
        // TODO: test validations
    }

    /**
     * Get Tse Announcements
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsGetTest() throws ApiException {
        UUID organisationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        Boolean isTest = null;
        TseAnnouncementOverviewPageResultModel response = api.apiV10ImplementationDeTseAnnouncementsGet(organisationId, pageOffset, pageSize, isTest);
        // TODO: test validations
    }

    /**
     * Create TseAnnouncement  starts a new tse announcement progress. An email per store will be sent to the user, containing the announcemnt data and a verification link.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsPostTest() throws ApiException {
        List<UUID> storeIds = null;
        Boolean isTest = null;
        TseAnnouncementCreateResult response = api.apiV10ImplementationDeTseAnnouncementsPost(storeIds, isTest);
        // TODO: test validations
    }

    /**
     * Get TSE Announcement status:  if there are tse changes or an open tse announcement process (changes in announcemnt structure store,terminal,tseInformation) which need to be reported
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsStatusGetTest() throws ApiException {
        UUID entityId = null;
        EntityTypes entityType = null;
        Boolean isTest = null;
        TseAnnouncementStatusInfo response = api.apiV10ImplementationDeTseAnnouncementsStatusGet(entityId, entityType, isTest);
        // TODO: test validations
    }

    /**
     * Cancel TseAnnouncement
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostTest() throws ApiException {
        UUID tseAnnouncementId = null;
        TseAnnouncementOverview response = api.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost(tseAnnouncementId);
        // TODO: test validations
    }

    /**
     * Returns the tse announcement data as pdf
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetTest() throws ApiException {
        UUID tseAnnouncementId = null;
        File response = api.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet(tseAnnouncementId);
        // TODO: test validations
    }

    /**
     * Get TseAnnouncement
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetTest() throws ApiException {
        UUID tseAnnouncementId = null;
        TseAnnouncementOverview response = api.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet(tseAnnouncementId);
        // TODO: test validations
    }

    /**
     * Resend Verification Email
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutTest() throws ApiException {
        UUID tseAnnouncementId = null;
        api.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut(tseAnnouncementId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostTest() throws ApiException {
        UUID tseAnnouncementId = null;
        String token = null;
        api.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost(tseAnnouncementId, token);
        // TODO: test validations
    }

    /**
     * Get all unannounced TseAnnouncements  This include all TseAnnouncements which has never been announced or include new changes, but not tseAnnouncements which are open (verificationPending or inProgress)
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseAnnouncementsUnannouncedGetTest() throws ApiException {
        UUID organisationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        Boolean isTest = null;
        GuidSimpleObjectPageResultModel response = api.apiV10ImplementationDeTseAnnouncementsUnannouncedGet(organisationId, pageOffset, pageSize, isTest);
        // TODO: test validations
    }

    /**
     * Download TSE Finder
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseFinderDownloadGetTest() throws ApiException {
        UUID distributorId = null;
        api.apiV10ImplementationDeTseFinderDownloadGet(distributorId);
        // TODO: test validations
    }

    /**
     * Export TseInformations as CSV
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseInformationExportGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        api.apiV10ImplementationDeTseInformationExportGet(pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Get tse informations for the given distributor
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseInformationGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        TseInformationOverviewPageResultModel response = api.apiV10ImplementationDeTseInformationGet(pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Assign OrganisationId and TerminalId to Tse
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDeTseInformationTseSerialHexAssignPutTest() throws ApiException {
        String tseSerialHex = null;
        UUID organisationId = null;
        UUID terminalId = null;
        TseInformationOverview response = api.apiV10ImplementationDeTseInformationTseSerialHexAssignPut(tseSerialHex, organisationId, terminalId);
        // TODO: test validations
    }

}
