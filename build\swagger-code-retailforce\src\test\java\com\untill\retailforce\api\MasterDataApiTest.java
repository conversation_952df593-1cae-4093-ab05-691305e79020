/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.BoolResponse;
import com.untill.retailforce.model.CloudParameter;
import com.untill.retailforce.model.EntityTypes;
import java.io.File;
import com.untill.retailforce.model.ImportModel;
import com.untill.retailforce.model.ImportModelPageResultModel;
import com.untill.retailforce.model.ParameterInfo;
import com.untill.retailforce.model.TimelogOverviewModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MasterDataApi
 */
@Disabled
public class MasterDataApiTest {

    private final MasterDataApi api = new MasterDataApi();

    /**
     * Get open support hours for distributor
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdSupportHoursGetTest() throws ApiException {
        UUID distributorId = null;
        List<TimelogOverviewModel> response = api.apiV10MasterdataDistributorsDistributorIdSupportHoursGet(distributorId);
        // TODO: test validations
    }

    /**
     * Get onboarding or customer care timelog for distributor
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdTimelogGetTest() throws ApiException {
        UUID distributorId = null;
        List<TimelogOverviewModel> response = api.apiV10MasterdataDistributorsDistributorIdTimelogGet(distributorId);
        // TODO: test validations
    }

    /**
     * Returns true when entity has allocated organizations.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataEntityEntityIdHasOrganizationGetTest() throws ApiException {
        UUID entityId = null;
        Boolean response = api.apiV10MasterdataEntityEntityIdHasOrganizationGet(entityId);
        // TODO: test validations
    }

    /**
     * Load imports of Organizations/Companies/Terminals imports with pagination for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataImportGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        ImportModelPageResultModel response = api.apiV10MasterdataImportGet(pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Returns the detail information of the requested import log.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataImportImportIdGetTest() throws ApiException {
        UUID importId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        ImportModelPageResultModel response = api.apiV10MasterdataImportImportIdGet(importId, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Import Organizations/Companies/Terminals as CSV
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataImportPostTest() throws ApiException {
        File _file = null;
        ImportModel response = api.apiV10MasterdataImportPost(_file);
        // TODO: test validations
    }

    /**
     * Returns all stored parameters for the given entity.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataParameterEntityTypeEntityIdGetTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        List<CloudParameter> response = api.apiV10MasterdataParameterEntityTypeEntityIdGet(entityType, entityId);
        // TODO: test validations
    }

    /**
     * Get parameter infos (available parameters) for the given entity.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataParameterEntityTypeEntityIdInfoGetTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        List<ParameterInfo> response = api.apiV10MasterdataParameterEntityTypeEntityIdInfoGet(entityType, entityId);
        // TODO: test validations
    }

    /**
     * Deletes an existing entity parameter.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        UUID parameterGuid = null;
        api.apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete(entityType, entityId, parameterGuid);
        // TODO: test validations
    }

    /**
     * Returns requested stored parameter for the given entity and parameterId.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        UUID parameterGuid = null;
        CloudParameter response = api.apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet(entityType, entityId, parameterGuid);
        // TODO: test validations
    }

    /**
     * Updates an existing entity parameter.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        UUID parameterGuid = null;
        CloudParameter cloudParameter = null;
        CloudParameter response = api.apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut(entityType, entityId, parameterGuid, cloudParameter);
        // TODO: test validations
    }

    /**
     * Creates a new entity parameter.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataParameterEntityTypeEntityIdPostTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        CloudParameter cloudParameter = null;
        CloudParameter response = api.apiV10MasterdataParameterEntityTypeEntityIdPost(entityType, entityId, cloudParameter);
        // TODO: test validations
    }

    /**
     * Returns whether the given vatNumber is ok or not.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataVatCheckSimpleGetTest() throws ApiException {
        String vatNumber = null;
        EntityTypes entityType = null;
        BoolResponse response = api.apiV10MasterdataVatCheckSimpleGet(vatNumber, entityType);
        // TODO: test validations
    }

}
