/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.annotations.SerializedName;

import java.io.IOException;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

/**
 * Tse Announcement Progress  Status of an announcement progress
 */
@JsonAdapter(TseAnnouncementProgress.Adapter.class)
public enum TseAnnouncementProgress {
  
  VERIFICATIONPENDING("VerificationPending"),
  
  INPROGRESS("InProgress"),
  
  SUCCEEDED("Succeeded"),
  
  CANCELLED("Cancelled"),
  
  FAILED("Failed");

  private String value;

  TseAnnouncementProgress(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  public static TseAnnouncementProgress fromValue(String value) {
    for (TseAnnouncementProgress b : TseAnnouncementProgress.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }

  public static class Adapter extends TypeAdapter<TseAnnouncementProgress> {
    @Override
    public void write(final JsonWriter jsonWriter, final TseAnnouncementProgress enumeration) throws IOException {
      jsonWriter.value(enumeration.getValue());
    }

    @Override
    public TseAnnouncementProgress read(final JsonReader jsonReader) throws IOException {
      String value = jsonReader.nextString();
      return TseAnnouncementProgress.fromValue(value);
    }
  }
}

