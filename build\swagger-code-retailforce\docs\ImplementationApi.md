# ImplementationApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ImplementationTerminalIdAuditLogGet**](ImplementationApi.md#apiV10ImplementationTerminalIdAuditLogGet) | **GET** /api/v1.0/implementation/{terminalId}/auditLog | Returns audit log records from storage. |
| [**apiV10ImplementationTerminalIdRestoreclientdataGet**](ImplementationApi.md#apiV10ImplementationTerminalIdRestoreclientdataGet) | **GET** /api/v1.0/implementation/{terminalId}/restoreclientdata | Exports current client data from cloud archive. |


<a id="apiV10ImplementationTerminalIdAuditLogGet"></a>
# **apiV10ImplementationTerminalIdAuditLogGet**
> List&lt;AuditLogEntry&gt; apiV10ImplementationTerminalIdAuditLogGet(terminalId, fromDate, tillDate, orderDesc, pageOffset, pageSize)

Returns audit log records from storage.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationApi apiInstance = new ImplementationApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal where the data is requested.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The start date of the requested records.
    OffsetDateTime tillDate = OffsetDateTime.now(); // OffsetDateTime | The end date of the requested records.
    Boolean orderDesc = false; // Boolean | Order is by date ascending, set to true for order by date descending.
    Integer pageOffset = 56; // Integer | Page offset parameter for paging.
    Integer pageSize = 56; // Integer | Page size parameter for paging.
    try {
      List<AuditLogEntry> result = apiInstance.apiV10ImplementationTerminalIdAuditLogGet(terminalId, fromDate, tillDate, orderDesc, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationApi#apiV10ImplementationTerminalIdAuditLogGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal where the data is requested. | |
| **fromDate** | **OffsetDateTime**| The start date of the requested records. | [optional] |
| **tillDate** | **OffsetDateTime**| The end date of the requested records. | [optional] |
| **orderDesc** | **Boolean**| Order is by date ascending, set to true for order by date descending. | [optional] [default to false] |
| **pageOffset** | **Integer**| Page offset parameter for paging. | [optional] |
| **pageSize** | **Integer**| Page size parameter for paging. | [optional] |

### Return type

[**List&lt;AuditLogEntry&gt;**](AuditLogEntry.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **422** | TerminalId was set to Guid.Empty. |  -  |

<a id="apiV10ImplementationTerminalIdRestoreclientdataGet"></a>
# **apiV10ImplementationTerminalIdRestoreclientdataGet**
> apiV10ImplementationTerminalIdRestoreclientdataGet(terminalId)

Exports current client data from cloud archive.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationApi apiInstance = new ImplementationApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal where the data should be exported.
    try {
      apiInstance.apiV10ImplementationTerminalIdRestoreclientdataGet(terminalId);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationApi#apiV10ImplementationTerminalIdRestoreclientdataGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal where the data should be exported. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **404** | No data found for export. |  -  |

