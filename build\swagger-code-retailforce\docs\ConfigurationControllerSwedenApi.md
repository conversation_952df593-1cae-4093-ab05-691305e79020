# ConfigurationControllerSwedenApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ConfigurationSeControlUnitDriverInfoGet**](ConfigurationControllerSwedenApi.md#apiV10ConfigurationSeControlUnitDriverInfoGet) | **GET** /api/v1.0/configuration/se/controlUnitDriverInfo | Returns supported smart card drivers for configuration in the cloud user interface. |


<a id="apiV10ConfigurationSeControlUnitDriverInfoGet"></a>
# **apiV10ConfigurationSeControlUnitDriverInfoGet**
> List&lt;ControlUnitDriverInfo&gt; apiV10ConfigurationSeControlUnitDriverInfoGet()

Returns supported smart card drivers for configuration in the cloud user interface.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerSwedenApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerSwedenApi apiInstance = new ConfigurationControllerSwedenApi(defaultClient);
    try {
      List<ControlUnitDriverInfo> result = apiInstance.apiV10ConfigurationSeControlUnitDriverInfoGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerSwedenApi#apiV10ConfigurationSeControlUnitDriverInfoGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;ControlUnitDriverInfo&gt;**](ControlUnitDriverInfo.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

