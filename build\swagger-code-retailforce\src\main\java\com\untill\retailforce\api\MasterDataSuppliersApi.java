/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import java.io.File;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import com.untill.retailforce.model.Supplier;
import com.untill.retailforce.model.SupplierContract;
import com.untill.retailforce.model.SupplierContractModel;
import com.untill.retailforce.model.SupplierContractModelPageResultModel;
import com.untill.retailforce.model.SupplierModel;
import com.untill.retailforce.model.SupplierModelPageResultModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class MasterDataSuppliersApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public MasterDataSuppliersApi() {
        this(Configuration.getDefaultApiClient());
    }

    public MasterDataSuppliersApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10MasterdataSuppliersContractsTypeSimpleGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersContractsTypeSimpleGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/contracts/type/simple";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersContractsTypeSimpleGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataSuppliersContractsTypeSimpleGetCall(_callback);

    }

    /**
     * Returns a list of RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 objects representing all available supplier contract types.
     * 
     * @return List&lt;GuidSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<GuidSimpleObject> apiV10MasterdataSuppliersContractsTypeSimpleGet() throws ApiException {
        ApiResponse<List<GuidSimpleObject>> localVarResp = apiV10MasterdataSuppliersContractsTypeSimpleGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns a list of RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 objects representing all available supplier contract types.
     * 
     * @return ApiResponse&lt;List&lt;GuidSimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<GuidSimpleObject>> apiV10MasterdataSuppliersContractsTypeSimpleGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersContractsTypeSimpleGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<GuidSimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a list of RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 objects representing all available supplier contract types. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersContractsTypeSimpleGetAsync(final ApiCallback<List<GuidSimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersContractsTypeSimpleGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<GuidSimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersGet
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersGetCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataSuppliersGetCall(pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all Suppliers for the authenticated user.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return SupplierModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupplierModelPageResultModel apiV10MasterdataSuppliersGet(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<SupplierModelPageResultModel> localVarResp = apiV10MasterdataSuppliersGetWithHttpInfo(pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all Suppliers for the authenticated user.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;SupplierModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupplierModelPageResultModel> apiV10MasterdataSuppliersGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersGetValidateBeforeCall(pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<SupplierModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all Suppliers for the authenticated user. (asynchronously)
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersGetAsync(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<SupplierModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersGetValidateBeforeCall(pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<SupplierModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersPost
     * @param supplier The supplier to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersPostCall(Supplier supplier, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = supplier;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersPostValidateBeforeCall(Supplier supplier, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataSuppliersPostCall(supplier, _callback);

    }

    /**
     * Creates a new supplier in the cloud store.
     * If RetailForce.Cloud.Model.Supplier.SupplierId set to System.Guid.Empty, then the supplier id will be generated by the service.
     * @param supplier The supplier to create. (optional)
     * @return SupplierModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupplierModel apiV10MasterdataSuppliersPost(Supplier supplier) throws ApiException {
        ApiResponse<SupplierModel> localVarResp = apiV10MasterdataSuppliersPostWithHttpInfo(supplier);
        return localVarResp.getData();
    }

    /**
     * Creates a new supplier in the cloud store.
     * If RetailForce.Cloud.Model.Supplier.SupplierId set to System.Guid.Empty, then the supplier id will be generated by the service.
     * @param supplier The supplier to create. (optional)
     * @return ApiResponse&lt;SupplierModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupplierModel> apiV10MasterdataSuppliersPostWithHttpInfo(Supplier supplier) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersPostValidateBeforeCall(supplier, null);
        Type localVarReturnType = new TypeToken<SupplierModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a new supplier in the cloud store. (asynchronously)
     * If RetailForce.Cloud.Model.Supplier.SupplierId set to System.Guid.Empty, then the supplier id will be generated by the service.
     * @param supplier The supplier to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersPostAsync(Supplier supplier, final ApiCallback<SupplierModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersPostValidateBeforeCall(supplier, _callback);
        Type localVarReturnType = new TypeToken<SupplierModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSimpleGet
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSimpleGetCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/simple";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSimpleGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataSuppliersSimpleGetCall(pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all Suppliers for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return GuidSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObjectPageResultModel apiV10MasterdataSuppliersSimpleGet(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<GuidSimpleObjectPageResultModel> localVarResp = apiV10MasterdataSuppliersSimpleGetWithHttpInfo(pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all Suppliers for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;GuidSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObjectPageResultModel> apiV10MasterdataSuppliersSimpleGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSimpleGetValidateBeforeCall(pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all Suppliers for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. (asynchronously)
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSimpleGetAsync(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<GuidSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSimpleGetValidateBeforeCall(pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdContractContractIdDelete
     * @param supplierId The supplier id of the contract to delete. (required)
     * @param contractId The id of the contract to delete. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteCall(UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId}"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()))
            .replace("{" + "contractId" + "}", localVarApiClient.escapeString(contractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteValidateBeforeCall(UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdContractContractIdDelete(Async)");
        }

        // verify the required parameter 'contractId' is set
        if (contractId == null) {
            throw new ApiException("Missing the required parameter 'contractId' when calling apiV10MasterdataSuppliersSupplierIdContractContractIdDelete(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteCall(supplierId, contractId, _callback);

    }

    /**
     * Deletes a contract.
     * 
     * @param supplierId The supplier id of the contract to delete. (required)
     * @param contractId The id of the contract to delete. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataSuppliersSupplierIdContractContractIdDelete(UUID supplierId, UUID contractId) throws ApiException {
        apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteWithHttpInfo(supplierId, contractId);
    }

    /**
     * Deletes a contract.
     * 
     * @param supplierId The supplier id of the contract to delete. (required)
     * @param contractId The id of the contract to delete. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteWithHttpInfo(UUID supplierId, UUID contractId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteValidateBeforeCall(supplierId, contractId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes a contract. (asynchronously)
     * 
     * @param supplierId The supplier id of the contract to delete. (required)
     * @param contractId The id of the contract to delete. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteAsync(UUID supplierId, UUID contractId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteValidateBeforeCall(supplierId, contractId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete
     * @param supplierId The supplier id of the contract. (required)
     * @param contractId The contract id of the contract. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteCall(UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId}/pdf"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()))
            .replace("{" + "contractId" + "}", localVarApiClient.escapeString(contractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteValidateBeforeCall(UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete(Async)");
        }

        // verify the required parameter 'contractId' is set
        if (contractId == null) {
            throw new ApiException("Missing the required parameter 'contractId' when calling apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteCall(supplierId, contractId, _callback);

    }

    /**
     * Removes a contract pdf from an existing contract.
     * 
     * @param supplierId The supplier id of the contract. (required)
     * @param contractId The contract id of the contract. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete(UUID supplierId, UUID contractId) throws ApiException {
        apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteWithHttpInfo(supplierId, contractId);
    }

    /**
     * Removes a contract pdf from an existing contract.
     * 
     * @param supplierId The supplier id of the contract. (required)
     * @param contractId The contract id of the contract. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteWithHttpInfo(UUID supplierId, UUID contractId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteValidateBeforeCall(supplierId, contractId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Removes a contract pdf from an existing contract. (asynchronously)
     * 
     * @param supplierId The supplier id of the contract. (required)
     * @param contractId The contract id of the contract. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteAsync(UUID supplierId, UUID contractId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteValidateBeforeCall(supplierId, contractId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost
     * @param supplierId The supplier id of the contract. (required)
     * @param contractId The contract id of the contract. (required)
     * @param pdf  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostCall(UUID supplierId, UUID contractId, File pdf, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId}/pdf"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()))
            .replace("{" + "contractId" + "}", localVarApiClient.escapeString(contractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pdf != null) {
            localVarFormParams.put("pdf", pdf);
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostValidateBeforeCall(UUID supplierId, UUID contractId, File pdf, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost(Async)");
        }

        // verify the required parameter 'contractId' is set
        if (contractId == null) {
            throw new ApiException("Missing the required parameter 'contractId' when calling apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostCall(supplierId, contractId, pdf, _callback);

    }

    /**
     * Adds a contract pdf file to an existing contract.
     * 
     * @param supplierId The supplier id of the contract. (required)
     * @param contractId The contract id of the contract. (required)
     * @param pdf  (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost(UUID supplierId, UUID contractId, File pdf) throws ApiException {
        apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostWithHttpInfo(supplierId, contractId, pdf);
    }

    /**
     * Adds a contract pdf file to an existing contract.
     * 
     * @param supplierId The supplier id of the contract. (required)
     * @param contractId The contract id of the contract. (required)
     * @param pdf  (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostWithHttpInfo(UUID supplierId, UUID contractId, File pdf) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostValidateBeforeCall(supplierId, contractId, pdf, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Adds a contract pdf file to an existing contract. (asynchronously)
     * 
     * @param supplierId The supplier id of the contract. (required)
     * @param contractId The contract id of the contract. (required)
     * @param pdf  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostAsync(UUID supplierId, UUID contractId, File pdf, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostValidateBeforeCall(supplierId, contractId, pdf, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdContractContractIdPut
     * @param supplierId The id of the supplier where the contract will be updated. (required)
     * @param contractId The id of the contract to update. (required)
     * @param supplierContract The new contract data. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPutCall(UUID supplierId, UUID contractId, SupplierContract supplierContract, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = supplierContract;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId}"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()))
            .replace("{" + "contractId" + "}", localVarApiClient.escapeString(contractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPutValidateBeforeCall(UUID supplierId, UUID contractId, SupplierContract supplierContract, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdContractContractIdPut(Async)");
        }

        // verify the required parameter 'contractId' is set
        if (contractId == null) {
            throw new ApiException("Missing the required parameter 'contractId' when calling apiV10MasterdataSuppliersSupplierIdContractContractIdPut(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdContractContractIdPutCall(supplierId, contractId, supplierContract, _callback);

    }

    /**
     * Updates the given contract of the supplier.
     * 
     * @param supplierId The id of the supplier where the contract will be updated. (required)
     * @param contractId The id of the contract to update. (required)
     * @param supplierContract The new contract data. (optional)
     * @return SupplierContractModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupplierContractModel apiV10MasterdataSuppliersSupplierIdContractContractIdPut(UUID supplierId, UUID contractId, SupplierContract supplierContract) throws ApiException {
        ApiResponse<SupplierContractModel> localVarResp = apiV10MasterdataSuppliersSupplierIdContractContractIdPutWithHttpInfo(supplierId, contractId, supplierContract);
        return localVarResp.getData();
    }

    /**
     * Updates the given contract of the supplier.
     * 
     * @param supplierId The id of the supplier where the contract will be updated. (required)
     * @param contractId The id of the contract to update. (required)
     * @param supplierContract The new contract data. (optional)
     * @return ApiResponse&lt;SupplierContractModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupplierContractModel> apiV10MasterdataSuppliersSupplierIdContractContractIdPutWithHttpInfo(UUID supplierId, UUID contractId, SupplierContract supplierContract) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractContractIdPutValidateBeforeCall(supplierId, contractId, supplierContract, null);
        Type localVarReturnType = new TypeToken<SupplierContractModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates the given contract of the supplier. (asynchronously)
     * 
     * @param supplierId The id of the supplier where the contract will be updated. (required)
     * @param contractId The id of the contract to update. (required)
     * @param supplierContract The new contract data. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractContractIdPutAsync(UUID supplierId, UUID contractId, SupplierContract supplierContract, final ApiCallback<SupplierContractModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractContractIdPutValidateBeforeCall(supplierId, contractId, supplierContract, _callback);
        Type localVarReturnType = new TypeToken<SupplierContractModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdContractPost
     * @param supplierId The supplier id of the supplier where the contract has to be created. (required)
     * @param supplierContract The contract to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractPostCall(UUID supplierId, SupplierContract supplierContract, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = supplierContract;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}/contract"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractPostValidateBeforeCall(UUID supplierId, SupplierContract supplierContract, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdContractPost(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdContractPostCall(supplierId, supplierContract, _callback);

    }

    /**
     * Creates a new contract for a supplier.
     * 
     * @param supplierId The supplier id of the supplier where the contract has to be created. (required)
     * @param supplierContract The contract to create. (optional)
     * @return SupplierContractModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupplierContractModel apiV10MasterdataSuppliersSupplierIdContractPost(UUID supplierId, SupplierContract supplierContract) throws ApiException {
        ApiResponse<SupplierContractModel> localVarResp = apiV10MasterdataSuppliersSupplierIdContractPostWithHttpInfo(supplierId, supplierContract);
        return localVarResp.getData();
    }

    /**
     * Creates a new contract for a supplier.
     * 
     * @param supplierId The supplier id of the supplier where the contract has to be created. (required)
     * @param supplierContract The contract to create. (optional)
     * @return ApiResponse&lt;SupplierContractModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupplierContractModel> apiV10MasterdataSuppliersSupplierIdContractPostWithHttpInfo(UUID supplierId, SupplierContract supplierContract) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractPostValidateBeforeCall(supplierId, supplierContract, null);
        Type localVarReturnType = new TypeToken<SupplierContractModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a new contract for a supplier. (asynchronously)
     * 
     * @param supplierId The supplier id of the supplier where the contract has to be created. (required)
     * @param supplierContract The contract to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractPostAsync(UUID supplierId, SupplierContract supplierContract, final ApiCallback<SupplierContractModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractPostValidateBeforeCall(supplierId, supplierContract, _callback);
        Type localVarReturnType = new TypeToken<SupplierContractModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdContractsContractIdGet
     * @param supplierId The if of the supplier of the requested contract. (required)
     * @param contractId The id of the requested contract. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractsContractIdGetCall(UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}/contracts/{contractId}"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()))
            .replace("{" + "contractId" + "}", localVarApiClient.escapeString(contractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractsContractIdGetValidateBeforeCall(UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdContractsContractIdGet(Async)");
        }

        // verify the required parameter 'contractId' is set
        if (contractId == null) {
            throw new ApiException("Missing the required parameter 'contractId' when calling apiV10MasterdataSuppliersSupplierIdContractsContractIdGet(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdContractsContractIdGetCall(supplierId, contractId, _callback);

    }

    /**
     * Returns the requested contract.
     * 
     * @param supplierId The if of the supplier of the requested contract. (required)
     * @param contractId The id of the requested contract. (required)
     * @return SupplierContractModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupplierContractModel apiV10MasterdataSuppliersSupplierIdContractsContractIdGet(UUID supplierId, UUID contractId) throws ApiException {
        ApiResponse<SupplierContractModel> localVarResp = apiV10MasterdataSuppliersSupplierIdContractsContractIdGetWithHttpInfo(supplierId, contractId);
        return localVarResp.getData();
    }

    /**
     * Returns the requested contract.
     * 
     * @param supplierId The if of the supplier of the requested contract. (required)
     * @param contractId The id of the requested contract. (required)
     * @return ApiResponse&lt;SupplierContractModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupplierContractModel> apiV10MasterdataSuppliersSupplierIdContractsContractIdGetWithHttpInfo(UUID supplierId, UUID contractId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractsContractIdGetValidateBeforeCall(supplierId, contractId, null);
        Type localVarReturnType = new TypeToken<SupplierContractModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the requested contract. (asynchronously)
     * 
     * @param supplierId The if of the supplier of the requested contract. (required)
     * @param contractId The id of the requested contract. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractsContractIdGetAsync(UUID supplierId, UUID contractId, final ApiCallback<SupplierContractModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractsContractIdGetValidateBeforeCall(supplierId, contractId, _callback);
        Type localVarReturnType = new TypeToken<SupplierContractModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdContractsGet
     * @param supplierId The supplier where the contacts are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractsGetCall(UUID supplierId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}/contracts"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractsGetValidateBeforeCall(UUID supplierId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdContractsGet(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdContractsGetCall(supplierId, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all contracts for a supplier.
     * 
     * @param supplierId The supplier where the contacts are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return SupplierContractModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupplierContractModelPageResultModel apiV10MasterdataSuppliersSupplierIdContractsGet(UUID supplierId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<SupplierContractModelPageResultModel> localVarResp = apiV10MasterdataSuppliersSupplierIdContractsGetWithHttpInfo(supplierId, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all contracts for a supplier.
     * 
     * @param supplierId The supplier where the contacts are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;SupplierContractModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupplierContractModelPageResultModel> apiV10MasterdataSuppliersSupplierIdContractsGetWithHttpInfo(UUID supplierId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractsGetValidateBeforeCall(supplierId, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<SupplierContractModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all contracts for a supplier. (asynchronously)
     * 
     * @param supplierId The supplier where the contacts are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdContractsGetAsync(UUID supplierId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<SupplierContractModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdContractsGetValidateBeforeCall(supplierId, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<SupplierContractModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdDelete
     * @param supplierId The id of the supplier which should be deleted. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdDeleteCall(UUID supplierId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdDeleteValidateBeforeCall(UUID supplierId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdDelete(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdDeleteCall(supplierId, _callback);

    }

    /**
     * Deletes an supplier from cloud store.
     * 
     * @param supplierId The id of the supplier which should be deleted. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataSuppliersSupplierIdDelete(UUID supplierId) throws ApiException {
        apiV10MasterdataSuppliersSupplierIdDeleteWithHttpInfo(supplierId);
    }

    /**
     * Deletes an supplier from cloud store.
     * 
     * @param supplierId The id of the supplier which should be deleted. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataSuppliersSupplierIdDeleteWithHttpInfo(UUID supplierId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdDeleteValidateBeforeCall(supplierId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes an supplier from cloud store. (asynchronously)
     * 
     * @param supplierId The id of the supplier which should be deleted. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdDeleteAsync(UUID supplierId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdDeleteValidateBeforeCall(supplierId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdGet
     * @param supplierId The id of the requested supplier. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdGetCall(UUID supplierId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdGetValidateBeforeCall(UUID supplierId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdGet(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdGetCall(supplierId, _callback);

    }

    /**
     * Returns the supplier requested by the given id.
     * 
     * @param supplierId The id of the requested supplier. (required)
     * @return SupplierModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupplierModel apiV10MasterdataSuppliersSupplierIdGet(UUID supplierId) throws ApiException {
        ApiResponse<SupplierModel> localVarResp = apiV10MasterdataSuppliersSupplierIdGetWithHttpInfo(supplierId);
        return localVarResp.getData();
    }

    /**
     * Returns the supplier requested by the given id.
     * 
     * @param supplierId The id of the requested supplier. (required)
     * @return ApiResponse&lt;SupplierModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupplierModel> apiV10MasterdataSuppliersSupplierIdGetWithHttpInfo(UUID supplierId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdGetValidateBeforeCall(supplierId, null);
        Type localVarReturnType = new TypeToken<SupplierModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the supplier requested by the given id. (asynchronously)
     * 
     * @param supplierId The id of the requested supplier. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdGetAsync(UUID supplierId, final ApiCallback<SupplierModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdGetValidateBeforeCall(supplierId, _callback);
        Type localVarReturnType = new TypeToken<SupplierModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdPut
     * @param supplierId The id of the supplier to be updated. (required)
     * @param supplier The supplier object to update the Supplier. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdPutCall(UUID supplierId, Supplier supplier, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = supplier;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdPutValidateBeforeCall(UUID supplierId, Supplier supplier, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdPut(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdPutCall(supplierId, supplier, _callback);

    }

    /**
     * Updates an supplier in the cloud store.
     * 
     * @param supplierId The id of the supplier to be updated. (required)
     * @param supplier The supplier object to update the Supplier. (optional)
     * @return SupplierModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupplierModel apiV10MasterdataSuppliersSupplierIdPut(UUID supplierId, Supplier supplier) throws ApiException {
        ApiResponse<SupplierModel> localVarResp = apiV10MasterdataSuppliersSupplierIdPutWithHttpInfo(supplierId, supplier);
        return localVarResp.getData();
    }

    /**
     * Updates an supplier in the cloud store.
     * 
     * @param supplierId The id of the supplier to be updated. (required)
     * @param supplier The supplier object to update the Supplier. (optional)
     * @return ApiResponse&lt;SupplierModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupplierModel> apiV10MasterdataSuppliersSupplierIdPutWithHttpInfo(UUID supplierId, Supplier supplier) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdPutValidateBeforeCall(supplierId, supplier, null);
        Type localVarReturnType = new TypeToken<SupplierModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates an supplier in the cloud store. (asynchronously)
     * 
     * @param supplierId The id of the supplier to be updated. (required)
     * @param supplier The supplier object to update the Supplier. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdPutAsync(UUID supplierId, Supplier supplier, final ApiCallback<SupplierModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdPutValidateBeforeCall(supplierId, supplier, _callback);
        Type localVarReturnType = new TypeToken<SupplierModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataSuppliersSupplierIdSimpleGet
     * @param supplierId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdSimpleGetCall(UUID supplierId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/suppliers/{supplierId}/simple"
            .replace("{" + "supplierId" + "}", localVarApiClient.escapeString(supplierId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataSuppliersSupplierIdSimpleGetValidateBeforeCall(UUID supplierId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supplierId' is set
        if (supplierId == null) {
            throw new ApiException("Missing the required parameter 'supplierId' when calling apiV10MasterdataSuppliersSupplierIdSimpleGet(Async)");
        }

        return apiV10MasterdataSuppliersSupplierIdSimpleGetCall(supplierId, _callback);

    }

    /**
     * Returns a simple supplierRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     * 
     * @param supplierId  (required)
     * @return GuidSimpleObject
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObject apiV10MasterdataSuppliersSupplierIdSimpleGet(UUID supplierId) throws ApiException {
        ApiResponse<GuidSimpleObject> localVarResp = apiV10MasterdataSuppliersSupplierIdSimpleGetWithHttpInfo(supplierId);
        return localVarResp.getData();
    }

    /**
     * Returns a simple supplierRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     * 
     * @param supplierId  (required)
     * @return ApiResponse&lt;GuidSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObject> apiV10MasterdataSuppliersSupplierIdSimpleGetWithHttpInfo(UUID supplierId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdSimpleGetValidateBeforeCall(supplierId, null);
        Type localVarReturnType = new TypeToken<GuidSimpleObject>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a simple supplierRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. (asynchronously)
     * 
     * @param supplierId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataSuppliersSupplierIdSimpleGetAsync(UUID supplierId, final ApiCallback<GuidSimpleObject> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataSuppliersSupplierIdSimpleGetValidateBeforeCall(supplierId, _callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObject>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
