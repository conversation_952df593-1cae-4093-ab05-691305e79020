/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.GuidEntityVersionPageResultModel;
import com.untill.retailforce.model.Int32SimpleObject;
import com.untill.retailforce.model.Organisation;
import com.untill.retailforce.model.OrganisationModel;
import com.untill.retailforce.model.OrganisationModelPageResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MasterDataOrganisationsApi
 */
@Disabled
public class MasterDataOrganisationsApiTest {

    private final MasterDataOrganisationsApi api = new MasterDataOrganisationsApi();

    /**
     * Returns all organisations for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        UUID organisationId = null;
        UUID distributorId = null;
        OrganisationModelPageResultModel response = api.apiV10MasterdataOrganisationsGet(pageOffset, pageSize, searchString, organisationId, distributorId);
        // TODO: test validations
    }

    /**
     * Deletes an organisation from cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsOrganisationIdDeleteTest() throws ApiException {
        UUID organisationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        api.apiV10MasterdataOrganisationsOrganisationIdDelete(organisationId, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Returns the organisation requested by the given id.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsOrganisationIdGetTest() throws ApiException {
        UUID organisationId = null;
        String entityVersion = null;
        OrganisationModel response = api.apiV10MasterdataOrganisationsOrganisationIdGet(organisationId, entityVersion);
        // TODO: test validations
    }

    /**
     * Updates an organisation in the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsOrganisationIdPutTest() throws ApiException {
        UUID organisationId = null;
        Organisation organisation = null;
        OrganisationModel response = api.apiV10MasterdataOrganisationsOrganisationIdPut(organisationId, organisation);
        // TODO: test validations
    }

    /**
     * Get organisation versions
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsOrganisationIdVersionsGetTest() throws ApiException {
        UUID organisationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        GuidEntityVersionPageResultModel response = api.apiV10MasterdataOrganisationsOrganisationIdVersionsGet(organisationId, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Returns all custom document types for the requested organization.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetTest() throws ApiException {
        UUID organizationId = null;
        Boolean allDocumentTypes = null;
        List<Int32SimpleObject> response = api.apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet(organizationId, allDocumentTypes);
        // TODO: test validations
    }

    /**
     * Updates the list of custom document types for the given organization.
     *
     * Important: You have to send the whole list of custom document types; otherwise existing custom document types will be deleted.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutTest() throws ApiException {
        UUID organizationId = null;
        List<Int32SimpleObject> int32SimpleObject = null;
        api.apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut(organizationId, int32SimpleObject);
        // TODO: test validations
    }

    /**
     * Creates a new organization in the cloud store.
     *
     * If RetailForce.Cloud.Model.Organisation.OrganisationId set to System.Guid.Empty, then the organization id will be generated by the service.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsPostTest() throws ApiException {
        Organisation organisation = null;
        OrganisationModel response = api.apiV10MasterdataOrganisationsPost(organisation);
        // TODO: test validations
    }

}
