# ImplementationControllerDenmarkApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ImplementationDkSaftValidatePost**](ImplementationControllerDenmarkApi.md#apiV10ImplementationDkSaftValidatePost) | **POST** /api/v1.0/implementation/dk/saft/validate | Validates given zip file audit file (denmark saf-t) content. |
| [**apiV10ImplementationDkTerminalIdSaftGet**](ImplementationControllerDenmarkApi.md#apiV10ImplementationDkTerminalIdSaftGet) | **GET** /api/v1.0/implementation/dk/{terminalId}/saft | Exports denmark saf-t format for the given terminal. |


<a id="apiV10ImplementationDkSaftValidatePost"></a>
# **apiV10ImplementationDkSaftValidatePost**
> List&lt;ValidationError&gt; apiV10ImplementationDkSaftValidatePost(zipFile)

Validates given zip file audit file (denmark saf-t) content.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerDenmarkApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerDenmarkApi apiInstance = new ImplementationControllerDenmarkApi(defaultClient);
    File zipFile = new File("/path/to/file"); // File | 
    try {
      List<ValidationError> result = apiInstance.apiV10ImplementationDkSaftValidatePost(zipFile);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerDenmarkApi#apiV10ImplementationDkSaftValidatePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **zipFile** | **File**|  | [optional] |

### Return type

[**List&lt;ValidationError&gt;**](ValidationError.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **422** | Parameter zip file not supplied. |  -  |

<a id="apiV10ImplementationDkTerminalIdSaftGet"></a>
# **apiV10ImplementationDkTerminalIdSaftGet**
> apiV10ImplementationDkTerminalIdSaftGet(terminalId, fromDate, tillDate, exportRawData, exportFilePerClosing)

Exports denmark saf-t format for the given terminal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerDenmarkApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerDenmarkApi apiInstance = new ImplementationControllerDenmarkApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal where the data should be exported.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The start date of the export.
    OffsetDateTime tillDate = OffsetDateTime.now(); // OffsetDateTime | The end date of the export.
    Boolean exportRawData = false; // Boolean | Optional. If true signature raw data is exported in cashTransaction.Desc (description field). Do not use for export to authorities, this feature is for signature verification analysis.
    Boolean exportFilePerClosing = false; // Boolean | Optional. If true a saf-t file will be exported per day. Otherwise, all days are exported in one file.
    try {
      apiInstance.apiV10ImplementationDkTerminalIdSaftGet(terminalId, fromDate, tillDate, exportRawData, exportFilePerClosing);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerDenmarkApi#apiV10ImplementationDkTerminalIdSaftGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal where the data should be exported. | |
| **fromDate** | **OffsetDateTime**| The start date of the export. | |
| **tillDate** | **OffsetDateTime**| The end date of the export. | |
| **exportRawData** | **Boolean**| Optional. If true signature raw data is exported in cashTransaction.Desc (description field). Do not use for export to authorities, this feature is for signature verification analysis. | [optional] [default to false] |
| **exportFilePerClosing** | **Boolean**| Optional. If true a saf-t file will be exported per day. Otherwise, all days are exported in one file. | [optional] [default to false] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **404** | No data found for export or OCES3 certificate not found. |  -  |
| **422** | Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. |  -  |

