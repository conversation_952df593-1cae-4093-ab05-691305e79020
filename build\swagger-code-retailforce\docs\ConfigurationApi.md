# ConfigurationApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ConfigurationAccessLicenseConfigurationInfoGet**](ConfigurationApi.md#apiV10ConfigurationAccessLicenseConfigurationInfoGet) | **GET** /api/v1.0/configuration/access-license/configuration/info | Returns supported access license configuration parameters in the cloud user interface. |
| [**apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost**](ConfigurationApi.md#apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost) | **POST** /api/v1.0/configuration/certificate/csr/{entityType}/{entityId}/{csrRequestId} | Stores external certificate (with prior created certificate signing request (csr)). |
| [**apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet**](ConfigurationApi.md#apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet) | **GET** /api/v1.0/configuration/certificate/csr/{entityType}/{entityId} | Creates a certificate request file (csr) for the given distributor. |
| [**apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet**](ConfigurationApi.md#apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet) | **GET** /api/v1.0/configuration/certificate/csr/{entityType}/{entityId}/open | Get open csrs |
| [**apiV10ConfigurationCertificateEntityTypeEntityIdDelete**](ConfigurationApi.md#apiV10ConfigurationCertificateEntityTypeEntityIdDelete) | **DELETE** /api/v1.0/configuration/certificate/{entityType}/{entityId} | Removes the given external certificate from the store. |
| [**apiV10ConfigurationCertificateEntityTypeEntityIdFilePost**](ConfigurationApi.md#apiV10ConfigurationCertificateEntityTypeEntityIdFilePost) | **POST** /api/v1.0/configuration/certificate/{entityType}/{entityId}/file | Returns the external certificate for download. |
| [**apiV10ConfigurationCertificateEntityTypeEntityIdGet**](ConfigurationApi.md#apiV10ConfigurationCertificateEntityTypeEntityIdGet) | **GET** /api/v1.0/configuration/certificate/{entityType}/{entityId} | Returns all external certificates for the given entity. |
| [**apiV10ConfigurationCertificateEntityTypeEntityIdPost**](ConfigurationApi.md#apiV10ConfigurationCertificateEntityTypeEntityIdPost) | **POST** /api/v1.0/configuration/certificate/{entityType}/{entityId} | Stores the given certificate to the certificate store. |
| [**apiV10ConfigurationCertificateTerminalIdGet**](ConfigurationApi.md#apiV10ConfigurationCertificateTerminalIdGet) | **GET** /api/v1.0/configuration/certificate/{terminalId} | Returns the certificate structure for an external certificate for the client. |
| [**apiV10ConfigurationConfigurationIdDelete**](ConfigurationApi.md#apiV10ConfigurationConfigurationIdDelete) | **DELETE** /api/v1.0/configuration/{configurationId} | Deletes a configuration in the cloud. |
| [**apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions**](ConfigurationApi.md#apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions) | **OPTIONS** /api/v1.0/configuration/{configurationId}/digitalReceipt/mail | If special configuration for digital receipt mail sending is configured, you can test this here. |
| [**apiV10ConfigurationConfigurationIdGet**](ConfigurationApi.md#apiV10ConfigurationConfigurationIdGet) | **GET** /api/v1.0/configuration/{configurationId} | Returns a configuration requested by id for the authenticated user. |
| [**apiV10ConfigurationConfigurationIdPut**](ConfigurationApi.md#apiV10ConfigurationConfigurationIdPut) | **PUT** /api/v1.0/configuration/{configurationId} | Updates a configuration in the retail cloud service. |
| [**apiV10ConfigurationFiscalClientGet**](ConfigurationApi.md#apiV10ConfigurationFiscalClientGet) | **GET** /api/v1.0/configuration/fiscalClient | Function for TrustedFiscalModule to download fiscal client information (CreateClientByCloud). |
| [**apiV10ConfigurationFiscalClientPatch**](ConfigurationApi.md#apiV10ConfigurationFiscalClientPatch) | **PATCH** /api/v1.0/configuration/fiscalClient | Function for TrustedFiscalModule to commit fiscal client configuration download. |
| [**apiV10ConfigurationFiscalClientValidateGet**](ConfigurationApi.md#apiV10ConfigurationFiscalClientValidateGet) | **GET** /api/v1.0/configuration/fiscalClient/validate | Function to validate the fiscal client configured in the cloud. |
| [**apiV10ConfigurationFiscalClientValidateTerminalIdGet**](ConfigurationApi.md#apiV10ConfigurationFiscalClientValidateTerminalIdGet) | **GET** /api/v1.0/configuration/fiscalClient/validate/{terminalId} | Function to validate the fiscal client configured in the cloud. |
| [**apiV10ConfigurationFiscalclientPost**](ConfigurationApi.md#apiV10ConfigurationFiscalclientPost) | **POST** /api/v1.0/configuration/fiscalclient | Creates a client configuration based on fiscal client data. |
| [**apiV10ConfigurationGet**](ConfigurationApi.md#apiV10ConfigurationGet) | **GET** /api/v1.0/configuration | Returns all configurations for the given organisation for the authenticated user. |
| [**apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet**](ConfigurationApi.md#apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet) | **GET** /api/v1.0/configuration/id/{organisationId}/{storeNumber}/{terminalNumber} | Returns the configuration id for the requested terminal. |
| [**apiV10ConfigurationOrganisationCompanyGet**](ConfigurationApi.md#apiV10ConfigurationOrganisationCompanyGet) | **GET** /api/v1.0/configuration/organisation/company | Returns the guid for a organization by company companyidentification. |
| [**apiV10ConfigurationOrganisationGet**](ConfigurationApi.md#apiV10ConfigurationOrganisationGet) | **GET** /api/v1.0/configuration/organisation | Returns the guid for a organization. |
| [**apiV10ConfigurationPost**](ConfigurationApi.md#apiV10ConfigurationPost) | **POST** /api/v1.0/configuration | Create configuration in the retail cloud service. |
| [**apiV10ConfigurationSignatureGet**](ConfigurationApi.md#apiV10ConfigurationSignatureGet) | **GET** /api/v1.0/configuration/signature | Returns the public certificate for retailforce signature CA certificate (CER Format - base64 encoded, key length 4096 bit). |
| [**apiV10ConfigurationSignatureOrganizationIdGet**](ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdGet) | **GET** /api/v1.0/configuration/signature/{organizationId} | Returns the public certificate generated for the organization (CER Format - base64 encoded, key length 2048 bit). |
| [**apiV10ConfigurationSignatureOrganizationIdPatch**](ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdPatch) | **PATCH** /api/v1.0/configuration/signature/{organizationId} | Validates the given signature data with the signature using the organization certificate. |
| [**apiV10ConfigurationSignatureOrganizationIdPost**](ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdPost) | **POST** /api/v1.0/configuration/signature/{organizationId} | Validates the given certificate if it is valid (Certificate signature, correct organization, valid time). |
| [**apiV10ConfigurationSignatureOrganizationIdTerminalIdGet**](ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdTerminalIdGet) | **GET** /api/v1.0/configuration/signature/{organizationId}/{terminalId} | Returns the certificate for the terminal (pfx Format - base64 encoded, key length 1024 bit). |
| [**apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch**](ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch) | **PATCH** /api/v1.0/configuration/signature/{organizationId}/{terminalId} | Validates the given signature data with the signature using the organization certificate. |
| [**apiV10ConfigurationSignatureOrganizationIdTerminalIdPost**](ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdTerminalIdPost) | **POST** /api/v1.0/configuration/signature/{organizationId}/{terminalId} | Validates the given certificate if it is valid (Certificate signature, correct organization, valid time). |


<a id="apiV10ConfigurationAccessLicenseConfigurationInfoGet"></a>
# **apiV10ConfigurationAccessLicenseConfigurationInfoGet**
> List&lt;AccessLicenseConfigurationInfo&gt; apiV10ConfigurationAccessLicenseConfigurationInfoGet()

Returns supported access license configuration parameters in the cloud user interface.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    try {
      List<AccessLicenseConfigurationInfo> result = apiInstance.apiV10ConfigurationAccessLicenseConfigurationInfoGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationAccessLicenseConfigurationInfoGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;AccessLicenseConfigurationInfo&gt;**](AccessLicenseConfigurationInfo.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost"></a>
# **apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost**
> CertificateModel apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(entityType, entityId, csrRequestId, certificateName, certFile, additionalCertFile, rootCertFile)

Stores external certificate (with prior created certificate signing request (csr)).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    String entityType = "entityType_example"; // String | At the moment only \"Distributor\" is supported.
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity where the certificate should be stored.
    UUID csrRequestId = UUID.randomUUID(); // UUID | The id of the request.
    String certificateName = "certificateName_example"; // String | The name of the certificate in the store.
    File certFile = new File("/path/to/file"); // File | 
    File additionalCertFile = new File("/path/to/file"); // File | 
    File rootCertFile = new File("/path/to/file"); // File | 
    try {
      CertificateModel result = apiInstance.apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(entityType, entityId, csrRequestId, certificateName, certFile, additionalCertFile, rootCertFile);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| At the moment only \&quot;Distributor\&quot; is supported. | |
| **entityId** | **UUID**| The id of the entity where the certificate should be stored. | |
| **csrRequestId** | **UUID**| The id of the request. | |
| **certificateName** | **String**| The name of the certificate in the store. | |
| **certFile** | **File**|  | |
| **additionalCertFile** | **File**|  | [optional] |
| **rootCertFile** | **File**|  | [optional] |

### Return type

[**CertificateModel**](CertificateModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet"></a>
# **apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet**
> apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet(entityType, entityId, subjectInfo)

Creates a certificate request file (csr) for the given distributor.

The certificate request depends on the organization structure if organization or company data is returned.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    String entityType = "entityType_example"; // String | At the moment only \"Distributor\" is supported.
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity where the certificate should be stored.
    String subjectInfo = "subjectInfo_example"; // String | Additional information to the subject of the certificate request.
    try {
      apiInstance.apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet(entityType, entityId, subjectInfo);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| At the moment only \&quot;Distributor\&quot; is supported. | |
| **entityId** | **UUID**| The id of the entity where the certificate should be stored. | |
| **subjectInfo** | **String**| Additional information to the subject of the certificate request. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet"></a>
# **apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet**
> List&lt;GuidSimpleObject&gt; apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet(entityType, entityId)

Get open csrs

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    String entityType = "entityType_example"; // String | 
    UUID entityId = UUID.randomUUID(); // UUID | 
    try {
      List<GuidSimpleObject> result = apiInstance.apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet(entityType, entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**|  | |
| **entityId** | **UUID**|  | |

### Return type

[**List&lt;GuidSimpleObject&gt;**](GuidSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCertificateEntityTypeEntityIdDelete"></a>
# **apiV10ConfigurationCertificateEntityTypeEntityIdDelete**
> apiV10ConfigurationCertificateEntityTypeEntityIdDelete(entityType, entityId, certificateEntityId)

Removes the given external certificate from the store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    String entityType = "entityType_example"; // String | The entity type of the external certificate.
    UUID entityId = UUID.randomUUID(); // UUID | The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER).
    UUID certificateEntityId = UUID.randomUUID(); // UUID | The certificate id (if null 'Default' is used).
    try {
      apiInstance.apiV10ConfigurationCertificateEntityTypeEntityIdDelete(entityType, entityId, certificateEntityId);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationCertificateEntityTypeEntityIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The entity type of the external certificate. | |
| **entityId** | **UUID**| The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). | |
| **certificateEntityId** | **UUID**| The certificate id (if null &#39;Default&#39; is used). | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCertificateEntityTypeEntityIdFilePost"></a>
# **apiV10ConfigurationCertificateEntityTypeEntityIdFilePost**
> apiV10ConfigurationCertificateEntityTypeEntityIdFilePost(entityType, entityId, certificateName, certificatePassword)

Returns the external certificate for download.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    String entityType = "entityType_example"; // String | The entity type of the external certificate.
    UUID entityId = UUID.randomUUID(); // UUID | The entityId of the external certificate.
    String certificateName = "certificateName_example"; // String | The name of the certificate. If null \"Default\" is used.
    String certificatePassword = "certificatePassword_example"; // String | 
    try {
      apiInstance.apiV10ConfigurationCertificateEntityTypeEntityIdFilePost(entityType, entityId, certificateName, certificatePassword);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationCertificateEntityTypeEntityIdFilePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The entity type of the external certificate. | |
| **entityId** | **UUID**| The entityId of the external certificate. | |
| **certificateName** | **String**| The name of the certificate. If null \&quot;Default\&quot; is used. | [optional] |
| **certificatePassword** | **String**|  | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCertificateEntityTypeEntityIdGet"></a>
# **apiV10ConfigurationCertificateEntityTypeEntityIdGet**
> List&lt;CertificateModel&gt; apiV10ConfigurationCertificateEntityTypeEntityIdGet(entityType, entityId)

Returns all external certificates for the given entity.

Value of private key encrypted will not be returned from this method.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    String entityType = "entityType_example"; // String | The entity type of the external certificate.
    UUID entityId = UUID.randomUUID(); // UUID | The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER).
    try {
      List<CertificateModel> result = apiInstance.apiV10ConfigurationCertificateEntityTypeEntityIdGet(entityType, entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationCertificateEntityTypeEntityIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The entity type of the external certificate. | |
| **entityId** | **UUID**| The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). | |

### Return type

[**List&lt;CertificateModel&gt;**](CertificateModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCertificateEntityTypeEntityIdPost"></a>
# **apiV10ConfigurationCertificateEntityTypeEntityIdPost**
> CertificateModel apiV10ConfigurationCertificateEntityTypeEntityIdPost(entityType, entityId, certificateFormat, _file, certificatePassword, certificateName)

Stores the given certificate to the certificate store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    String entityType = "entityType_example"; // String | The entity type of the external certificate.
    UUID entityId = UUID.randomUUID(); // UUID | The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER).
    CertificateFormat certificateFormat = CertificateFormat.fromValue("pfx"); // CertificateFormat | A RetailForce.Fiscalisation.Signing.CertificateFormat enum value defining the certificate format. Default is RetailForce.Fiscalisation.Signing.CertificateFormat.Pfx.
    File _file = new File("/path/to/file"); // File | 
    String certificatePassword = "certificatePassword_example"; // String | 
    String certificateName = "certificateName_example"; // String | 
    try {
      CertificateModel result = apiInstance.apiV10ConfigurationCertificateEntityTypeEntityIdPost(entityType, entityId, certificateFormat, _file, certificatePassword, certificateName);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationCertificateEntityTypeEntityIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The entity type of the external certificate. | |
| **entityId** | **UUID**| The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). | |
| **certificateFormat** | [**CertificateFormat**](.md)| A RetailForce.Fiscalisation.Signing.CertificateFormat enum value defining the certificate format. Default is RetailForce.Fiscalisation.Signing.CertificateFormat.Pfx. | [optional] [enum: pfx, der] |
| **_file** | **File**|  | [optional] |
| **certificatePassword** | **String**|  | [optional] |
| **certificateName** | **String**|  | [optional] |

### Return type

[**CertificateModel**](CertificateModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCertificateTerminalIdGet"></a>
# **apiV10ConfigurationCertificateTerminalIdGet**
> Certificate apiV10ConfigurationCertificateTerminalIdGet(terminalId, certificateName)

Returns the certificate structure for an external certificate for the client.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id where the certificate is requested.
    String certificateName = "certificateName_example"; // String | The name of the certificate to download; if null the default certificate is downloaded.
    try {
      Certificate result = apiInstance.apiV10ConfigurationCertificateTerminalIdGet(terminalId, certificateName);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationCertificateTerminalIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id where the certificate is requested. | |
| **certificateName** | **String**| The name of the certificate to download; if null the default certificate is downloaded. | [optional] |

### Return type

[**Certificate**](Certificate.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationConfigurationIdDelete"></a>
# **apiV10ConfigurationConfigurationIdDelete**
> apiV10ConfigurationConfigurationIdDelete(configurationId)

Deletes a configuration in the cloud.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration to delete.
    try {
      apiInstance.apiV10ConfigurationConfigurationIdDelete(configurationId);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationConfigurationIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration to delete. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions"></a>
# **apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions**
> apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions(configurationId, testEmail)

If special configuration for digital receipt mail sending is configured, you can test this here.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration where the special mail configuration was done.
    String testEmail = "testEmail_example"; // String | The email address where the test mail should be send.
    try {
      apiInstance.apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions(configurationId, testEmail);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration where the special mail configuration was done. | |
| **testEmail** | **String**| The email address where the test mail should be send. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationConfigurationIdGet"></a>
# **apiV10ConfigurationConfigurationIdGet**
> FiscalClientConfigurationModel apiV10ConfigurationConfigurationIdGet(configurationId)

Returns a configuration requested by id for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration which is requested.
    try {
      FiscalClientConfigurationModel result = apiInstance.apiV10ConfigurationConfigurationIdGet(configurationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationConfigurationIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration which is requested. | |

### Return type

[**FiscalClientConfigurationModel**](FiscalClientConfigurationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationConfigurationIdPut"></a>
# **apiV10ConfigurationConfigurationIdPut**
> FiscalClientConfigurationModel apiV10ConfigurationConfigurationIdPut(configurationId, fiscalClientConfiguration)

Updates a configuration in the retail cloud service.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration to update.
    FiscalClientConfiguration fiscalClientConfiguration = new FiscalClientConfiguration(); // FiscalClientConfiguration | The data of the configuration to update.
    try {
      FiscalClientConfigurationModel result = apiInstance.apiV10ConfigurationConfigurationIdPut(configurationId, fiscalClientConfiguration);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationConfigurationIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration to update. | |
| **fiscalClientConfiguration** | [**FiscalClientConfiguration**](FiscalClientConfiguration.md)| The data of the configuration to update. | |

### Return type

[**FiscalClientConfigurationModel**](FiscalClientConfigurationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationFiscalClientGet"></a>
# **apiV10ConfigurationFiscalClientGet**
> FiscalClient apiV10ConfigurationFiscalClientGet(organisationId, storeNumber, terminalNumber, restore, cloudFiscalisation)

Function for TrustedFiscalModule to download fiscal client information (CreateClientByCloud).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the organisation.
    String storeNumber = "storeNumber_example"; // String | The store number for the client configuration.
    String terminalNumber = "terminalNumber_example"; // String | The terminal number for the client configuration.
    Boolean restore = false; // Boolean | True if the configuration should be loaded in a restore process; default is false.
    Boolean cloudFiscalisation = false; // Boolean | True if the configuration is downloaded for cloud fiscalisation client; otherwise false. Default is false.
    try {
      FiscalClient result = apiInstance.apiV10ConfigurationFiscalClientGet(organisationId, storeNumber, terminalNumber, restore, cloudFiscalisation);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationFiscalClientGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the organisation. | [optional] |
| **storeNumber** | **String**| The store number for the client configuration. | [optional] |
| **terminalNumber** | **String**| The terminal number for the client configuration. | [optional] |
| **restore** | **Boolean**| True if the configuration should be loaded in a restore process; default is false. | [optional] [default to false] |
| **cloudFiscalisation** | **Boolean**| True if the configuration is downloaded for cloud fiscalisation client; otherwise false. Default is false. | [optional] [default to false] |

### Return type

[**FiscalClient**](FiscalClient.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | OK |  -  |
| **402** | Appropriate license (GetConfiguration) does not exist for this client. |  -  |
| **403** | Nobody is authenticated. |  -  |
| **409** | Requested organization, store or terminal is not found. |  -  |
| **422** | Parameter organisationId is Guid.Empty or parameter storeNumber / terminalNumber are set to null or empty string. |  -  |
| **501** | Method is called to create client for cloud fiscalisation, but fiscal country does not support cloud fiscalisation. |  -  |

<a id="apiV10ConfigurationFiscalClientPatch"></a>
# **apiV10ConfigurationFiscalClientPatch**
> apiV10ConfigurationFiscalClientPatch(organisationId, storeNumber, terminalNumber)

Function for TrustedFiscalModule to commit fiscal client configuration download.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the organization.
    String storeNumber = "storeNumber_example"; // String | The store number for the client configuration.
    String terminalNumber = "terminalNumber_example"; // String | The terminal number for the client configuration.
    try {
      apiInstance.apiV10ConfigurationFiscalClientPatch(organisationId, storeNumber, terminalNumber);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationFiscalClientPatch");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the organization. | [optional] |
| **storeNumber** | **String**| The store number for the client configuration. | [optional] |
| **terminalNumber** | **String**| The terminal number for the client configuration. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationFiscalClientValidateGet"></a>
# **apiV10ConfigurationFiscalClientValidateGet**
> List&lt;ValidationError&gt; apiV10ConfigurationFiscalClientValidateGet(organisationId, storeNumber, terminalNumber)

Function to validate the fiscal client configured in the cloud.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the organisation.
    String storeNumber = "storeNumber_example"; // String | The store number for the client configuration.
    String terminalNumber = "terminalNumber_example"; // String | The terminal number for the client configuration.
    try {
      List<ValidationError> result = apiInstance.apiV10ConfigurationFiscalClientValidateGet(organisationId, storeNumber, terminalNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationFiscalClientValidateGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the organisation. | [optional] |
| **storeNumber** | **String**| The store number for the client configuration. | [optional] |
| **terminalNumber** | **String**| The terminal number for the client configuration. | [optional] |

### Return type

[**List&lt;ValidationError&gt;**](ValidationError.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationFiscalClientValidateTerminalIdGet"></a>
# **apiV10ConfigurationFiscalClientValidateTerminalIdGet**
> List&lt;ValidationError&gt; apiV10ConfigurationFiscalClientValidateTerminalIdGet(terminalId)

Function to validate the fiscal client configured in the cloud.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal (uniqueClientId).
    try {
      List<ValidationError> result = apiInstance.apiV10ConfigurationFiscalClientValidateTerminalIdGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationFiscalClientValidateTerminalIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal (uniqueClientId). | |

### Return type

[**List&lt;ValidationError&gt;**](ValidationError.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationFiscalclientPost"></a>
# **apiV10ConfigurationFiscalclientPost**
> FiscalClient apiV10ConfigurationFiscalclientPost(fiscalClient)

Creates a client configuration based on fiscal client data.



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    FiscalClient fiscalClient = new FiscalClient(); // FiscalClient | The fiscal client to create.
    try {
      FiscalClient result = apiInstance.apiV10ConfigurationFiscalclientPost(fiscalClient);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationFiscalclientPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **fiscalClient** | [**FiscalClient**](FiscalClient.md)| The fiscal client to create. | [optional] |

### Return type

[**FiscalClient**](FiscalClient.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationGet"></a>
# **apiV10ConfigurationGet**
> List&lt;GuidExtendedSimpleCountryObject&gt; apiV10ConfigurationGet(entityId, allCountries)

Returns all configurations for the given organisation for the authenticated user.

All countries options only works on organisation level.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity we are going to load all possible configurations for.
    Boolean allCountries = true; // Boolean | if set load all countries for this entity
    try {
      List<GuidExtendedSimpleCountryObject> result = apiInstance.apiV10ConfigurationGet(entityId, allCountries);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| The id of the entity we are going to load all possible configurations for. | [optional] |
| **allCountries** | **Boolean**| if set load all countries for this entity | [optional] |

### Return type

[**List&lt;GuidExtendedSimpleCountryObject&gt;**](GuidExtendedSimpleCountryObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet"></a>
# **apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet**
> UUID apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet(organisationId, storeNumber, terminalNumber)

Returns the configuration id for the requested terminal.

Only returns the configuration id of the configuration stored at terminal level. No inheritance used.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The organisation id of the terminal of the requested configuration.
    String storeNumber = "storeNumber_example"; // String | The store number of the terminal of the requested configuration.
    String terminalNumber = "terminalNumber_example"; // String | The terminal number of the terminal of the requested configuration.
    try {
      UUID result = apiInstance.apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet(organisationId, storeNumber, terminalNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The organisation id of the terminal of the requested configuration. | |
| **storeNumber** | **String**| The store number of the terminal of the requested configuration. | |
| **terminalNumber** | **String**| The terminal number of the terminal of the requested configuration. | |

### Return type

[**UUID**](UUID.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationOrganisationCompanyGet"></a>
# **apiV10ConfigurationOrganisationCompanyGet**
> UUID apiV10ConfigurationOrganisationCompanyGet(identificationType, identification)

Returns the guid for a organization by company companyidentification.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    IdentificationType identificationType = IdentificationType.fromValue("[0] = VatNumber"); // IdentificationType | The type of the identification for the organisation id.
    String identification = "identification_example"; // String | The identification.
    try {
      UUID result = apiInstance.apiV10ConfigurationOrganisationCompanyGet(identificationType, identification);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationOrganisationCompanyGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **identificationType** | [**IdentificationType**](.md)| The type of the identification for the organisation id. | [optional] [enum: [0] = VatNumber, [1] = TaxNumber, [2] = GlnNumber, [3] = BusinessIdentificationNumber, [4] = Statistical classification, [5] = CommercialRegisterNumber, [6] = CommercialRegisterNumberOffice, [7] = TradeRegisterNumber, [8] = TaxOfficeNumber] |
| **identification** | **String**| The identification. | [optional] |

### Return type

[**UUID**](UUID.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationOrganisationGet"></a>
# **apiV10ConfigurationOrganisationGet**
> UUID apiV10ConfigurationOrganisationGet(identificationType, identification)

Returns the guid for a organization.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    IdentificationType identificationType = IdentificationType.fromValue("[0] = VatNumber"); // IdentificationType | The type of the identification for the organisation id.
    String identification = "identification_example"; // String | The identification.
    try {
      UUID result = apiInstance.apiV10ConfigurationOrganisationGet(identificationType, identification);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationOrganisationGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **identificationType** | [**IdentificationType**](.md)| The type of the identification for the organisation id. | [optional] [enum: [0] = VatNumber, [1] = TaxNumber, [2] = GlnNumber, [3] = BusinessIdentificationNumber, [4] = Statistical classification, [5] = CommercialRegisterNumber, [6] = CommercialRegisterNumberOffice, [7] = TradeRegisterNumber, [8] = TaxOfficeNumber] |
| **identification** | **String**| The identification. | [optional] |

### Return type

[**UUID**](UUID.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationPost"></a>
# **apiV10ConfigurationPost**
> FiscalClientConfigurationModel apiV10ConfigurationPost(fiscalClientConfiguration)

Create configuration in the retail cloud service.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    FiscalClientConfiguration fiscalClientConfiguration = new FiscalClientConfiguration(); // FiscalClientConfiguration | A RetailForce.Cloud.Model.Configuration.FiscalClientConfiguration object representing the configuration to create.
    try {
      FiscalClientConfigurationModel result = apiInstance.apiV10ConfigurationPost(fiscalClientConfiguration);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **fiscalClientConfiguration** | [**FiscalClientConfiguration**](FiscalClientConfiguration.md)| A RetailForce.Cloud.Model.Configuration.FiscalClientConfiguration object representing the configuration to create. | |

### Return type

[**FiscalClientConfigurationModel**](FiscalClientConfigurationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationSignatureGet"></a>
# **apiV10ConfigurationSignatureGet**
> String apiV10ConfigurationSignatureGet()

Returns the public certificate for retailforce signature CA certificate (CER Format - base64 encoded, key length 4096 bit).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    try {
      String result = apiInstance.apiV10ConfigurationSignatureGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationSignatureGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Nobody is authenticated. |  -  |

<a id="apiV10ConfigurationSignatureOrganizationIdGet"></a>
# **apiV10ConfigurationSignatureOrganizationIdGet**
> String apiV10ConfigurationSignatureOrganizationIdGet(organizationId)

Returns the public certificate generated for the organization (CER Format - base64 encoded, key length 2048 bit).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization id of the organization where the certificate is requested.
    try {
      String result = apiInstance.apiV10ConfigurationSignatureOrganizationIdGet(organizationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationSignatureOrganizationIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization id of the organization where the certificate is requested. | |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Nobody is authenticated or access to organization is not allowed. |  -  |
| **404** | Organization is not found. |  -  |

<a id="apiV10ConfigurationSignatureOrganizationIdPatch"></a>
# **apiV10ConfigurationSignatureOrganizationIdPatch**
> Boolean apiV10ConfigurationSignatureOrganizationIdPatch(organizationId, signatureData, signature)

Validates the given signature data with the signature using the organization certificate.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of organization of the certificate to validate.
    String signatureData = "signatureData_example"; // String | The string which was signed.
    String signature = "signature_example"; // String | The signature for the string which was signed.
    try {
      Boolean result = apiInstance.apiV10ConfigurationSignatureOrganizationIdPatch(organizationId, signatureData, signature);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationSignatureOrganizationIdPatch");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of organization of the certificate to validate. | |
| **signatureData** | **String**| The string which was signed. | [optional] |
| **signature** | **String**| The signature for the string which was signed. | [optional] |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Nobody is authenticated or access to organization is not allowed. |  -  |
| **404** | Organization is not found. |  -  |
| **422** | OrganizationId or signatureData is null or empty string. |  -  |

<a id="apiV10ConfigurationSignatureOrganizationIdPost"></a>
# **apiV10ConfigurationSignatureOrganizationIdPost**
> Boolean apiV10ConfigurationSignatureOrganizationIdPost(organizationId, body)

Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of organization of the certificate to validate.
    String body = "body_example"; // String | The certificate in pem format as a string.
    try {
      Boolean result = apiInstance.apiV10ConfigurationSignatureOrganizationIdPost(organizationId, body);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationSignatureOrganizationIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of organization of the certificate to validate. | |
| **body** | **String**| The certificate in pem format as a string. | [optional] |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Nobody is authenticated or access to organization is not allowed. |  -  |
| **404** | Organization is not found. |  -  |

<a id="apiV10ConfigurationSignatureOrganizationIdTerminalIdGet"></a>
# **apiV10ConfigurationSignatureOrganizationIdTerminalIdGet**
> String apiV10ConfigurationSignatureOrganizationIdTerminalIdGet(organizationId, terminalId)

Returns the certificate for the terminal (pfx Format - base64 encoded, key length 1024 bit).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization where the terminal belongs.
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal where the certificate is requested.
    try {
      String result = apiInstance.apiV10ConfigurationSignatureOrganizationIdTerminalIdGet(organizationId, terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationSignatureOrganizationIdTerminalIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of the organization where the terminal belongs. | |
| **terminalId** | **UUID**| The id of the terminal where the certificate is requested. | |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Nobody is authenticated or access to terminal is not allowed. |  -  |
| **404** | Terminal is not found. |  -  |
| **500** | Generation of the certificate failed. |  -  |

<a id="apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch"></a>
# **apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch**
> Boolean apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch(organizationId, terminalId, signatureData, signature)

Validates the given signature data with the signature using the organization certificate.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization where the terminal belongs.
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal where the certificate is requested.
    String signatureData = "signatureData_example"; // String | The string which was signed.
    String signature = "signature_example"; // String | The signature for the string which was signed.
    try {
      Boolean result = apiInstance.apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch(organizationId, terminalId, signatureData, signature);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of the organization where the terminal belongs. | |
| **terminalId** | **UUID**| The id of the terminal where the certificate is requested. | |
| **signatureData** | **String**| The string which was signed. | [optional] |
| **signature** | **String**| The signature for the string which was signed. | [optional] |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Nobody is authenticated or access to organization is not allowed. |  -  |
| **404** | Terminal is not found. |  -  |
| **422** | OrganizationId or TerminalId is null or signatureData is null or empty string. |  -  |

<a id="apiV10ConfigurationSignatureOrganizationIdTerminalIdPost"></a>
# **apiV10ConfigurationSignatureOrganizationIdTerminalIdPost**
> Boolean apiV10ConfigurationSignatureOrganizationIdTerminalIdPost(organizationId, terminalId, body)

Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationApi apiInstance = new ConfigurationApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization where the terminal belongs.
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal where the certificate is requested.
    String body = "body_example"; // String | The certificate in pem format as a string.
    try {
      Boolean result = apiInstance.apiV10ConfigurationSignatureOrganizationIdTerminalIdPost(organizationId, terminalId, body);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationApi#apiV10ConfigurationSignatureOrganizationIdTerminalIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of the organization where the terminal belongs. | |
| **terminalId** | **UUID**| The id of the terminal where the certificate is requested. | |
| **body** | **String**| The certificate in pem format as a string. | [optional] |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Nobody is authenticated or access to organization is not allowed. |  -  |
| **404** | Terminal is not found. |  -  |

