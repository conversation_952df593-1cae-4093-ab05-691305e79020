# ConfigurationControllerGermanyApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ConfigurationDeClientConfigurationTerminalIdPost**](ConfigurationControllerGermanyApi.md#apiV10ConfigurationDeClientConfigurationTerminalIdPost) | **POST** /api/v1.0/configuration/de/clientConfiguration/{terminalId} | Stores the necessary fields of the client configuration to the insights tables for germany. |
| [**apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost**](ConfigurationControllerGermanyApi.md#apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost) | **POST** /api/v1.0/configuration/de/clientConfiguration/{terminalId}/tseVersion | Stores the actual tse version information to the terminal insights table. |
| [**apiV10ConfigurationDeTseDriverInfoGet**](ConfigurationControllerGermanyApi.md#apiV10ConfigurationDeTseDriverInfoGet) | **GET** /api/v1.0/configuration/de/tseDriverInfo | Returns supported tse drivers for configuration in the cloud user interface. |


<a id="apiV10ConfigurationDeClientConfigurationTerminalIdPost"></a>
# **apiV10ConfigurationDeClientConfigurationTerminalIdPost**
> Boolean apiV10ConfigurationDeClientConfigurationTerminalIdPost(terminalId, clientConfigurationGermany)

Stores the necessary fields of the client configuration to the insights tables for germany.

At the moment only primary tse configuration is stored to the system.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerGermanyApi apiInstance = new ConfigurationControllerGermanyApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminalId of the configuration.
    ClientConfigurationGermany clientConfigurationGermany = new ClientConfigurationGermany(); // ClientConfigurationGermany | The configuration to store.
    try {
      Boolean result = apiInstance.apiV10ConfigurationDeClientConfigurationTerminalIdPost(terminalId, clientConfigurationGermany);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerGermanyApi#apiV10ConfigurationDeClientConfigurationTerminalIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminalId of the configuration. | |
| **clientConfigurationGermany** | [**ClientConfigurationGermany**](ClientConfigurationGermany.md)| The configuration to store. | [optional] |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost"></a>
# **apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost**
> apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost(terminalId, tseVersionInformation)

Stores the actual tse version information to the terminal insights table.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerGermanyApi apiInstance = new ConfigurationControllerGermanyApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminalId of the configuration.
    String tseVersionInformation = "tseVersionInformation_example"; // String | The tse version information of the client.
    try {
      apiInstance.apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost(terminalId, tseVersionInformation);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerGermanyApi#apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminalId of the configuration. | |
| **tseVersionInformation** | **String**| The tse version information of the client. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationDeTseDriverInfoGet"></a>
# **apiV10ConfigurationDeTseDriverInfoGet**
> List&lt;TseDriverInfo&gt; apiV10ConfigurationDeTseDriverInfoGet()

Returns supported tse drivers for configuration in the cloud user interface.

This method does not return parameters marked with RetailForce.Fiscalisation.Configuration.ParameterInfo.PortalHidden &#x3D; true.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerGermanyApi apiInstance = new ConfigurationControllerGermanyApi(defaultClient);
    try {
      List<TseDriverInfo> result = apiInstance.apiV10ConfigurationDeTseDriverInfoGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerGermanyApi#apiV10ConfigurationDeTseDriverInfoGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;TseDriverInfo&gt;**](TseDriverInfo.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

