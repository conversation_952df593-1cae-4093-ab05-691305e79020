# MoveToCloudApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MoveToCloudTerminalIdFinalizePut**](MoveToCloudApi.md#apiV10MoveToCloudTerminalIdFinalizePut) | **PUT** /api/v1.0/moveToCloud/{terminalId}/finalize | Finalize the move from local to cloud for this terminal. |
| [**apiV10MoveToCloudTerminalIdStartPost**](MoveToCloudApi.md#apiV10MoveToCloudTerminalIdStartPost) | **POST** /api/v1.0/moveToCloud/{terminalId}/start | Starts the move from local to cloud for this terminal. |
| [**apiV10MoveToCloudTerminalIdStateGet**](MoveToCloudApi.md#apiV10MoveToCloudTerminalIdStateGet) | **GET** /api/v1.0/moveToCloud/{terminalId}/state | Get the state of the move 2 cloud task. |


<a id="apiV10MoveToCloudTerminalIdFinalizePut"></a>
# **apiV10MoveToCloudTerminalIdFinalizePut**
> Boolean apiV10MoveToCloudTerminalIdFinalizePut(terminalId)

Finalize the move from local to cloud for this terminal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MoveToCloudApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MoveToCloudApi apiInstance = new MoveToCloudApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal which should be transfered to cloud service.
    try {
      Boolean result = apiInstance.apiV10MoveToCloudTerminalIdFinalizePut(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MoveToCloudApi#apiV10MoveToCloudTerminalIdFinalizePut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal which should be transfered to cloud service. | |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MoveToCloudTerminalIdStartPost"></a>
# **apiV10MoveToCloudTerminalIdStartPost**
> Boolean apiV10MoveToCloudTerminalIdStartPost(terminalId)

Starts the move from local to cloud for this terminal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MoveToCloudApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MoveToCloudApi apiInstance = new MoveToCloudApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal which should be transfered to cloud service.
    try {
      Boolean result = apiInstance.apiV10MoveToCloudTerminalIdStartPost(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MoveToCloudApi#apiV10MoveToCloudTerminalIdStartPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal which should be transfered to cloud service. | |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MoveToCloudTerminalIdStateGet"></a>
# **apiV10MoveToCloudTerminalIdStateGet**
> String apiV10MoveToCloudTerminalIdStateGet(terminalId)

Get the state of the move 2 cloud task.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MoveToCloudApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MoveToCloudApi apiInstance = new MoveToCloudApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal to move.
    try {
      String result = apiInstance.apiV10MoveToCloudTerminalIdStateGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MoveToCloudApi#apiV10MoveToCloudTerminalIdStateGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal to move. | |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

