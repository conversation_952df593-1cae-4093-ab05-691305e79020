

# TerminalModel

Viewmodel object for terminal object (extending RetailForce.Cloud.Model.Terminal object.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**organisationCaption** | **String** | The caption of the organisation the terminal belongs. |  [optional] |
|**companyCaption** | **String** | The caption of the company the terminal belongs (if the store is attached to a company). |  [optional] |
|**storeCaption** | **String** | The caption of the store the terminal belongs. |  [optional] |
|**storeNumber** | **String** | The store number of the store the terminal belongs. |  [optional] |
|**alert** | **Boolean** | True if the terminal has an alert. Otherwise false. |  [optional] |
|**canEdit** | **Boolean** | True if the terminal can be edited. Otherwise false. |  [optional] |
|**canDelete** | **Boolean** | True if the terminal can be deleted. Otherwise false. |  [optional] |
|**configurationCaption** | **String** | The caption of the configuration assigned to the terminal. |  [optional] |
|**newConfigurationCaption** | **String** | The caption of a possible new configuration assigned to the terminal. |  [optional] |
|**deactivationDate** | **OffsetDateTime** |  |  [optional] |
|**lastLicenseUsage** | **OffsetDateTime** |  |  [optional] |
|**isDeactivated** | **Boolean** | True if the terminal is deactivated (seasonal deactivation) |  [optional] |
|**isArchived** | **Boolean** | True if the terminal is archived |  [optional] [readonly] |
|**version** | **String** | Version (validFrom &#x3D;&gt; UTC ticks, as string for precision) |  [optional] [readonly] |
|**terminalId** | **UUID** | The id of the terminal. |  [optional] |
|**storeId** | **UUID** | The store of the terminal. |  [optional] |
|**terminalNumber** | **String** | The terminal number. |  [optional] |
|**fiscalCountry** | **FiscalCountry** |  |  [optional] |
|**caption** | **String** | Gets or sets a possible caption for a terminal. |  [optional] |
|**platformType** | **PlatformType** |  |  [optional] |
|**isTest** | **Boolean** | True if it is a test terminal. |  [optional] |
|**archiveDate** | **OffsetDateTime** | The date when the terminal was archived (decommissioned). |  [optional] |
|**clientConfigurationId** | **UUID** | The configuration for this terminal. |  [optional] |
|**newClientConfigurationId** | **UUID** | The new configuration for this terminal valid from RetailForce.Cloud.Model.Terminal.NewClientConfigurationValidFrom. |  [optional] |
|**newClientConfigurationValidFrom** | **OffsetDateTime** | The validity date for the new configuration. |  [optional] |
|**globalShortId** | **String** | Represents a 4 digit (alphanumeric, case-sensitive) value representing a global unique short id for the terminal. |  [optional] |
|**purchaseDate** | **OffsetDateTime** | Purchase date |  [optional] |
|**commissioningDate** | **OffsetDateTime** | Commissioning date |  [optional] |
|**decommissioningDate** | **OffsetDateTime** | Decommissioning date |  [optional] |
|**terminalType** | **TerminalType** |  |  [optional] |
|**updatedByPrincipalId** | **UUID** | Updated by PrincipalId |  [optional] |
|**cashRegisterId** | **String** | The id of the cash register hardware. |  [optional] |
|**cashRegisterBrand** | **String** | The brand of the cash register hardware. |  [optional] |
|**cashRegisterModelname** | **String** | The model name of the cash register hardware. |  [optional] |
|**cashRegisterSoftwareBrand** | **String** | The software of the cash register. |  [optional] |
|**cashRegisterSoftwareVersion** | **String** | The version of the software of the cash register. |  [optional] |
|**cashRegisterSoftwareCompany** | **String** | The company name of the creator of the cash register software. |  [optional] |



