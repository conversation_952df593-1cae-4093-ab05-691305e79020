/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.DownloadLink;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.SupportTicketModel;
import com.untill.retailforce.model.SupportTicketModelPageResultModel;
import com.untill.retailforce.model.SupportTicketSimple;
import com.untill.retailforce.model.SupportTicketStatus;
import com.untill.retailforce.model.TimelogOverviewModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class SupportTicketApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public SupportTicketApi() {
        this(Configuration.getDefaultApiClient());
    }

    public SupportTicketApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10SupportTicketChargePut
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketChargePutCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket/charge";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketChargePutValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10SupportTicketChargePutCall(_callback);

    }

    /**
     * Charge tickets
     * 
     * @return DownloadLink
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DownloadLink apiV10SupportTicketChargePut() throws ApiException {
        ApiResponse<DownloadLink> localVarResp = apiV10SupportTicketChargePutWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Charge tickets
     * 
     * @return ApiResponse&lt;DownloadLink&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DownloadLink> apiV10SupportTicketChargePutWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketChargePutValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<DownloadLink>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Charge tickets (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketChargePutAsync(final ApiCallback<DownloadLink> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketChargePutValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<DownloadLink>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SupportTicketGet
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param statusFilter ticket status filter (optional)
     * @param searchString search text (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketGetCall(Integer pageOffset, Integer pageSize, SupportTicketStatus statusFilter, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (statusFilter != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("statusFilter", statusFilter));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketGetValidateBeforeCall(Integer pageOffset, Integer pageSize, SupportTicketStatus statusFilter, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10SupportTicketGetCall(pageOffset, pageSize, statusFilter, searchString, _callback);

    }

    /**
     * Get SupportTickets
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param statusFilter ticket status filter (optional)
     * @param searchString search text (optional)
     * @return SupportTicketModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupportTicketModelPageResultModel apiV10SupportTicketGet(Integer pageOffset, Integer pageSize, SupportTicketStatus statusFilter, String searchString) throws ApiException {
        ApiResponse<SupportTicketModelPageResultModel> localVarResp = apiV10SupportTicketGetWithHttpInfo(pageOffset, pageSize, statusFilter, searchString);
        return localVarResp.getData();
    }

    /**
     * Get SupportTickets
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param statusFilter ticket status filter (optional)
     * @param searchString search text (optional)
     * @return ApiResponse&lt;SupportTicketModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupportTicketModelPageResultModel> apiV10SupportTicketGetWithHttpInfo(Integer pageOffset, Integer pageSize, SupportTicketStatus statusFilter, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketGetValidateBeforeCall(pageOffset, pageSize, statusFilter, searchString, null);
        Type localVarReturnType = new TypeToken<SupportTicketModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get SupportTickets (asynchronously)
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param statusFilter ticket status filter (optional)
     * @param searchString search text (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketGetAsync(Integer pageOffset, Integer pageSize, SupportTicketStatus statusFilter, String searchString, final ApiCallback<SupportTicketModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketGetValidateBeforeCall(pageOffset, pageSize, statusFilter, searchString, _callback);
        Type localVarReturnType = new TypeToken<SupportTicketModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SupportTicketOnboardingGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketOnboardingGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket/onboarding";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketOnboardingGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10SupportTicketOnboardingGetCall(_callback);

    }

    /**
     * Returns a list of onboarding tickets to move support ticket to onboarding.
     * 
     * @return List&lt;SupportTicketSimple&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<SupportTicketSimple> apiV10SupportTicketOnboardingGet() throws ApiException {
        ApiResponse<List<SupportTicketSimple>> localVarResp = apiV10SupportTicketOnboardingGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns a list of onboarding tickets to move support ticket to onboarding.
     * 
     * @return ApiResponse&lt;List&lt;SupportTicketSimple&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<SupportTicketSimple>> apiV10SupportTicketOnboardingGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketOnboardingGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<SupportTicketSimple>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a list of onboarding tickets to move support ticket to onboarding. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketOnboardingGetAsync(final ApiCallback<List<SupportTicketSimple>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketOnboardingGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<SupportTicketSimple>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SupportTicketOrderGet
     * @param supportTicketNumber The support ticket number of the support ticket to move to an order. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketOrderGetCall(String supportTicketNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket/order";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (supportTicketNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("supportTicketNumber", supportTicketNumber));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketOrderGetValidateBeforeCall(String supportTicketNumber, final ApiCallback _callback) throws ApiException {
        return apiV10SupportTicketOrderGetCall(supportTicketNumber, _callback);

    }

    /**
     * Returns possible orders to map support tickets.
     * 
     * @param supportTicketNumber The support ticket number of the support ticket to move to an order. (optional)
     * @return List&lt;GuidSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<GuidSimpleObject> apiV10SupportTicketOrderGet(String supportTicketNumber) throws ApiException {
        ApiResponse<List<GuidSimpleObject>> localVarResp = apiV10SupportTicketOrderGetWithHttpInfo(supportTicketNumber);
        return localVarResp.getData();
    }

    /**
     * Returns possible orders to map support tickets.
     * 
     * @param supportTicketNumber The support ticket number of the support ticket to move to an order. (optional)
     * @return ApiResponse&lt;List&lt;GuidSimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<GuidSimpleObject>> apiV10SupportTicketOrderGetWithHttpInfo(String supportTicketNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketOrderGetValidateBeforeCall(supportTicketNumber, null);
        Type localVarReturnType = new TypeToken<List<GuidSimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns possible orders to map support tickets. (asynchronously)
     * 
     * @param supportTicketNumber The support ticket number of the support ticket to move to an order. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketOrderGetAsync(String supportTicketNumber, final ApiCallback<List<GuidSimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketOrderGetValidateBeforeCall(supportTicketNumber, _callback);
        Type localVarReturnType = new TypeToken<List<GuidSimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SupportTicketSupportTicketNumberCustomerPut
     * @param supportTicketNumber supportTicketNumber (required)
     * @param customerId customer id (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberCustomerPutCall(String supportTicketNumber, UUID customerId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket/{supportTicketNumber}/customer"
            .replace("{" + "supportTicketNumber" + "}", localVarApiClient.escapeString(supportTicketNumber.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (customerId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("customerId", customerId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketSupportTicketNumberCustomerPutValidateBeforeCall(String supportTicketNumber, UUID customerId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supportTicketNumber' is set
        if (supportTicketNumber == null) {
            throw new ApiException("Missing the required parameter 'supportTicketNumber' when calling apiV10SupportTicketSupportTicketNumberCustomerPut(Async)");
        }

        return apiV10SupportTicketSupportTicketNumberCustomerPutCall(supportTicketNumber, customerId, _callback);

    }

    /**
     * AssignCustomer
     * 
     * @param supportTicketNumber supportTicketNumber (required)
     * @param customerId customer id (optional)
     * @return SupportTicketModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupportTicketModel apiV10SupportTicketSupportTicketNumberCustomerPut(String supportTicketNumber, UUID customerId) throws ApiException {
        ApiResponse<SupportTicketModel> localVarResp = apiV10SupportTicketSupportTicketNumberCustomerPutWithHttpInfo(supportTicketNumber, customerId);
        return localVarResp.getData();
    }

    /**
     * AssignCustomer
     * 
     * @param supportTicketNumber supportTicketNumber (required)
     * @param customerId customer id (optional)
     * @return ApiResponse&lt;SupportTicketModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupportTicketModel> apiV10SupportTicketSupportTicketNumberCustomerPutWithHttpInfo(String supportTicketNumber, UUID customerId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberCustomerPutValidateBeforeCall(supportTicketNumber, customerId, null);
        Type localVarReturnType = new TypeToken<SupportTicketModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * AssignCustomer (asynchronously)
     * 
     * @param supportTicketNumber supportTicketNumber (required)
     * @param customerId customer id (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberCustomerPutAsync(String supportTicketNumber, UUID customerId, final ApiCallback<SupportTicketModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberCustomerPutValidateBeforeCall(supportTicketNumber, customerId, _callback);
        Type localVarReturnType = new TypeToken<SupportTicketModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SupportTicketSupportTicketNumberOnboardingPost
     * @param supportTicketNumber The support ticket number to move time tracking entries to onboarding ticket. (required)
     * @param onboardingTicketNumber The guid of the onboarding ticket. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberOnboardingPostCall(String supportTicketNumber, UUID onboardingTicketNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket/{supportTicketNumber}/onboarding"
            .replace("{" + "supportTicketNumber" + "}", localVarApiClient.escapeString(supportTicketNumber.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (onboardingTicketNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("onboardingTicketNumber", onboardingTicketNumber));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketSupportTicketNumberOnboardingPostValidateBeforeCall(String supportTicketNumber, UUID onboardingTicketNumber, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supportTicketNumber' is set
        if (supportTicketNumber == null) {
            throw new ApiException("Missing the required parameter 'supportTicketNumber' when calling apiV10SupportTicketSupportTicketNumberOnboardingPost(Async)");
        }

        return apiV10SupportTicketSupportTicketNumberOnboardingPostCall(supportTicketNumber, onboardingTicketNumber, _callback);

    }

    /**
     * Map a support ticket to onboarding of a customer.
     * Only hours in time tracking were moved to onboarding which are not already charged.
     * @param supportTicketNumber The support ticket number to move time tracking entries to onboarding ticket. (required)
     * @param onboardingTicketNumber The guid of the onboarding ticket. (optional)
     * @return SupportTicketModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupportTicketModel apiV10SupportTicketSupportTicketNumberOnboardingPost(String supportTicketNumber, UUID onboardingTicketNumber) throws ApiException {
        ApiResponse<SupportTicketModel> localVarResp = apiV10SupportTicketSupportTicketNumberOnboardingPostWithHttpInfo(supportTicketNumber, onboardingTicketNumber);
        return localVarResp.getData();
    }

    /**
     * Map a support ticket to onboarding of a customer.
     * Only hours in time tracking were moved to onboarding which are not already charged.
     * @param supportTicketNumber The support ticket number to move time tracking entries to onboarding ticket. (required)
     * @param onboardingTicketNumber The guid of the onboarding ticket. (optional)
     * @return ApiResponse&lt;SupportTicketModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupportTicketModel> apiV10SupportTicketSupportTicketNumberOnboardingPostWithHttpInfo(String supportTicketNumber, UUID onboardingTicketNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberOnboardingPostValidateBeforeCall(supportTicketNumber, onboardingTicketNumber, null);
        Type localVarReturnType = new TypeToken<SupportTicketModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Map a support ticket to onboarding of a customer. (asynchronously)
     * Only hours in time tracking were moved to onboarding which are not already charged.
     * @param supportTicketNumber The support ticket number to move time tracking entries to onboarding ticket. (required)
     * @param onboardingTicketNumber The guid of the onboarding ticket. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberOnboardingPostAsync(String supportTicketNumber, UUID onboardingTicketNumber, final ApiCallback<SupportTicketModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberOnboardingPostValidateBeforeCall(supportTicketNumber, onboardingTicketNumber, _callback);
        Type localVarReturnType = new TypeToken<SupportTicketModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SupportTicketSupportTicketNumberOrderPost
     * @param supportTicketNumber The support ticket number to move time tracking entries to order ticket. (required)
     * @param orderTicketNumber The guid of the order ticket. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberOrderPostCall(String supportTicketNumber, UUID orderTicketNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket/{supportTicketNumber}/order"
            .replace("{" + "supportTicketNumber" + "}", localVarApiClient.escapeString(supportTicketNumber.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (orderTicketNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("orderTicketNumber", orderTicketNumber));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketSupportTicketNumberOrderPostValidateBeforeCall(String supportTicketNumber, UUID orderTicketNumber, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supportTicketNumber' is set
        if (supportTicketNumber == null) {
            throw new ApiException("Missing the required parameter 'supportTicketNumber' when calling apiV10SupportTicketSupportTicketNumberOrderPost(Async)");
        }

        return apiV10SupportTicketSupportTicketNumberOrderPostCall(supportTicketNumber, orderTicketNumber, _callback);

    }

    /**
     * Map a support ticket to order of a customer.
     * Only hours in time tracking were moved to order which are not already charged.
     * @param supportTicketNumber The support ticket number to move time tracking entries to order ticket. (required)
     * @param orderTicketNumber The guid of the order ticket. (optional)
     * @return SupportTicketModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupportTicketModel apiV10SupportTicketSupportTicketNumberOrderPost(String supportTicketNumber, UUID orderTicketNumber) throws ApiException {
        ApiResponse<SupportTicketModel> localVarResp = apiV10SupportTicketSupportTicketNumberOrderPostWithHttpInfo(supportTicketNumber, orderTicketNumber);
        return localVarResp.getData();
    }

    /**
     * Map a support ticket to order of a customer.
     * Only hours in time tracking were moved to order which are not already charged.
     * @param supportTicketNumber The support ticket number to move time tracking entries to order ticket. (required)
     * @param orderTicketNumber The guid of the order ticket. (optional)
     * @return ApiResponse&lt;SupportTicketModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupportTicketModel> apiV10SupportTicketSupportTicketNumberOrderPostWithHttpInfo(String supportTicketNumber, UUID orderTicketNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberOrderPostValidateBeforeCall(supportTicketNumber, orderTicketNumber, null);
        Type localVarReturnType = new TypeToken<SupportTicketModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Map a support ticket to order of a customer. (asynchronously)
     * Only hours in time tracking were moved to order which are not already charged.
     * @param supportTicketNumber The support ticket number to move time tracking entries to order ticket. (required)
     * @param orderTicketNumber The guid of the order ticket. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberOrderPostAsync(String supportTicketNumber, UUID orderTicketNumber, final ApiCallback<SupportTicketModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberOrderPostValidateBeforeCall(supportTicketNumber, orderTicketNumber, _callback);
        Type localVarReturnType = new TypeToken<SupportTicketModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SupportTicketSupportTicketNumberStatusPut
     * @param supportTicketNumber support ticket number (required)
     * @param chargeable sets the charable status of the ticket (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberStatusPutCall(String supportTicketNumber, Boolean chargeable, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket/{supportTicketNumber}/status"
            .replace("{" + "supportTicketNumber" + "}", localVarApiClient.escapeString(supportTicketNumber.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (chargeable != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("chargeable", chargeable));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketSupportTicketNumberStatusPutValidateBeforeCall(String supportTicketNumber, Boolean chargeable, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supportTicketNumber' is set
        if (supportTicketNumber == null) {
            throw new ApiException("Missing the required parameter 'supportTicketNumber' when calling apiV10SupportTicketSupportTicketNumberStatusPut(Async)");
        }

        return apiV10SupportTicketSupportTicketNumberStatusPutCall(supportTicketNumber, chargeable, _callback);

    }

    /**
     * UpdateChargeable status of ticket
     * 
     * @param supportTicketNumber support ticket number (required)
     * @param chargeable sets the charable status of the ticket (optional)
     * @return SupportTicketModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public SupportTicketModel apiV10SupportTicketSupportTicketNumberStatusPut(String supportTicketNumber, Boolean chargeable) throws ApiException {
        ApiResponse<SupportTicketModel> localVarResp = apiV10SupportTicketSupportTicketNumberStatusPutWithHttpInfo(supportTicketNumber, chargeable);
        return localVarResp.getData();
    }

    /**
     * UpdateChargeable status of ticket
     * 
     * @param supportTicketNumber support ticket number (required)
     * @param chargeable sets the charable status of the ticket (optional)
     * @return ApiResponse&lt;SupportTicketModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<SupportTicketModel> apiV10SupportTicketSupportTicketNumberStatusPutWithHttpInfo(String supportTicketNumber, Boolean chargeable) throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberStatusPutValidateBeforeCall(supportTicketNumber, chargeable, null);
        Type localVarReturnType = new TypeToken<SupportTicketModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * UpdateChargeable status of ticket (asynchronously)
     * 
     * @param supportTicketNumber support ticket number (required)
     * @param chargeable sets the charable status of the ticket (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberStatusPutAsync(String supportTicketNumber, Boolean chargeable, final ApiCallback<SupportTicketModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberStatusPutValidateBeforeCall(supportTicketNumber, chargeable, _callback);
        Type localVarReturnType = new TypeToken<SupportTicketModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SupportTicketSupportTicketNumberTimelogGet
     * @param supportTicketNumber  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberTimelogGetCall(String supportTicketNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/support-ticket/{supportTicketNumber}/timelog"
            .replace("{" + "supportTicketNumber" + "}", localVarApiClient.escapeString(supportTicketNumber.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SupportTicketSupportTicketNumberTimelogGetValidateBeforeCall(String supportTicketNumber, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'supportTicketNumber' is set
        if (supportTicketNumber == null) {
            throw new ApiException("Missing the required parameter 'supportTicketNumber' when calling apiV10SupportTicketSupportTicketNumberTimelogGet(Async)");
        }

        return apiV10SupportTicketSupportTicketNumberTimelogGetCall(supportTicketNumber, _callback);

    }

    /**
     * Get Timelog for a support ticket
     * 
     * @param supportTicketNumber  (required)
     * @return List&lt;TimelogOverviewModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<TimelogOverviewModel> apiV10SupportTicketSupportTicketNumberTimelogGet(String supportTicketNumber) throws ApiException {
        ApiResponse<List<TimelogOverviewModel>> localVarResp = apiV10SupportTicketSupportTicketNumberTimelogGetWithHttpInfo(supportTicketNumber);
        return localVarResp.getData();
    }

    /**
     * Get Timelog for a support ticket
     * 
     * @param supportTicketNumber  (required)
     * @return ApiResponse&lt;List&lt;TimelogOverviewModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<TimelogOverviewModel>> apiV10SupportTicketSupportTicketNumberTimelogGetWithHttpInfo(String supportTicketNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberTimelogGetValidateBeforeCall(supportTicketNumber, null);
        Type localVarReturnType = new TypeToken<List<TimelogOverviewModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get Timelog for a support ticket (asynchronously)
     * 
     * @param supportTicketNumber  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SupportTicketSupportTicketNumberTimelogGetAsync(String supportTicketNumber, final ApiCallback<List<TimelogOverviewModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SupportTicketSupportTicketNumberTimelogGetValidateBeforeCall(supportTicketNumber, _callback);
        Type localVarReturnType = new TypeToken<List<TimelogOverviewModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
