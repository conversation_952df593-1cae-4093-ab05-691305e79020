/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.Session;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for AuthenticationApi
 */
@Disabled
public class AuthenticationApiTest {

    private final AuthenticationApi api = new AuthenticationApi();

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10AuthenticateLogon2PostTest() throws ApiException {
        String key = null;
        String secret = null;
        String response = api.apiV10AuthenticateLogon2Post(key, secret);
        // TODO: test validations
    }

    /**
     * Logon on the service and returns web token for service communication.
     *
     * This method is also used for api key authentication.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10AuthenticateLogonPostTest() throws ApiException {
        String key = null;
        String secret = null;
        String response = api.apiV10AuthenticateLogonPost(key, secret);
        // TODO: test validations
    }

    /**
     * 
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10AuthenticateLogoutPostTest() throws ApiException {
        api.apiV10AuthenticateLogoutPost();
        // TODO: test validations
    }

    /**
     * Returns user session data.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10AuthenticateSessionGetTest() throws ApiException {
        Session response = api.apiV10AuthenticateSessionGet();
        // TODO: test validations
    }

    /**
     * 
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10AuthenticateTokenGetTest() throws ApiException {
        api.apiV10AuthenticateTokenGet();
        // TODO: test validations
    }

}
