.github/workflows/maven.yml
.gitignore
.openapi-generator-ignore
.travis.yml
README.md
api/openapi.yaml
build.gradle
build.sbt
docs/AccessLicense.md
docs/AccessLicenseAllocation.md
docs/AccessLicenseConfigurationInfo.md
docs/AccessLicenseConfigurationParameter.md
docs/AccessLicenseContract.md
docs/AccessLicensePageResultModel.md
docs/Address.md
docs/ApiKey.md
docs/AuditLogEntry.md
docs/AuthenticationApi.md
docs/AuthenticationType.md
docs/AutomaticVatCalculation.md
docs/BackupApi.md
docs/BackupData.md
docs/BackupDataPageResultModel.md
docs/BarcodeType.md
docs/BillingAccessLicenseCount.md
docs/BillingApi.md
docs/BillingDistributorLicenseCount.md
docs/BillingDistributorLicenseCountBillingLicenseOverview.md
docs/BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel.md
docs/BillingLicenseCount.md
docs/BillingLicenseCountBillingLicenseOverview.md
docs/BillingLicenseCountBillingLicenseOverviewPageResultModel.md
docs/BillingLicenseDetail.md
docs/BillingLicenseDetailPageResultModel.md
docs/BoolResponse.md
docs/BusinessSector.md
docs/BusinessTransactionType.md
docs/BusinessTransactionTypeDocumentTypeMapping.md
docs/CashRegister.md
docs/CashRegisterDropoutReason.md
docs/Certificate.md
docs/CertificateFormat.md
docs/CertificateModel.md
docs/ClearingRun.md
docs/ClientConfigurationGermany.md
docs/CloudParameter.md
docs/Company.md
docs/CompanyIdentification.md
docs/CompanyModel.md
docs/CompanyModelPageResultModel.md
docs/ConfigLicenseModel.md
docs/ConfigurationApi.md
docs/ConfigurationControllerAustriaApi.md
docs/ConfigurationControllerGermanyApi.md
docs/ConfigurationControllerItalyApi.md
docs/ConfigurationControllerLithuaniaApi.md
docs/ConfigurationControllerSwedenApi.md
docs/ConfigurationParameter.md
docs/ControlUnitDriver.md
docs/ControlUnitDriverInfo.md
docs/CosmosDocument.md
docs/DashboardApi.md
docs/DashboardTerminalDetail.md
docs/DashboardTerminalDetailPageResultModel.md
docs/DashboardTerminalOverview.md
docs/DashboardTerminalOverviewPageResultModel.md
docs/DigitalReceipt.md
docs/Distributor.md
docs/DistributorContract.md
docs/DistributorContractModel.md
docs/DistributorContractModelPageResultModel.md
docs/DistributorModel.md
docs/DistributorModelPageResultModel.md
docs/Document.md
docs/DocumentApi.md
docs/DocumentCoupon.md
docs/DocumentCouponTextLine.md
docs/DocumentIssueType.md
docs/DocumentPayment.md
docs/DocumentPaymentCardData.md
docs/DocumentPositionBase.md
docs/DocumentPositionReference.md
docs/DocumentPositionType.md
docs/DocumentReference.md
docs/DocumentTaxPosition.md
docs/DocumentType.md
docs/DownloadLink.md
docs/DownloadLinkPageResultModel.md
docs/EntityParameterInfo.md
docs/EntityPermissions.md
docs/EntitySecurity.md
docs/EntitySecurityPageResultModel.md
docs/EntityTypes.md
docs/ErrorLevel.md
docs/FiscalClient.md
docs/FiscalClientConfiguration.md
docs/FiscalClientConfigurationModel.md
docs/FiscalCountry.md
docs/FiscalModuleEnvironment.md
docs/FiscalResponse.md
docs/FiscalisationType.md
docs/FonConnectionLogMessage.md
docs/FonConnectionLogMessagePageResultModel.md
docs/FonCredentials.md
docs/GuidBreadCrumb.md
docs/GuidEntityVersion.md
docs/GuidEntityVersionPageResultModel.md
docs/GuidExtendedSimpleCountryObject.md
docs/GuidExtendedSimpleCountryObjectPageResultModel.md
docs/GuidHierarchicalSimpleObject.md
docs/GuidHierarchicalSimpleObjectPageResultModel.md
docs/GuidSimpleObject.md
docs/GuidSimpleObjectPageResultModel.md
docs/HelpInformation.md
docs/IFiscalCountryProperties.md
docs/IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport.md
docs/IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md
docs/IFiscalCountryPropertiesPageResultModel.md
docs/IFiscalImplementationConfiguration.md
docs/INotification.md
docs/IdentificationType.md
docs/ImplementationApi.md
docs/ImplementationControllerAustriaApi.md
docs/ImplementationControllerDenmarkApi.md
docs/ImplementationControllerFranceApi.md
docs/ImplementationControllerGermanyApi.md
docs/ImportModel.md
docs/ImportModelPageResultModel.md
docs/InformationApi.md
docs/Int32SimpleObject.md
docs/Int32SimpleObjectPageResultModel.md
docs/InvitationInfoModel.md
docs/InvitationModel.md
docs/JwtLicenseClaim.md
docs/LegalForm.md
docs/LicencingApi.md
docs/License.md
docs/LicenseDetailUsage.md
docs/LicenseModel.md
docs/LicenseModelPageResultModel.md
docs/LicenseOption.md
docs/LogEntryType.md
docs/MasterDataApi.md
docs/MasterDataCompaniesApi.md
docs/MasterDataDistributorsApi.md
docs/MasterDataOrganisationsApi.md
docs/MasterDataStoresApi.md
docs/MasterDataSuppliersApi.md
docs/MasterDataTerminalsApi.md
docs/MasterDatsaOrganisationsApi.md
docs/MessageApiModel.md
docs/MoveToCloudApi.md
docs/NotificationApi.md
docs/NotificationResult.md
docs/NotificationType.md
docs/NotificationsInfo.md
docs/OfflineConfiguration.md
docs/OnboardingFinishData.md
docs/OnboardingFinishDistributorData.md
docs/Organisation.md
docs/OrganisationModel.md
docs/OrganisationModelPageResultModel.md
docs/OutstandingPayment.md
docs/ParameterInfo.md
docs/Partner.md
docs/PartnerIdentificationType.md
docs/PartnerType.md
docs/PayOutType.md
docs/PaymentTerms.md
docs/PaymentType.md
docs/PlatformType.md
docs/Principal.md
docs/PrinterDriverInfo.md
docs/PrinterImageFile.md
docs/PrinterModel.md
docs/ProfilePictureModel.md
docs/ReceiptData.md
docs/ReceiptDataScrollResultModel.md
docs/ReceiptDomain.md
docs/ReceiptsApi.md
docs/ReferenceType.md
docs/RegistrationModel.md
docs/Release.md
docs/ResultResponse.md
docs/ReturnAndSaleBehavior.md
docs/ReturnReasonType.md
docs/ReturnReferenceBehavior.md
docs/Salutation.md
docs/SearchApi.md
docs/SearchResultModel.md
docs/SearchResultModelPageResultModel.md
docs/SecurityApi.md
docs/SecurityCertificateDropoutReason.md
docs/SecurityCertificateIssuer.md
docs/SecurityCertificateType.md
docs/Session.md
docs/SignDeviceDriver.md
docs/SignDeviceDriverInfo.md
docs/SignatureDevice.md
docs/Software.md
docs/StaticApi.md
docs/Store.md
docs/StoreModel.md
docs/StoreModelPageResultModel.md
docs/StringSimpleObject.md
docs/StringSimpleObjectPageResultModel.md
docs/Supplier.md
docs/SupplierContract.md
docs/SupplierContractModel.md
docs/SupplierContractModelPageResultModel.md
docs/SupplierModel.md
docs/SupplierModelPageResultModel.md
docs/SupportTicketApi.md
docs/SupportTicketModel.md
docs/SupportTicketModelPageResultModel.md
docs/SupportTicketSimple.md
docs/SupportTicketStatus.md
docs/TaxonomyCloudStoreConfiguration.md
docs/TaxonomyFileStoreConfiguration.md
docs/Terminal.md
docs/TerminalContactType.md
docs/TerminalInsightModel.md
docs/TerminalModel.md
docs/TerminalModelPageResultModel.md
docs/TerminalType.md
docs/ThemingApi.md
docs/TimelogOverviewModel.md
docs/TseAnnouncementCreateResult.md
docs/TseAnnouncementOverview.md
docs/TseAnnouncementOverviewPageResultModel.md
docs/TseAnnouncementProgress.md
docs/TseAnnouncementStatus.md
docs/TseAnnouncementStatusInfo.md
docs/TseConfiguration.md
docs/TseDriver.md
docs/TseDriverInfo.md
docs/TseInformationOverview.md
docs/TseInformationOverviewPageResultModel.md
docs/TseType.md
docs/User.md
docs/ValidationError.md
docs/Vat.md
git_push.sh
gradle.properties
gradle/wrapper/gradle-wrapper.jar
gradle/wrapper/gradle-wrapper.properties
gradlew
gradlew.bat
pom.xml
settings.gradle
src/main/AndroidManifest.xml
src/main/java/com/untill/retailforce/ApiCallback.java
src/main/java/com/untill/retailforce/ApiClient.java
src/main/java/com/untill/retailforce/ApiException.java
src/main/java/com/untill/retailforce/ApiResponse.java
src/main/java/com/untill/retailforce/Configuration.java
src/main/java/com/untill/retailforce/GzipRequestInterceptor.java
src/main/java/com/untill/retailforce/JSON.java
src/main/java/com/untill/retailforce/Pair.java
src/main/java/com/untill/retailforce/ProgressRequestBody.java
src/main/java/com/untill/retailforce/ProgressResponseBody.java
src/main/java/com/untill/retailforce/ServerConfiguration.java
src/main/java/com/untill/retailforce/ServerVariable.java
src/main/java/com/untill/retailforce/StringUtil.java
src/main/java/com/untill/retailforce/api/AuthenticationApi.java
src/main/java/com/untill/retailforce/api/BackupApi.java
src/main/java/com/untill/retailforce/api/BillingApi.java
src/main/java/com/untill/retailforce/api/ConfigurationApi.java
src/main/java/com/untill/retailforce/api/ConfigurationControllerAustriaApi.java
src/main/java/com/untill/retailforce/api/ConfigurationControllerGermanyApi.java
src/main/java/com/untill/retailforce/api/ConfigurationControllerItalyApi.java
src/main/java/com/untill/retailforce/api/ConfigurationControllerLithuaniaApi.java
src/main/java/com/untill/retailforce/api/ConfigurationControllerSwedenApi.java
src/main/java/com/untill/retailforce/api/DashboardApi.java
src/main/java/com/untill/retailforce/api/DocumentApi.java
src/main/java/com/untill/retailforce/api/ImplementationApi.java
src/main/java/com/untill/retailforce/api/ImplementationControllerAustriaApi.java
src/main/java/com/untill/retailforce/api/ImplementationControllerDenmarkApi.java
src/main/java/com/untill/retailforce/api/ImplementationControllerFranceApi.java
src/main/java/com/untill/retailforce/api/ImplementationControllerGermanyApi.java
src/main/java/com/untill/retailforce/api/InformationApi.java
src/main/java/com/untill/retailforce/api/LicencingApi.java
src/main/java/com/untill/retailforce/api/MasterDataApi.java
src/main/java/com/untill/retailforce/api/MasterDataCompaniesApi.java
src/main/java/com/untill/retailforce/api/MasterDataDistributorsApi.java
src/main/java/com/untill/retailforce/api/MasterDataOrganisationsApi.java
src/main/java/com/untill/retailforce/api/MasterDataStoresApi.java
src/main/java/com/untill/retailforce/api/MasterDataSuppliersApi.java
src/main/java/com/untill/retailforce/api/MasterDataTerminalsApi.java
src/main/java/com/untill/retailforce/api/MasterDatsaOrganisationsApi.java
src/main/java/com/untill/retailforce/api/MoveToCloudApi.java
src/main/java/com/untill/retailforce/api/NotificationApi.java
src/main/java/com/untill/retailforce/api/ReceiptsApi.java
src/main/java/com/untill/retailforce/api/SearchApi.java
src/main/java/com/untill/retailforce/api/SecurityApi.java
src/main/java/com/untill/retailforce/api/StaticApi.java
src/main/java/com/untill/retailforce/api/SupportTicketApi.java
src/main/java/com/untill/retailforce/api/ThemingApi.java
src/main/java/com/untill/retailforce/auth/ApiKeyAuth.java
src/main/java/com/untill/retailforce/auth/Authentication.java
src/main/java/com/untill/retailforce/auth/HttpBasicAuth.java
src/main/java/com/untill/retailforce/auth/HttpBearerAuth.java
src/main/java/com/untill/retailforce/model/AbstractOpenApiSchema.java
src/main/java/com/untill/retailforce/model/AccessLicense.java
src/main/java/com/untill/retailforce/model/AccessLicenseAllocation.java
src/main/java/com/untill/retailforce/model/AccessLicenseConfigurationInfo.java
src/main/java/com/untill/retailforce/model/AccessLicenseConfigurationParameter.java
src/main/java/com/untill/retailforce/model/AccessLicenseContract.java
src/main/java/com/untill/retailforce/model/AccessLicensePageResultModel.java
src/main/java/com/untill/retailforce/model/Address.java
src/main/java/com/untill/retailforce/model/ApiKey.java
src/main/java/com/untill/retailforce/model/AuditLogEntry.java
src/main/java/com/untill/retailforce/model/AuthenticationType.java
src/main/java/com/untill/retailforce/model/AutomaticVatCalculation.java
src/main/java/com/untill/retailforce/model/BackupData.java
src/main/java/com/untill/retailforce/model/BackupDataPageResultModel.java
src/main/java/com/untill/retailforce/model/BarcodeType.java
src/main/java/com/untill/retailforce/model/BillingAccessLicenseCount.java
src/main/java/com/untill/retailforce/model/BillingDistributorLicenseCount.java
src/main/java/com/untill/retailforce/model/BillingDistributorLicenseCountBillingLicenseOverview.java
src/main/java/com/untill/retailforce/model/BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel.java
src/main/java/com/untill/retailforce/model/BillingLicenseCount.java
src/main/java/com/untill/retailforce/model/BillingLicenseCountBillingLicenseOverview.java
src/main/java/com/untill/retailforce/model/BillingLicenseCountBillingLicenseOverviewPageResultModel.java
src/main/java/com/untill/retailforce/model/BillingLicenseDetail.java
src/main/java/com/untill/retailforce/model/BillingLicenseDetailPageResultModel.java
src/main/java/com/untill/retailforce/model/BoolResponse.java
src/main/java/com/untill/retailforce/model/BusinessSector.java
src/main/java/com/untill/retailforce/model/BusinessTransactionType.java
src/main/java/com/untill/retailforce/model/BusinessTransactionTypeDocumentTypeMapping.java
src/main/java/com/untill/retailforce/model/CashRegister.java
src/main/java/com/untill/retailforce/model/CashRegisterDropoutReason.java
src/main/java/com/untill/retailforce/model/Certificate.java
src/main/java/com/untill/retailforce/model/CertificateFormat.java
src/main/java/com/untill/retailforce/model/CertificateModel.java
src/main/java/com/untill/retailforce/model/ClearingRun.java
src/main/java/com/untill/retailforce/model/ClientConfigurationGermany.java
src/main/java/com/untill/retailforce/model/CloudParameter.java
src/main/java/com/untill/retailforce/model/Company.java
src/main/java/com/untill/retailforce/model/CompanyIdentification.java
src/main/java/com/untill/retailforce/model/CompanyModel.java
src/main/java/com/untill/retailforce/model/CompanyModelPageResultModel.java
src/main/java/com/untill/retailforce/model/ConfigLicenseModel.java
src/main/java/com/untill/retailforce/model/ConfigurationParameter.java
src/main/java/com/untill/retailforce/model/ControlUnitDriver.java
src/main/java/com/untill/retailforce/model/ControlUnitDriverInfo.java
src/main/java/com/untill/retailforce/model/CosmosDocument.java
src/main/java/com/untill/retailforce/model/DashboardTerminalDetail.java
src/main/java/com/untill/retailforce/model/DashboardTerminalDetailPageResultModel.java
src/main/java/com/untill/retailforce/model/DashboardTerminalOverview.java
src/main/java/com/untill/retailforce/model/DashboardTerminalOverviewPageResultModel.java
src/main/java/com/untill/retailforce/model/DigitalReceipt.java
src/main/java/com/untill/retailforce/model/Distributor.java
src/main/java/com/untill/retailforce/model/DistributorContract.java
src/main/java/com/untill/retailforce/model/DistributorContractModel.java
src/main/java/com/untill/retailforce/model/DistributorContractModelPageResultModel.java
src/main/java/com/untill/retailforce/model/DistributorModel.java
src/main/java/com/untill/retailforce/model/DistributorModelPageResultModel.java
src/main/java/com/untill/retailforce/model/Document.java
src/main/java/com/untill/retailforce/model/DocumentCoupon.java
src/main/java/com/untill/retailforce/model/DocumentCouponTextLine.java
src/main/java/com/untill/retailforce/model/DocumentIssueType.java
src/main/java/com/untill/retailforce/model/DocumentPayment.java
src/main/java/com/untill/retailforce/model/DocumentPaymentCardData.java
src/main/java/com/untill/retailforce/model/DocumentPositionBase.java
src/main/java/com/untill/retailforce/model/DocumentPositionReference.java
src/main/java/com/untill/retailforce/model/DocumentPositionType.java
src/main/java/com/untill/retailforce/model/DocumentReference.java
src/main/java/com/untill/retailforce/model/DocumentTaxPosition.java
src/main/java/com/untill/retailforce/model/DocumentType.java
src/main/java/com/untill/retailforce/model/DownloadLink.java
src/main/java/com/untill/retailforce/model/DownloadLinkPageResultModel.java
src/main/java/com/untill/retailforce/model/EntityParameterInfo.java
src/main/java/com/untill/retailforce/model/EntityPermissions.java
src/main/java/com/untill/retailforce/model/EntitySecurity.java
src/main/java/com/untill/retailforce/model/EntitySecurityPageResultModel.java
src/main/java/com/untill/retailforce/model/EntityTypes.java
src/main/java/com/untill/retailforce/model/ErrorLevel.java
src/main/java/com/untill/retailforce/model/FiscalClient.java
src/main/java/com/untill/retailforce/model/FiscalClientConfiguration.java
src/main/java/com/untill/retailforce/model/FiscalClientConfigurationModel.java
src/main/java/com/untill/retailforce/model/FiscalCountry.java
src/main/java/com/untill/retailforce/model/FiscalModuleEnvironment.java
src/main/java/com/untill/retailforce/model/FiscalResponse.java
src/main/java/com/untill/retailforce/model/FiscalisationType.java
src/main/java/com/untill/retailforce/model/FonConnectionLogMessage.java
src/main/java/com/untill/retailforce/model/FonConnectionLogMessagePageResultModel.java
src/main/java/com/untill/retailforce/model/FonCredentials.java
src/main/java/com/untill/retailforce/model/GuidBreadCrumb.java
src/main/java/com/untill/retailforce/model/GuidEntityVersion.java
src/main/java/com/untill/retailforce/model/GuidEntityVersionPageResultModel.java
src/main/java/com/untill/retailforce/model/GuidExtendedSimpleCountryObject.java
src/main/java/com/untill/retailforce/model/GuidExtendedSimpleCountryObjectPageResultModel.java
src/main/java/com/untill/retailforce/model/GuidHierarchicalSimpleObject.java
src/main/java/com/untill/retailforce/model/GuidHierarchicalSimpleObjectPageResultModel.java
src/main/java/com/untill/retailforce/model/GuidSimpleObject.java
src/main/java/com/untill/retailforce/model/GuidSimpleObjectPageResultModel.java
src/main/java/com/untill/retailforce/model/HelpInformation.java
src/main/java/com/untill/retailforce/model/IFiscalCountryProperties.java
src/main/java/com/untill/retailforce/model/IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport.java
src/main/java/com/untill/retailforce/model/IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.java
src/main/java/com/untill/retailforce/model/IFiscalCountryPropertiesPageResultModel.java
src/main/java/com/untill/retailforce/model/IFiscalImplementationConfiguration.java
src/main/java/com/untill/retailforce/model/INotification.java
src/main/java/com/untill/retailforce/model/IdentificationType.java
src/main/java/com/untill/retailforce/model/ImportModel.java
src/main/java/com/untill/retailforce/model/ImportModelPageResultModel.java
src/main/java/com/untill/retailforce/model/Int32SimpleObject.java
src/main/java/com/untill/retailforce/model/Int32SimpleObjectPageResultModel.java
src/main/java/com/untill/retailforce/model/InvitationInfoModel.java
src/main/java/com/untill/retailforce/model/InvitationModel.java
src/main/java/com/untill/retailforce/model/JwtLicenseClaim.java
src/main/java/com/untill/retailforce/model/LegalForm.java
src/main/java/com/untill/retailforce/model/License.java
src/main/java/com/untill/retailforce/model/LicenseDetailUsage.java
src/main/java/com/untill/retailforce/model/LicenseModel.java
src/main/java/com/untill/retailforce/model/LicenseModelPageResultModel.java
src/main/java/com/untill/retailforce/model/LicenseOption.java
src/main/java/com/untill/retailforce/model/LogEntryType.java
src/main/java/com/untill/retailforce/model/MessageApiModel.java
src/main/java/com/untill/retailforce/model/NotificationResult.java
src/main/java/com/untill/retailforce/model/NotificationType.java
src/main/java/com/untill/retailforce/model/NotificationsInfo.java
src/main/java/com/untill/retailforce/model/OfflineConfiguration.java
src/main/java/com/untill/retailforce/model/OnboardingFinishData.java
src/main/java/com/untill/retailforce/model/OnboardingFinishDistributorData.java
src/main/java/com/untill/retailforce/model/Organisation.java
src/main/java/com/untill/retailforce/model/OrganisationModel.java
src/main/java/com/untill/retailforce/model/OrganisationModelPageResultModel.java
src/main/java/com/untill/retailforce/model/OutstandingPayment.java
src/main/java/com/untill/retailforce/model/ParameterInfo.java
src/main/java/com/untill/retailforce/model/Partner.java
src/main/java/com/untill/retailforce/model/PartnerIdentificationType.java
src/main/java/com/untill/retailforce/model/PartnerType.java
src/main/java/com/untill/retailforce/model/PayOutType.java
src/main/java/com/untill/retailforce/model/PaymentTerms.java
src/main/java/com/untill/retailforce/model/PaymentType.java
src/main/java/com/untill/retailforce/model/PlatformType.java
src/main/java/com/untill/retailforce/model/Principal.java
src/main/java/com/untill/retailforce/model/PrinterDriverInfo.java
src/main/java/com/untill/retailforce/model/PrinterImageFile.java
src/main/java/com/untill/retailforce/model/PrinterModel.java
src/main/java/com/untill/retailforce/model/ProfilePictureModel.java
src/main/java/com/untill/retailforce/model/ReceiptData.java
src/main/java/com/untill/retailforce/model/ReceiptDataScrollResultModel.java
src/main/java/com/untill/retailforce/model/ReceiptDomain.java
src/main/java/com/untill/retailforce/model/ReferenceType.java
src/main/java/com/untill/retailforce/model/RegistrationModel.java
src/main/java/com/untill/retailforce/model/Release.java
src/main/java/com/untill/retailforce/model/ResultResponse.java
src/main/java/com/untill/retailforce/model/ReturnAndSaleBehavior.java
src/main/java/com/untill/retailforce/model/ReturnReasonType.java
src/main/java/com/untill/retailforce/model/ReturnReferenceBehavior.java
src/main/java/com/untill/retailforce/model/Salutation.java
src/main/java/com/untill/retailforce/model/SearchResultModel.java
src/main/java/com/untill/retailforce/model/SearchResultModelPageResultModel.java
src/main/java/com/untill/retailforce/model/SecurityCertificateDropoutReason.java
src/main/java/com/untill/retailforce/model/SecurityCertificateIssuer.java
src/main/java/com/untill/retailforce/model/SecurityCertificateType.java
src/main/java/com/untill/retailforce/model/Session.java
src/main/java/com/untill/retailforce/model/SignDeviceDriver.java
src/main/java/com/untill/retailforce/model/SignDeviceDriverInfo.java
src/main/java/com/untill/retailforce/model/SignatureDevice.java
src/main/java/com/untill/retailforce/model/Software.java
src/main/java/com/untill/retailforce/model/Store.java
src/main/java/com/untill/retailforce/model/StoreModel.java
src/main/java/com/untill/retailforce/model/StoreModelPageResultModel.java
src/main/java/com/untill/retailforce/model/StringSimpleObject.java
src/main/java/com/untill/retailforce/model/StringSimpleObjectPageResultModel.java
src/main/java/com/untill/retailforce/model/Supplier.java
src/main/java/com/untill/retailforce/model/SupplierContract.java
src/main/java/com/untill/retailforce/model/SupplierContractModel.java
src/main/java/com/untill/retailforce/model/SupplierContractModelPageResultModel.java
src/main/java/com/untill/retailforce/model/SupplierModel.java
src/main/java/com/untill/retailforce/model/SupplierModelPageResultModel.java
src/main/java/com/untill/retailforce/model/SupportTicketModel.java
src/main/java/com/untill/retailforce/model/SupportTicketModelPageResultModel.java
src/main/java/com/untill/retailforce/model/SupportTicketSimple.java
src/main/java/com/untill/retailforce/model/SupportTicketStatus.java
src/main/java/com/untill/retailforce/model/TaxonomyCloudStoreConfiguration.java
src/main/java/com/untill/retailforce/model/TaxonomyFileStoreConfiguration.java
src/main/java/com/untill/retailforce/model/Terminal.java
src/main/java/com/untill/retailforce/model/TerminalContactType.java
src/main/java/com/untill/retailforce/model/TerminalInsightModel.java
src/main/java/com/untill/retailforce/model/TerminalModel.java
src/main/java/com/untill/retailforce/model/TerminalModelPageResultModel.java
src/main/java/com/untill/retailforce/model/TerminalType.java
src/main/java/com/untill/retailforce/model/TimelogOverviewModel.java
src/main/java/com/untill/retailforce/model/TseAnnouncementCreateResult.java
src/main/java/com/untill/retailforce/model/TseAnnouncementOverview.java
src/main/java/com/untill/retailforce/model/TseAnnouncementOverviewPageResultModel.java
src/main/java/com/untill/retailforce/model/TseAnnouncementProgress.java
src/main/java/com/untill/retailforce/model/TseAnnouncementStatus.java
src/main/java/com/untill/retailforce/model/TseAnnouncementStatusInfo.java
src/main/java/com/untill/retailforce/model/TseConfiguration.java
src/main/java/com/untill/retailforce/model/TseDriver.java
src/main/java/com/untill/retailforce/model/TseDriverInfo.java
src/main/java/com/untill/retailforce/model/TseInformationOverview.java
src/main/java/com/untill/retailforce/model/TseInformationOverviewPageResultModel.java
src/main/java/com/untill/retailforce/model/TseType.java
src/main/java/com/untill/retailforce/model/User.java
src/main/java/com/untill/retailforce/model/ValidationError.java
src/main/java/com/untill/retailforce/model/Vat.java
src/test/java/com/untill/retailforce/api/AuthenticationApiTest.java
src/test/java/com/untill/retailforce/api/BackupApiTest.java
src/test/java/com/untill/retailforce/api/BillingApiTest.java
src/test/java/com/untill/retailforce/api/ConfigurationApiTest.java
src/test/java/com/untill/retailforce/api/ConfigurationControllerAustriaApiTest.java
src/test/java/com/untill/retailforce/api/ConfigurationControllerGermanyApiTest.java
src/test/java/com/untill/retailforce/api/ConfigurationControllerItalyApiTest.java
src/test/java/com/untill/retailforce/api/ConfigurationControllerLithuaniaApiTest.java
src/test/java/com/untill/retailforce/api/ConfigurationControllerSwedenApiTest.java
src/test/java/com/untill/retailforce/api/DashboardApiTest.java
src/test/java/com/untill/retailforce/api/DocumentApiTest.java
src/test/java/com/untill/retailforce/api/ImplementationApiTest.java
src/test/java/com/untill/retailforce/api/ImplementationControllerAustriaApiTest.java
src/test/java/com/untill/retailforce/api/ImplementationControllerDenmarkApiTest.java
src/test/java/com/untill/retailforce/api/ImplementationControllerFranceApiTest.java
src/test/java/com/untill/retailforce/api/ImplementationControllerGermanyApiTest.java
src/test/java/com/untill/retailforce/api/InformationApiTest.java
src/test/java/com/untill/retailforce/api/LicencingApiTest.java
src/test/java/com/untill/retailforce/api/MasterDataApiTest.java
src/test/java/com/untill/retailforce/api/MasterDataCompaniesApiTest.java
src/test/java/com/untill/retailforce/api/MasterDataDistributorsApiTest.java
src/test/java/com/untill/retailforce/api/MasterDataOrganisationsApiTest.java
src/test/java/com/untill/retailforce/api/MasterDataStoresApiTest.java
src/test/java/com/untill/retailforce/api/MasterDataSuppliersApiTest.java
src/test/java/com/untill/retailforce/api/MasterDataTerminalsApiTest.java
src/test/java/com/untill/retailforce/api/MasterDatsaOrganisationsApiTest.java
src/test/java/com/untill/retailforce/api/MoveToCloudApiTest.java
src/test/java/com/untill/retailforce/api/NotificationApiTest.java
src/test/java/com/untill/retailforce/api/ReceiptsApiTest.java
src/test/java/com/untill/retailforce/api/SearchApiTest.java
src/test/java/com/untill/retailforce/api/SecurityApiTest.java
src/test/java/com/untill/retailforce/api/StaticApiTest.java
src/test/java/com/untill/retailforce/api/SupportTicketApiTest.java
src/test/java/com/untill/retailforce/api/ThemingApiTest.java
src/test/java/com/untill/retailforce/model/AccessLicenseAllocationTest.java
src/test/java/com/untill/retailforce/model/AccessLicenseConfigurationInfoTest.java
src/test/java/com/untill/retailforce/model/AccessLicenseConfigurationParameterTest.java
src/test/java/com/untill/retailforce/model/AccessLicenseContractTest.java
src/test/java/com/untill/retailforce/model/AccessLicensePageResultModelTest.java
src/test/java/com/untill/retailforce/model/AccessLicenseTest.java
src/test/java/com/untill/retailforce/model/AddressTest.java
src/test/java/com/untill/retailforce/model/ApiKeyTest.java
src/test/java/com/untill/retailforce/model/AuditLogEntryTest.java
src/test/java/com/untill/retailforce/model/AuthenticationTypeTest.java
src/test/java/com/untill/retailforce/model/AutomaticVatCalculationTest.java
src/test/java/com/untill/retailforce/model/BackupDataPageResultModelTest.java
src/test/java/com/untill/retailforce/model/BackupDataTest.java
src/test/java/com/untill/retailforce/model/BarcodeTypeTest.java
src/test/java/com/untill/retailforce/model/BillingAccessLicenseCountTest.java
src/test/java/com/untill/retailforce/model/BillingDistributorLicenseCountBillingLicenseOverviewPageResultModelTest.java
src/test/java/com/untill/retailforce/model/BillingDistributorLicenseCountBillingLicenseOverviewTest.java
src/test/java/com/untill/retailforce/model/BillingDistributorLicenseCountTest.java
src/test/java/com/untill/retailforce/model/BillingLicenseCountBillingLicenseOverviewPageResultModelTest.java
src/test/java/com/untill/retailforce/model/BillingLicenseCountBillingLicenseOverviewTest.java
src/test/java/com/untill/retailforce/model/BillingLicenseCountTest.java
src/test/java/com/untill/retailforce/model/BillingLicenseDetailPageResultModelTest.java
src/test/java/com/untill/retailforce/model/BillingLicenseDetailTest.java
src/test/java/com/untill/retailforce/model/BoolResponseTest.java
src/test/java/com/untill/retailforce/model/BusinessSectorTest.java
src/test/java/com/untill/retailforce/model/BusinessTransactionTypeDocumentTypeMappingTest.java
src/test/java/com/untill/retailforce/model/BusinessTransactionTypeTest.java
src/test/java/com/untill/retailforce/model/CashRegisterDropoutReasonTest.java
src/test/java/com/untill/retailforce/model/CashRegisterTest.java
src/test/java/com/untill/retailforce/model/CertificateFormatTest.java
src/test/java/com/untill/retailforce/model/CertificateModelTest.java
src/test/java/com/untill/retailforce/model/CertificateTest.java
src/test/java/com/untill/retailforce/model/ClearingRunTest.java
src/test/java/com/untill/retailforce/model/ClientConfigurationGermanyTest.java
src/test/java/com/untill/retailforce/model/CloudParameterTest.java
src/test/java/com/untill/retailforce/model/CompanyIdentificationTest.java
src/test/java/com/untill/retailforce/model/CompanyModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/CompanyModelTest.java
src/test/java/com/untill/retailforce/model/CompanyTest.java
src/test/java/com/untill/retailforce/model/ConfigLicenseModelTest.java
src/test/java/com/untill/retailforce/model/ConfigurationParameterTest.java
src/test/java/com/untill/retailforce/model/ControlUnitDriverInfoTest.java
src/test/java/com/untill/retailforce/model/ControlUnitDriverTest.java
src/test/java/com/untill/retailforce/model/CosmosDocumentTest.java
src/test/java/com/untill/retailforce/model/DashboardTerminalDetailPageResultModelTest.java
src/test/java/com/untill/retailforce/model/DashboardTerminalDetailTest.java
src/test/java/com/untill/retailforce/model/DashboardTerminalOverviewPageResultModelTest.java
src/test/java/com/untill/retailforce/model/DashboardTerminalOverviewTest.java
src/test/java/com/untill/retailforce/model/DigitalReceiptTest.java
src/test/java/com/untill/retailforce/model/DistributorContractModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/DistributorContractModelTest.java
src/test/java/com/untill/retailforce/model/DistributorContractTest.java
src/test/java/com/untill/retailforce/model/DistributorModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/DistributorModelTest.java
src/test/java/com/untill/retailforce/model/DistributorTest.java
src/test/java/com/untill/retailforce/model/DocumentCouponTest.java
src/test/java/com/untill/retailforce/model/DocumentCouponTextLineTest.java
src/test/java/com/untill/retailforce/model/DocumentIssueTypeTest.java
src/test/java/com/untill/retailforce/model/DocumentPaymentCardDataTest.java
src/test/java/com/untill/retailforce/model/DocumentPaymentTest.java
src/test/java/com/untill/retailforce/model/DocumentPositionBaseTest.java
src/test/java/com/untill/retailforce/model/DocumentPositionReferenceTest.java
src/test/java/com/untill/retailforce/model/DocumentPositionTypeTest.java
src/test/java/com/untill/retailforce/model/DocumentReferenceTest.java
src/test/java/com/untill/retailforce/model/DocumentTaxPositionTest.java
src/test/java/com/untill/retailforce/model/DocumentTest.java
src/test/java/com/untill/retailforce/model/DocumentTypeTest.java
src/test/java/com/untill/retailforce/model/DownloadLinkPageResultModelTest.java
src/test/java/com/untill/retailforce/model/DownloadLinkTest.java
src/test/java/com/untill/retailforce/model/EntityParameterInfoTest.java
src/test/java/com/untill/retailforce/model/EntityPermissionsTest.java
src/test/java/com/untill/retailforce/model/EntitySecurityPageResultModelTest.java
src/test/java/com/untill/retailforce/model/EntitySecurityTest.java
src/test/java/com/untill/retailforce/model/EntityTypesTest.java
src/test/java/com/untill/retailforce/model/ErrorLevelTest.java
src/test/java/com/untill/retailforce/model/FiscalClientConfigurationModelTest.java
src/test/java/com/untill/retailforce/model/FiscalClientConfigurationTest.java
src/test/java/com/untill/retailforce/model/FiscalClientTest.java
src/test/java/com/untill/retailforce/model/FiscalCountryTest.java
src/test/java/com/untill/retailforce/model/FiscalModuleEnvironmentTest.java
src/test/java/com/untill/retailforce/model/FiscalResponseTest.java
src/test/java/com/untill/retailforce/model/FiscalisationTypeTest.java
src/test/java/com/untill/retailforce/model/FonConnectionLogMessagePageResultModelTest.java
src/test/java/com/untill/retailforce/model/FonConnectionLogMessageTest.java
src/test/java/com/untill/retailforce/model/FonCredentialsTest.java
src/test/java/com/untill/retailforce/model/GuidBreadCrumbTest.java
src/test/java/com/untill/retailforce/model/GuidEntityVersionPageResultModelTest.java
src/test/java/com/untill/retailforce/model/GuidEntityVersionTest.java
src/test/java/com/untill/retailforce/model/GuidExtendedSimpleCountryObjectPageResultModelTest.java
src/test/java/com/untill/retailforce/model/GuidExtendedSimpleCountryObjectTest.java
src/test/java/com/untill/retailforce/model/GuidHierarchicalSimpleObjectPageResultModelTest.java
src/test/java/com/untill/retailforce/model/GuidHierarchicalSimpleObjectTest.java
src/test/java/com/untill/retailforce/model/GuidSimpleObjectPageResultModelTest.java
src/test/java/com/untill/retailforce/model/GuidSimpleObjectTest.java
src/test/java/com/untill/retailforce/model/HelpInformationTest.java
src/test/java/com/untill/retailforce/model/IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0ReceiptTest.java
src/test/java/com/untill/retailforce/model/IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupportTest.java
src/test/java/com/untill/retailforce/model/IFiscalCountryPropertiesPageResultModelTest.java
src/test/java/com/untill/retailforce/model/IFiscalCountryPropertiesTest.java
src/test/java/com/untill/retailforce/model/IFiscalImplementationConfigurationTest.java
src/test/java/com/untill/retailforce/model/INotificationTest.java
src/test/java/com/untill/retailforce/model/IdentificationTypeTest.java
src/test/java/com/untill/retailforce/model/ImportModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/ImportModelTest.java
src/test/java/com/untill/retailforce/model/Int32SimpleObjectPageResultModelTest.java
src/test/java/com/untill/retailforce/model/Int32SimpleObjectTest.java
src/test/java/com/untill/retailforce/model/InvitationInfoModelTest.java
src/test/java/com/untill/retailforce/model/InvitationModelTest.java
src/test/java/com/untill/retailforce/model/JwtLicenseClaimTest.java
src/test/java/com/untill/retailforce/model/LegalFormTest.java
src/test/java/com/untill/retailforce/model/LicenseDetailUsageTest.java
src/test/java/com/untill/retailforce/model/LicenseModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/LicenseModelTest.java
src/test/java/com/untill/retailforce/model/LicenseOptionTest.java
src/test/java/com/untill/retailforce/model/LicenseTest.java
src/test/java/com/untill/retailforce/model/LogEntryTypeTest.java
src/test/java/com/untill/retailforce/model/MessageApiModelTest.java
src/test/java/com/untill/retailforce/model/NotificationResultTest.java
src/test/java/com/untill/retailforce/model/NotificationTypeTest.java
src/test/java/com/untill/retailforce/model/NotificationsInfoTest.java
src/test/java/com/untill/retailforce/model/OfflineConfigurationTest.java
src/test/java/com/untill/retailforce/model/OnboardingFinishDataTest.java
src/test/java/com/untill/retailforce/model/OnboardingFinishDistributorDataTest.java
src/test/java/com/untill/retailforce/model/OrganisationModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/OrganisationModelTest.java
src/test/java/com/untill/retailforce/model/OrganisationTest.java
src/test/java/com/untill/retailforce/model/OutstandingPaymentTest.java
src/test/java/com/untill/retailforce/model/ParameterInfoTest.java
src/test/java/com/untill/retailforce/model/PartnerIdentificationTypeTest.java
src/test/java/com/untill/retailforce/model/PartnerTest.java
src/test/java/com/untill/retailforce/model/PartnerTypeTest.java
src/test/java/com/untill/retailforce/model/PayOutTypeTest.java
src/test/java/com/untill/retailforce/model/PaymentTermsTest.java
src/test/java/com/untill/retailforce/model/PaymentTypeTest.java
src/test/java/com/untill/retailforce/model/PlatformTypeTest.java
src/test/java/com/untill/retailforce/model/PrincipalTest.java
src/test/java/com/untill/retailforce/model/PrinterDriverInfoTest.java
src/test/java/com/untill/retailforce/model/PrinterImageFileTest.java
src/test/java/com/untill/retailforce/model/PrinterModelTest.java
src/test/java/com/untill/retailforce/model/ProfilePictureModelTest.java
src/test/java/com/untill/retailforce/model/ReceiptDataScrollResultModelTest.java
src/test/java/com/untill/retailforce/model/ReceiptDataTest.java
src/test/java/com/untill/retailforce/model/ReceiptDomainTest.java
src/test/java/com/untill/retailforce/model/ReferenceTypeTest.java
src/test/java/com/untill/retailforce/model/RegistrationModelTest.java
src/test/java/com/untill/retailforce/model/ReleaseTest.java
src/test/java/com/untill/retailforce/model/ResultResponseTest.java
src/test/java/com/untill/retailforce/model/ReturnAndSaleBehaviorTest.java
src/test/java/com/untill/retailforce/model/ReturnReasonTypeTest.java
src/test/java/com/untill/retailforce/model/ReturnReferenceBehaviorTest.java
src/test/java/com/untill/retailforce/model/SalutationTest.java
src/test/java/com/untill/retailforce/model/SearchResultModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/SearchResultModelTest.java
src/test/java/com/untill/retailforce/model/SecurityCertificateDropoutReasonTest.java
src/test/java/com/untill/retailforce/model/SecurityCertificateIssuerTest.java
src/test/java/com/untill/retailforce/model/SecurityCertificateTypeTest.java
src/test/java/com/untill/retailforce/model/SessionTest.java
src/test/java/com/untill/retailforce/model/SignDeviceDriverInfoTest.java
src/test/java/com/untill/retailforce/model/SignDeviceDriverTest.java
src/test/java/com/untill/retailforce/model/SignatureDeviceTest.java
src/test/java/com/untill/retailforce/model/SoftwareTest.java
src/test/java/com/untill/retailforce/model/StoreModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/StoreModelTest.java
src/test/java/com/untill/retailforce/model/StoreTest.java
src/test/java/com/untill/retailforce/model/StringSimpleObjectPageResultModelTest.java
src/test/java/com/untill/retailforce/model/StringSimpleObjectTest.java
src/test/java/com/untill/retailforce/model/SupplierContractModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/SupplierContractModelTest.java
src/test/java/com/untill/retailforce/model/SupplierContractTest.java
src/test/java/com/untill/retailforce/model/SupplierModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/SupplierModelTest.java
src/test/java/com/untill/retailforce/model/SupplierTest.java
src/test/java/com/untill/retailforce/model/SupportTicketModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/SupportTicketModelTest.java
src/test/java/com/untill/retailforce/model/SupportTicketSimpleTest.java
src/test/java/com/untill/retailforce/model/SupportTicketStatusTest.java
src/test/java/com/untill/retailforce/model/TaxonomyCloudStoreConfigurationTest.java
src/test/java/com/untill/retailforce/model/TaxonomyFileStoreConfigurationTest.java
src/test/java/com/untill/retailforce/model/TerminalContactTypeTest.java
src/test/java/com/untill/retailforce/model/TerminalInsightModelTest.java
src/test/java/com/untill/retailforce/model/TerminalModelPageResultModelTest.java
src/test/java/com/untill/retailforce/model/TerminalModelTest.java
src/test/java/com/untill/retailforce/model/TerminalTest.java
src/test/java/com/untill/retailforce/model/TerminalTypeTest.java
src/test/java/com/untill/retailforce/model/TimelogOverviewModelTest.java
src/test/java/com/untill/retailforce/model/TseAnnouncementCreateResultTest.java
src/test/java/com/untill/retailforce/model/TseAnnouncementOverviewPageResultModelTest.java
src/test/java/com/untill/retailforce/model/TseAnnouncementOverviewTest.java
src/test/java/com/untill/retailforce/model/TseAnnouncementProgressTest.java
src/test/java/com/untill/retailforce/model/TseAnnouncementStatusInfoTest.java
src/test/java/com/untill/retailforce/model/TseAnnouncementStatusTest.java
src/test/java/com/untill/retailforce/model/TseConfigurationTest.java
src/test/java/com/untill/retailforce/model/TseDriverInfoTest.java
src/test/java/com/untill/retailforce/model/TseDriverTest.java
src/test/java/com/untill/retailforce/model/TseInformationOverviewPageResultModelTest.java
src/test/java/com/untill/retailforce/model/TseInformationOverviewTest.java
src/test/java/com/untill/retailforce/model/TseTypeTest.java
src/test/java/com/untill/retailforce/model/UserTest.java
src/test/java/com/untill/retailforce/model/ValidationErrorTest.java
src/test/java/com/untill/retailforce/model/VatTest.java
