/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for Session
 */
public class SessionTest {
    private final Session model = new Session();

    /**
     * Model tests for Session
     */
    @Test
    public void testSession() {
        // TODO: test Session
    }

    /**
     * Test the property 'username'
     */
    @Test
    public void usernameTest() {
        // TODO: test username
    }

    /**
     * Test the property 'email'
     */
    @Test
    public void emailTest() {
        // TODO: test email
    }

    /**
     * Test the property 'profilePicture'
     */
    @Test
    public void profilePictureTest() {
        // TODO: test profilePicture
    }

    /**
     * Test the property 'distributorId'
     */
    @Test
    public void distributorIdTest() {
        // TODO: test distributorId
    }

    /**
     * Test the property 'supplierId'
     */
    @Test
    public void supplierIdTest() {
        // TODO: test supplierId
    }

    /**
     * Test the property 'isRetailForceUser'
     */
    @Test
    public void isRetailForceUserTest() {
        // TODO: test isRetailForceUser
    }

}
