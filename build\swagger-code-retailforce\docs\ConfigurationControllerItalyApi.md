# ConfigurationControllerItalyApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet**](ConfigurationControllerItalyApi.md#apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet) | **GET** /api/v1.0/configuration/it/clientConfiguration/{terminalId}/printerImages | Returns stored printer image ids |
| [**apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet**](ConfigurationControllerItalyApi.md#apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet) | **GET** /api/v1.0/configuration/it/clientConfiguration/{terminalId}/printerImages/{order} | returns the requeste image |
| [**apiV10ConfigurationItConfigurationIdPrinterImagesGet**](ConfigurationControllerItalyApi.md#apiV10ConfigurationItConfigurationIdPrinterImagesGet) | **GET** /api/v1.0/configuration/it/{configurationId}/printerImages | Returns stored printer image ids |
| [**apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete**](ConfigurationControllerItalyApi.md#apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete) | **DELETE** /api/v1.0/configuration/it/{configurationId}/printerImages/{order} | Delete printer image |
| [**apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet**](ConfigurationControllerItalyApi.md#apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet) | **GET** /api/v1.0/configuration/it/{configurationId}/printerImages/{order} | returns the requeste image |
| [**apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost**](ConfigurationControllerItalyApi.md#apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost) | **POST** /api/v1.0/configuration/it/{configurationId}/printerImages/{order} | Upload printer image |
| [**apiV10ConfigurationItPrinterDriverInfosGet**](ConfigurationControllerItalyApi.md#apiV10ConfigurationItPrinterDriverInfosGet) | **GET** /api/v1.0/configuration/it/printerDriverInfos | Returns the printer driver infos. |


<a id="apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet"></a>
# **apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet**
> List&lt;PrinterImageFile&gt; apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet(terminalId)

Returns stored printer image ids

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerItalyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerItalyApi apiInstance = new ConfigurationControllerItalyApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminalId of the configuration.
    try {
      List<PrinterImageFile> result = apiInstance.apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerItalyApi#apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminalId of the configuration. | |

### Return type

[**List&lt;PrinterImageFile&gt;**](PrinterImageFile.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet"></a>
# **apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet**
> File apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet(terminalId, order)

returns the requeste image

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerItalyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerItalyApi apiInstance = new ConfigurationControllerItalyApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminalId of the configuration.
    Integer order = 56; // Integer | order of the requested image (only 0-9) are allowed
    try {
      File result = apiInstance.apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet(terminalId, order);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerItalyApi#apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminalId of the configuration. | |
| **order** | **Integer**| order of the requested image (only 0-9) are allowed | |

### Return type

[**File**](File.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationItConfigurationIdPrinterImagesGet"></a>
# **apiV10ConfigurationItConfigurationIdPrinterImagesGet**
> List&lt;Integer&gt; apiV10ConfigurationItConfigurationIdPrinterImagesGet(configurationId)

Returns stored printer image ids

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerItalyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerItalyApi apiInstance = new ConfigurationControllerItalyApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration
    try {
      List<Integer> result = apiInstance.apiV10ConfigurationItConfigurationIdPrinterImagesGet(configurationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerItalyApi#apiV10ConfigurationItConfigurationIdPrinterImagesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration | |

### Return type

**List&lt;Integer&gt;**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete"></a>
# **apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete**
> apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete(configurationId, order)

Delete printer image

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerItalyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerItalyApi apiInstance = new ConfigurationControllerItalyApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration
    Integer order = 56; // Integer | order of the requested image (only 0-9) are allowed
    try {
      apiInstance.apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete(configurationId, order);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerItalyApi#apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration | |
| **order** | **Integer**| order of the requested image (only 0-9) are allowed | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet"></a>
# **apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet**
> File apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet(configurationId, order)

returns the requeste image

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerItalyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerItalyApi apiInstance = new ConfigurationControllerItalyApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration
    Integer order = 56; // Integer | order of the requested image (only 0-9) are allowed
    try {
      File result = apiInstance.apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet(configurationId, order);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerItalyApi#apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration | |
| **order** | **Integer**| order of the requested image (only 0-9) are allowed | |

### Return type

[**File**](File.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost"></a>
# **apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost**
> apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost(configurationId, order, _file)

Upload printer image

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerItalyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerItalyApi apiInstance = new ConfigurationControllerItalyApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration
    Integer order = 56; // Integer | order of the requested image (only 0-9) are allowed
    File _file = new File("/path/to/file"); // File | 
    try {
      apiInstance.apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost(configurationId, order, _file);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerItalyApi#apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration | |
| **order** | **Integer**| order of the requested image (only 0-9) are allowed | |
| **_file** | **File**|  | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationItPrinterDriverInfosGet"></a>
# **apiV10ConfigurationItPrinterDriverInfosGet**
> List&lt;PrinterDriverInfo&gt; apiV10ConfigurationItPrinterDriverInfosGet()

Returns the printer driver infos.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerItalyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerItalyApi apiInstance = new ConfigurationControllerItalyApi(defaultClient);
    try {
      List<PrinterDriverInfo> result = apiInstance.apiV10ConfigurationItPrinterDriverInfosGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerItalyApi#apiV10ConfigurationItPrinterDriverInfosGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;PrinterDriverInfo&gt;**](PrinterDriverInfo.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

