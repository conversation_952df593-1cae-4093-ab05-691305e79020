/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.SupportTicketStatus;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for SupportTicketModel
 */
public class SupportTicketModelTest {
    private final SupportTicketModel model = new SupportTicketModel();

    /**
     * Model tests for SupportTicketModel
     */
    @Test
    public void testSupportTicketModel() {
        // TODO: test SupportTicketModel
    }

    /**
     * Test the property 'ticketId'
     */
    @Test
    public void ticketIdTest() {
        // TODO: test ticketId
    }

    /**
     * Test the property 'ticketNumber'
     */
    @Test
    public void ticketNumberTest() {
        // TODO: test ticketNumber
    }

    /**
     * Test the property 'ticketText'
     */
    @Test
    public void ticketTextTest() {
        // TODO: test ticketText
    }

    /**
     * Test the property 'requester'
     */
    @Test
    public void requesterTest() {
        // TODO: test requester
    }

    /**
     * Test the property 'requestingOrganisation'
     */
    @Test
    public void requestingOrganisationTest() {
        // TODO: test requestingOrganisation
    }

    /**
     * Test the property 'customer'
     */
    @Test
    public void customerTest() {
        // TODO: test customer
    }

    /**
     * Test the property 'accruedHours'
     */
    @Test
    public void accruedHoursTest() {
        // TODO: test accruedHours
    }

    /**
     * Test the property 'ticketUrl'
     */
    @Test
    public void ticketUrlTest() {
        // TODO: test ticketUrl
    }

    /**
     * Test the property 'status'
     */
    @Test
    public void statusTest() {
        // TODO: test status
    }

}
