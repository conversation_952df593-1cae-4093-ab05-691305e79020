# MasterDataSuppliersApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MasterdataSuppliersContractsTypeSimpleGet**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersContractsTypeSimpleGet) | **GET** /api/v1.0/masterdata/suppliers/contracts/type/simple | Returns a list of RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 objects representing all available supplier contract types. |
| [**apiV10MasterdataSuppliersGet**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersGet) | **GET** /api/v1.0/masterdata/suppliers | Returns all Suppliers for the authenticated user. |
| [**apiV10MasterdataSuppliersPost**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersPost) | **POST** /api/v1.0/masterdata/suppliers | Creates a new supplier in the cloud store. |
| [**apiV10MasterdataSuppliersSimpleGet**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSimpleGet) | **GET** /api/v1.0/masterdata/suppliers/simple | Returns all Suppliers for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. |
| [**apiV10MasterdataSuppliersSupplierIdContractContractIdDelete**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractContractIdDelete) | **DELETE** /api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId} | Deletes a contract. |
| [**apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete) | **DELETE** /api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId}/pdf | Removes a contract pdf from an existing contract. |
| [**apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost) | **POST** /api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId}/pdf | Adds a contract pdf file to an existing contract. |
| [**apiV10MasterdataSuppliersSupplierIdContractContractIdPut**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractContractIdPut) | **PUT** /api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId} | Updates the given contract of the supplier. |
| [**apiV10MasterdataSuppliersSupplierIdContractPost**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractPost) | **POST** /api/v1.0/masterdata/suppliers/{supplierId}/contract | Creates a new contract for a supplier. |
| [**apiV10MasterdataSuppliersSupplierIdContractsContractIdGet**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractsContractIdGet) | **GET** /api/v1.0/masterdata/suppliers/{supplierId}/contracts/{contractId} | Returns the requested contract. |
| [**apiV10MasterdataSuppliersSupplierIdContractsGet**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractsGet) | **GET** /api/v1.0/masterdata/suppliers/{supplierId}/contracts | Returns all contracts for a supplier. |
| [**apiV10MasterdataSuppliersSupplierIdDelete**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdDelete) | **DELETE** /api/v1.0/masterdata/suppliers/{supplierId} | Deletes an supplier from cloud store. |
| [**apiV10MasterdataSuppliersSupplierIdGet**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdGet) | **GET** /api/v1.0/masterdata/suppliers/{supplierId} | Returns the supplier requested by the given id. |
| [**apiV10MasterdataSuppliersSupplierIdPut**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdPut) | **PUT** /api/v1.0/masterdata/suppliers/{supplierId} | Updates an supplier in the cloud store. |
| [**apiV10MasterdataSuppliersSupplierIdSimpleGet**](MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdSimpleGet) | **GET** /api/v1.0/masterdata/suppliers/{supplierId}/simple | Returns a simple supplierRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. |


<a id="apiV10MasterdataSuppliersContractsTypeSimpleGet"></a>
# **apiV10MasterdataSuppliersContractsTypeSimpleGet**
> List&lt;GuidSimpleObject&gt; apiV10MasterdataSuppliersContractsTypeSimpleGet()

Returns a list of RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 objects representing all available supplier contract types.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    try {
      List<GuidSimpleObject> result = apiInstance.apiV10MasterdataSuppliersContractsTypeSimpleGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersContractsTypeSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;GuidSimpleObject&gt;**](GuidSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersGet"></a>
# **apiV10MasterdataSuppliersGet**
> SupplierModelPageResultModel apiV10MasterdataSuppliersGet(pageOffset, pageSize, searchString)

Returns all Suppliers for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      SupplierModelPageResultModel result = apiInstance.apiV10MasterdataSuppliersGet(pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**SupplierModelPageResultModel**](SupplierModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersPost"></a>
# **apiV10MasterdataSuppliersPost**
> SupplierModel apiV10MasterdataSuppliersPost(supplier)

Creates a new supplier in the cloud store.

If RetailForce.Cloud.Model.Supplier.SupplierId set to System.Guid.Empty, then the supplier id will be generated by the service.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    Supplier supplier = new Supplier(); // Supplier | The supplier to create.
    try {
      SupplierModel result = apiInstance.apiV10MasterdataSuppliersPost(supplier);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplier** | [**Supplier**](Supplier.md)| The supplier to create. | [optional] |

### Return type

[**SupplierModel**](SupplierModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSimpleGet"></a>
# **apiV10MasterdataSuppliersSimpleGet**
> GuidSimpleObjectPageResultModel apiV10MasterdataSuppliersSimpleGet(pageOffset, pageSize, searchString)

Returns all Suppliers for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      GuidSimpleObjectPageResultModel result = apiInstance.apiV10MasterdataSuppliersSimpleGet(pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**GuidSimpleObjectPageResultModel**](GuidSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdContractContractIdDelete"></a>
# **apiV10MasterdataSuppliersSupplierIdContractContractIdDelete**
> apiV10MasterdataSuppliersSupplierIdContractContractIdDelete(supplierId, contractId)

Deletes a contract.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The supplier id of the contract to delete.
    UUID contractId = UUID.randomUUID(); // UUID | The id of the contract to delete.
    try {
      apiInstance.apiV10MasterdataSuppliersSupplierIdContractContractIdDelete(supplierId, contractId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdContractContractIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The supplier id of the contract to delete. | |
| **contractId** | **UUID**| The id of the contract to delete. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete"></a>
# **apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete**
> apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete(supplierId, contractId)

Removes a contract pdf from an existing contract.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The supplier id of the contract.
    UUID contractId = UUID.randomUUID(); // UUID | The contract id of the contract.
    try {
      apiInstance.apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete(supplierId, contractId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The supplier id of the contract. | |
| **contractId** | **UUID**| The contract id of the contract. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost"></a>
# **apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost**
> apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost(supplierId, contractId, pdf)

Adds a contract pdf file to an existing contract.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The supplier id of the contract.
    UUID contractId = UUID.randomUUID(); // UUID | The contract id of the contract.
    File pdf = new File("/path/to/file"); // File | 
    try {
      apiInstance.apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost(supplierId, contractId, pdf);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The supplier id of the contract. | |
| **contractId** | **UUID**| The contract id of the contract. | |
| **pdf** | **File**|  | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdContractContractIdPut"></a>
# **apiV10MasterdataSuppliersSupplierIdContractContractIdPut**
> SupplierContractModel apiV10MasterdataSuppliersSupplierIdContractContractIdPut(supplierId, contractId, supplierContract)

Updates the given contract of the supplier.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The id of the supplier where the contract will be updated.
    UUID contractId = UUID.randomUUID(); // UUID | The id of the contract to update.
    SupplierContract supplierContract = new SupplierContract(); // SupplierContract | The new contract data.
    try {
      SupplierContractModel result = apiInstance.apiV10MasterdataSuppliersSupplierIdContractContractIdPut(supplierId, contractId, supplierContract);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdContractContractIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The id of the supplier where the contract will be updated. | |
| **contractId** | **UUID**| The id of the contract to update. | |
| **supplierContract** | [**SupplierContract**](SupplierContract.md)| The new contract data. | [optional] |

### Return type

[**SupplierContractModel**](SupplierContractModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdContractPost"></a>
# **apiV10MasterdataSuppliersSupplierIdContractPost**
> SupplierContractModel apiV10MasterdataSuppliersSupplierIdContractPost(supplierId, supplierContract)

Creates a new contract for a supplier.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The supplier id of the supplier where the contract has to be created.
    SupplierContract supplierContract = new SupplierContract(); // SupplierContract | The contract to create.
    try {
      SupplierContractModel result = apiInstance.apiV10MasterdataSuppliersSupplierIdContractPost(supplierId, supplierContract);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdContractPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The supplier id of the supplier where the contract has to be created. | |
| **supplierContract** | [**SupplierContract**](SupplierContract.md)| The contract to create. | [optional] |

### Return type

[**SupplierContractModel**](SupplierContractModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdContractsContractIdGet"></a>
# **apiV10MasterdataSuppliersSupplierIdContractsContractIdGet**
> SupplierContractModel apiV10MasterdataSuppliersSupplierIdContractsContractIdGet(supplierId, contractId)

Returns the requested contract.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The if of the supplier of the requested contract.
    UUID contractId = UUID.randomUUID(); // UUID | The id of the requested contract.
    try {
      SupplierContractModel result = apiInstance.apiV10MasterdataSuppliersSupplierIdContractsContractIdGet(supplierId, contractId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdContractsContractIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The if of the supplier of the requested contract. | |
| **contractId** | **UUID**| The id of the requested contract. | |

### Return type

[**SupplierContractModel**](SupplierContractModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdContractsGet"></a>
# **apiV10MasterdataSuppliersSupplierIdContractsGet**
> SupplierContractModelPageResultModel apiV10MasterdataSuppliersSupplierIdContractsGet(supplierId, pageOffset, pageSize, searchString)

Returns all contracts for a supplier.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The supplier where the contacts are requested.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      SupplierContractModelPageResultModel result = apiInstance.apiV10MasterdataSuppliersSupplierIdContractsGet(supplierId, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdContractsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The supplier where the contacts are requested. | |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**SupplierContractModelPageResultModel**](SupplierContractModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdDelete"></a>
# **apiV10MasterdataSuppliersSupplierIdDelete**
> apiV10MasterdataSuppliersSupplierIdDelete(supplierId)

Deletes an supplier from cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The id of the supplier which should be deleted.
    try {
      apiInstance.apiV10MasterdataSuppliersSupplierIdDelete(supplierId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The id of the supplier which should be deleted. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdGet"></a>
# **apiV10MasterdataSuppliersSupplierIdGet**
> SupplierModel apiV10MasterdataSuppliersSupplierIdGet(supplierId)

Returns the supplier requested by the given id.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The id of the requested supplier.
    try {
      SupplierModel result = apiInstance.apiV10MasterdataSuppliersSupplierIdGet(supplierId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The id of the requested supplier. | |

### Return type

[**SupplierModel**](SupplierModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdPut"></a>
# **apiV10MasterdataSuppliersSupplierIdPut**
> SupplierModel apiV10MasterdataSuppliersSupplierIdPut(supplierId, supplier)

Updates an supplier in the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | The id of the supplier to be updated.
    Supplier supplier = new Supplier(); // Supplier | The supplier object to update the Supplier.
    try {
      SupplierModel result = apiInstance.apiV10MasterdataSuppliersSupplierIdPut(supplierId, supplier);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**| The id of the supplier to be updated. | |
| **supplier** | [**Supplier**](Supplier.md)| The supplier object to update the Supplier. | [optional] |

### Return type

[**SupplierModel**](SupplierModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataSuppliersSupplierIdSimpleGet"></a>
# **apiV10MasterdataSuppliersSupplierIdSimpleGet**
> GuidSimpleObject apiV10MasterdataSuppliersSupplierIdSimpleGet(supplierId)

Returns a simple supplierRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataSuppliersApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataSuppliersApi apiInstance = new MasterDataSuppliersApi(defaultClient);
    UUID supplierId = UUID.randomUUID(); // UUID | 
    try {
      GuidSimpleObject result = apiInstance.apiV10MasterdataSuppliersSupplierIdSimpleGet(supplierId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataSuppliersApi#apiV10MasterdataSuppliersSupplierIdSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supplierId** | **UUID**|  | |

### Return type

[**GuidSimpleObject**](GuidSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

