

# TerminalInsightModel

Some information about the terminal for the ui.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**terminalId** | **UUID** | The guid of the terminal. |  [optional] |
|**isTest** | **Boolean** |  |  [optional] |
|**fiscalClientVersion** | **String** | This is the actual version of the client. |  [optional] |
|**versionUpdate** | **OffsetDateTime** | The date/time when the version was last time updated. |  [optional] |
|**configurationUpdate** | **OffsetDateTime** | The date/time when the configuration of the client was last time updated. |  [optional] |
|**archiveDate** | **OffsetDateTime** | The date/time when the terminal was archived. |  [optional] |
|**countryValues** | [**List&lt;StringSimpleObject&gt;**](StringSimpleObject.md) | Country values in a dictionary to show specific country values in terminal insights app widget. |  [optional] |



