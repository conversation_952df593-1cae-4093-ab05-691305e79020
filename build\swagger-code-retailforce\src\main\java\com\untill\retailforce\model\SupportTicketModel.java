/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.SupportTicketStatus;
import java.io.IOException;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * SupportTicketModel
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SupportTicketModel {
  public static final String SERIALIZED_NAME_TICKET_ID = "ticketId";
  @SerializedName(SERIALIZED_NAME_TICKET_ID)
  private UUID ticketId;

  public static final String SERIALIZED_NAME_TICKET_NUMBER = "ticketNumber";
  @SerializedName(SERIALIZED_NAME_TICKET_NUMBER)
  private String ticketNumber;

  public static final String SERIALIZED_NAME_TICKET_TEXT = "ticketText";
  @SerializedName(SERIALIZED_NAME_TICKET_TEXT)
  private String ticketText;

  public static final String SERIALIZED_NAME_REQUESTER = "requester";
  @SerializedName(SERIALIZED_NAME_REQUESTER)
  private String requester;

  public static final String SERIALIZED_NAME_REQUESTING_ORGANISATION = "requestingOrganisation";
  @SerializedName(SERIALIZED_NAME_REQUESTING_ORGANISATION)
  private String requestingOrganisation;

  public static final String SERIALIZED_NAME_CUSTOMER = "customer";
  @SerializedName(SERIALIZED_NAME_CUSTOMER)
  private String customer;

  public static final String SERIALIZED_NAME_ACCRUED_HOURS = "accruedHours";
  @SerializedName(SERIALIZED_NAME_ACCRUED_HOURS)
  private Double accruedHours;

  public static final String SERIALIZED_NAME_TICKET_URL = "ticketUrl";
  @SerializedName(SERIALIZED_NAME_TICKET_URL)
  private String ticketUrl;

  public static final String SERIALIZED_NAME_STATUS = "status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private SupportTicketStatus status;

  public SupportTicketModel() {
  }

  public SupportTicketModel ticketId(UUID ticketId) {
    
    this.ticketId = ticketId;
    return this;
  }

   /**
   * Get ticketId
   * @return ticketId
  **/
  @javax.annotation.Nullable
  public UUID getTicketId() {
    return ticketId;
  }


  public void setTicketId(UUID ticketId) {
    this.ticketId = ticketId;
  }


  public SupportTicketModel ticketNumber(String ticketNumber) {
    
    this.ticketNumber = ticketNumber;
    return this;
  }

   /**
   * Get ticketNumber
   * @return ticketNumber
  **/
  @javax.annotation.Nullable
  public String getTicketNumber() {
    return ticketNumber;
  }


  public void setTicketNumber(String ticketNumber) {
    this.ticketNumber = ticketNumber;
  }


  public SupportTicketModel ticketText(String ticketText) {
    
    this.ticketText = ticketText;
    return this;
  }

   /**
   * Get ticketText
   * @return ticketText
  **/
  @javax.annotation.Nullable
  public String getTicketText() {
    return ticketText;
  }


  public void setTicketText(String ticketText) {
    this.ticketText = ticketText;
  }


  public SupportTicketModel requester(String requester) {
    
    this.requester = requester;
    return this;
  }

   /**
   * Get requester
   * @return requester
  **/
  @javax.annotation.Nullable
  public String getRequester() {
    return requester;
  }


  public void setRequester(String requester) {
    this.requester = requester;
  }


  public SupportTicketModel requestingOrganisation(String requestingOrganisation) {
    
    this.requestingOrganisation = requestingOrganisation;
    return this;
  }

   /**
   * Get requestingOrganisation
   * @return requestingOrganisation
  **/
  @javax.annotation.Nullable
  public String getRequestingOrganisation() {
    return requestingOrganisation;
  }


  public void setRequestingOrganisation(String requestingOrganisation) {
    this.requestingOrganisation = requestingOrganisation;
  }


  public SupportTicketModel customer(String customer) {
    
    this.customer = customer;
    return this;
  }

   /**
   * Get customer
   * @return customer
  **/
  @javax.annotation.Nullable
  public String getCustomer() {
    return customer;
  }


  public void setCustomer(String customer) {
    this.customer = customer;
  }


  public SupportTicketModel accruedHours(Double accruedHours) {
    
    this.accruedHours = accruedHours;
    return this;
  }

   /**
   * Get accruedHours
   * @return accruedHours
  **/
  @javax.annotation.Nullable
  public Double getAccruedHours() {
    return accruedHours;
  }


  public void setAccruedHours(Double accruedHours) {
    this.accruedHours = accruedHours;
  }


  public SupportTicketModel ticketUrl(String ticketUrl) {
    
    this.ticketUrl = ticketUrl;
    return this;
  }

   /**
   * Zendesk TicketUrl
   * @return ticketUrl
  **/
  @javax.annotation.Nullable
  public String getTicketUrl() {
    return ticketUrl;
  }


  public void setTicketUrl(String ticketUrl) {
    this.ticketUrl = ticketUrl;
  }


  public SupportTicketModel status(SupportTicketStatus status) {
    
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @javax.annotation.Nullable
  public SupportTicketStatus getStatus() {
    return status;
  }


  public void setStatus(SupportTicketStatus status) {
    this.status = status;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SupportTicketModel supportTicketModel = (SupportTicketModel) o;
    return Objects.equals(this.ticketId, supportTicketModel.ticketId) &&
        Objects.equals(this.ticketNumber, supportTicketModel.ticketNumber) &&
        Objects.equals(this.ticketText, supportTicketModel.ticketText) &&
        Objects.equals(this.requester, supportTicketModel.requester) &&
        Objects.equals(this.requestingOrganisation, supportTicketModel.requestingOrganisation) &&
        Objects.equals(this.customer, supportTicketModel.customer) &&
        Objects.equals(this.accruedHours, supportTicketModel.accruedHours) &&
        Objects.equals(this.ticketUrl, supportTicketModel.ticketUrl) &&
        Objects.equals(this.status, supportTicketModel.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ticketId, ticketNumber, ticketText, requester, requestingOrganisation, customer, accruedHours, ticketUrl, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SupportTicketModel {\n");
    sb.append("    ticketId: ").append(toIndentedString(ticketId)).append("\n");
    sb.append("    ticketNumber: ").append(toIndentedString(ticketNumber)).append("\n");
    sb.append("    ticketText: ").append(toIndentedString(ticketText)).append("\n");
    sb.append("    requester: ").append(toIndentedString(requester)).append("\n");
    sb.append("    requestingOrganisation: ").append(toIndentedString(requestingOrganisation)).append("\n");
    sb.append("    customer: ").append(toIndentedString(customer)).append("\n");
    sb.append("    accruedHours: ").append(toIndentedString(accruedHours)).append("\n");
    sb.append("    ticketUrl: ").append(toIndentedString(ticketUrl)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("ticketId");
    openapiFields.add("ticketNumber");
    openapiFields.add("ticketText");
    openapiFields.add("requester");
    openapiFields.add("requestingOrganisation");
    openapiFields.add("customer");
    openapiFields.add("accruedHours");
    openapiFields.add("ticketUrl");
    openapiFields.add("status");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to SupportTicketModel
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!SupportTicketModel.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in SupportTicketModel is not found in the empty JSON string", SupportTicketModel.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!SupportTicketModel.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `SupportTicketModel` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("ticketId") != null && !jsonObj.get("ticketId").isJsonNull()) && !jsonObj.get("ticketId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `ticketId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("ticketId").toString()));
      }
      if ((jsonObj.get("ticketNumber") != null && !jsonObj.get("ticketNumber").isJsonNull()) && !jsonObj.get("ticketNumber").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `ticketNumber` to be a primitive type in the JSON string but got `%s`", jsonObj.get("ticketNumber").toString()));
      }
      if ((jsonObj.get("ticketText") != null && !jsonObj.get("ticketText").isJsonNull()) && !jsonObj.get("ticketText").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `ticketText` to be a primitive type in the JSON string but got `%s`", jsonObj.get("ticketText").toString()));
      }
      if ((jsonObj.get("requester") != null && !jsonObj.get("requester").isJsonNull()) && !jsonObj.get("requester").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `requester` to be a primitive type in the JSON string but got `%s`", jsonObj.get("requester").toString()));
      }
      if ((jsonObj.get("requestingOrganisation") != null && !jsonObj.get("requestingOrganisation").isJsonNull()) && !jsonObj.get("requestingOrganisation").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `requestingOrganisation` to be a primitive type in the JSON string but got `%s`", jsonObj.get("requestingOrganisation").toString()));
      }
      if ((jsonObj.get("customer") != null && !jsonObj.get("customer").isJsonNull()) && !jsonObj.get("customer").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `customer` to be a primitive type in the JSON string but got `%s`", jsonObj.get("customer").toString()));
      }
      if ((jsonObj.get("ticketUrl") != null && !jsonObj.get("ticketUrl").isJsonNull()) && !jsonObj.get("ticketUrl").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `ticketUrl` to be a primitive type in the JSON string but got `%s`", jsonObj.get("ticketUrl").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!SupportTicketModel.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'SupportTicketModel' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<SupportTicketModel> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(SupportTicketModel.class));

       return (TypeAdapter<T>) new TypeAdapter<SupportTicketModel>() {
           @Override
           public void write(JsonWriter out, SupportTicketModel value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public SupportTicketModel read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of SupportTicketModel given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of SupportTicketModel
  * @throws IOException if the JSON string is invalid with respect to SupportTicketModel
  */
  public static SupportTicketModel fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, SupportTicketModel.class);
  }

 /**
  * Convert an instance of SupportTicketModel to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

