# ImplementationControllerFranceApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ImplementationFrTerminalIdStoreArchivePost**](ImplementationControllerFranceApi.md#apiV10ImplementationFrTerminalIdStoreArchivePost) | **POST** /api/v1.0/implementation/fr/{terminalId}/storeArchive | Stores the french archive to the french archive store. |
| [**apiV10ImplementationFrTerminalIdTaxArchiveGet**](ImplementationControllerFranceApi.md#apiV10ImplementationFrTerminalIdTaxArchiveGet) | **GET** /api/v1.0/implementation/fr/{terminalId}/taxArchive | Exports one or more french fiscal archives according to date parameter. |
| [**apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost**](ImplementationControllerFranceApi.md#apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost) | **POST** /api/v1.0/implementation/fr/{terminalId}/taxArchive/verify | Method to verify the french archive. |


<a id="apiV10ImplementationFrTerminalIdStoreArchivePost"></a>
# **apiV10ImplementationFrTerminalIdStoreArchivePost**
> apiV10ImplementationFrTerminalIdStoreArchivePost(terminalId, archiveFilename, frenchArchiveFile)

Stores the french archive to the french archive store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerFranceApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerFranceApi apiInstance = new ImplementationControllerFranceApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal which wants to store the french archive.
    String archiveFilename = "archiveFilename_example"; // String | The filename of the archive.
    File frenchArchiveFile = new File("/path/to/file"); // File | 
    try {
      apiInstance.apiV10ImplementationFrTerminalIdStoreArchivePost(terminalId, archiveFilename, frenchArchiveFile);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerFranceApi#apiV10ImplementationFrTerminalIdStoreArchivePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal which wants to store the french archive. | |
| **archiveFilename** | **String**| The filename of the archive. | [optional] |
| **frenchArchiveFile** | **File**|  | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | OK |  -  |
| **422** | TerminalId &#x3D; Guid.Empty or Frencharchive is null or archiveFilename is null or string.Empty or the unique client id of the fiscal client in the french archive does not correspond to parameter terminalId. |  -  |

<a id="apiV10ImplementationFrTerminalIdTaxArchiveGet"></a>
# **apiV10ImplementationFrTerminalIdTaxArchiveGet**
> apiV10ImplementationFrTerminalIdTaxArchiveGet(terminalId, fromDate, tillDate)

Exports one or more french fiscal archives according to date parameter.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerFranceApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerFranceApi apiInstance = new ImplementationControllerFranceApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id where the archives should be exported.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The start date of the request.
    OffsetDateTime tillDate = OffsetDateTime.now(); // OffsetDateTime | The end date of the request.
    try {
      apiInstance.apiV10ImplementationFrTerminalIdTaxArchiveGet(terminalId, fromDate, tillDate);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerFranceApi#apiV10ImplementationFrTerminalIdTaxArchiveGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id where the archives should be exported. | |
| **fromDate** | **OffsetDateTime**| The start date of the request. | |
| **tillDate** | **OffsetDateTime**| The end date of the request. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | OK |  -  |
| **422** | Terminal id was set to Guid.Empty or from date / till date are not in valid range (1.1.2016 - now). |  -  |

<a id="apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost"></a>
# **apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost**
> List&lt;ValidationError&gt; apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost(terminalId, frenchArchiveFile)

Method to verify the french archive.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerFranceApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerFranceApi apiInstance = new ImplementationControllerFranceApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the french archive.
    File frenchArchiveFile = new File("/path/to/file"); // File | 
    try {
      List<ValidationError> result = apiInstance.apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost(terminalId, frenchArchiveFile);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerFranceApi#apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the french archive. | |
| **frenchArchiveFile** | **File**|  | |

### Return type

[**List&lt;ValidationError&gt;**](ValidationError.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | OK |  -  |
| **422** | TerminalId &#x3D; Guid.Empty or no file was uploaded. |  -  |

