# ConfigurationControllerAustriaApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ConfigurationAtFonCheckFonCredentialsPut**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonCheckFonCredentialsPut) | **PUT** /api/v1.0/configuration/at/fon/checkFonCredentials | Checks if an authentication can be done with the given fon credentials. |
| [**apiV10ConfigurationAtFonClientIdAesKeyPost**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdAesKeyPost) | **POST** /api/v1.0/configuration/at/fon/{clientId}/aesKey | Updates a the aes key for the given terminal in the retailforce database (or adds). |
| [**apiV10ConfigurationAtFonClientIdCacheDelete**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCacheDelete) | **DELETE** /api/v1.0/configuration/at/fon/{clientId}/cache | Clears fon cache variable on server (for faster fon communication). |
| [**apiV10ConfigurationAtFonClientIdCashregisterDelete**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterDelete) | **DELETE** /api/v1.0/configuration/at/fon/{clientId}/cashregister | Unregister cash register at fon service. You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdCashregisterGet**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/cashregister | Checks the cash register state with the FON Service. You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdCashregisterPost**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterPost) | **POST** /api/v1.0/configuration/at/fon/{clientId}/cashregister | Register of cash register system. You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdCashregisterPut**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterPut) | **PUT** /api/v1.0/configuration/at/fon/{clientId}/cashregister | Recommissioning of cash register system |
| [**apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete) | **DELETE** /api/v1.0/configuration/at/fon/{clientId}/cashregister/simple | Unregister cash register at fon service. You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdCashregisterSimpleGet**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterSimpleGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/cashregister/simple | Checks the cash register state with the FON Service. You have to be authenticated. The format for the cash registerid is storeNumber/terminalNumber. |
| [**apiV10ConfigurationAtFonClientIdCertificateDelete**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCertificateDelete) | **DELETE** /api/v1.0/configuration/at/fon/{clientId}/certificate | Unregister a security certificate at fon (Finanzonline). You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdCertificateGet**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCertificateGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/certificate | Returns true if the given security certificate is activated (IN_BETRIEB) at fon (Finanzonline). You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdCertificatePost**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCertificatePost) | **POST** /api/v1.0/configuration/at/fon/{clientId}/certificate | Register a security certificate at fon (Finanzonline). You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdCertificatePut**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCertificatePut) | **PUT** /api/v1.0/configuration/at/fon/{clientId}/certificate | Recommissioning a security certificate at fon (Finanzonline). You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdDocumentValidateGet**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdDocumentValidateGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/document/validate | Does document validation on fon service. You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdDocumentValidateLastGet**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdDocumentValidateLastGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/document/validate/last | Does document validation on fon service. You have to be authenticated. |
| [**apiV10ConfigurationAtFonClientIdGet**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdGet) | **GET** /api/v1.0/configuration/at/fon/{clientId} | Returns whether the client has fon access (if the data is configured and license is exi |
| [**apiV10ConfigurationAtFonDropoutReasonsGet**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonDropoutReasonsGet) | **GET** /api/v1.0/configuration/at/fon/dropoutReasons | Returns the possible drop out reasons for cash register drop out. |
| [**apiV10ConfigurationAtHsmTerminalIdPatch**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtHsmTerminalIdPatch) | **PATCH** /api/v1.0/configuration/at/hsm/{terminalId} | Tests configured hsm connections for the given client. |
| [**apiV10ConfigurationAtSignDeviceDriverInfoGet**](ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtSignDeviceDriverInfoGet) | **GET** /api/v1.0/configuration/at/signDeviceDriverInfo | Returns supported smart card drivers for configuration in the cloud user interface. |


<a id="apiV10ConfigurationAtFonCheckFonCredentialsPut"></a>
# **apiV10ConfigurationAtFonCheckFonCredentialsPut**
> BoolResponse apiV10ConfigurationAtFonCheckFonCredentialsPut(fonCredentials)

Checks if an authentication can be done with the given fon credentials.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    FonCredentials fonCredentials = new FonCredentials(); // FonCredentials | The fon credentials to logon.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonCheckFonCredentialsPut(fonCredentials);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonCheckFonCredentialsPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **fonCredentials** | [**FonCredentials**](FonCredentials.md)| The fon credentials to logon. | |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **422** | fonCredentials is null or any of the properties of fonCredentials is null or empty string. |  -  |

<a id="apiV10ConfigurationAtFonClientIdAesKeyPost"></a>
# **apiV10ConfigurationAtFonClientIdAesKeyPost**
> apiV10ConfigurationAtFonClientIdAesKeyPost(clientId, body)

Updates a the aes key for the given terminal in the retailforce database (or adds).

This method does not update the aes key at fon (Finanzonline).  Handle with care: Settings this value to a wrong value will result in an error when recovering fiscal client with RestoreByCloud and decrypting of turnover counter of dep of this client will no longer be possible.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String body = "body_example"; // String | A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: '{aesKey}'.
    try {
      apiInstance.apiV10ConfigurationAtFonClientIdAesKeyPost(clientId, body);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdAesKeyPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **body** | **String**| A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **400** | Aes key was not send with single quotes enclosing. |  -  |
| **403** | Client is not authenticated or has no access to the given client. |  -  |
| **422** | clientId was set to System.Guid.Empty or aesKey was set to null or empty string. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCacheDelete"></a>
# **apiV10ConfigurationAtFonClientIdCacheDelete**
> BoolResponse apiV10ConfigurationAtFonClientIdCacheDelete(clientId)

Clears fon cache variable on server (for faster fon communication).

Removes both (Test and Productive) cache objects.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The clientId where the cache should be removed.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCacheDelete(clientId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCacheDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The clientId where the cache should be removed. | |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCashregisterDelete"></a>
# **apiV10ConfigurationAtFonClientIdCashregisterDelete**
> ResultResponse apiV10ConfigurationAtFonClientIdCashregisterDelete(clientId, cashRegisterId, reason, fromDate, requestDateTime, isTest)

Unregister cash register at fon service. You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String cashRegisterId = "cashRegisterId_example"; // String | The cash register id for which the request is done.
    CashRegisterDropoutReason reason = CashRegisterDropoutReason.fromValue("[1] = Stolen"); // CashRegisterDropoutReason | Reason for cash register dropout. Attention: just permanent dropout reasons are allowed.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The date of the dropout, has to be set at reason = [1] Stolen, [5] NotWorking, [99] = Others. Default is DateTime.Now.
    OffsetDateTime requestDateTime = OffsetDateTime.now(); // OffsetDateTime | The datetime of the request.
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    try {
      ResultResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCashregisterDelete(clientId, cashRegisterId, reason, fromDate, requestDateTime, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCashregisterDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **cashRegisterId** | **String**| The cash register id for which the request is done. | |
| **reason** | [**CashRegisterDropoutReason**](.md)| Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. | [enum: [1] = Stolen, [5] = NotWorking, [6] = PlanedDropout, [7] = Unrepairable, [99] = Others] |
| **fromDate** | **OffsetDateTime**| The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [5] NotWorking, [99] &#x3D; Others. Default is DateTime.Now. | [optional] |
| **requestDateTime** | **OffsetDateTime**| The datetime of the request. | [optional] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |

### Return type

[**ResultResponse**](ResultResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropriate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCashregisterGet"></a>
# **apiV10ConfigurationAtFonClientIdCashregisterGet**
> BoolResponse apiV10ConfigurationAtFonClientIdCashregisterGet(clientId, cashRegisterId, expectedResponse, isTest)

Checks the cash register state with the FON Service. You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String cashRegisterId = "cashRegisterId_example"; // String | The client for which the request is done.
    Boolean expectedResponse = false; // Boolean | A bool representing the expected response for this call. Default value is false.
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCashregisterGet(clientId, cashRegisterId, expectedResponse, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCashregisterGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **cashRegisterId** | **String**| The client for which the request is done. | |
| **expectedResponse** | **Boolean**| A bool representing the expected response for this call. Default value is false. | [optional] [default to false] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropriate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCashregisterPost"></a>
# **apiV10ConfigurationAtFonClientIdCashregisterPost**
> ResultResponse apiV10ConfigurationAtFonClientIdCashregisterPost(clientId, cashRegisterId, aesKey, requestDateTime, isTest, body)

Register of cash register system. You have to be authenticated.

Security patch: do not use longer query parameter aesKey. Use request body instead (You have to send the aeskeyBody with single quotes: &#39;{aesKey}&#39;).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String cashRegisterId = "cashRegisterId_example"; // String | A string representing the unique cash register id.
    String aesKey = "aesKey_example"; // String | Do not use any more. Use request body instead.
    OffsetDateTime requestDateTime = OffsetDateTime.now(); // OffsetDateTime | The datetime of the request.
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    String body = "body_example"; // String | A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: '{aesKey}'.
    try {
      ResultResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCashregisterPost(clientId, cashRegisterId, aesKey, requestDateTime, isTest, body);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCashregisterPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **cashRegisterId** | **String**| A string representing the unique cash register id. | |
| **aesKey** | **String**| Do not use any more. Use request body instead. | [optional] |
| **requestDateTime** | **OffsetDateTime**| The datetime of the request. | [optional] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |
| **body** | **String**| A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. | [optional] |

### Return type

[**ResultResponse**](ResultResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropriate license. |  -  |
| **403** | Client is not authenticated or has no access to the given client. |  -  |
| **422** | clientId was set to System.Guid.Empty or cashRegisterId or aesKey was set to null or empty string. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCashregisterPut"></a>
# **apiV10ConfigurationAtFonClientIdCashregisterPut**
> ResultResponse apiV10ConfigurationAtFonClientIdCashregisterPut(clientId, cashRegisterId, recommissionDate, requestDateTime, isTest)

Recommissioning of cash register system

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String cashRegisterId = "cashRegisterId_example"; // String | A string representing the unique cash register id.
    OffsetDateTime recommissionDate = OffsetDateTime.now(); // OffsetDateTime | The date and time of recommissioning.
    OffsetDateTime requestDateTime = OffsetDateTime.now(); // OffsetDateTime | The datetime of the request.
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    try {
      ResultResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCashregisterPut(clientId, cashRegisterId, recommissionDate, requestDateTime, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCashregisterPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **cashRegisterId** | **String**| A string representing the unique cash register id. | |
| **recommissionDate** | **OffsetDateTime**| The date and time of recommissioning. | |
| **requestDateTime** | **OffsetDateTime**| The datetime of the request. | [optional] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |

### Return type

[**ResultResponse**](ResultResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete"></a>
# **apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete**
> BoolResponse apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete(clientId, reason, requestDateTime)

Unregister cash register at fon service. You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    CashRegisterDropoutReason reason = CashRegisterDropoutReason.fromValue("[1] = Stolen"); // CashRegisterDropoutReason | Reason for cash register dropout. Attention: just permanent dropout reasons are allowed.
    OffsetDateTime requestDateTime = OffsetDateTime.now(); // OffsetDateTime | The datetime of the request.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete(clientId, reason, requestDateTime);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **reason** | [**CashRegisterDropoutReason**](.md)| Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. | [enum: [1] = Stolen, [5] = NotWorking, [6] = PlanedDropout, [7] = Unrepairable, [99] = Others] |
| **requestDateTime** | **OffsetDateTime**| The datetime of the request. | [optional] |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropriate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty was set to null or empty string. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCashregisterSimpleGet"></a>
# **apiV10ConfigurationAtFonClientIdCashregisterSimpleGet**
> BoolResponse apiV10ConfigurationAtFonClientIdCashregisterSimpleGet(clientId)

Checks the cash register state with the FON Service. You have to be authenticated. The format for the cash registerid is storeNumber/terminalNumber.

The expected result is true.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCashregisterSimpleGet(clientId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCashregisterSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropriate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty was set to null or empty string. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCertificateDelete"></a>
# **apiV10ConfigurationAtFonClientIdCertificateDelete**
> ResultResponse apiV10ConfigurationAtFonClientIdCertificateDelete(clientId, securityCertificate, reason, fromDate, requestDateTime, isTest, logMessage)

Unregister a security certificate at fon (Finanzonline). You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String securityCertificate = "securityCertificate_example"; // String | The certificate to unregister.
    SecurityCertificateDropoutReason reason = SecurityCertificateDropoutReason.fromValue("stolen"); // SecurityCertificateDropoutReason | The reason of the dropout.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The date of the dropout, has to be set at reason = [1] Stolen, [2] NotWorking, [99] = Others.
    OffsetDateTime requestDateTime = OffsetDateTime.now(); // OffsetDateTime | The datetime of the request.
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    String logMessage = ""; // String | Additional log message for fon protocol log.
    try {
      ResultResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCertificateDelete(clientId, securityCertificate, reason, fromDate, requestDateTime, isTest, logMessage);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCertificateDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **securityCertificate** | **String**| The certificate to unregister. | |
| **reason** | [**SecurityCertificateDropoutReason**](.md)| The reason of the dropout. | [enum: stolen, notWorking, planedDropout, unrepairable, others] |
| **fromDate** | **OffsetDateTime**| The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [2] NotWorking, [99] &#x3D; Others. | [optional] |
| **requestDateTime** | **OffsetDateTime**| The datetime of the request. | [optional] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |
| **logMessage** | **String**| Additional log message for fon protocol log. | [optional] [default to ] |

### Return type

[**ResultResponse**](ResultResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropriate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty or securityCertificate was set to 0. Also returns if fromDate is set to null, when reason is set to  [1] Stolen, [2] NotWorking, [99] &#x3D; Others. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCertificateGet"></a>
# **apiV10ConfigurationAtFonClientIdCertificateGet**
> BoolResponse apiV10ConfigurationAtFonClientIdCertificateGet(clientId, securityCertificate, expectedResponse, isTest)

Returns true if the given security certificate is activated (IN_BETRIEB) at fon (Finanzonline). You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String securityCertificate = "securityCertificate_example"; // String | The certificate id of the security certificate to check.
    Boolean expectedResponse = false; // Boolean | A bool representing the expected response for this call. Default value is false.
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCertificateGet(clientId, securityCertificate, expectedResponse, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCertificateGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **securityCertificate** | **String**| The certificate id of the security certificate to check. | |
| **expectedResponse** | **Boolean**| A bool representing the expected response for this call. Default value is false. | [optional] [default to false] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropriate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty or securityCertificate was set to 0. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCertificatePost"></a>
# **apiV10ConfigurationAtFonClientIdCertificatePost**
> ResultResponse apiV10ConfigurationAtFonClientIdCertificatePost(clientId, securityCertificate, certificateType, issuer, requestDateTime, isTest)

Register a security certificate at fon (Finanzonline). You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String securityCertificate = "securityCertificate_example"; // String | The certificate to register.
    SecurityCertificateType certificateType = SecurityCertificateType.fromValue("securityCard"); // SecurityCertificateType | The type of the certificate.
    SecurityCertificateIssuer issuer = SecurityCertificateIssuer.fromValue("aTrust"); // SecurityCertificateIssuer | The issuer of the certificate.
    OffsetDateTime requestDateTime = OffsetDateTime.now(); // OffsetDateTime | The datetime of the request.
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    try {
      ResultResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCertificatePost(clientId, securityCertificate, certificateType, issuer, requestDateTime, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCertificatePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **securityCertificate** | **String**| The certificate to register. | |
| **certificateType** | [**SecurityCertificateType**](.md)| The type of the certificate. | [enum: securityCard, hsm, serviceProviderHsm] |
| **issuer** | [**SecurityCertificateIssuer**](.md)| The issuer of the certificate. | [enum: aTrust, globalTrust, primeSign, test, unkown] |
| **requestDateTime** | **OffsetDateTime**| The datetime of the request. | [optional] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |

### Return type

[**ResultResponse**](ResultResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropriate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty or securityCertificate was set to 0. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdCertificatePut"></a>
# **apiV10ConfigurationAtFonClientIdCertificatePut**
> ResultResponse apiV10ConfigurationAtFonClientIdCertificatePut(clientId, securityCertificate, recommissionDate, requestDateTime, isTest, logMessage)

Recommissioning a security certificate at fon (Finanzonline). You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String securityCertificate = "securityCertificate_example"; // String | The certificate to register.
    OffsetDateTime recommissionDate = OffsetDateTime.now(); // OffsetDateTime | The date and time of recommissioning.
    OffsetDateTime requestDateTime = OffsetDateTime.now(); // OffsetDateTime | The datetime of the request.
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    String logMessage = ""; // String | Additional log message for fon protocol log.
    try {
      ResultResponse result = apiInstance.apiV10ConfigurationAtFonClientIdCertificatePut(clientId, securityCertificate, recommissionDate, requestDateTime, isTest, logMessage);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdCertificatePut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **securityCertificate** | **String**| The certificate to register. | |
| **recommissionDate** | **OffsetDateTime**| The date and time of recommissioning. | [optional] |
| **requestDateTime** | **OffsetDateTime**| The datetime of the request. | [optional] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |
| **logMessage** | **String**| Additional log message for fon protocol log. | [optional] [default to ] |

### Return type

[**ResultResponse**](ResultResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationAtFonClientIdDocumentValidateGet"></a>
# **apiV10ConfigurationAtFonClientIdDocumentValidateGet**
> BoolResponse apiV10ConfigurationAtFonClientIdDocumentValidateGet(clientId, depDocument, annualReceiptYear, isTest)

Does document validation on fon service. You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    String depDocument = "depDocument_example"; // String | A string representing the dep record of the document to check.
    String annualReceiptYear = "annualReceiptYear_example"; // String | String value. Set this value if the validated document is an annual receipt (check will be stored to database).
    Boolean isTest = false; // Boolean | True if the request should be done in test mode; otherwise false. Default is false.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonClientIdDocumentValidateGet(clientId, depDocument, annualReceiptYear, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdDocumentValidateGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |
| **depDocument** | **String**| A string representing the dep record of the document to check. | |
| **annualReceiptYear** | **String**| String value. Set this value if the validated document is an annual receipt (check will be stored to database). | [optional] |
| **isTest** | **Boolean**| True if the request should be done in test mode; otherwise false. Default is false. | [optional] [default to false] |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropiate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty or depDocument was set to null or empty string. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdDocumentValidateLastGet"></a>
# **apiV10ConfigurationAtFonClientIdDocumentValidateLastGet**
> BoolResponse apiV10ConfigurationAtFonClientIdDocumentValidateLastGet(clientId)

Does document validation on fon service. You have to be authenticated.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the request is done.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonClientIdDocumentValidateLastGet(clientId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdDocumentValidateLastGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the request is done. | |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | Client has not appropiate license. |  -  |
| **403** | Client is not authenticated. |  -  |
| **404** | Client is not found or no dep is found. |  -  |
| **406** | Configured fon data is missing or not correct. |  -  |
| **422** | clientId was set to System.Guid.Empty was set to null or empty string. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonClientIdGet"></a>
# **apiV10ConfigurationAtFonClientIdGet**
> BoolResponse apiV10ConfigurationAtFonClientIdGet(clientId)

Returns whether the client has fon access (if the data is configured and license is exi

In possibility of having no license no exception is raised, but appropiate message is returned.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID clientId = UUID.randomUUID(); // UUID | The client for which the access is requested.
    try {
      BoolResponse result = apiInstance.apiV10ConfigurationAtFonClientIdGet(clientId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonClientIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clientId** | **UUID**| The client for which the access is requested. | |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Client is not authenticated. |  -  |
| **422** | clientId was set to System.Guid.Empty. |  -  |
| **400** | Bad request. |  -  |
| **500** | Internal server error. |  -  |

<a id="apiV10ConfigurationAtFonDropoutReasonsGet"></a>
# **apiV10ConfigurationAtFonDropoutReasonsGet**
> List&lt;StringSimpleObject&gt; apiV10ConfigurationAtFonDropoutReasonsGet()

Returns the possible drop out reasons for cash register drop out.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    try {
      List<StringSimpleObject> result = apiInstance.apiV10ConfigurationAtFonDropoutReasonsGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtFonDropoutReasonsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;StringSimpleObject&gt;**](StringSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Forbidden (not authorized). |  -  |
| **403** | Client is not authenticated. |  -  |

<a id="apiV10ConfigurationAtHsmTerminalIdPatch"></a>
# **apiV10ConfigurationAtHsmTerminalIdPatch**
> Boolean apiV10ConfigurationAtHsmTerminalIdPatch(terminalId)

Tests configured hsm connections for the given client.

In normal case only one connection is configured.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminalId of the terminal where the check has to be done.
    try {
      Boolean result = apiInstance.apiV10ConfigurationAtHsmTerminalIdPatch(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtHsmTerminalIdPatch");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminalId of the terminal where the check has to be done. | |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **400** | The requested terminal has no valid configuration configured. |  -  |
| **403** | Nobody is authenticated or access to requested terminal is denied. |  -  |
| **404** | The requested terminal is not found. |  -  |
| **406** | The requested terminal has no valid hsm configuration to test. |  -  |
| **409** | The requested terminal is not an austrian terminal. |  -  |
| **422** | Thrown if parameter terminalId is set to Guid.Empty. |  -  |

<a id="apiV10ConfigurationAtSignDeviceDriverInfoGet"></a>
# **apiV10ConfigurationAtSignDeviceDriverInfoGet**
> List&lt;SignDeviceDriverInfo&gt; apiV10ConfigurationAtSignDeviceDriverInfoGet()

Returns supported smart card drivers for configuration in the cloud user interface.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerAustriaApi apiInstance = new ConfigurationControllerAustriaApi(defaultClient);
    try {
      List<SignDeviceDriverInfo> result = apiInstance.apiV10ConfigurationAtSignDeviceDriverInfoGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerAustriaApi#apiV10ConfigurationAtSignDeviceDriverInfoGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;SignDeviceDriverInfo&gt;**](SignDeviceDriverInfo.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

