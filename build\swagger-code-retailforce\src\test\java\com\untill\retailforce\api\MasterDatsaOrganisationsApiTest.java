/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.GuidExtendedSimpleCountryObjectPageResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MasterDatsaOrganisationsApi
 */
@Disabled
public class MasterDatsaOrganisationsApiTest {

    private final MasterDatsaOrganisationsApi api = new MasterDatsaOrganisationsApi();

    /**
     * Returns all organisations for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataOrganisationsSimpleGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        UUID distributorId = null;
        GuidExtendedSimpleCountryObjectPageResultModel response = api.apiV10MasterdataOrganisationsSimpleGet(pageOffset, pageSize, searchString, distributorId);
        // TODO: test validations
    }

}
