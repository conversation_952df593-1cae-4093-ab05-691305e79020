# MasterDataStoresApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MasterdataStoresGet**](MasterDataStoresApi.md#apiV10MasterdataStoresGet) | **GET** /api/v1.0/masterdata/stores | Returns all stores for the given organisation/company for the authenticated user. |
| [**apiV10MasterdataStoresIdGet**](MasterDataStoresApi.md#apiV10MasterdataStoresIdGet) | **GET** /api/v1.0/masterdata/stores/id | Returns the store id of the requested store. |
| [**apiV10MasterdataStoresPost**](MasterDataStoresApi.md#apiV10MasterdataStoresPost) | **POST** /api/v1.0/masterdata/stores | Creates a store in the cloud store. |
| [**apiV10MasterdataStoresSimpleGet**](MasterDataStoresApi.md#apiV10MasterdataStoresSimpleGet) | **GET** /api/v1.0/masterdata/stores/simple | Returns all stores as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the given organisation/company for the authenticated user. |
| [**apiV10MasterdataStoresStoreIdDelete**](MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdDelete) | **DELETE** /api/v1.0/masterdata/stores/{storeId} | Deletes a store from the cloud store. |
| [**apiV10MasterdataStoresStoreIdGet**](MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdGet) | **GET** /api/v1.0/masterdata/stores/{storeId} | Returns the requested store for the authenticated users. |
| [**apiV10MasterdataStoresStoreIdHidePost**](MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdHidePost) | **POST** /api/v1.0/masterdata/stores/{storeId}/hide | Hides the store from the standard list. |
| [**apiV10MasterdataStoresStoreIdPut**](MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdPut) | **PUT** /api/v1.0/masterdata/stores/{storeId} | Updates a store in the cloud store. |
| [**apiV10MasterdataStoresStoreIdUnHidePost**](MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdUnHidePost) | **POST** /api/v1.0/masterdata/stores/{storeId}/unHide | Un-hides a previously hidden store to the standard list. |
| [**apiV10MasterdataStoresStoreIdVersionsGet**](MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdVersionsGet) | **GET** /api/v1.0/masterdata/stores/{storeId}/versions | Get store versions |


<a id="apiV10MasterdataStoresGet"></a>
# **apiV10MasterdataStoresGet**
> StoreModelPageResultModel apiV10MasterdataStoresGet(organisationId, companyId, pageOffset, pageSize, searchString, showHiddenStores)

Returns all stores for the given organisation/company for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The organization id for the requested stores.
    UUID companyId = UUID.randomUUID(); // UUID | The company id for the requested stores. If set to null filter is not applied.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    Boolean showHiddenStores = true; // Boolean | include hidden stores
    try {
      StoreModelPageResultModel result = apiInstance.apiV10MasterdataStoresGet(organisationId, companyId, pageOffset, pageSize, searchString, showHiddenStores);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The organization id for the requested stores. | [optional] |
| **companyId** | **UUID**| The company id for the requested stores. If set to null filter is not applied. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |
| **showHiddenStores** | **Boolean**| include hidden stores | [optional] |

### Return type

[**StoreModelPageResultModel**](StoreModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresIdGet"></a>
# **apiV10MasterdataStoresIdGet**
> UUID apiV10MasterdataStoresIdGet(organizationId, storeNumber)

Returns the store id of the requested store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization of the store.
    String storeNumber = "storeNumber_example"; // String | The store number of the store.
    try {
      UUID result = apiInstance.apiV10MasterdataStoresIdGet(organizationId, storeNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of the organization of the store. | [optional] |
| **storeNumber** | **String**| The store number of the store. | [optional] |

### Return type

[**UUID**](UUID.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresPost"></a>
# **apiV10MasterdataStoresPost**
> StoreModel apiV10MasterdataStoresPost(store)

Creates a store in the cloud store.

If RetailForce.Cloud.Model.Store.StoreId set to System.Guid.Empty, then the store id will be generated by the service.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    Store store = new Store(); // Store | The store to create.
    try {
      StoreModel result = apiInstance.apiV10MasterdataStoresPost(store);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **store** | [**Store**](Store.md)| The store to create. | |

### Return type

[**StoreModel**](StoreModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresSimpleGet"></a>
# **apiV10MasterdataStoresSimpleGet**
> GuidSimpleObjectPageResultModel apiV10MasterdataStoresSimpleGet(organisationId, companyId, pageOffset, pageSize, searchString)

Returns all stores as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the given organisation/company for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The organisation id for the requested stores.
    UUID companyId = UUID.randomUUID(); // UUID | The company id for the requested stores. If set to null filter is not applied.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      GuidSimpleObjectPageResultModel result = apiInstance.apiV10MasterdataStoresSimpleGet(organisationId, companyId, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The organisation id for the requested stores. | [optional] |
| **companyId** | **UUID**| The company id for the requested stores. If set to null filter is not applied. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**GuidSimpleObjectPageResultModel**](GuidSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresStoreIdDelete"></a>
# **apiV10MasterdataStoresStoreIdDelete**
> apiV10MasterdataStoresStoreIdDelete(storeId)

Deletes a store from the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID storeId = UUID.randomUUID(); // UUID | The id of the store to delete.
    try {
      apiInstance.apiV10MasterdataStoresStoreIdDelete(storeId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresStoreIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeId** | **UUID**| The id of the store to delete. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresStoreIdGet"></a>
# **apiV10MasterdataStoresStoreIdGet**
> StoreModel apiV10MasterdataStoresStoreIdGet(storeId, entityVersion)

Returns the requested store for the authenticated users.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID storeId = UUID.randomUUID(); // UUID | The id of the requested store.
    String entityVersion = "entityVersion_example"; // String | load specific version
    try {
      StoreModel result = apiInstance.apiV10MasterdataStoresStoreIdGet(storeId, entityVersion);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresStoreIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeId** | **UUID**| The id of the requested store. | |
| **entityVersion** | **String**| load specific version | [optional] |

### Return type

[**StoreModel**](StoreModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresStoreIdHidePost"></a>
# **apiV10MasterdataStoresStoreIdHidePost**
> StoreModel apiV10MasterdataStoresStoreIdHidePost(storeId)

Hides the store from the standard list.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID storeId = UUID.randomUUID(); // UUID | The id of the store to hide.
    try {
      StoreModel result = apiInstance.apiV10MasterdataStoresStoreIdHidePost(storeId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresStoreIdHidePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeId** | **UUID**| The id of the store to hide. | |

### Return type

[**StoreModel**](StoreModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresStoreIdPut"></a>
# **apiV10MasterdataStoresStoreIdPut**
> StoreModel apiV10MasterdataStoresStoreIdPut(storeId, store)

Updates a store in the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID storeId = UUID.randomUUID(); // UUID | The id of the store to update.
    Store store = new Store(); // Store | The data of the store to update.
    try {
      StoreModel result = apiInstance.apiV10MasterdataStoresStoreIdPut(storeId, store);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresStoreIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeId** | **UUID**| The id of the store to update. | |
| **store** | [**Store**](Store.md)| The data of the store to update. | |

### Return type

[**StoreModel**](StoreModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresStoreIdUnHidePost"></a>
# **apiV10MasterdataStoresStoreIdUnHidePost**
> StoreModel apiV10MasterdataStoresStoreIdUnHidePost(storeId)

Un-hides a previously hidden store to the standard list.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID storeId = UUID.randomUUID(); // UUID | The id of the store to un-hide.
    try {
      StoreModel result = apiInstance.apiV10MasterdataStoresStoreIdUnHidePost(storeId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresStoreIdUnHidePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeId** | **UUID**| The id of the store to un-hide. | |

### Return type

[**StoreModel**](StoreModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataStoresStoreIdVersionsGet"></a>
# **apiV10MasterdataStoresStoreIdVersionsGet**
> GuidEntityVersionPageResultModel apiV10MasterdataStoresStoreIdVersionsGet(storeId, pageOffset, pageSize)

Get store versions

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataStoresApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataStoresApi apiInstance = new MasterDataStoresApi(defaultClient);
    UUID storeId = UUID.randomUUID(); // UUID | 
    Integer pageOffset = 56; // Integer | 
    Integer pageSize = 56; // Integer | 
    try {
      GuidEntityVersionPageResultModel result = apiInstance.apiV10MasterdataStoresStoreIdVersionsGet(storeId, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataStoresApi#apiV10MasterdataStoresStoreIdVersionsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeId** | **UUID**|  | |
| **pageOffset** | **Integer**|  | [optional] |
| **pageSize** | **Integer**|  | [optional] |

### Return type

[**GuidEntityVersionPageResultModel**](GuidEntityVersionPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

