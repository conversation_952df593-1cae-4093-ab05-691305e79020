# ImplementationControllerAustriaApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet**](ImplementationControllerAustriaApi.md#apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet) | **GET** /api/v1.0/implementation/at/organization/{organizationId}/missingAnnualReceipts | Returns all missing annual year receipts (annual year receipt check). |
| [**apiV10ImplementationAtSignatureDeviceEntityIdGet**](ImplementationControllerAustriaApi.md#apiV10ImplementationAtSignatureDeviceEntityIdGet) | **GET** /api/v1.0/implementation/at/signatureDevice/{entityId} | Returns the signature devices for the given entity. |
| [**apiV10ImplementationAtTerminalIdAsitCryptoContainerGet**](ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdAsitCryptoContainerGet) | **GET** /api/v1.0/implementation/at/{terminalId}/asitCryptoContainer | Returns the asit crypto container for asit check (as json string). |
| [**apiV10ImplementationAtTerminalIdDep131Get**](ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdDep131Get) | **GET** /api/v1.0/implementation/at/{terminalId}/dep131 | Exports all data requested for dep131 protocol out of stored data. |
| [**apiV10ImplementationAtTerminalIdDepGet**](ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdDepGet) | **GET** /api/v1.0/implementation/at/{terminalId}/dep | Exports austrian dep from cloud archive. |
| [**apiV10ImplementationAtTerminalIdFonGet**](ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdFonGet) | **GET** /api/v1.0/implementation/at/{terminalId}/fon | Returns the pages fon connection log for the requested client. |
| [**apiV10ImplementationAtTerminalIdYearReceiptPost**](ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdYearReceiptPost) | **POST** /api/v1.0/implementation/at/{terminalId}/yearReceipt | Creates an automatic year receipt and validates this at fon. |


<a id="apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet"></a>
# **apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet**
> List&lt;StringSimpleObject&gt; apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet(organizationId)

Returns all missing annual year receipts (annual year receipt check).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerAustriaApi apiInstance = new ImplementationControllerAustriaApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization where the annual year receipts are requested.
    try {
      List<StringSimpleObject> result = apiInstance.apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet(organizationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerAustriaApi#apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization where the annual year receipts are requested. | |

### Return type

[**List&lt;StringSimpleObject&gt;**](StringSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given organization. |  -  |
| **404** | Organization not found. |  -  |

<a id="apiV10ImplementationAtSignatureDeviceEntityIdGet"></a>
# **apiV10ImplementationAtSignatureDeviceEntityIdGet**
> List&lt;SignatureDevice&gt; apiV10ImplementationAtSignatureDeviceEntityIdGet(entityId)

Returns the signature devices for the given entity.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerAustriaApi apiInstance = new ImplementationControllerAustriaApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity (organization or company) where the signature devices are requested.
    try {
      List<SignatureDevice> result = apiInstance.apiV10ImplementationAtSignatureDeviceEntityIdGet(entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerAustriaApi#apiV10ImplementationAtSignatureDeviceEntityIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| The id of the entity (organization or company) where the signature devices are requested. | |

### Return type

[**List&lt;SignatureDevice&gt;**](SignatureDevice.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationAtTerminalIdAsitCryptoContainerGet"></a>
# **apiV10ImplementationAtTerminalIdAsitCryptoContainerGet**
> String apiV10ImplementationAtTerminalIdAsitCryptoContainerGet(terminalId)

Returns the asit crypto container for asit check (as json string).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerAustriaApi apiInstance = new ImplementationControllerAustriaApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal to fetch the container.
    try {
      String result = apiInstance.apiV10ImplementationAtTerminalIdAsitCryptoContainerGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerAustriaApi#apiV10ImplementationAtTerminalIdAsitCryptoContainerGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal to fetch the container. | |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationAtTerminalIdDep131Get"></a>
# **apiV10ImplementationAtTerminalIdDep131Get**
> apiV10ImplementationAtTerminalIdDep131Get(terminalId, fromDate, tillDate)

Exports all data requested for dep131 protocol out of stored data.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerAustriaApi apiInstance = new ImplementationControllerAustriaApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The client id for which the export is requested.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The start date of the requested receipts.
    OffsetDateTime tillDate = OffsetDateTime.now(); // OffsetDateTime | The end date of the requested receipts.
    try {
      apiInstance.apiV10ImplementationAtTerminalIdDep131Get(terminalId, fromDate, tillDate);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerAustriaApi#apiV10ImplementationAtTerminalIdDep131Get");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The client id for which the export is requested. | |
| **fromDate** | **OffsetDateTime**| The start date of the requested receipts. | |
| **tillDate** | **OffsetDateTime**| The end date of the requested receipts. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **404** | No data found for export. |  -  |
| **422** | Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. |  -  |

<a id="apiV10ImplementationAtTerminalIdDepGet"></a>
# **apiV10ImplementationAtTerminalIdDepGet**
> apiV10ImplementationAtTerminalIdDepGet(terminalId, fromDate, tillDate)

Exports austrian dep from cloud archive.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerAustriaApi apiInstance = new ImplementationControllerAustriaApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal where the data should be exported.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The start date of the export.
    OffsetDateTime tillDate = OffsetDateTime.now(); // OffsetDateTime | The end date of the export.
    try {
      apiInstance.apiV10ImplementationAtTerminalIdDepGet(terminalId, fromDate, tillDate);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerAustriaApi#apiV10ImplementationAtTerminalIdDepGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal where the data should be exported. | |
| **fromDate** | **OffsetDateTime**| The start date of the export. | |
| **tillDate** | **OffsetDateTime**| The end date of the export. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **404** | No data found for export. |  -  |
| **422** | Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. |  -  |

<a id="apiV10ImplementationAtTerminalIdFonGet"></a>
# **apiV10ImplementationAtTerminalIdFonGet**
> FonConnectionLogMessagePageResultModel apiV10ImplementationAtTerminalIdFonGet(terminalId, pageOffset, pageSize)

Returns the pages fon connection log for the requested client.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerAustriaApi apiInstance = new ImplementationControllerAustriaApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminalid of the requested client.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit
    try {
      FonConnectionLogMessagePageResultModel result = apiInstance.apiV10ImplementationAtTerminalIdFonGet(terminalId, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerAustriaApi#apiV10ImplementationAtTerminalIdFonGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminalid of the requested client. | |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit | [optional] |

### Return type

[**FonConnectionLogMessagePageResultModel**](FonConnectionLogMessagePageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **422** | TerminalId &#x3D; Guid.Empty. |  -  |

<a id="apiV10ImplementationAtTerminalIdYearReceiptPost"></a>
# **apiV10ImplementationAtTerminalIdYearReceiptPost**
> String apiV10ImplementationAtTerminalIdYearReceiptPost(terminalId, year)

Creates an automatic year receipt and validates this at fon.

In order to use this function it is necessary to have online connection to the terminal (signalr) or second dep validation channel (see documentation) is set.  The automatic year receipt will not be created if cloud store is not in sync with client dep store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerAustriaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerAustriaApi apiInstance = new ImplementationControllerAustriaApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminalid of the terminal where the automatic year receipt should be created.
    Integer year = 56; // Integer | The year of the year receipt to create.
    try {
      String result = apiInstance.apiV10ImplementationAtTerminalIdYearReceiptPost(terminalId, year);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerAustriaApi#apiV10ImplementationAtTerminalIdYearReceiptPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminalid of the terminal where the automatic year receipt should be created. | |
| **year** | **Integer**| The year of the year receipt to create. | [optional] |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **422** | TerminalId &#x3D; Guid.Empty or year not between 2016 and current year. |  -  |

