/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.GuidEntityVersionPageResultModel;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import com.untill.retailforce.model.Store;
import com.untill.retailforce.model.StoreModel;
import com.untill.retailforce.model.StoreModelPageResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MasterDataStoresApi
 */
@Disabled
public class MasterDataStoresApiTest {

    private final MasterDataStoresApi api = new MasterDataStoresApi();

    /**
     * Returns all stores for the given organisation/company for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresGetTest() throws ApiException {
        UUID organisationId = null;
        UUID companyId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        Boolean showHiddenStores = null;
        StoreModelPageResultModel response = api.apiV10MasterdataStoresGet(organisationId, companyId, pageOffset, pageSize, searchString, showHiddenStores);
        // TODO: test validations
    }

    /**
     * Returns the store id of the requested store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresIdGetTest() throws ApiException {
        UUID organizationId = null;
        String storeNumber = null;
        UUID response = api.apiV10MasterdataStoresIdGet(organizationId, storeNumber);
        // TODO: test validations
    }

    /**
     * Creates a store in the cloud store.
     *
     * If RetailForce.Cloud.Model.Store.StoreId set to System.Guid.Empty, then the store id will be generated by the service.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresPostTest() throws ApiException {
        Store store = null;
        StoreModel response = api.apiV10MasterdataStoresPost(store);
        // TODO: test validations
    }

    /**
     * Returns all stores as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the given organisation/company for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresSimpleGetTest() throws ApiException {
        UUID organisationId = null;
        UUID companyId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        GuidSimpleObjectPageResultModel response = api.apiV10MasterdataStoresSimpleGet(organisationId, companyId, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Deletes a store from the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresStoreIdDeleteTest() throws ApiException {
        UUID storeId = null;
        api.apiV10MasterdataStoresStoreIdDelete(storeId);
        // TODO: test validations
    }

    /**
     * Returns the requested store for the authenticated users.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresStoreIdGetTest() throws ApiException {
        UUID storeId = null;
        String entityVersion = null;
        StoreModel response = api.apiV10MasterdataStoresStoreIdGet(storeId, entityVersion);
        // TODO: test validations
    }

    /**
     * Hides the store from the standard list.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresStoreIdHidePostTest() throws ApiException {
        UUID storeId = null;
        StoreModel response = api.apiV10MasterdataStoresStoreIdHidePost(storeId);
        // TODO: test validations
    }

    /**
     * Updates a store in the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresStoreIdPutTest() throws ApiException {
        UUID storeId = null;
        Store store = null;
        StoreModel response = api.apiV10MasterdataStoresStoreIdPut(storeId, store);
        // TODO: test validations
    }

    /**
     * Un-hides a previously hidden store to the standard list.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresStoreIdUnHidePostTest() throws ApiException {
        UUID storeId = null;
        StoreModel response = api.apiV10MasterdataStoresStoreIdUnHidePost(storeId);
        // TODO: test validations
    }

    /**
     * Get store versions
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataStoresStoreIdVersionsGetTest() throws ApiException {
        UUID storeId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        GuidEntityVersionPageResultModel response = api.apiV10MasterdataStoresStoreIdVersionsGet(storeId, pageOffset, pageSize);
        // TODO: test validations
    }

}
