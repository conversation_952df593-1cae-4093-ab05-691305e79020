/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.ParameterInfo;
import com.untill.retailforce.model.SignDeviceDriver;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * SignDeviceDriverInfo
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SignDeviceDriverInfo {
  public static final String SERIALIZED_NAME_SIGN_DEVICE_DRIVER = "signDeviceDriver";
  @SerializedName(SERIALIZED_NAME_SIGN_DEVICE_DRIVER)
  private SignDeviceDriver signDeviceDriver;

  public static final String SERIALIZED_NAME_PARAMETERS = "parameters";
  @SerializedName(SERIALIZED_NAME_PARAMETERS)
  private List<ParameterInfo> parameters;

  public SignDeviceDriverInfo() {
  }

  public SignDeviceDriverInfo signDeviceDriver(SignDeviceDriver signDeviceDriver) {
    
    this.signDeviceDriver = signDeviceDriver;
    return this;
  }

   /**
   * Get signDeviceDriver
   * @return signDeviceDriver
  **/
  @javax.annotation.Nullable
  public SignDeviceDriver getSignDeviceDriver() {
    return signDeviceDriver;
  }


  public void setSignDeviceDriver(SignDeviceDriver signDeviceDriver) {
    this.signDeviceDriver = signDeviceDriver;
  }


  public SignDeviceDriverInfo parameters(List<ParameterInfo> parameters) {
    
    this.parameters = parameters;
    return this;
  }

  public SignDeviceDriverInfo addParametersItem(ParameterInfo parametersItem) {
    if (this.parameters == null) {
      this.parameters = new ArrayList<>();
    }
    this.parameters.add(parametersItem);
    return this;
  }

   /**
   * Get parameters
   * @return parameters
  **/
  @javax.annotation.Nullable
  public List<ParameterInfo> getParameters() {
    return parameters;
  }


  public void setParameters(List<ParameterInfo> parameters) {
    this.parameters = parameters;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SignDeviceDriverInfo signDeviceDriverInfo = (SignDeviceDriverInfo) o;
    return Objects.equals(this.signDeviceDriver, signDeviceDriverInfo.signDeviceDriver) &&
        Objects.equals(this.parameters, signDeviceDriverInfo.parameters);
  }

  @Override
  public int hashCode() {
    return Objects.hash(signDeviceDriver, parameters);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SignDeviceDriverInfo {\n");
    sb.append("    signDeviceDriver: ").append(toIndentedString(signDeviceDriver)).append("\n");
    sb.append("    parameters: ").append(toIndentedString(parameters)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("signDeviceDriver");
    openapiFields.add("parameters");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to SignDeviceDriverInfo
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!SignDeviceDriverInfo.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in SignDeviceDriverInfo is not found in the empty JSON string", SignDeviceDriverInfo.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!SignDeviceDriverInfo.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `SignDeviceDriverInfo` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if (jsonObj.get("parameters") != null && !jsonObj.get("parameters").isJsonNull()) {
        JsonArray jsonArrayparameters = jsonObj.getAsJsonArray("parameters");
        if (jsonArrayparameters != null) {
          // ensure the json data is an array
          if (!jsonObj.get("parameters").isJsonArray()) {
            throw new IllegalArgumentException(String.format("Expected the field `parameters` to be an array in the JSON string but got `%s`", jsonObj.get("parameters").toString()));
          }

          // validate the optional field `parameters` (array)
          for (int i = 0; i < jsonArrayparameters.size(); i++) {
            ParameterInfo.validateJsonObject(jsonArrayparameters.get(i).getAsJsonObject());
          };
        }
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!SignDeviceDriverInfo.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'SignDeviceDriverInfo' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<SignDeviceDriverInfo> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(SignDeviceDriverInfo.class));

       return (TypeAdapter<T>) new TypeAdapter<SignDeviceDriverInfo>() {
           @Override
           public void write(JsonWriter out, SignDeviceDriverInfo value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public SignDeviceDriverInfo read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of SignDeviceDriverInfo given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of SignDeviceDriverInfo
  * @throws IOException if the JSON string is invalid with respect to SignDeviceDriverInfo
  */
  public static SignDeviceDriverInfo fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, SignDeviceDriverInfo.class);
  }

 /**
  * Convert an instance of SignDeviceDriverInfo to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

