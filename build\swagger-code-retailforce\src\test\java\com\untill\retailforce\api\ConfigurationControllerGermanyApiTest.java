/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.ClientConfigurationGermany;
import com.untill.retailforce.model.TseDriverInfo;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ConfigurationControllerGermanyApi
 */
@Disabled
public class ConfigurationControllerGermanyApiTest {

    private final ConfigurationControllerGermanyApi api = new ConfigurationControllerGermanyApi();

    /**
     * Stores the necessary fields of the client configuration to the insights tables for germany.
     *
     * At the moment only primary tse configuration is stored to the system.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationDeClientConfigurationTerminalIdPostTest() throws ApiException {
        UUID terminalId = null;
        ClientConfigurationGermany clientConfigurationGermany = null;
        Boolean response = api.apiV10ConfigurationDeClientConfigurationTerminalIdPost(terminalId, clientConfigurationGermany);
        // TODO: test validations
    }

    /**
     * Stores the actual tse version information to the terminal insights table.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostTest() throws ApiException {
        UUID terminalId = null;
        String tseVersionInformation = null;
        api.apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost(terminalId, tseVersionInformation);
        // TODO: test validations
    }

    /**
     * Returns supported tse drivers for configuration in the cloud user interface.
     *
     * This method does not return parameters marked with RetailForce.Fiscalisation.Configuration.ParameterInfo.PortalHidden &#x3D; true.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationDeTseDriverInfoGetTest() throws ApiException {
        List<TseDriverInfo> response = api.apiV10ConfigurationDeTseDriverInfoGet();
        // TODO: test validations
    }

}
