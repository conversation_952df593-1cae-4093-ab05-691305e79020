/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Return data for receipts controller to return receipt data to the client.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class ReceiptData {
  public static final String SERIALIZED_NAME_DOCUMENT_URL = "documentUrl";
  @SerializedName(SERIALIZED_NAME_DOCUMENT_URL)
  private String documentUrl;

  public static final String SERIALIZED_NAME_PROCESS_ID = "processId";
  @SerializedName(SERIALIZED_NAME_PROCESS_ID)
  private String processId;

  public ReceiptData() {
  }

  public ReceiptData documentUrl(String documentUrl) {
    
    this.documentUrl = documentUrl;
    return this;
  }

   /**
   * The url for the download of the receipt.
   * @return documentUrl
  **/
  @javax.annotation.Nullable
  public String getDocumentUrl() {
    return documentUrl;
  }


  public void setDocumentUrl(String documentUrl) {
    this.documentUrl = documentUrl;
  }


  public ReceiptData processId(String processId) {
    
    this.processId = processId;
    return this;
  }

   /**
   * The processId to connect more than one receipt together.
   * @return processId
  **/
  @javax.annotation.Nullable
  public String getProcessId() {
    return processId;
  }


  public void setProcessId(String processId) {
    this.processId = processId;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReceiptData receiptData = (ReceiptData) o;
    return Objects.equals(this.documentUrl, receiptData.documentUrl) &&
        Objects.equals(this.processId, receiptData.processId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(documentUrl, processId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReceiptData {\n");
    sb.append("    documentUrl: ").append(toIndentedString(documentUrl)).append("\n");
    sb.append("    processId: ").append(toIndentedString(processId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("documentUrl");
    openapiFields.add("processId");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to ReceiptData
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!ReceiptData.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in ReceiptData is not found in the empty JSON string", ReceiptData.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!ReceiptData.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `ReceiptData` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("documentUrl") != null && !jsonObj.get("documentUrl").isJsonNull()) && !jsonObj.get("documentUrl").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `documentUrl` to be a primitive type in the JSON string but got `%s`", jsonObj.get("documentUrl").toString()));
      }
      if ((jsonObj.get("processId") != null && !jsonObj.get("processId").isJsonNull()) && !jsonObj.get("processId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `processId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("processId").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!ReceiptData.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'ReceiptData' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<ReceiptData> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(ReceiptData.class));

       return (TypeAdapter<T>) new TypeAdapter<ReceiptData>() {
           @Override
           public void write(JsonWriter out, ReceiptData value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public ReceiptData read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of ReceiptData given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of ReceiptData
  * @throws IOException if the JSON string is invalid with respect to ReceiptData
  */
  public static ReceiptData fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, ReceiptData.class);
  }

 /**
  * Convert an instance of ReceiptData to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

