/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.AccessLicenseConfigurationInfo;
import com.untill.retailforce.model.Certificate;
import com.untill.retailforce.model.CertificateFormat;
import com.untill.retailforce.model.CertificateModel;
import java.io.File;
import com.untill.retailforce.model.FiscalClient;
import com.untill.retailforce.model.FiscalClientConfiguration;
import com.untill.retailforce.model.FiscalClientConfigurationModel;
import com.untill.retailforce.model.GuidExtendedSimpleCountryObject;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.IdentificationType;
import java.util.UUID;
import com.untill.retailforce.model.ValidationError;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ConfigurationApi
 */
@Disabled
public class ConfigurationApiTest {

    private final ConfigurationApi api = new ConfigurationApi();

    /**
     * Returns supported access license configuration parameters in the cloud user interface.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAccessLicenseConfigurationInfoGetTest() throws ApiException {
        List<AccessLicenseConfigurationInfo> response = api.apiV10ConfigurationAccessLicenseConfigurationInfoGet();
        // TODO: test validations
    }

    /**
     * Stores external certificate (with prior created certificate signing request (csr)).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        UUID csrRequestId = null;
        String certificateName = null;
        File certFile = null;
        File additionalCertFile = null;
        File rootCertFile = null;
        CertificateModel response = api.apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(entityType, entityId, csrRequestId, certificateName, certFile, additionalCertFile, rootCertFile);
        // TODO: test validations
    }

    /**
     * Creates a certificate request file (csr) for the given distributor.
     *
     * The certificate request depends on the organization structure if organization or company data is returned.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        String subjectInfo = null;
        api.apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet(entityType, entityId, subjectInfo);
        // TODO: test validations
    }

    /**
     * Get open csrs
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        List<GuidSimpleObject> response = api.apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet(entityType, entityId);
        // TODO: test validations
    }

    /**
     * Removes the given external certificate from the store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCertificateEntityTypeEntityIdDeleteTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        UUID certificateEntityId = null;
        api.apiV10ConfigurationCertificateEntityTypeEntityIdDelete(entityType, entityId, certificateEntityId);
        // TODO: test validations
    }

    /**
     * Returns the external certificate for download.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCertificateEntityTypeEntityIdFilePostTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        String certificateName = null;
        String certificatePassword = null;
        api.apiV10ConfigurationCertificateEntityTypeEntityIdFilePost(entityType, entityId, certificateName, certificatePassword);
        // TODO: test validations
    }

    /**
     * Returns all external certificates for the given entity.
     *
     * Value of private key encrypted will not be returned from this method.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCertificateEntityTypeEntityIdGetTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        List<CertificateModel> response = api.apiV10ConfigurationCertificateEntityTypeEntityIdGet(entityType, entityId);
        // TODO: test validations
    }

    /**
     * Stores the given certificate to the certificate store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCertificateEntityTypeEntityIdPostTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        CertificateFormat certificateFormat = null;
        File _file = null;
        String certificatePassword = null;
        String certificateName = null;
        CertificateModel response = api.apiV10ConfigurationCertificateEntityTypeEntityIdPost(entityType, entityId, certificateFormat, _file, certificatePassword, certificateName);
        // TODO: test validations
    }

    /**
     * Returns the certificate structure for an external certificate for the client.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCertificateTerminalIdGetTest() throws ApiException {
        UUID terminalId = null;
        String certificateName = null;
        Certificate response = api.apiV10ConfigurationCertificateTerminalIdGet(terminalId, certificateName);
        // TODO: test validations
    }

    /**
     * Deletes a configuration in the cloud.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationConfigurationIdDeleteTest() throws ApiException {
        UUID configurationId = null;
        api.apiV10ConfigurationConfigurationIdDelete(configurationId);
        // TODO: test validations
    }

    /**
     * If special configuration for digital receipt mail sending is configured, you can test this here.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsTest() throws ApiException {
        UUID configurationId = null;
        String testEmail = null;
        api.apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions(configurationId, testEmail);
        // TODO: test validations
    }

    /**
     * Returns a configuration requested by id for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationConfigurationIdGetTest() throws ApiException {
        UUID configurationId = null;
        FiscalClientConfigurationModel response = api.apiV10ConfigurationConfigurationIdGet(configurationId);
        // TODO: test validations
    }

    /**
     * Updates a configuration in the retail cloud service.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationConfigurationIdPutTest() throws ApiException {
        UUID configurationId = null;
        FiscalClientConfiguration fiscalClientConfiguration = null;
        FiscalClientConfigurationModel response = api.apiV10ConfigurationConfigurationIdPut(configurationId, fiscalClientConfiguration);
        // TODO: test validations
    }

    /**
     * Function for TrustedFiscalModule to download fiscal client information (CreateClientByCloud).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationFiscalClientGetTest() throws ApiException {
        UUID organisationId = null;
        String storeNumber = null;
        String terminalNumber = null;
        Boolean restore = null;
        Boolean cloudFiscalisation = null;
        FiscalClient response = api.apiV10ConfigurationFiscalClientGet(organisationId, storeNumber, terminalNumber, restore, cloudFiscalisation);
        // TODO: test validations
    }

    /**
     * Function for TrustedFiscalModule to commit fiscal client configuration download.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationFiscalClientPatchTest() throws ApiException {
        UUID organisationId = null;
        String storeNumber = null;
        String terminalNumber = null;
        api.apiV10ConfigurationFiscalClientPatch(organisationId, storeNumber, terminalNumber);
        // TODO: test validations
    }

    /**
     * Function to validate the fiscal client configured in the cloud.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationFiscalClientValidateGetTest() throws ApiException {
        UUID organisationId = null;
        String storeNumber = null;
        String terminalNumber = null;
        List<ValidationError> response = api.apiV10ConfigurationFiscalClientValidateGet(organisationId, storeNumber, terminalNumber);
        // TODO: test validations
    }

    /**
     * Function to validate the fiscal client configured in the cloud.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationFiscalClientValidateTerminalIdGetTest() throws ApiException {
        UUID terminalId = null;
        List<ValidationError> response = api.apiV10ConfigurationFiscalClientValidateTerminalIdGet(terminalId);
        // TODO: test validations
    }

    /**
     * Creates a client configuration based on fiscal client data.
     *
     * 
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationFiscalclientPostTest() throws ApiException {
        FiscalClient fiscalClient = null;
        FiscalClient response = api.apiV10ConfigurationFiscalclientPost(fiscalClient);
        // TODO: test validations
    }

    /**
     * Returns all configurations for the given organisation for the authenticated user.
     *
     * All countries options only works on organisation level.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationGetTest() throws ApiException {
        UUID entityId = null;
        Boolean allCountries = null;
        List<GuidExtendedSimpleCountryObject> response = api.apiV10ConfigurationGet(entityId, allCountries);
        // TODO: test validations
    }

    /**
     * Returns the configuration id for the requested terminal.
     *
     * Only returns the configuration id of the configuration stored at terminal level. No inheritance used.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetTest() throws ApiException {
        UUID organisationId = null;
        String storeNumber = null;
        String terminalNumber = null;
        UUID response = api.apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet(organisationId, storeNumber, terminalNumber);
        // TODO: test validations
    }

    /**
     * Returns the guid for a organization by company companyidentification.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationOrganisationCompanyGetTest() throws ApiException {
        IdentificationType identificationType = null;
        String identification = null;
        UUID response = api.apiV10ConfigurationOrganisationCompanyGet(identificationType, identification);
        // TODO: test validations
    }

    /**
     * Returns the guid for a organization.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationOrganisationGetTest() throws ApiException {
        IdentificationType identificationType = null;
        String identification = null;
        UUID response = api.apiV10ConfigurationOrganisationGet(identificationType, identification);
        // TODO: test validations
    }

    /**
     * Create configuration in the retail cloud service.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationPostTest() throws ApiException {
        FiscalClientConfiguration fiscalClientConfiguration = null;
        FiscalClientConfigurationModel response = api.apiV10ConfigurationPost(fiscalClientConfiguration);
        // TODO: test validations
    }

    /**
     * Returns the public certificate for retailforce signature CA certificate (CER Format - base64 encoded, key length 4096 bit).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationSignatureGetTest() throws ApiException {
        String response = api.apiV10ConfigurationSignatureGet();
        // TODO: test validations
    }

    /**
     * Returns the public certificate generated for the organization (CER Format - base64 encoded, key length 2048 bit).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationSignatureOrganizationIdGetTest() throws ApiException {
        UUID organizationId = null;
        String response = api.apiV10ConfigurationSignatureOrganizationIdGet(organizationId);
        // TODO: test validations
    }

    /**
     * Validates the given signature data with the signature using the organization certificate.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationSignatureOrganizationIdPatchTest() throws ApiException {
        UUID organizationId = null;
        String signatureData = null;
        String signature = null;
        Boolean response = api.apiV10ConfigurationSignatureOrganizationIdPatch(organizationId, signatureData, signature);
        // TODO: test validations
    }

    /**
     * Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationSignatureOrganizationIdPostTest() throws ApiException {
        UUID organizationId = null;
        String body = null;
        Boolean response = api.apiV10ConfigurationSignatureOrganizationIdPost(organizationId, body);
        // TODO: test validations
    }

    /**
     * Returns the certificate for the terminal (pfx Format - base64 encoded, key length 1024 bit).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationSignatureOrganizationIdTerminalIdGetTest() throws ApiException {
        UUID organizationId = null;
        UUID terminalId = null;
        String response = api.apiV10ConfigurationSignatureOrganizationIdTerminalIdGet(organizationId, terminalId);
        // TODO: test validations
    }

    /**
     * Validates the given signature data with the signature using the organization certificate.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchTest() throws ApiException {
        UUID organizationId = null;
        UUID terminalId = null;
        String signatureData = null;
        String signature = null;
        Boolean response = api.apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch(organizationId, terminalId, signatureData, signature);
        // TODO: test validations
    }

    /**
     * Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationSignatureOrganizationIdTerminalIdPostTest() throws ApiException {
        UUID organizationId = null;
        UUID terminalId = null;
        String body = null;
        Boolean response = api.apiV10ConfigurationSignatureOrganizationIdTerminalIdPost(organizationId, terminalId, body);
        // TODO: test validations
    }

}
