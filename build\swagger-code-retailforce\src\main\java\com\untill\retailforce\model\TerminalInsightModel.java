/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.StringSimpleObject;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Some information about the terminal for the ui.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TerminalInsightModel {
  public static final String SERIALIZED_NAME_TERMINAL_ID = "terminalId";
  @SerializedName(SERIALIZED_NAME_TERMINAL_ID)
  private UUID terminalId;

  public static final String SERIALIZED_NAME_IS_TEST = "isTest";
  @SerializedName(SERIALIZED_NAME_IS_TEST)
  private Boolean isTest;

  public static final String SERIALIZED_NAME_FISCAL_CLIENT_VERSION = "fiscalClientVersion";
  @SerializedName(SERIALIZED_NAME_FISCAL_CLIENT_VERSION)
  private String fiscalClientVersion;

  public static final String SERIALIZED_NAME_VERSION_UPDATE = "versionUpdate";
  @SerializedName(SERIALIZED_NAME_VERSION_UPDATE)
  private OffsetDateTime versionUpdate;

  public static final String SERIALIZED_NAME_CONFIGURATION_UPDATE = "configurationUpdate";
  @SerializedName(SERIALIZED_NAME_CONFIGURATION_UPDATE)
  private OffsetDateTime configurationUpdate;

  public static final String SERIALIZED_NAME_ARCHIVE_DATE = "archiveDate";
  @SerializedName(SERIALIZED_NAME_ARCHIVE_DATE)
  private OffsetDateTime archiveDate;

  public static final String SERIALIZED_NAME_COUNTRY_VALUES = "countryValues";
  @SerializedName(SERIALIZED_NAME_COUNTRY_VALUES)
  private List<StringSimpleObject> countryValues;

  public TerminalInsightModel() {
  }

  public TerminalInsightModel terminalId(UUID terminalId) {
    
    this.terminalId = terminalId;
    return this;
  }

   /**
   * The guid of the terminal.
   * @return terminalId
  **/
  @javax.annotation.Nullable
  public UUID getTerminalId() {
    return terminalId;
  }


  public void setTerminalId(UUID terminalId) {
    this.terminalId = terminalId;
  }


  public TerminalInsightModel isTest(Boolean isTest) {
    
    this.isTest = isTest;
    return this;
  }

   /**
   * 
   * @return isTest
  **/
  @javax.annotation.Nullable
  public Boolean getIsTest() {
    return isTest;
  }


  public void setIsTest(Boolean isTest) {
    this.isTest = isTest;
  }


  public TerminalInsightModel fiscalClientVersion(String fiscalClientVersion) {
    
    this.fiscalClientVersion = fiscalClientVersion;
    return this;
  }

   /**
   * This is the actual version of the client.
   * @return fiscalClientVersion
  **/
  @javax.annotation.Nullable
  public String getFiscalClientVersion() {
    return fiscalClientVersion;
  }


  public void setFiscalClientVersion(String fiscalClientVersion) {
    this.fiscalClientVersion = fiscalClientVersion;
  }


  public TerminalInsightModel versionUpdate(OffsetDateTime versionUpdate) {
    
    this.versionUpdate = versionUpdate;
    return this;
  }

   /**
   * The date/time when the version was last time updated.
   * @return versionUpdate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getVersionUpdate() {
    return versionUpdate;
  }


  public void setVersionUpdate(OffsetDateTime versionUpdate) {
    this.versionUpdate = versionUpdate;
  }


  public TerminalInsightModel configurationUpdate(OffsetDateTime configurationUpdate) {
    
    this.configurationUpdate = configurationUpdate;
    return this;
  }

   /**
   * The date/time when the configuration of the client was last time updated.
   * @return configurationUpdate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getConfigurationUpdate() {
    return configurationUpdate;
  }


  public void setConfigurationUpdate(OffsetDateTime configurationUpdate) {
    this.configurationUpdate = configurationUpdate;
  }


  public TerminalInsightModel archiveDate(OffsetDateTime archiveDate) {
    
    this.archiveDate = archiveDate;
    return this;
  }

   /**
   * The date/time when the terminal was archived.
   * @return archiveDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getArchiveDate() {
    return archiveDate;
  }


  public void setArchiveDate(OffsetDateTime archiveDate) {
    this.archiveDate = archiveDate;
  }


  public TerminalInsightModel countryValues(List<StringSimpleObject> countryValues) {
    
    this.countryValues = countryValues;
    return this;
  }

  public TerminalInsightModel addCountryValuesItem(StringSimpleObject countryValuesItem) {
    if (this.countryValues == null) {
      this.countryValues = new ArrayList<>();
    }
    this.countryValues.add(countryValuesItem);
    return this;
  }

   /**
   * Country values in a dictionary to show specific country values in terminal insights app widget.
   * @return countryValues
  **/
  @javax.annotation.Nullable
  public List<StringSimpleObject> getCountryValues() {
    return countryValues;
  }


  public void setCountryValues(List<StringSimpleObject> countryValues) {
    this.countryValues = countryValues;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TerminalInsightModel terminalInsightModel = (TerminalInsightModel) o;
    return Objects.equals(this.terminalId, terminalInsightModel.terminalId) &&
        Objects.equals(this.isTest, terminalInsightModel.isTest) &&
        Objects.equals(this.fiscalClientVersion, terminalInsightModel.fiscalClientVersion) &&
        Objects.equals(this.versionUpdate, terminalInsightModel.versionUpdate) &&
        Objects.equals(this.configurationUpdate, terminalInsightModel.configurationUpdate) &&
        Objects.equals(this.archiveDate, terminalInsightModel.archiveDate) &&
        Objects.equals(this.countryValues, terminalInsightModel.countryValues);
  }

  @Override
  public int hashCode() {
    return Objects.hash(terminalId, isTest, fiscalClientVersion, versionUpdate, configurationUpdate, archiveDate, countryValues);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TerminalInsightModel {\n");
    sb.append("    terminalId: ").append(toIndentedString(terminalId)).append("\n");
    sb.append("    isTest: ").append(toIndentedString(isTest)).append("\n");
    sb.append("    fiscalClientVersion: ").append(toIndentedString(fiscalClientVersion)).append("\n");
    sb.append("    versionUpdate: ").append(toIndentedString(versionUpdate)).append("\n");
    sb.append("    configurationUpdate: ").append(toIndentedString(configurationUpdate)).append("\n");
    sb.append("    archiveDate: ").append(toIndentedString(archiveDate)).append("\n");
    sb.append("    countryValues: ").append(toIndentedString(countryValues)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("terminalId");
    openapiFields.add("isTest");
    openapiFields.add("fiscalClientVersion");
    openapiFields.add("versionUpdate");
    openapiFields.add("configurationUpdate");
    openapiFields.add("archiveDate");
    openapiFields.add("countryValues");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TerminalInsightModel
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TerminalInsightModel.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TerminalInsightModel is not found in the empty JSON string", TerminalInsightModel.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TerminalInsightModel.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TerminalInsightModel` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("terminalId") != null && !jsonObj.get("terminalId").isJsonNull()) && !jsonObj.get("terminalId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `terminalId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("terminalId").toString()));
      }
      if ((jsonObj.get("fiscalClientVersion") != null && !jsonObj.get("fiscalClientVersion").isJsonNull()) && !jsonObj.get("fiscalClientVersion").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `fiscalClientVersion` to be a primitive type in the JSON string but got `%s`", jsonObj.get("fiscalClientVersion").toString()));
      }
      if (jsonObj.get("countryValues") != null && !jsonObj.get("countryValues").isJsonNull()) {
        JsonArray jsonArraycountryValues = jsonObj.getAsJsonArray("countryValues");
        if (jsonArraycountryValues != null) {
          // ensure the json data is an array
          if (!jsonObj.get("countryValues").isJsonArray()) {
            throw new IllegalArgumentException(String.format("Expected the field `countryValues` to be an array in the JSON string but got `%s`", jsonObj.get("countryValues").toString()));
          }

          // validate the optional field `countryValues` (array)
          for (int i = 0; i < jsonArraycountryValues.size(); i++) {
            StringSimpleObject.validateJsonObject(jsonArraycountryValues.get(i).getAsJsonObject());
          };
        }
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TerminalInsightModel.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TerminalInsightModel' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TerminalInsightModel> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TerminalInsightModel.class));

       return (TypeAdapter<T>) new TypeAdapter<TerminalInsightModel>() {
           @Override
           public void write(JsonWriter out, TerminalInsightModel value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TerminalInsightModel read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TerminalInsightModel given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TerminalInsightModel
  * @throws IOException if the JSON string is invalid with respect to TerminalInsightModel
  */
  public static TerminalInsightModel fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TerminalInsightModel.class);
  }

 /**
  * Convert an instance of TerminalInsightModel to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

