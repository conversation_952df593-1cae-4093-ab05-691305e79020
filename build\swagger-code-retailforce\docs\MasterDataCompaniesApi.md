# MasterDataCompaniesApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MasterdataCompaniesCompanyIdDelete**](MasterDataCompaniesApi.md#apiV10MasterdataCompaniesCompanyIdDelete) | **DELETE** /api/v1.0/masterdata/companies/{companyId} | Deletes a company from the cloud store. |
| [**apiV10MasterdataCompaniesCompanyIdGet**](MasterDataCompaniesApi.md#apiV10MasterdataCompaniesCompanyIdGet) | **GET** /api/v1.0/masterdata/companies/{companyId} | Returns the requested company by id. |
| [**apiV10MasterdataCompaniesCompanyIdPut**](MasterDataCompaniesApi.md#apiV10MasterdataCompaniesCompanyIdPut) | **PUT** /api/v1.0/masterdata/companies/{companyId} | Updates a company in the cloud store. |
| [**apiV10MasterdataCompaniesCompanyIdVersionsGet**](MasterDataCompaniesApi.md#apiV10MasterdataCompaniesCompanyIdVersionsGet) | **GET** /api/v1.0/masterdata/companies/{companyId}/versions | Get company versions |
| [**apiV10MasterdataCompaniesGet**](MasterDataCompaniesApi.md#apiV10MasterdataCompaniesGet) | **GET** /api/v1.0/masterdata/companies | Returns all companies for the requested organisation for the authenticated user. |
| [**apiV10MasterdataCompaniesIdGet**](MasterDataCompaniesApi.md#apiV10MasterdataCompaniesIdGet) | **GET** /api/v1.0/masterdata/companies/id | Returns the company id (if applicable) of the requested store. |
| [**apiV10MasterdataCompaniesPost**](MasterDataCompaniesApi.md#apiV10MasterdataCompaniesPost) | **POST** /api/v1.0/masterdata/companies | Creates a new company in the cloud store. |
| [**apiV10MasterdataCompaniesSimpleGet**](MasterDataCompaniesApi.md#apiV10MasterdataCompaniesSimpleGet) | **GET** /api/v1.0/masterdata/companies/simple | Returns all companies as a RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested organisation for the authenticated user. |


<a id="apiV10MasterdataCompaniesCompanyIdDelete"></a>
# **apiV10MasterdataCompaniesCompanyIdDelete**
> apiV10MasterdataCompaniesCompanyIdDelete(companyId)

Deletes a company from the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataCompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataCompaniesApi apiInstance = new MasterDataCompaniesApi(defaultClient);
    UUID companyId = UUID.randomUUID(); // UUID | The id of the company to delete.
    try {
      apiInstance.apiV10MasterdataCompaniesCompanyIdDelete(companyId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataCompaniesApi#apiV10MasterdataCompaniesCompanyIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **companyId** | **UUID**| The id of the company to delete. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataCompaniesCompanyIdGet"></a>
# **apiV10MasterdataCompaniesCompanyIdGet**
> CompanyModel apiV10MasterdataCompaniesCompanyIdGet(companyId, entityVersion)

Returns the requested company by id.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataCompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataCompaniesApi apiInstance = new MasterDataCompaniesApi(defaultClient);
    UUID companyId = UUID.randomUUID(); // UUID | The if of the requested company.
    String entityVersion = "entityVersion_example"; // String | load specific version
    try {
      CompanyModel result = apiInstance.apiV10MasterdataCompaniesCompanyIdGet(companyId, entityVersion);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataCompaniesApi#apiV10MasterdataCompaniesCompanyIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **companyId** | **UUID**| The if of the requested company. | |
| **entityVersion** | **String**| load specific version | [optional] |

### Return type

[**CompanyModel**](CompanyModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataCompaniesCompanyIdPut"></a>
# **apiV10MasterdataCompaniesCompanyIdPut**
> CompanyModel apiV10MasterdataCompaniesCompanyIdPut(companyId, company)

Updates a company in the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataCompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataCompaniesApi apiInstance = new MasterDataCompaniesApi(defaultClient);
    UUID companyId = UUID.randomUUID(); // UUID | The id of the company to update.
    Company company = new Company(); // Company | The company data.
    try {
      CompanyModel result = apiInstance.apiV10MasterdataCompaniesCompanyIdPut(companyId, company);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataCompaniesApi#apiV10MasterdataCompaniesCompanyIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **companyId** | **UUID**| The id of the company to update. | |
| **company** | [**Company**](Company.md)| The company data. | [optional] |

### Return type

[**CompanyModel**](CompanyModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataCompaniesCompanyIdVersionsGet"></a>
# **apiV10MasterdataCompaniesCompanyIdVersionsGet**
> GuidEntityVersionPageResultModel apiV10MasterdataCompaniesCompanyIdVersionsGet(companyId, pageOffset, pageSize)

Get company versions

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataCompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataCompaniesApi apiInstance = new MasterDataCompaniesApi(defaultClient);
    UUID companyId = UUID.randomUUID(); // UUID | 
    Integer pageOffset = 56; // Integer | 
    Integer pageSize = 56; // Integer | 
    try {
      GuidEntityVersionPageResultModel result = apiInstance.apiV10MasterdataCompaniesCompanyIdVersionsGet(companyId, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataCompaniesApi#apiV10MasterdataCompaniesCompanyIdVersionsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **companyId** | **UUID**|  | |
| **pageOffset** | **Integer**|  | [optional] |
| **pageSize** | **Integer**|  | [optional] |

### Return type

[**GuidEntityVersionPageResultModel**](GuidEntityVersionPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataCompaniesGet"></a>
# **apiV10MasterdataCompaniesGet**
> CompanyModelPageResultModel apiV10MasterdataCompaniesGet(organisationId, pageOffset, pageSize, searchString)

Returns all companies for the requested organisation for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataCompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataCompaniesApi apiInstance = new MasterDataCompaniesApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the organisation for which the companies are requested.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      CompanyModelPageResultModel result = apiInstance.apiV10MasterdataCompaniesGet(organisationId, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataCompaniesApi#apiV10MasterdataCompaniesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the organisation for which the companies are requested. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**CompanyModelPageResultModel**](CompanyModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataCompaniesIdGet"></a>
# **apiV10MasterdataCompaniesIdGet**
> UUID apiV10MasterdataCompaniesIdGet(organizationId, storeNumber)

Returns the company id (if applicable) of the requested store.

Not every store belongs to a company, therefore it possible that no companyid is returned.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataCompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataCompaniesApi apiInstance = new MasterDataCompaniesApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization of the requested store.
    String storeNumber = "storeNumber_example"; // String | The store number of the requested store.
    try {
      UUID result = apiInstance.apiV10MasterdataCompaniesIdGet(organizationId, storeNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataCompaniesApi#apiV10MasterdataCompaniesIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of the organization of the requested store. | [optional] |
| **storeNumber** | **String**| The store number of the requested store. | [optional] |

### Return type

[**UUID**](UUID.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataCompaniesPost"></a>
# **apiV10MasterdataCompaniesPost**
> CompanyModel apiV10MasterdataCompaniesPost(company)

Creates a new company in the cloud store.

If RetailForce.Cloud.Model.Company.CompanyId set to System.Guid.Empty, then the company id will be generated by the service.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataCompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataCompaniesApi apiInstance = new MasterDataCompaniesApi(defaultClient);
    Company company = new Company(); // Company | The new company to create.
    try {
      CompanyModel result = apiInstance.apiV10MasterdataCompaniesPost(company);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataCompaniesApi#apiV10MasterdataCompaniesPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **company** | [**Company**](Company.md)| The new company to create. | [optional] |

### Return type

[**CompanyModel**](CompanyModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataCompaniesSimpleGet"></a>
# **apiV10MasterdataCompaniesSimpleGet**
> GuidSimpleObjectPageResultModel apiV10MasterdataCompaniesSimpleGet(organisationId, pageOffset, pageSize, searchString)

Returns all companies as a RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested organisation for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataCompaniesApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataCompaniesApi apiInstance = new MasterDataCompaniesApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the organisation for which the companies are requested.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      GuidSimpleObjectPageResultModel result = apiInstance.apiV10MasterdataCompaniesSimpleGet(organisationId, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataCompaniesApi#apiV10MasterdataCompaniesSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the organisation for which the companies are requested. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**GuidSimpleObjectPageResultModel**](GuidSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

