/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.GuidEntityVersionPageResultModel;
import com.untill.retailforce.model.Int32SimpleObject;
import com.untill.retailforce.model.Organisation;
import com.untill.retailforce.model.OrganisationModel;
import com.untill.retailforce.model.OrganisationModelPageResultModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class MasterDataOrganisationsApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public MasterDataOrganisationsApi() {
        this(Configuration.getDefaultApiClient());
    }

    public MasterDataOrganisationsApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10MasterdataOrganisationsGet
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param organisationId Optional. For organisation view in portal to filter only actual active organisation. (optional)
     * @param distributorId Optional. For optional distributor filter. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsGetCall(Integer pageOffset, Integer pageSize, String searchString, UUID organisationId, UUID distributorId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/organisations";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataOrganisationsGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, UUID organisationId, UUID distributorId, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataOrganisationsGetCall(pageOffset, pageSize, searchString, organisationId, distributorId, _callback);

    }

    /**
     * Returns all organisations for the authenticated user.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param organisationId Optional. For organisation view in portal to filter only actual active organisation. (optional)
     * @param distributorId Optional. For optional distributor filter. (optional)
     * @return OrganisationModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public OrganisationModelPageResultModel apiV10MasterdataOrganisationsGet(Integer pageOffset, Integer pageSize, String searchString, UUID organisationId, UUID distributorId) throws ApiException {
        ApiResponse<OrganisationModelPageResultModel> localVarResp = apiV10MasterdataOrganisationsGetWithHttpInfo(pageOffset, pageSize, searchString, organisationId, distributorId);
        return localVarResp.getData();
    }

    /**
     * Returns all organisations for the authenticated user.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param organisationId Optional. For organisation view in portal to filter only actual active organisation. (optional)
     * @param distributorId Optional. For optional distributor filter. (optional)
     * @return ApiResponse&lt;OrganisationModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<OrganisationModelPageResultModel> apiV10MasterdataOrganisationsGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString, UUID organisationId, UUID distributorId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsGetValidateBeforeCall(pageOffset, pageSize, searchString, organisationId, distributorId, null);
        Type localVarReturnType = new TypeToken<OrganisationModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all organisations for the authenticated user. (asynchronously)
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param organisationId Optional. For organisation view in portal to filter only actual active organisation. (optional)
     * @param distributorId Optional. For optional distributor filter. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsGetAsync(Integer pageOffset, Integer pageSize, String searchString, UUID organisationId, UUID distributorId, final ApiCallback<OrganisationModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsGetValidateBeforeCall(pageOffset, pageSize, searchString, organisationId, distributorId, _callback);
        Type localVarReturnType = new TypeToken<OrganisationModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataOrganisationsOrganisationIdDelete
     * @param organisationId The id of the organisation which should be deleted. (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdDeleteCall(UUID organisationId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/organisations/{organisationId}"
            .replace("{" + "organisationId" + "}", localVarApiClient.escapeString(organisationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdDeleteValidateBeforeCall(UUID organisationId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organisationId' is set
        if (organisationId == null) {
            throw new ApiException("Missing the required parameter 'organisationId' when calling apiV10MasterdataOrganisationsOrganisationIdDelete(Async)");
        }

        return apiV10MasterdataOrganisationsOrganisationIdDeleteCall(organisationId, pageOffset, pageSize, _callback);

    }

    /**
     * Deletes an organisation from cloud store.
     * 
     * @param organisationId The id of the organisation which should be deleted. (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataOrganisationsOrganisationIdDelete(UUID organisationId, Integer pageOffset, Integer pageSize) throws ApiException {
        apiV10MasterdataOrganisationsOrganisationIdDeleteWithHttpInfo(organisationId, pageOffset, pageSize);
    }

    /**
     * Deletes an organisation from cloud store.
     * 
     * @param organisationId The id of the organisation which should be deleted. (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataOrganisationsOrganisationIdDeleteWithHttpInfo(UUID organisationId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganisationIdDeleteValidateBeforeCall(organisationId, pageOffset, pageSize, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes an organisation from cloud store. (asynchronously)
     * 
     * @param organisationId The id of the organisation which should be deleted. (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdDeleteAsync(UUID organisationId, Integer pageOffset, Integer pageSize, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganisationIdDeleteValidateBeforeCall(organisationId, pageOffset, pageSize, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataOrganisationsOrganisationIdGet
     * @param organisationId The id of the requested organisation. (required)
     * @param entityVersion load specific version (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdGetCall(UUID organisationId, String entityVersion, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/organisations/{organisationId}"
            .replace("{" + "organisationId" + "}", localVarApiClient.escapeString(organisationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (entityVersion != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityVersion", entityVersion));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdGetValidateBeforeCall(UUID organisationId, String entityVersion, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organisationId' is set
        if (organisationId == null) {
            throw new ApiException("Missing the required parameter 'organisationId' when calling apiV10MasterdataOrganisationsOrganisationIdGet(Async)");
        }

        return apiV10MasterdataOrganisationsOrganisationIdGetCall(organisationId, entityVersion, _callback);

    }

    /**
     * Returns the organisation requested by the given id.
     * 
     * @param organisationId The id of the requested organisation. (required)
     * @param entityVersion load specific version (optional)
     * @return OrganisationModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public OrganisationModel apiV10MasterdataOrganisationsOrganisationIdGet(UUID organisationId, String entityVersion) throws ApiException {
        ApiResponse<OrganisationModel> localVarResp = apiV10MasterdataOrganisationsOrganisationIdGetWithHttpInfo(organisationId, entityVersion);
        return localVarResp.getData();
    }

    /**
     * Returns the organisation requested by the given id.
     * 
     * @param organisationId The id of the requested organisation. (required)
     * @param entityVersion load specific version (optional)
     * @return ApiResponse&lt;OrganisationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<OrganisationModel> apiV10MasterdataOrganisationsOrganisationIdGetWithHttpInfo(UUID organisationId, String entityVersion) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganisationIdGetValidateBeforeCall(organisationId, entityVersion, null);
        Type localVarReturnType = new TypeToken<OrganisationModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the organisation requested by the given id. (asynchronously)
     * 
     * @param organisationId The id of the requested organisation. (required)
     * @param entityVersion load specific version (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdGetAsync(UUID organisationId, String entityVersion, final ApiCallback<OrganisationModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganisationIdGetValidateBeforeCall(organisationId, entityVersion, _callback);
        Type localVarReturnType = new TypeToken<OrganisationModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataOrganisationsOrganisationIdPut
     * @param organisationId The id of the organisation to be updated. (required)
     * @param organisation The organisation object to update the organisation. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdPutCall(UUID organisationId, Organisation organisation, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = organisation;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/organisations/{organisationId}"
            .replace("{" + "organisationId" + "}", localVarApiClient.escapeString(organisationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdPutValidateBeforeCall(UUID organisationId, Organisation organisation, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organisationId' is set
        if (organisationId == null) {
            throw new ApiException("Missing the required parameter 'organisationId' when calling apiV10MasterdataOrganisationsOrganisationIdPut(Async)");
        }

        return apiV10MasterdataOrganisationsOrganisationIdPutCall(organisationId, organisation, _callback);

    }

    /**
     * Updates an organisation in the cloud store.
     * 
     * @param organisationId The id of the organisation to be updated. (required)
     * @param organisation The organisation object to update the organisation. (optional)
     * @return OrganisationModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public OrganisationModel apiV10MasterdataOrganisationsOrganisationIdPut(UUID organisationId, Organisation organisation) throws ApiException {
        ApiResponse<OrganisationModel> localVarResp = apiV10MasterdataOrganisationsOrganisationIdPutWithHttpInfo(organisationId, organisation);
        return localVarResp.getData();
    }

    /**
     * Updates an organisation in the cloud store.
     * 
     * @param organisationId The id of the organisation to be updated. (required)
     * @param organisation The organisation object to update the organisation. (optional)
     * @return ApiResponse&lt;OrganisationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<OrganisationModel> apiV10MasterdataOrganisationsOrganisationIdPutWithHttpInfo(UUID organisationId, Organisation organisation) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganisationIdPutValidateBeforeCall(organisationId, organisation, null);
        Type localVarReturnType = new TypeToken<OrganisationModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates an organisation in the cloud store. (asynchronously)
     * 
     * @param organisationId The id of the organisation to be updated. (required)
     * @param organisation The organisation object to update the organisation. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdPutAsync(UUID organisationId, Organisation organisation, final ApiCallback<OrganisationModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganisationIdPutValidateBeforeCall(organisationId, organisation, _callback);
        Type localVarReturnType = new TypeToken<OrganisationModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataOrganisationsOrganisationIdVersionsGet
     * @param organisationId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdVersionsGetCall(UUID organisationId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/organisations/{organisationId}/versions"
            .replace("{" + "organisationId" + "}", localVarApiClient.escapeString(organisationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdVersionsGetValidateBeforeCall(UUID organisationId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organisationId' is set
        if (organisationId == null) {
            throw new ApiException("Missing the required parameter 'organisationId' when calling apiV10MasterdataOrganisationsOrganisationIdVersionsGet(Async)");
        }

        return apiV10MasterdataOrganisationsOrganisationIdVersionsGetCall(organisationId, pageOffset, pageSize, _callback);

    }

    /**
     * Get organisation versions
     * 
     * @param organisationId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return GuidEntityVersionPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidEntityVersionPageResultModel apiV10MasterdataOrganisationsOrganisationIdVersionsGet(UUID organisationId, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<GuidEntityVersionPageResultModel> localVarResp = apiV10MasterdataOrganisationsOrganisationIdVersionsGetWithHttpInfo(organisationId, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Get organisation versions
     * 
     * @param organisationId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return ApiResponse&lt;GuidEntityVersionPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidEntityVersionPageResultModel> apiV10MasterdataOrganisationsOrganisationIdVersionsGetWithHttpInfo(UUID organisationId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganisationIdVersionsGetValidateBeforeCall(organisationId, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<GuidEntityVersionPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get organisation versions (asynchronously)
     * 
     * @param organisationId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganisationIdVersionsGetAsync(UUID organisationId, Integer pageOffset, Integer pageSize, final ApiCallback<GuidEntityVersionPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganisationIdVersionsGetValidateBeforeCall(organisationId, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<GuidEntityVersionPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet
     * @param organizationId The organization id for this request. (required)
     * @param allDocumentTypes  (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetCall(UUID organizationId, Boolean allDocumentTypes, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/organisations/{organizationId}/documentTypes"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (allDocumentTypes != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("allDocumentTypes", allDocumentTypes));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetValidateBeforeCall(UUID organizationId, Boolean allDocumentTypes, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet(Async)");
        }

        return apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetCall(organizationId, allDocumentTypes, _callback);

    }

    /**
     * Returns all custom document types for the requested organization.
     * 
     * @param organizationId The organization id for this request. (required)
     * @param allDocumentTypes  (optional, default to false)
     * @return List&lt;Int32SimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<Int32SimpleObject> apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet(UUID organizationId, Boolean allDocumentTypes) throws ApiException {
        ApiResponse<List<Int32SimpleObject>> localVarResp = apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetWithHttpInfo(organizationId, allDocumentTypes);
        return localVarResp.getData();
    }

    /**
     * Returns all custom document types for the requested organization.
     * 
     * @param organizationId The organization id for this request. (required)
     * @param allDocumentTypes  (optional, default to false)
     * @return ApiResponse&lt;List&lt;Int32SimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<Int32SimpleObject>> apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetWithHttpInfo(UUID organizationId, Boolean allDocumentTypes) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetValidateBeforeCall(organizationId, allDocumentTypes, null);
        Type localVarReturnType = new TypeToken<List<Int32SimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all custom document types for the requested organization. (asynchronously)
     * 
     * @param organizationId The organization id for this request. (required)
     * @param allDocumentTypes  (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetAsync(UUID organizationId, Boolean allDocumentTypes, final ApiCallback<List<Int32SimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGetValidateBeforeCall(organizationId, allDocumentTypes, _callback);
        Type localVarReturnType = new TypeToken<List<Int32SimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut
     * @param organizationId The organization id for this request. (required)
     * @param int32SimpleObject A list of custom document to store to the backend. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutCall(UUID organizationId, List<Int32SimpleObject> int32SimpleObject, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = int32SimpleObject;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/organisations/{organizationId}/documentTypes"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutValidateBeforeCall(UUID organizationId, List<Int32SimpleObject> int32SimpleObject, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut(Async)");
        }

        return apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutCall(organizationId, int32SimpleObject, _callback);

    }

    /**
     * Updates the list of custom document types for the given organization.
     * Important: You have to send the whole list of custom document types; otherwise existing custom document types will be deleted.
     * @param organizationId The organization id for this request. (required)
     * @param int32SimpleObject A list of custom document to store to the backend. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut(UUID organizationId, List<Int32SimpleObject> int32SimpleObject) throws ApiException {
        apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutWithHttpInfo(organizationId, int32SimpleObject);
    }

    /**
     * Updates the list of custom document types for the given organization.
     * Important: You have to send the whole list of custom document types; otherwise existing custom document types will be deleted.
     * @param organizationId The organization id for this request. (required)
     * @param int32SimpleObject A list of custom document to store to the backend. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutWithHttpInfo(UUID organizationId, List<Int32SimpleObject> int32SimpleObject) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutValidateBeforeCall(organizationId, int32SimpleObject, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Updates the list of custom document types for the given organization. (asynchronously)
     * Important: You have to send the whole list of custom document types; otherwise existing custom document types will be deleted.
     * @param organizationId The organization id for this request. (required)
     * @param int32SimpleObject A list of custom document to store to the backend. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutAsync(UUID organizationId, List<Int32SimpleObject> int32SimpleObject, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPutValidateBeforeCall(organizationId, int32SimpleObject, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataOrganisationsPost
     * @param organisation The organization to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsPostCall(Organisation organisation, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = organisation;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/organisations";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataOrganisationsPostValidateBeforeCall(Organisation organisation, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataOrganisationsPostCall(organisation, _callback);

    }

    /**
     * Creates a new organization in the cloud store.
     * If RetailForce.Cloud.Model.Organisation.OrganisationId set to System.Guid.Empty, then the organization id will be generated by the service.
     * @param organisation The organization to create. (optional)
     * @return OrganisationModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public OrganisationModel apiV10MasterdataOrganisationsPost(Organisation organisation) throws ApiException {
        ApiResponse<OrganisationModel> localVarResp = apiV10MasterdataOrganisationsPostWithHttpInfo(organisation);
        return localVarResp.getData();
    }

    /**
     * Creates a new organization in the cloud store.
     * If RetailForce.Cloud.Model.Organisation.OrganisationId set to System.Guid.Empty, then the organization id will be generated by the service.
     * @param organisation The organization to create. (optional)
     * @return ApiResponse&lt;OrganisationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<OrganisationModel> apiV10MasterdataOrganisationsPostWithHttpInfo(Organisation organisation) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsPostValidateBeforeCall(organisation, null);
        Type localVarReturnType = new TypeToken<OrganisationModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a new organization in the cloud store. (asynchronously)
     * If RetailForce.Cloud.Model.Organisation.OrganisationId set to System.Guid.Empty, then the organization id will be generated by the service.
     * @param organisation The organization to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataOrganisationsPostAsync(Organisation organisation, final ApiCallback<OrganisationModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataOrganisationsPostValidateBeforeCall(organisation, _callback);
        Type localVarReturnType = new TypeToken<OrganisationModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
