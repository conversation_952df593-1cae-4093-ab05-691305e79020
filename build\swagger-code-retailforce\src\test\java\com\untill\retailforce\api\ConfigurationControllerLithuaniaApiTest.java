/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.Certificate;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ConfigurationControllerLithuaniaApi
 */
@Disabled
public class ConfigurationControllerLithuaniaApiTest {

    private final ConfigurationControllerLithuaniaApi api = new ConfigurationControllerLithuaniaApi();

    /**
     * 
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationLtCertificatesDistributorIdPostTest() throws ApiException {
        UUID distributorId = null;
        api.apiV10ConfigurationLtCertificatesDistributorIdPost(distributorId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationLtCertificatesTerminalIdSecurityGetTest() throws ApiException {
        UUID terminalId = null;
        Certificate response = api.apiV10ConfigurationLtCertificatesTerminalIdSecurityGet(terminalId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationLtCertificatesTerminalIdTransportGetTest() throws ApiException {
        UUID terminalId = null;
        Certificate response = api.apiV10ConfigurationLtCertificatesTerminalIdTransportGet(terminalId);
        // TODO: test validations
    }

}
