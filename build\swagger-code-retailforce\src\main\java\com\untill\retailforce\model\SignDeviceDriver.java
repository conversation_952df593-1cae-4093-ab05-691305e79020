/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.annotations.SerializedName;

import java.io.IOException;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

/**
 * Gets or Sets SignDeviceDriver
 */
@JsonAdapter(SignDeviceDriver.Adapter.class)
public enum SignDeviceDriver {
  
  _0_ATRUSTHSMLOCAL("[0] = ATrustHsmLocal"),
  
  _1_ATRUSTHSM("[1] = ATrustHsm"),
  
  _2_PRIMESIGNHSM("[2] = PrimeSignHsm");

  private String value;

  SignDeviceDriver(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  public static SignDeviceDriver fromValue(String value) {
    for (SignDeviceDriver b : SignDeviceDriver.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }

  public static class Adapter extends TypeAdapter<SignDeviceDriver> {
    @Override
    public void write(final JsonWriter jsonWriter, final SignDeviceDriver enumeration) throws IOException {
      jsonWriter.value(enumeration.getValue());
    }

    @Override
    public SignDeviceDriver read(final JsonReader jsonReader) throws IOException {
      String value = jsonReader.nextString();
      return SignDeviceDriver.fromValue(value);
    }
  }
}

