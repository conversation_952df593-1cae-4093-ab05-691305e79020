# ImplementationControllerGermanyApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ImplementationDeDistributorIdTseInformation2Post**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeDistributorIdTseInformation2Post) | **POST** /api/v1.0/implementation/de/{distributorId}/tse/information2 | Store the given tse information |
| [**apiV10ImplementationDeDistributorIdTseInformationPost**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeDistributorIdTseInformationPost) | **POST** /api/v1.0/implementation/de/{distributorId}/tse/information | Store the given tse information |
| [**apiV10ImplementationDeTerminalIdTarGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTerminalIdTarGet) | **GET** /api/v1.0/implementation/de/{terminalId}/tar | Exports germany tar data (tse) from cloud archive. |
| [**apiV10ImplementationDeTerminalIdTaxonomyGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTerminalIdTaxonomyGet) | **GET** /api/v1.0/implementation/de/{terminalId}/taxonomy | Exports germany taxonomy data from cloud archive. |
| [**apiV10ImplementationDeTerminalIdTseProvisioningPatch**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTerminalIdTseProvisioningPatch) | **PATCH** /api/v1.0/implementation/de/{terminalId}/tse/provisioning | Starts tse provisioning for the given terminal. |
| [**apiV10ImplementationDeTseAnnouncementsGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsGet) | **GET** /api/v1.0/implementation/de/tse/announcements | Get Tse Announcements |
| [**apiV10ImplementationDeTseAnnouncementsPost**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsPost) | **POST** /api/v1.0/implementation/de/tse/announcements | Create TseAnnouncement  starts a new tse announcement progress. An email per store will be sent to the user, containing the announcemnt data and a verification link. |
| [**apiV10ImplementationDeTseAnnouncementsStatusGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsStatusGet) | **GET** /api/v1.0/implementation/de/tse/announcements/status | Get TSE Announcement status:  if there are tse changes or an open tse announcement process (changes in announcemnt structure store,terminal,tseInformation) which need to be reported |
| [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost) | **POST** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/cancel | Cancel TseAnnouncement |
| [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet) | **GET** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/data/pdf | Returns the tse announcement data as pdf |
| [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet) | **GET** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId} | Get TseAnnouncement |
| [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut) | **PUT** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/verification/email | Resend Verification Email |
| [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost) | **POST** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/verification |  |
| [**apiV10ImplementationDeTseAnnouncementsUnannouncedGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsUnannouncedGet) | **GET** /api/v1.0/implementation/de/tse/announcements/unannounced | Get all unannounced TseAnnouncements  This include all TseAnnouncements which has never been announced or include new changes, but not tseAnnouncements which are open (verificationPending or inProgress) |
| [**apiV10ImplementationDeTseFinderDownloadGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseFinderDownloadGet) | **GET** /api/v1.0/implementation/de/tse/finder/download | Download TSE Finder |
| [**apiV10ImplementationDeTseInformationExportGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseInformationExportGet) | **GET** /api/v1.0/implementation/de/tse/information/export | Export TseInformations as CSV |
| [**apiV10ImplementationDeTseInformationGet**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseInformationGet) | **GET** /api/v1.0/implementation/de/tse/information | Get tse informations for the given distributor |
| [**apiV10ImplementationDeTseInformationTseSerialHexAssignPut**](ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseInformationTseSerialHexAssignPut) | **PUT** /api/v1.0/implementation/de/tse/information/{tseSerialHex}/assign | Assign OrganisationId and TerminalId to Tse |


<a id="apiV10ImplementationDeDistributorIdTseInformation2Post"></a>
# **apiV10ImplementationDeDistributorIdTseInformation2Post**
> apiV10ImplementationDeDistributorIdTseInformation2Post(distributorId, organizationId, terminalId, serialNumber, certificateExpirationDate, bsiCertificationId, initializationDate, type, tseId)

Store the given tse information

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the distributor.
    UUID organizationId = UUID.randomUUID(); // UUID | A possible guid of a organization id of the tse. Optional.
    UUID terminalId = UUID.randomUUID(); // UUID | A possible guid of a terminal id of the tse. Optional.
    String serialNumber = "serialNumber_example"; // String | The serial number of the tse.
    OffsetDateTime certificateExpirationDate = OffsetDateTime.now(); // OffsetDateTime | The expiration date of the tse's certificate.
    String bsiCertificationId = "bsiCertificationId_example"; // String | BSI Certificate ID (-), without the prefix BSI-K-TR- (e.g.: 1234-1234) Optional.
    OffsetDateTime initializationDate = OffsetDateTime.now(); // OffsetDateTime | The date when the tse was activated. Optional.
    TseType type = TseType.fromValue("sdCard"); // TseType | The type of the tse. Optional.
    UUID tseId = UUID.randomUUID(); // UUID | The id of the tse. Optional.
    try {
      apiInstance.apiV10ImplementationDeDistributorIdTseInformation2Post(distributorId, organizationId, terminalId, serialNumber, certificateExpirationDate, bsiCertificationId, initializationDate, type, tseId);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeDistributorIdTseInformation2Post");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the distributor. | |
| **organizationId** | **UUID**| A possible guid of a organization id of the tse. Optional. | [optional] |
| **terminalId** | **UUID**| A possible guid of a terminal id of the tse. Optional. | [optional] |
| **serialNumber** | **String**| The serial number of the tse. | [optional] |
| **certificateExpirationDate** | **OffsetDateTime**| The expiration date of the tse&#39;s certificate. | [optional] |
| **bsiCertificationId** | **String**| BSI Certificate ID (-), without the prefix BSI-K-TR- (e.g.: 1234-1234) Optional. | [optional] |
| **initializationDate** | **OffsetDateTime**| The date when the tse was activated. Optional. | [optional] |
| **type** | [**TseType**](.md)| The type of the tse. Optional. | [optional] [enum: sdCard, usbStick, cloud, other] |
| **tseId** | **UUID**| The id of the tse. Optional. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given distributor. |  -  |
| **422** | tseData was sent is null. |  -  |

<a id="apiV10ImplementationDeDistributorIdTseInformationPost"></a>
# **apiV10ImplementationDeDistributorIdTseInformationPost**
> apiV10ImplementationDeDistributorIdTseInformationPost(distributorId, organizationId, terminalId, body)

Store the given tse information

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the distributor.
    UUID organizationId = UUID.randomUUID(); // UUID | A possible guid of a organization id of the tse. Optional.
    UUID terminalId = UUID.randomUUID(); // UUID | A possible guid of a terminal id of the tse. Optional.
    byte[] body = null; // byte[] | Data of the tse (512 bytes)
    try {
      apiInstance.apiV10ImplementationDeDistributorIdTseInformationPost(distributorId, organizationId, terminalId, body);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeDistributorIdTseInformationPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the distributor. | |
| **organizationId** | **UUID**| A possible guid of a organization id of the tse. Optional. | [optional] |
| **terminalId** | **UUID**| A possible guid of a terminal id of the tse. Optional. | [optional] |
| **body** | **byte[]**| Data of the tse (512 bytes) | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given distributor. |  -  |
| **422** | tseData was sent is null. |  -  |

<a id="apiV10ImplementationDeTerminalIdTarGet"></a>
# **apiV10ImplementationDeTerminalIdTarGet**
> apiV10ImplementationDeTerminalIdTarGet(terminalId, fromDate, tillDate)

Exports germany tar data (tse) from cloud archive.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal where the data should be exported.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The start date of the export.
    OffsetDateTime tillDate = OffsetDateTime.now(); // OffsetDateTime | The end date of the export.
    try {
      apiInstance.apiV10ImplementationDeTerminalIdTarGet(terminalId, fromDate, tillDate);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTerminalIdTarGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal where the data should be exported. | |
| **fromDate** | **OffsetDateTime**| The start date of the export. | |
| **tillDate** | **OffsetDateTime**| The end date of the export. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **404** | No data found for export. |  -  |
| **422** | Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. |  -  |

<a id="apiV10ImplementationDeTerminalIdTaxonomyGet"></a>
# **apiV10ImplementationDeTerminalIdTaxonomyGet**
> apiV10ImplementationDeTerminalIdTaxonomyGet(terminalId, fromDate, tillDate, type)

Exports germany taxonomy data from cloud archive.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal where the data should be exported.
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | The start date of the export.
    OffsetDateTime tillDate = OffsetDateTime.now(); // OffsetDateTime | The end date of the export.
    Integer type = 56; // Integer | The type of the export (json = 0, csv = 1).
    try {
      apiInstance.apiV10ImplementationDeTerminalIdTaxonomyGet(terminalId, fromDate, tillDate, type);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTerminalIdTaxonomyGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal where the data should be exported. | |
| **fromDate** | **OffsetDateTime**| The start date of the export. | |
| **tillDate** | **OffsetDateTime**| The end date of the export. | |
| **type** | **Integer**| The type of the export (json &#x3D; 0, csv &#x3D; 1). | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **404** | No data found for export. |  -  |
| **422** | Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. |  -  |

<a id="apiV10ImplementationDeTerminalIdTseProvisioningPatch"></a>
# **apiV10ImplementationDeTerminalIdTseProvisioningPatch**
> FiscalClient apiV10ImplementationDeTerminalIdTseProvisioningPatch(terminalId)

Starts tse provisioning for the given terminal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal to start the provisioning.
    try {
      FiscalClient result = apiInstance.apiV10ImplementationDeTerminalIdTseProvisioningPatch(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTerminalIdTseProvisioningPatch");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal to start the provisioning. | |

### Return type

[**FiscalClient**](FiscalClient.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **402** | License for provisioning not found. |  -  |
| **403** | Not allowed to access the given terminal. |  -  |
| **422** | TerminalId was sent with Guid.Empty. |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsGet"></a>
# **apiV10ImplementationDeTseAnnouncementsGet**
> TseAnnouncementOverviewPageResultModel apiV10ImplementationDeTseAnnouncementsGet(organisationId, pageOffset, pageSize, isTest)

Get Tse Announcements

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | organisation id
    Integer pageOffset = 56; // Integer | page offset
    Integer pageSize = 56; // Integer | page size
    Boolean isTest = true; // Boolean | if true only test otherwise not test
    try {
      TseAnnouncementOverviewPageResultModel result = apiInstance.apiV10ImplementationDeTseAnnouncementsGet(organisationId, pageOffset, pageSize, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| organisation id | |
| **pageOffset** | **Integer**| page offset | [optional] |
| **pageSize** | **Integer**| page size | [optional] |
| **isTest** | **Boolean**| if true only test otherwise not test | [optional] |

### Return type

[**TseAnnouncementOverviewPageResultModel**](TseAnnouncementOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsPost"></a>
# **apiV10ImplementationDeTseAnnouncementsPost**
> TseAnnouncementCreateResult apiV10ImplementationDeTseAnnouncementsPost(storeIds, isTest)

Create TseAnnouncement  starts a new tse announcement progress. An email per store will be sent to the user, containing the announcemnt data and a verification link.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    List<UUID> storeIds = Arrays.asList(); // List<UUID> | 
    Boolean isTest = true; // Boolean | 
    try {
      TseAnnouncementCreateResult result = apiInstance.apiV10ImplementationDeTseAnnouncementsPost(storeIds, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeIds** | [**List&lt;UUID&gt;**](UUID.md)|  | [optional] |
| **isTest** | **Boolean**|  | [optional] |

### Return type

[**TseAnnouncementCreateResult**](TseAnnouncementCreateResult.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsStatusGet"></a>
# **apiV10ImplementationDeTseAnnouncementsStatusGet**
> TseAnnouncementStatusInfo apiV10ImplementationDeTseAnnouncementsStatusGet(entityId, entityType, isTest)

Get TSE Announcement status:  if there are tse changes or an open tse announcement process (changes in announcemnt structure store,terminal,tseInformation) which need to be reported

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | id of organisation / company, store or terminal
    EntityTypes entityType = EntityTypes.fromValue("organisation"); // EntityTypes | entity type
    Boolean isTest = true; // Boolean | filter for test or not test
    try {
      TseAnnouncementStatusInfo result = apiInstance.apiV10ImplementationDeTseAnnouncementsStatusGet(entityId, entityType, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsStatusGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| id of organisation / company, store or terminal | [optional] |
| **entityType** | [**EntityTypes**](.md)| entity type | [optional] [enum: organisation, distributor, distributorContract, configuration, invitation, import, company, store, terminal, profile, configurationLicense, userAssignment, supportPackage, receipt, billingDistributor, billingOrganisation, billingPricing, certificate, backupdata, entityParameter, releases, license, accessLicense, signatureDevice, upload, supplier, supplierContract, accessLicenseContract, supportTicket, timelog, tseInformation, clearingRun, tseAnnouncement] |
| **isTest** | **Boolean**| filter for test or not test | [optional] |

### Return type

[**TseAnnouncementStatusInfo**](TseAnnouncementStatusInfo.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost"></a>
# **apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost**
> TseAnnouncementOverview apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost(tseAnnouncementId)

Cancel TseAnnouncement

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID tseAnnouncementId = UUID.randomUUID(); // UUID | 
    try {
      TseAnnouncementOverview result = apiInstance.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost(tseAnnouncementId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **tseAnnouncementId** | **UUID**|  | |

### Return type

[**TseAnnouncementOverview**](TseAnnouncementOverview.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet"></a>
# **apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet**
> File apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet(tseAnnouncementId)

Returns the tse announcement data as pdf

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID tseAnnouncementId = UUID.randomUUID(); // UUID | 
    try {
      File result = apiInstance.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet(tseAnnouncementId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **tseAnnouncementId** | **UUID**|  | |

### Return type

[**File**](File.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet"></a>
# **apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet**
> TseAnnouncementOverview apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet(tseAnnouncementId)

Get TseAnnouncement

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID tseAnnouncementId = UUID.randomUUID(); // UUID | 
    try {
      TseAnnouncementOverview result = apiInstance.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet(tseAnnouncementId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **tseAnnouncementId** | **UUID**|  | |

### Return type

[**TseAnnouncementOverview**](TseAnnouncementOverview.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut"></a>
# **apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut**
> apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut(tseAnnouncementId)

Resend Verification Email

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID tseAnnouncementId = UUID.randomUUID(); // UUID | 
    try {
      apiInstance.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut(tseAnnouncementId);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **tseAnnouncementId** | **UUID**|  | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost"></a>
# **apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost**
> apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost(tseAnnouncementId, token)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID tseAnnouncementId = UUID.randomUUID(); // UUID | 
    String token = "token_example"; // String | 
    try {
      apiInstance.apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost(tseAnnouncementId, token);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **tseAnnouncementId** | **UUID**|  | |
| **token** | **String**|  | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseAnnouncementsUnannouncedGet"></a>
# **apiV10ImplementationDeTseAnnouncementsUnannouncedGet**
> GuidSimpleObjectPageResultModel apiV10ImplementationDeTseAnnouncementsUnannouncedGet(organisationId, pageOffset, pageSize, isTest)

Get all unannounced TseAnnouncements  This include all TseAnnouncements which has never been announced or include new changes, but not tseAnnouncements which are open (verificationPending or inProgress)

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | organisation id
    Integer pageOffset = 56; // Integer | page offset
    Integer pageSize = 56; // Integer | page size
    Boolean isTest = true; // Boolean | is test
    try {
      GuidSimpleObjectPageResultModel result = apiInstance.apiV10ImplementationDeTseAnnouncementsUnannouncedGet(organisationId, pageOffset, pageSize, isTest);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseAnnouncementsUnannouncedGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| organisation id | |
| **pageOffset** | **Integer**| page offset | [optional] |
| **pageSize** | **Integer**| page size | [optional] |
| **isTest** | **Boolean**| is test | [optional] |

### Return type

[**GuidSimpleObjectPageResultModel**](GuidSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseFinderDownloadGet"></a>
# **apiV10ImplementationDeTseFinderDownloadGet**
> apiV10ImplementationDeTseFinderDownloadGet(distributorId)

Download TSE Finder

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | 
    try {
      apiInstance.apiV10ImplementationDeTseFinderDownloadGet(distributorId);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseFinderDownloadGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**|  | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseInformationExportGet"></a>
# **apiV10ImplementationDeTseInformationExportGet**
> apiV10ImplementationDeTseInformationExportGet(pageOffset, pageSize, searchString)

Export TseInformations as CSV

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    Integer pageOffset = 56; // Integer | page offset
    Integer pageSize = 56; // Integer | page size
    String searchString = "searchString_example"; // String | search string
    try {
      apiInstance.apiV10ImplementationDeTseInformationExportGet(pageOffset, pageSize, searchString);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseInformationExportGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page offset | [optional] |
| **pageSize** | **Integer**| page size | [optional] |
| **searchString** | **String**| search string | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseInformationGet"></a>
# **apiV10ImplementationDeTseInformationGet**
> TseInformationOverviewPageResultModel apiV10ImplementationDeTseInformationGet(pageOffset, pageSize, searchString)

Get tse informations for the given distributor

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    Integer pageOffset = 56; // Integer | page offset
    Integer pageSize = 56; // Integer | page size
    String searchString = "searchString_example"; // String | search text search for serial hex
    try {
      TseInformationOverviewPageResultModel result = apiInstance.apiV10ImplementationDeTseInformationGet(pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseInformationGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page offset | [optional] |
| **pageSize** | **Integer**| page size | [optional] |
| **searchString** | **String**| search text search for serial hex | [optional] |

### Return type

[**TseInformationOverviewPageResultModel**](TseInformationOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ImplementationDeTseInformationTseSerialHexAssignPut"></a>
# **apiV10ImplementationDeTseInformationTseSerialHexAssignPut**
> TseInformationOverview apiV10ImplementationDeTseInformationTseSerialHexAssignPut(tseSerialHex, organisationId, terminalId)

Assign OrganisationId and TerminalId to Tse

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ImplementationControllerGermanyApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ImplementationControllerGermanyApi apiInstance = new ImplementationControllerGermanyApi(defaultClient);
    String tseSerialHex = "tseSerialHex_example"; // String | tse serial hex
    UUID organisationId = UUID.randomUUID(); // UUID | new orgnaisationId
    UUID terminalId = UUID.randomUUID(); // UUID | new terminalId
    try {
      TseInformationOverview result = apiInstance.apiV10ImplementationDeTseInformationTseSerialHexAssignPut(tseSerialHex, organisationId, terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ImplementationControllerGermanyApi#apiV10ImplementationDeTseInformationTseSerialHexAssignPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **tseSerialHex** | **String**| tse serial hex | |
| **organisationId** | **UUID**| new orgnaisationId | [optional] |
| **terminalId** | **UUID**| new terminalId | [optional] |

### Return type

[**TseInformationOverview**](TseInformationOverview.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

