/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for SupplierContractModel
 */
public class SupplierContractModelTest {
    private final SupplierContractModel model = new SupplierContractModel();

    /**
     * Model tests for SupplierContractModel
     */
    @Test
    public void testSupplierContractModel() {
        // TODO: test SupplierContractModel
    }

    /**
     * Test the property 'canEdit'
     */
    @Test
    public void canEditTest() {
        // TODO: test canEdit
    }

    /**
     * Test the property 'canDelete'
     */
    @Test
    public void canDeleteTest() {
        // TODO: test canDelete
    }

    /**
     * Test the property 'supplierId'
     */
    @Test
    public void supplierIdTest() {
        // TODO: test supplierId
    }

    /**
     * Test the property 'contractId'
     */
    @Test
    public void contractIdTest() {
        // TODO: test contractId
    }

    /**
     * Test the property 'contractTypeId'
     */
    @Test
    public void contractTypeIdTest() {
        // TODO: test contractTypeId
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'startDate'
     */
    @Test
    public void startDateTest() {
        // TODO: test startDate
    }

    /**
     * Test the property 'endDate'
     */
    @Test
    public void endDateTest() {
        // TODO: test endDate
    }

    /**
     * Test the property 'mainDueDate'
     */
    @Test
    public void mainDueDateTest() {
        // TODO: test mainDueDate
    }

    /**
     * Test the property 'contractChangeId'
     */
    @Test
    public void contractChangeIdTest() {
        // TODO: test contractChangeId
    }

    /**
     * Test the property 'contractChangeDate'
     */
    @Test
    public void contractChangeDateTest() {
        // TODO: test contractChangeDate
    }

}
