/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for TaxonomyCloudStoreConfiguration
 */
public class TaxonomyCloudStoreConfigurationTest {
    private final TaxonomyCloudStoreConfiguration model = new TaxonomyCloudStoreConfiguration();

    /**
     * Model tests for TaxonomyCloudStoreConfiguration
     */
    @Test
    public void testTaxonomyCloudStoreConfiguration() {
        // TODO: test TaxonomyCloudStoreConfiguration
    }

    /**
     * Test the property 'compress'
     */
    @Test
    public void compressTest() {
        // TODO: test compress
    }

}
