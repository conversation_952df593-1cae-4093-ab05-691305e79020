/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.CosmosDocument;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for DocumentApi
 */
@Disabled
public class DocumentApiTest {

    private final DocumentApi api = new DocumentApi();

    /**
     * Stores a document / fiscal response tuple as RetailForce.Fiscalisation.Model.Helper.CosmosDocument.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10DocumentsDocumentOrganizationIdPostTest() throws ApiException {
        UUID organizationId = null;
        CosmosDocument cosmosDocument = null;
        api.apiV10DocumentsDocumentOrganizationIdPost(organizationId, cosmosDocument);
        // TODO: test validations
    }

}
