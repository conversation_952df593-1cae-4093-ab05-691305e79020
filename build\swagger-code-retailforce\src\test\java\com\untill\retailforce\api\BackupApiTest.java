/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.BackupDataPageResultModel;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for BackupApi
 */
@Disabled
public class BackupApiTest {

    private final BackupApi api = new BackupApi();

    /**
     * Deletes a backup from the storage.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BackupDataTerminalIdDeleteTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime backupDate = null;
        api.apiV10BackupDataTerminalIdDelete(terminalId, backupDate);
        // TODO: test validations
    }

    /**
     * Returns the backups which are available for this terminal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BackupDataTerminalIdGetTest() throws ApiException {
        UUID terminalId = null;
        BackupDataPageResultModel response = api.apiV10BackupDataTerminalIdGet(terminalId);
        // TODO: test validations
    }

}
