/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.ErrorLevel;
import java.io.IOException;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * ValidationError
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class ValidationError {
  public static final String SERIALIZED_NAME_ERROR_LEVEL = "errorLevel";
  @SerializedName(SERIALIZED_NAME_ERROR_LEVEL)
  private ErrorLevel errorLevel;

  public static final String SERIALIZED_NAME_ERROR_TEXT = "errorText";
  @SerializedName(SERIALIZED_NAME_ERROR_TEXT)
  private String errorText;

  public static final String SERIALIZED_NAME_ERROR_SOURCE = "errorSource";
  @SerializedName(SERIALIZED_NAME_ERROR_SOURCE)
  private String errorSource;

  public ValidationError() {
  }

  public ValidationError errorLevel(ErrorLevel errorLevel) {
    
    this.errorLevel = errorLevel;
    return this;
  }

   /**
   * Get errorLevel
   * @return errorLevel
  **/
  @javax.annotation.Nullable
  public ErrorLevel getErrorLevel() {
    return errorLevel;
  }


  public void setErrorLevel(ErrorLevel errorLevel) {
    this.errorLevel = errorLevel;
  }


  public ValidationError errorText(String errorText) {
    
    this.errorText = errorText;
    return this;
  }

   /**
   * Get errorText
   * @return errorText
  **/
  @javax.annotation.Nullable
  public String getErrorText() {
    return errorText;
  }


  public void setErrorText(String errorText) {
    this.errorText = errorText;
  }


  public ValidationError errorSource(String errorSource) {
    
    this.errorSource = errorSource;
    return this;
  }

   /**
   * Get errorSource
   * @return errorSource
  **/
  @javax.annotation.Nullable
  public String getErrorSource() {
    return errorSource;
  }


  public void setErrorSource(String errorSource) {
    this.errorSource = errorSource;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ValidationError validationError = (ValidationError) o;
    return Objects.equals(this.errorLevel, validationError.errorLevel) &&
        Objects.equals(this.errorText, validationError.errorText) &&
        Objects.equals(this.errorSource, validationError.errorSource);
  }

  @Override
  public int hashCode() {
    return Objects.hash(errorLevel, errorText, errorSource);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ValidationError {\n");
    sb.append("    errorLevel: ").append(toIndentedString(errorLevel)).append("\n");
    sb.append("    errorText: ").append(toIndentedString(errorText)).append("\n");
    sb.append("    errorSource: ").append(toIndentedString(errorSource)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("errorLevel");
    openapiFields.add("errorText");
    openapiFields.add("errorSource");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to ValidationError
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!ValidationError.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in ValidationError is not found in the empty JSON string", ValidationError.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!ValidationError.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `ValidationError` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("errorText") != null && !jsonObj.get("errorText").isJsonNull()) && !jsonObj.get("errorText").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `errorText` to be a primitive type in the JSON string but got `%s`", jsonObj.get("errorText").toString()));
      }
      if ((jsonObj.get("errorSource") != null && !jsonObj.get("errorSource").isJsonNull()) && !jsonObj.get("errorSource").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `errorSource` to be a primitive type in the JSON string but got `%s`", jsonObj.get("errorSource").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!ValidationError.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'ValidationError' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<ValidationError> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(ValidationError.class));

       return (TypeAdapter<T>) new TypeAdapter<ValidationError>() {
           @Override
           public void write(JsonWriter out, ValidationError value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public ValidationError read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of ValidationError given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of ValidationError
  * @throws IOException if the JSON string is invalid with respect to ValidationError
  */
  public static ValidationError fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, ValidationError.class);
  }

 /**
  * Convert an instance of ValidationError to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

