# DashboardApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet**](DashboardApi.md#apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet) | **GET** /api/v1.0/dashboard/terminal/annualYearCheck/{organizationId} | Returns an overview of all annual year checks for all active terminals (archived terminals will not be returned). |
| [**apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet**](DashboardApi.md#apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet) | **GET** /api/v1.0/dashboard/terminal/annualYearCheck/{organizationId}/{year} | Returns a detail list of terminals belonging to the requested organization and containing the requested annual year check. |
| [**apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet**](DashboardApi.md#apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet) | **GET** /api/v1.0/dashboard/terminal/clientVersion/{organizationId}/{clientVersion} | Returns a detail list of terminals belonging to the requested organization and having requested client version. |
| [**apiV10DashboardTerminalClientVersionOrganizationIdGet**](DashboardApi.md#apiV10DashboardTerminalClientVersionOrganizationIdGet) | **GET** /api/v1.0/dashboard/terminal/clientVersion/{organizationId} | Returns an overview of all terminals and their versions. |


<a id="apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet"></a>
# **apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet**
> DashboardTerminalOverviewPageResultModel apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet(organizationId, includeTest, pageOffset, pageSize)

Returns an overview of all annual year checks for all active terminals (archived terminals will not be returned).

If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.DashboardApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    DashboardApi apiInstance = new DashboardApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization of the terminals where the annual year check should be shown.
    Boolean includeTest = false; // Boolean | True if test terminals should also be returned. Default is false.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    try {
      DashboardTerminalOverviewPageResultModel result = apiInstance.apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet(organizationId, includeTest, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DashboardApi#apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization of the terminals where the annual year check should be shown. | |
| **includeTest** | **Boolean**| True if test terminals should also be returned. Default is false. | [optional] [default to false] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |

### Return type

[**DashboardTerminalOverviewPageResultModel**](DashboardTerminalOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet"></a>
# **apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet**
> DashboardTerminalDetailPageResultModel apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet(organizationId, year, includeTest, pageOffset, pageSize, searchString)

Returns a detail list of terminals belonging to the requested organization and containing the requested annual year check.

If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.DashboardApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    DashboardApi apiInstance = new DashboardApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization of the terminals where the annual year check should be shown.
    String year = "year_example"; // String | The requested year.
    Boolean includeTest = false; // Boolean | True if test terminals should also be returned. Default is false.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | Search for terminal or store number (or caption).
    try {
      DashboardTerminalDetailPageResultModel result = apiInstance.apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet(organizationId, year, includeTest, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DashboardApi#apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization of the terminals where the annual year check should be shown. | |
| **year** | **String**| The requested year. | |
| **includeTest** | **Boolean**| True if test terminals should also be returned. Default is false. | [optional] [default to false] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| Search for terminal or store number (or caption). | [optional] |

### Return type

[**DashboardTerminalDetailPageResultModel**](DashboardTerminalDetailPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet"></a>
# **apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet**
> DashboardTerminalDetailPageResultModel apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet(organizationId, clientVersion, includeTest, pageOffset, pageSize, searchString)

Returns a detail list of terminals belonging to the requested organization and having requested client version.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.DashboardApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    DashboardApi apiInstance = new DashboardApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization where the terminal versions are requested.
    String clientVersion = "clientVersion_example"; // String | The requested client version.
    Boolean includeTest = false; // Boolean | True if test terminals should also be returned. Default is false.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | Search for terminal or store number (or caption).
    try {
      DashboardTerminalDetailPageResultModel result = apiInstance.apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet(organizationId, clientVersion, includeTest, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DashboardApi#apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization where the terminal versions are requested. | |
| **clientVersion** | **String**| The requested client version. | |
| **includeTest** | **Boolean**| True if test terminals should also be returned. Default is false. | [optional] [default to false] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| Search for terminal or store number (or caption). | [optional] |

### Return type

[**DashboardTerminalDetailPageResultModel**](DashboardTerminalDetailPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10DashboardTerminalClientVersionOrganizationIdGet"></a>
# **apiV10DashboardTerminalClientVersionOrganizationIdGet**
> DashboardTerminalOverviewPageResultModel apiV10DashboardTerminalClientVersionOrganizationIdGet(organizationId, includeTest, pageOffset, pageSize)

Returns an overview of all terminals and their versions.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.DashboardApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    DashboardApi apiInstance = new DashboardApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization where the terminal versions are requested.
    Boolean includeTest = false; // Boolean | True if test terminals should also be returned. Default is false.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    try {
      DashboardTerminalOverviewPageResultModel result = apiInstance.apiV10DashboardTerminalClientVersionOrganizationIdGet(organizationId, includeTest, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling DashboardApi#apiV10DashboardTerminalClientVersionOrganizationIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization where the terminal versions are requested. | |
| **includeTest** | **Boolean**| True if test terminals should also be returned. Default is false. | [optional] [default to false] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |

### Return type

[**DashboardTerminalOverviewPageResultModel**](DashboardTerminalOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

