

# ReturnReasonType

## Enum


* `USERMISTAKE` (value: `"userMistake"`)

* `MASTERDATAWRONG` (value: `"masterDataWrong"`)

* `ITEMNOTINSTOCK` (value: `"itemNotInStock"`)

* `CUSTOMERCOMPLAINT` (value: `"customerComplaint"`)

* `CUSTOMERRETURN` (value: `"customerReturn"`)

* `CUSTOMERDISCOUNT` (value: `"customerDiscount"`)

* `CUSTOMERDATAWRONG` (value: `"customerDataWrong"`)

* `TECHNICALMYSTERYSHOPPING` (value: `"technicalMysteryShopping"`)



