/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.FiscalCountry;
import com.untill.retailforce.model.PlatformType;
import com.untill.retailforce.model.TerminalType;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Viewmodel object for terminal object (extending RetailForce.Cloud.Model.Terminal object.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TerminalModel {
  public static final String SERIALIZED_NAME_ORGANISATION_CAPTION = "organisationCaption";
  @SerializedName(SERIALIZED_NAME_ORGANISATION_CAPTION)
  private String organisationCaption;

  public static final String SERIALIZED_NAME_COMPANY_CAPTION = "companyCaption";
  @SerializedName(SERIALIZED_NAME_COMPANY_CAPTION)
  private String companyCaption;

  public static final String SERIALIZED_NAME_STORE_CAPTION = "storeCaption";
  @SerializedName(SERIALIZED_NAME_STORE_CAPTION)
  private String storeCaption;

  public static final String SERIALIZED_NAME_STORE_NUMBER = "storeNumber";
  @SerializedName(SERIALIZED_NAME_STORE_NUMBER)
  private String storeNumber;

  public static final String SERIALIZED_NAME_ALERT = "alert";
  @SerializedName(SERIALIZED_NAME_ALERT)
  private Boolean alert;

  public static final String SERIALIZED_NAME_CAN_EDIT = "canEdit";
  @SerializedName(SERIALIZED_NAME_CAN_EDIT)
  private Boolean canEdit;

  public static final String SERIALIZED_NAME_CAN_DELETE = "canDelete";
  @SerializedName(SERIALIZED_NAME_CAN_DELETE)
  private Boolean canDelete;

  public static final String SERIALIZED_NAME_CONFIGURATION_CAPTION = "configurationCaption";
  @SerializedName(SERIALIZED_NAME_CONFIGURATION_CAPTION)
  private String configurationCaption;

  public static final String SERIALIZED_NAME_NEW_CONFIGURATION_CAPTION = "newConfigurationCaption";
  @SerializedName(SERIALIZED_NAME_NEW_CONFIGURATION_CAPTION)
  private String newConfigurationCaption;

  public static final String SERIALIZED_NAME_DEACTIVATION_DATE = "deactivationDate";
  @SerializedName(SERIALIZED_NAME_DEACTIVATION_DATE)
  private OffsetDateTime deactivationDate;

  public static final String SERIALIZED_NAME_LAST_LICENSE_USAGE = "lastLicenseUsage";
  @SerializedName(SERIALIZED_NAME_LAST_LICENSE_USAGE)
  private OffsetDateTime lastLicenseUsage;

  public static final String SERIALIZED_NAME_IS_DEACTIVATED = "isDeactivated";
  @SerializedName(SERIALIZED_NAME_IS_DEACTIVATED)
  private Boolean isDeactivated;

  public static final String SERIALIZED_NAME_IS_ARCHIVED = "isArchived";
  @SerializedName(SERIALIZED_NAME_IS_ARCHIVED)
  private Boolean isArchived;

  public static final String SERIALIZED_NAME_VERSION = "version";
  @SerializedName(SERIALIZED_NAME_VERSION)
  private String version;

  public static final String SERIALIZED_NAME_TERMINAL_ID = "terminalId";
  @SerializedName(SERIALIZED_NAME_TERMINAL_ID)
  private UUID terminalId;

  public static final String SERIALIZED_NAME_STORE_ID = "storeId";
  @SerializedName(SERIALIZED_NAME_STORE_ID)
  private UUID storeId;

  public static final String SERIALIZED_NAME_TERMINAL_NUMBER = "terminalNumber";
  @SerializedName(SERIALIZED_NAME_TERMINAL_NUMBER)
  private String terminalNumber;

  public static final String SERIALIZED_NAME_FISCAL_COUNTRY = "fiscalCountry";
  @SerializedName(SERIALIZED_NAME_FISCAL_COUNTRY)
  private FiscalCountry fiscalCountry;

  public static final String SERIALIZED_NAME_CAPTION = "caption";
  @SerializedName(SERIALIZED_NAME_CAPTION)
  private String caption;

  public static final String SERIALIZED_NAME_PLATFORM_TYPE = "platformType";
  @SerializedName(SERIALIZED_NAME_PLATFORM_TYPE)
  private PlatformType platformType;

  public static final String SERIALIZED_NAME_IS_TEST = "isTest";
  @SerializedName(SERIALIZED_NAME_IS_TEST)
  private Boolean isTest;

  public static final String SERIALIZED_NAME_ARCHIVE_DATE = "archiveDate";
  @SerializedName(SERIALIZED_NAME_ARCHIVE_DATE)
  private OffsetDateTime archiveDate;

  public static final String SERIALIZED_NAME_CLIENT_CONFIGURATION_ID = "clientConfigurationId";
  @SerializedName(SERIALIZED_NAME_CLIENT_CONFIGURATION_ID)
  private UUID clientConfigurationId;

  public static final String SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_ID = "newClientConfigurationId";
  @SerializedName(SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_ID)
  private UUID newClientConfigurationId;

  public static final String SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_VALID_FROM = "newClientConfigurationValidFrom";
  @SerializedName(SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_VALID_FROM)
  private OffsetDateTime newClientConfigurationValidFrom;

  public static final String SERIALIZED_NAME_GLOBAL_SHORT_ID = "globalShortId";
  @SerializedName(SERIALIZED_NAME_GLOBAL_SHORT_ID)
  private String globalShortId;

  public static final String SERIALIZED_NAME_PURCHASE_DATE = "purchaseDate";
  @SerializedName(SERIALIZED_NAME_PURCHASE_DATE)
  private OffsetDateTime purchaseDate;

  public static final String SERIALIZED_NAME_COMMISSIONING_DATE = "commissioningDate";
  @SerializedName(SERIALIZED_NAME_COMMISSIONING_DATE)
  private OffsetDateTime commissioningDate;

  public static final String SERIALIZED_NAME_DECOMMISSIONING_DATE = "decommissioningDate";
  @SerializedName(SERIALIZED_NAME_DECOMMISSIONING_DATE)
  private OffsetDateTime decommissioningDate;

  public static final String SERIALIZED_NAME_TERMINAL_TYPE = "terminalType";
  @SerializedName(SERIALIZED_NAME_TERMINAL_TYPE)
  private TerminalType terminalType;

  public static final String SERIALIZED_NAME_UPDATED_BY_PRINCIPAL_ID = "updatedByPrincipalId";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY_PRINCIPAL_ID)
  private UUID updatedByPrincipalId;

  public static final String SERIALIZED_NAME_CASH_REGISTER_ID = "cashRegisterId";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_ID)
  private String cashRegisterId;

  public static final String SERIALIZED_NAME_CASH_REGISTER_BRAND = "cashRegisterBrand";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_BRAND)
  private String cashRegisterBrand;

  public static final String SERIALIZED_NAME_CASH_REGISTER_MODELNAME = "cashRegisterModelname";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_MODELNAME)
  private String cashRegisterModelname;

  public static final String SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_BRAND = "cashRegisterSoftwareBrand";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_BRAND)
  private String cashRegisterSoftwareBrand;

  public static final String SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_VERSION = "cashRegisterSoftwareVersion";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_VERSION)
  private String cashRegisterSoftwareVersion;

  public static final String SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_COMPANY = "cashRegisterSoftwareCompany";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_COMPANY)
  private String cashRegisterSoftwareCompany;

  public TerminalModel() {
  }

  
  public TerminalModel(
     Boolean isArchived, 
     String version
  ) {
    this();
    this.isArchived = isArchived;
    this.version = version;
  }

  public TerminalModel organisationCaption(String organisationCaption) {
    
    this.organisationCaption = organisationCaption;
    return this;
  }

   /**
   * The caption of the organisation the terminal belongs.
   * @return organisationCaption
  **/
  @javax.annotation.Nullable
  public String getOrganisationCaption() {
    return organisationCaption;
  }


  public void setOrganisationCaption(String organisationCaption) {
    this.organisationCaption = organisationCaption;
  }


  public TerminalModel companyCaption(String companyCaption) {
    
    this.companyCaption = companyCaption;
    return this;
  }

   /**
   * The caption of the company the terminal belongs (if the store is attached to a company).
   * @return companyCaption
  **/
  @javax.annotation.Nullable
  public String getCompanyCaption() {
    return companyCaption;
  }


  public void setCompanyCaption(String companyCaption) {
    this.companyCaption = companyCaption;
  }


  public TerminalModel storeCaption(String storeCaption) {
    
    this.storeCaption = storeCaption;
    return this;
  }

   /**
   * The caption of the store the terminal belongs.
   * @return storeCaption
  **/
  @javax.annotation.Nullable
  public String getStoreCaption() {
    return storeCaption;
  }


  public void setStoreCaption(String storeCaption) {
    this.storeCaption = storeCaption;
  }


  public TerminalModel storeNumber(String storeNumber) {
    
    this.storeNumber = storeNumber;
    return this;
  }

   /**
   * The store number of the store the terminal belongs.
   * @return storeNumber
  **/
  @javax.annotation.Nullable
  public String getStoreNumber() {
    return storeNumber;
  }


  public void setStoreNumber(String storeNumber) {
    this.storeNumber = storeNumber;
  }


  public TerminalModel alert(Boolean alert) {
    
    this.alert = alert;
    return this;
  }

   /**
   * True if the terminal has an alert. Otherwise false.
   * @return alert
  **/
  @javax.annotation.Nullable
  public Boolean getAlert() {
    return alert;
  }


  public void setAlert(Boolean alert) {
    this.alert = alert;
  }


  public TerminalModel canEdit(Boolean canEdit) {
    
    this.canEdit = canEdit;
    return this;
  }

   /**
   * True if the terminal can be edited. Otherwise false.
   * @return canEdit
  **/
  @javax.annotation.Nullable
  public Boolean getCanEdit() {
    return canEdit;
  }


  public void setCanEdit(Boolean canEdit) {
    this.canEdit = canEdit;
  }


  public TerminalModel canDelete(Boolean canDelete) {
    
    this.canDelete = canDelete;
    return this;
  }

   /**
   * True if the terminal can be deleted. Otherwise false.
   * @return canDelete
  **/
  @javax.annotation.Nullable
  public Boolean getCanDelete() {
    return canDelete;
  }


  public void setCanDelete(Boolean canDelete) {
    this.canDelete = canDelete;
  }


  public TerminalModel configurationCaption(String configurationCaption) {
    
    this.configurationCaption = configurationCaption;
    return this;
  }

   /**
   * The caption of the configuration assigned to the terminal.
   * @return configurationCaption
  **/
  @javax.annotation.Nullable
  public String getConfigurationCaption() {
    return configurationCaption;
  }


  public void setConfigurationCaption(String configurationCaption) {
    this.configurationCaption = configurationCaption;
  }


  public TerminalModel newConfigurationCaption(String newConfigurationCaption) {
    
    this.newConfigurationCaption = newConfigurationCaption;
    return this;
  }

   /**
   * The caption of a possible new configuration assigned to the terminal.
   * @return newConfigurationCaption
  **/
  @javax.annotation.Nullable
  public String getNewConfigurationCaption() {
    return newConfigurationCaption;
  }


  public void setNewConfigurationCaption(String newConfigurationCaption) {
    this.newConfigurationCaption = newConfigurationCaption;
  }


  public TerminalModel deactivationDate(OffsetDateTime deactivationDate) {
    
    this.deactivationDate = deactivationDate;
    return this;
  }

   /**
   * 
   * @return deactivationDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getDeactivationDate() {
    return deactivationDate;
  }


  public void setDeactivationDate(OffsetDateTime deactivationDate) {
    this.deactivationDate = deactivationDate;
  }


  public TerminalModel lastLicenseUsage(OffsetDateTime lastLicenseUsage) {
    
    this.lastLicenseUsage = lastLicenseUsage;
    return this;
  }

   /**
   * Get lastLicenseUsage
   * @return lastLicenseUsage
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getLastLicenseUsage() {
    return lastLicenseUsage;
  }


  public void setLastLicenseUsage(OffsetDateTime lastLicenseUsage) {
    this.lastLicenseUsage = lastLicenseUsage;
  }


  public TerminalModel isDeactivated(Boolean isDeactivated) {
    
    this.isDeactivated = isDeactivated;
    return this;
  }

   /**
   * True if the terminal is deactivated (seasonal deactivation)
   * @return isDeactivated
  **/
  @javax.annotation.Nullable
  public Boolean getIsDeactivated() {
    return isDeactivated;
  }


  public void setIsDeactivated(Boolean isDeactivated) {
    this.isDeactivated = isDeactivated;
  }


   /**
   * True if the terminal is archived
   * @return isArchived
  **/
  @javax.annotation.Nullable
  public Boolean getIsArchived() {
    return isArchived;
  }




   /**
   * Version (validFrom &#x3D;&gt; UTC ticks, as string for precision)
   * @return version
  **/
  @javax.annotation.Nullable
  public String getVersion() {
    return version;
  }




  public TerminalModel terminalId(UUID terminalId) {
    
    this.terminalId = terminalId;
    return this;
  }

   /**
   * The id of the terminal.
   * @return terminalId
  **/
  @javax.annotation.Nullable
  public UUID getTerminalId() {
    return terminalId;
  }


  public void setTerminalId(UUID terminalId) {
    this.terminalId = terminalId;
  }


  public TerminalModel storeId(UUID storeId) {
    
    this.storeId = storeId;
    return this;
  }

   /**
   * The store of the terminal.
   * @return storeId
  **/
  @javax.annotation.Nullable
  public UUID getStoreId() {
    return storeId;
  }


  public void setStoreId(UUID storeId) {
    this.storeId = storeId;
  }


  public TerminalModel terminalNumber(String terminalNumber) {
    
    this.terminalNumber = terminalNumber;
    return this;
  }

   /**
   * The terminal number.
   * @return terminalNumber
  **/
  @javax.annotation.Nullable
  public String getTerminalNumber() {
    return terminalNumber;
  }


  public void setTerminalNumber(String terminalNumber) {
    this.terminalNumber = terminalNumber;
  }


  public TerminalModel fiscalCountry(FiscalCountry fiscalCountry) {
    
    this.fiscalCountry = fiscalCountry;
    return this;
  }

   /**
   * Get fiscalCountry
   * @return fiscalCountry
  **/
  @javax.annotation.Nullable
  public FiscalCountry getFiscalCountry() {
    return fiscalCountry;
  }


  public void setFiscalCountry(FiscalCountry fiscalCountry) {
    this.fiscalCountry = fiscalCountry;
  }


  public TerminalModel caption(String caption) {
    
    this.caption = caption;
    return this;
  }

   /**
   * Gets or sets a possible caption for a terminal.
   * @return caption
  **/
  @javax.annotation.Nullable
  public String getCaption() {
    return caption;
  }


  public void setCaption(String caption) {
    this.caption = caption;
  }


  public TerminalModel platformType(PlatformType platformType) {
    
    this.platformType = platformType;
    return this;
  }

   /**
   * Get platformType
   * @return platformType
  **/
  @javax.annotation.Nullable
  public PlatformType getPlatformType() {
    return platformType;
  }


  public void setPlatformType(PlatformType platformType) {
    this.platformType = platformType;
  }


  public TerminalModel isTest(Boolean isTest) {
    
    this.isTest = isTest;
    return this;
  }

   /**
   * True if it is a test terminal.
   * @return isTest
  **/
  @javax.annotation.Nullable
  public Boolean getIsTest() {
    return isTest;
  }


  public void setIsTest(Boolean isTest) {
    this.isTest = isTest;
  }


  public TerminalModel archiveDate(OffsetDateTime archiveDate) {
    
    this.archiveDate = archiveDate;
    return this;
  }

   /**
   * The date when the terminal was archived (decommissioned).
   * @return archiveDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getArchiveDate() {
    return archiveDate;
  }


  public void setArchiveDate(OffsetDateTime archiveDate) {
    this.archiveDate = archiveDate;
  }


  public TerminalModel clientConfigurationId(UUID clientConfigurationId) {
    
    this.clientConfigurationId = clientConfigurationId;
    return this;
  }

   /**
   * The configuration for this terminal.
   * @return clientConfigurationId
  **/
  @javax.annotation.Nullable
  public UUID getClientConfigurationId() {
    return clientConfigurationId;
  }


  public void setClientConfigurationId(UUID clientConfigurationId) {
    this.clientConfigurationId = clientConfigurationId;
  }


  public TerminalModel newClientConfigurationId(UUID newClientConfigurationId) {
    
    this.newClientConfigurationId = newClientConfigurationId;
    return this;
  }

   /**
   * The new configuration for this terminal valid from RetailForce.Cloud.Model.Terminal.NewClientConfigurationValidFrom.
   * @return newClientConfigurationId
  **/
  @javax.annotation.Nullable
  public UUID getNewClientConfigurationId() {
    return newClientConfigurationId;
  }


  public void setNewClientConfigurationId(UUID newClientConfigurationId) {
    this.newClientConfigurationId = newClientConfigurationId;
  }


  public TerminalModel newClientConfigurationValidFrom(OffsetDateTime newClientConfigurationValidFrom) {
    
    this.newClientConfigurationValidFrom = newClientConfigurationValidFrom;
    return this;
  }

   /**
   * The validity date for the new configuration.
   * @return newClientConfigurationValidFrom
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getNewClientConfigurationValidFrom() {
    return newClientConfigurationValidFrom;
  }


  public void setNewClientConfigurationValidFrom(OffsetDateTime newClientConfigurationValidFrom) {
    this.newClientConfigurationValidFrom = newClientConfigurationValidFrom;
  }


  public TerminalModel globalShortId(String globalShortId) {
    
    this.globalShortId = globalShortId;
    return this;
  }

   /**
   * Represents a 4 digit (alphanumeric, case-sensitive) value representing a global unique short id for the terminal.
   * @return globalShortId
  **/
  @javax.annotation.Nullable
  public String getGlobalShortId() {
    return globalShortId;
  }


  public void setGlobalShortId(String globalShortId) {
    this.globalShortId = globalShortId;
  }


  public TerminalModel purchaseDate(OffsetDateTime purchaseDate) {
    
    this.purchaseDate = purchaseDate;
    return this;
  }

   /**
   * Purchase date
   * @return purchaseDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getPurchaseDate() {
    return purchaseDate;
  }


  public void setPurchaseDate(OffsetDateTime purchaseDate) {
    this.purchaseDate = purchaseDate;
  }


  public TerminalModel commissioningDate(OffsetDateTime commissioningDate) {
    
    this.commissioningDate = commissioningDate;
    return this;
  }

   /**
   * Commissioning date
   * @return commissioningDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getCommissioningDate() {
    return commissioningDate;
  }


  public void setCommissioningDate(OffsetDateTime commissioningDate) {
    this.commissioningDate = commissioningDate;
  }


  public TerminalModel decommissioningDate(OffsetDateTime decommissioningDate) {
    
    this.decommissioningDate = decommissioningDate;
    return this;
  }

   /**
   * Decommissioning date
   * @return decommissioningDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getDecommissioningDate() {
    return decommissioningDate;
  }


  public void setDecommissioningDate(OffsetDateTime decommissioningDate) {
    this.decommissioningDate = decommissioningDate;
  }


  public TerminalModel terminalType(TerminalType terminalType) {
    
    this.terminalType = terminalType;
    return this;
  }

   /**
   * Get terminalType
   * @return terminalType
  **/
  @javax.annotation.Nullable
  public TerminalType getTerminalType() {
    return terminalType;
  }


  public void setTerminalType(TerminalType terminalType) {
    this.terminalType = terminalType;
  }


  public TerminalModel updatedByPrincipalId(UUID updatedByPrincipalId) {
    
    this.updatedByPrincipalId = updatedByPrincipalId;
    return this;
  }

   /**
   * Updated by PrincipalId
   * @return updatedByPrincipalId
  **/
  @javax.annotation.Nullable
  public UUID getUpdatedByPrincipalId() {
    return updatedByPrincipalId;
  }


  public void setUpdatedByPrincipalId(UUID updatedByPrincipalId) {
    this.updatedByPrincipalId = updatedByPrincipalId;
  }


  public TerminalModel cashRegisterId(String cashRegisterId) {
    
    this.cashRegisterId = cashRegisterId;
    return this;
  }

   /**
   * The id of the cash register hardware.
   * @return cashRegisterId
  **/
  @javax.annotation.Nullable
  public String getCashRegisterId() {
    return cashRegisterId;
  }


  public void setCashRegisterId(String cashRegisterId) {
    this.cashRegisterId = cashRegisterId;
  }


  public TerminalModel cashRegisterBrand(String cashRegisterBrand) {
    
    this.cashRegisterBrand = cashRegisterBrand;
    return this;
  }

   /**
   * The brand of the cash register hardware.
   * @return cashRegisterBrand
  **/
  @javax.annotation.Nullable
  public String getCashRegisterBrand() {
    return cashRegisterBrand;
  }


  public void setCashRegisterBrand(String cashRegisterBrand) {
    this.cashRegisterBrand = cashRegisterBrand;
  }


  public TerminalModel cashRegisterModelname(String cashRegisterModelname) {
    
    this.cashRegisterModelname = cashRegisterModelname;
    return this;
  }

   /**
   * The model name of the cash register hardware.
   * @return cashRegisterModelname
  **/
  @javax.annotation.Nullable
  public String getCashRegisterModelname() {
    return cashRegisterModelname;
  }


  public void setCashRegisterModelname(String cashRegisterModelname) {
    this.cashRegisterModelname = cashRegisterModelname;
  }


  public TerminalModel cashRegisterSoftwareBrand(String cashRegisterSoftwareBrand) {
    
    this.cashRegisterSoftwareBrand = cashRegisterSoftwareBrand;
    return this;
  }

   /**
   * The software of the cash register.
   * @return cashRegisterSoftwareBrand
  **/
  @javax.annotation.Nullable
  public String getCashRegisterSoftwareBrand() {
    return cashRegisterSoftwareBrand;
  }


  public void setCashRegisterSoftwareBrand(String cashRegisterSoftwareBrand) {
    this.cashRegisterSoftwareBrand = cashRegisterSoftwareBrand;
  }


  public TerminalModel cashRegisterSoftwareVersion(String cashRegisterSoftwareVersion) {
    
    this.cashRegisterSoftwareVersion = cashRegisterSoftwareVersion;
    return this;
  }

   /**
   * The version of the software of the cash register.
   * @return cashRegisterSoftwareVersion
  **/
  @javax.annotation.Nullable
  public String getCashRegisterSoftwareVersion() {
    return cashRegisterSoftwareVersion;
  }


  public void setCashRegisterSoftwareVersion(String cashRegisterSoftwareVersion) {
    this.cashRegisterSoftwareVersion = cashRegisterSoftwareVersion;
  }


  public TerminalModel cashRegisterSoftwareCompany(String cashRegisterSoftwareCompany) {
    
    this.cashRegisterSoftwareCompany = cashRegisterSoftwareCompany;
    return this;
  }

   /**
   * The company name of the creator of the cash register software.
   * @return cashRegisterSoftwareCompany
  **/
  @javax.annotation.Nullable
  public String getCashRegisterSoftwareCompany() {
    return cashRegisterSoftwareCompany;
  }


  public void setCashRegisterSoftwareCompany(String cashRegisterSoftwareCompany) {
    this.cashRegisterSoftwareCompany = cashRegisterSoftwareCompany;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TerminalModel terminalModel = (TerminalModel) o;
    return Objects.equals(this.organisationCaption, terminalModel.organisationCaption) &&
        Objects.equals(this.companyCaption, terminalModel.companyCaption) &&
        Objects.equals(this.storeCaption, terminalModel.storeCaption) &&
        Objects.equals(this.storeNumber, terminalModel.storeNumber) &&
        Objects.equals(this.alert, terminalModel.alert) &&
        Objects.equals(this.canEdit, terminalModel.canEdit) &&
        Objects.equals(this.canDelete, terminalModel.canDelete) &&
        Objects.equals(this.configurationCaption, terminalModel.configurationCaption) &&
        Objects.equals(this.newConfigurationCaption, terminalModel.newConfigurationCaption) &&
        Objects.equals(this.deactivationDate, terminalModel.deactivationDate) &&
        Objects.equals(this.lastLicenseUsage, terminalModel.lastLicenseUsage) &&
        Objects.equals(this.isDeactivated, terminalModel.isDeactivated) &&
        Objects.equals(this.isArchived, terminalModel.isArchived) &&
        Objects.equals(this.version, terminalModel.version) &&
        Objects.equals(this.terminalId, terminalModel.terminalId) &&
        Objects.equals(this.storeId, terminalModel.storeId) &&
        Objects.equals(this.terminalNumber, terminalModel.terminalNumber) &&
        Objects.equals(this.fiscalCountry, terminalModel.fiscalCountry) &&
        Objects.equals(this.caption, terminalModel.caption) &&
        Objects.equals(this.platformType, terminalModel.platformType) &&
        Objects.equals(this.isTest, terminalModel.isTest) &&
        Objects.equals(this.archiveDate, terminalModel.archiveDate) &&
        Objects.equals(this.clientConfigurationId, terminalModel.clientConfigurationId) &&
        Objects.equals(this.newClientConfigurationId, terminalModel.newClientConfigurationId) &&
        Objects.equals(this.newClientConfigurationValidFrom, terminalModel.newClientConfigurationValidFrom) &&
        Objects.equals(this.globalShortId, terminalModel.globalShortId) &&
        Objects.equals(this.purchaseDate, terminalModel.purchaseDate) &&
        Objects.equals(this.commissioningDate, terminalModel.commissioningDate) &&
        Objects.equals(this.decommissioningDate, terminalModel.decommissioningDate) &&
        Objects.equals(this.terminalType, terminalModel.terminalType) &&
        Objects.equals(this.updatedByPrincipalId, terminalModel.updatedByPrincipalId) &&
        Objects.equals(this.cashRegisterId, terminalModel.cashRegisterId) &&
        Objects.equals(this.cashRegisterBrand, terminalModel.cashRegisterBrand) &&
        Objects.equals(this.cashRegisterModelname, terminalModel.cashRegisterModelname) &&
        Objects.equals(this.cashRegisterSoftwareBrand, terminalModel.cashRegisterSoftwareBrand) &&
        Objects.equals(this.cashRegisterSoftwareVersion, terminalModel.cashRegisterSoftwareVersion) &&
        Objects.equals(this.cashRegisterSoftwareCompany, terminalModel.cashRegisterSoftwareCompany);
  }

  @Override
  public int hashCode() {
    return Objects.hash(organisationCaption, companyCaption, storeCaption, storeNumber, alert, canEdit, canDelete, configurationCaption, newConfigurationCaption, deactivationDate, lastLicenseUsage, isDeactivated, isArchived, version, terminalId, storeId, terminalNumber, fiscalCountry, caption, platformType, isTest, archiveDate, clientConfigurationId, newClientConfigurationId, newClientConfigurationValidFrom, globalShortId, purchaseDate, commissioningDate, decommissioningDate, terminalType, updatedByPrincipalId, cashRegisterId, cashRegisterBrand, cashRegisterModelname, cashRegisterSoftwareBrand, cashRegisterSoftwareVersion, cashRegisterSoftwareCompany);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TerminalModel {\n");
    sb.append("    organisationCaption: ").append(toIndentedString(organisationCaption)).append("\n");
    sb.append("    companyCaption: ").append(toIndentedString(companyCaption)).append("\n");
    sb.append("    storeCaption: ").append(toIndentedString(storeCaption)).append("\n");
    sb.append("    storeNumber: ").append(toIndentedString(storeNumber)).append("\n");
    sb.append("    alert: ").append(toIndentedString(alert)).append("\n");
    sb.append("    canEdit: ").append(toIndentedString(canEdit)).append("\n");
    sb.append("    canDelete: ").append(toIndentedString(canDelete)).append("\n");
    sb.append("    configurationCaption: ").append(toIndentedString(configurationCaption)).append("\n");
    sb.append("    newConfigurationCaption: ").append(toIndentedString(newConfigurationCaption)).append("\n");
    sb.append("    deactivationDate: ").append(toIndentedString(deactivationDate)).append("\n");
    sb.append("    lastLicenseUsage: ").append(toIndentedString(lastLicenseUsage)).append("\n");
    sb.append("    isDeactivated: ").append(toIndentedString(isDeactivated)).append("\n");
    sb.append("    isArchived: ").append(toIndentedString(isArchived)).append("\n");
    sb.append("    version: ").append(toIndentedString(version)).append("\n");
    sb.append("    terminalId: ").append(toIndentedString(terminalId)).append("\n");
    sb.append("    storeId: ").append(toIndentedString(storeId)).append("\n");
    sb.append("    terminalNumber: ").append(toIndentedString(terminalNumber)).append("\n");
    sb.append("    fiscalCountry: ").append(toIndentedString(fiscalCountry)).append("\n");
    sb.append("    caption: ").append(toIndentedString(caption)).append("\n");
    sb.append("    platformType: ").append(toIndentedString(platformType)).append("\n");
    sb.append("    isTest: ").append(toIndentedString(isTest)).append("\n");
    sb.append("    archiveDate: ").append(toIndentedString(archiveDate)).append("\n");
    sb.append("    clientConfigurationId: ").append(toIndentedString(clientConfigurationId)).append("\n");
    sb.append("    newClientConfigurationId: ").append(toIndentedString(newClientConfigurationId)).append("\n");
    sb.append("    newClientConfigurationValidFrom: ").append(toIndentedString(newClientConfigurationValidFrom)).append("\n");
    sb.append("    globalShortId: ").append(toIndentedString(globalShortId)).append("\n");
    sb.append("    purchaseDate: ").append(toIndentedString(purchaseDate)).append("\n");
    sb.append("    commissioningDate: ").append(toIndentedString(commissioningDate)).append("\n");
    sb.append("    decommissioningDate: ").append(toIndentedString(decommissioningDate)).append("\n");
    sb.append("    terminalType: ").append(toIndentedString(terminalType)).append("\n");
    sb.append("    updatedByPrincipalId: ").append(toIndentedString(updatedByPrincipalId)).append("\n");
    sb.append("    cashRegisterId: ").append(toIndentedString(cashRegisterId)).append("\n");
    sb.append("    cashRegisterBrand: ").append(toIndentedString(cashRegisterBrand)).append("\n");
    sb.append("    cashRegisterModelname: ").append(toIndentedString(cashRegisterModelname)).append("\n");
    sb.append("    cashRegisterSoftwareBrand: ").append(toIndentedString(cashRegisterSoftwareBrand)).append("\n");
    sb.append("    cashRegisterSoftwareVersion: ").append(toIndentedString(cashRegisterSoftwareVersion)).append("\n");
    sb.append("    cashRegisterSoftwareCompany: ").append(toIndentedString(cashRegisterSoftwareCompany)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("organisationCaption");
    openapiFields.add("companyCaption");
    openapiFields.add("storeCaption");
    openapiFields.add("storeNumber");
    openapiFields.add("alert");
    openapiFields.add("canEdit");
    openapiFields.add("canDelete");
    openapiFields.add("configurationCaption");
    openapiFields.add("newConfigurationCaption");
    openapiFields.add("deactivationDate");
    openapiFields.add("lastLicenseUsage");
    openapiFields.add("isDeactivated");
    openapiFields.add("isArchived");
    openapiFields.add("version");
    openapiFields.add("terminalId");
    openapiFields.add("storeId");
    openapiFields.add("terminalNumber");
    openapiFields.add("fiscalCountry");
    openapiFields.add("caption");
    openapiFields.add("platformType");
    openapiFields.add("isTest");
    openapiFields.add("archiveDate");
    openapiFields.add("clientConfigurationId");
    openapiFields.add("newClientConfigurationId");
    openapiFields.add("newClientConfigurationValidFrom");
    openapiFields.add("globalShortId");
    openapiFields.add("purchaseDate");
    openapiFields.add("commissioningDate");
    openapiFields.add("decommissioningDate");
    openapiFields.add("terminalType");
    openapiFields.add("updatedByPrincipalId");
    openapiFields.add("cashRegisterId");
    openapiFields.add("cashRegisterBrand");
    openapiFields.add("cashRegisterModelname");
    openapiFields.add("cashRegisterSoftwareBrand");
    openapiFields.add("cashRegisterSoftwareVersion");
    openapiFields.add("cashRegisterSoftwareCompany");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TerminalModel
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TerminalModel.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TerminalModel is not found in the empty JSON string", TerminalModel.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TerminalModel.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TerminalModel` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("organisationCaption") != null && !jsonObj.get("organisationCaption").isJsonNull()) && !jsonObj.get("organisationCaption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `organisationCaption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("organisationCaption").toString()));
      }
      if ((jsonObj.get("companyCaption") != null && !jsonObj.get("companyCaption").isJsonNull()) && !jsonObj.get("companyCaption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `companyCaption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("companyCaption").toString()));
      }
      if ((jsonObj.get("storeCaption") != null && !jsonObj.get("storeCaption").isJsonNull()) && !jsonObj.get("storeCaption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeCaption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeCaption").toString()));
      }
      if ((jsonObj.get("storeNumber") != null && !jsonObj.get("storeNumber").isJsonNull()) && !jsonObj.get("storeNumber").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeNumber` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeNumber").toString()));
      }
      if ((jsonObj.get("configurationCaption") != null && !jsonObj.get("configurationCaption").isJsonNull()) && !jsonObj.get("configurationCaption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `configurationCaption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("configurationCaption").toString()));
      }
      if ((jsonObj.get("newConfigurationCaption") != null && !jsonObj.get("newConfigurationCaption").isJsonNull()) && !jsonObj.get("newConfigurationCaption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `newConfigurationCaption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("newConfigurationCaption").toString()));
      }
      if ((jsonObj.get("version") != null && !jsonObj.get("version").isJsonNull()) && !jsonObj.get("version").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `version` to be a primitive type in the JSON string but got `%s`", jsonObj.get("version").toString()));
      }
      if ((jsonObj.get("terminalId") != null && !jsonObj.get("terminalId").isJsonNull()) && !jsonObj.get("terminalId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `terminalId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("terminalId").toString()));
      }
      if ((jsonObj.get("storeId") != null && !jsonObj.get("storeId").isJsonNull()) && !jsonObj.get("storeId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeId").toString()));
      }
      if ((jsonObj.get("terminalNumber") != null && !jsonObj.get("terminalNumber").isJsonNull()) && !jsonObj.get("terminalNumber").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `terminalNumber` to be a primitive type in the JSON string but got `%s`", jsonObj.get("terminalNumber").toString()));
      }
      if ((jsonObj.get("caption") != null && !jsonObj.get("caption").isJsonNull()) && !jsonObj.get("caption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption").toString()));
      }
      if ((jsonObj.get("clientConfigurationId") != null && !jsonObj.get("clientConfigurationId").isJsonNull()) && !jsonObj.get("clientConfigurationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `clientConfigurationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("clientConfigurationId").toString()));
      }
      if ((jsonObj.get("newClientConfigurationId") != null && !jsonObj.get("newClientConfigurationId").isJsonNull()) && !jsonObj.get("newClientConfigurationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `newClientConfigurationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("newClientConfigurationId").toString()));
      }
      if ((jsonObj.get("globalShortId") != null && !jsonObj.get("globalShortId").isJsonNull()) && !jsonObj.get("globalShortId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `globalShortId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("globalShortId").toString()));
      }
      if ((jsonObj.get("updatedByPrincipalId") != null && !jsonObj.get("updatedByPrincipalId").isJsonNull()) && !jsonObj.get("updatedByPrincipalId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `updatedByPrincipalId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("updatedByPrincipalId").toString()));
      }
      if ((jsonObj.get("cashRegisterId") != null && !jsonObj.get("cashRegisterId").isJsonNull()) && !jsonObj.get("cashRegisterId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterId").toString()));
      }
      if ((jsonObj.get("cashRegisterBrand") != null && !jsonObj.get("cashRegisterBrand").isJsonNull()) && !jsonObj.get("cashRegisterBrand").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterBrand` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterBrand").toString()));
      }
      if ((jsonObj.get("cashRegisterModelname") != null && !jsonObj.get("cashRegisterModelname").isJsonNull()) && !jsonObj.get("cashRegisterModelname").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterModelname` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterModelname").toString()));
      }
      if ((jsonObj.get("cashRegisterSoftwareBrand") != null && !jsonObj.get("cashRegisterSoftwareBrand").isJsonNull()) && !jsonObj.get("cashRegisterSoftwareBrand").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterSoftwareBrand` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterSoftwareBrand").toString()));
      }
      if ((jsonObj.get("cashRegisterSoftwareVersion") != null && !jsonObj.get("cashRegisterSoftwareVersion").isJsonNull()) && !jsonObj.get("cashRegisterSoftwareVersion").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterSoftwareVersion` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterSoftwareVersion").toString()));
      }
      if ((jsonObj.get("cashRegisterSoftwareCompany") != null && !jsonObj.get("cashRegisterSoftwareCompany").isJsonNull()) && !jsonObj.get("cashRegisterSoftwareCompany").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterSoftwareCompany` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterSoftwareCompany").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TerminalModel.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TerminalModel' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TerminalModel> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TerminalModel.class));

       return (TypeAdapter<T>) new TypeAdapter<TerminalModel>() {
           @Override
           public void write(JsonWriter out, TerminalModel value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TerminalModel read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TerminalModel given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TerminalModel
  * @throws IOException if the JSON string is invalid with respect to TerminalModel
  */
  public static TerminalModel fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TerminalModel.class);
  }

 /**
  * Convert an instance of TerminalModel to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

