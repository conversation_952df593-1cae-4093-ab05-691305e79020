/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.ApiKey;
import com.untill.retailforce.model.EntityPermissions;
import com.untill.retailforce.model.EntitySecurityPageResultModel;
import com.untill.retailforce.model.EntityTypes;
import java.io.File;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.InvitationInfoModel;
import com.untill.retailforce.model.InvitationModel;
import com.untill.retailforce.model.Principal;
import com.untill.retailforce.model.ProfilePictureModel;
import com.untill.retailforce.model.RegistrationModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for SecurityApi
 */
@Disabled
public class SecurityApiTest {

    private final SecurityApi api = new SecurityApi();

    /**
     * Removes an principal access from an entity.
     *
     * If principal access was inherited from parent distributor this method will fail.                To add a principal again to an entity you have to invite the principal again with the invitation.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityAccessEntityIdDeleteTest() throws ApiException {
        UUID entityId = null;
        UUID principalId = null;
        api.apiV10SecurityAccessEntityIdDelete(entityId, principalId);
        // TODO: test validations
    }

    /**
     * Returns a list of principals which have access to the given entity. Entity must be of type organization or distributor.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityAccessEntityIdGetTest() throws ApiException {
        UUID entityId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        EntitySecurityPageResultModel response = api.apiV10SecurityAccessEntityIdGet(entityId, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Returns all api keys for the given entity (organisation or distributor). The given entity must exist (organisation or distributor).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityApikeyEntityIdGetTest() throws ApiException {
        UUID entityId = null;
        List<ApiKey> response = api.apiV10SecurityApikeyEntityIdGet(entityId);
        // TODO: test validations
    }

    /**
     * Creates an api key for an organisation or a distributor. The given entity must exist (organisation or distributor).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityApikeyEntityIdPostTest() throws ApiException {
        UUID entityId = null;
        ApiKey response = api.apiV10SecurityApikeyEntityIdPost(entityId);
        // TODO: test validations
    }

    /**
     * Self assign the current user to an entity, without the whole invitation process  this is only allowed for retail force users
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityAssignEntityTypeEntityIdPutTest() throws ApiException {
        UUID entityId = null;
        EntityTypes entityType = null;
        api.apiV10SecurityAssignEntityTypeEntityIdPut(entityId, entityType);
        // TODO: test validations
    }

    /**
     * Creates a new invitation in the backend.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityInvitationEntityTypeEntityIdPostTest() throws ApiException {
        String entityType = null;
        UUID entityId = null;
        String email = null;
        InvitationModel response = api.apiV10SecurityInvitationEntityTypeEntityIdPost(entityType, entityId, email);
        // TODO: test validations
    }

    /**
     * Returns all open invitation which where sent for current organisation/distributor where authenticated principal has access.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityInvitationGetTest() throws ApiException {
        List<InvitationModel> response = api.apiV10SecurityInvitationGet();
        // TODO: test validations
    }

    /**
     * Accepts an invitation.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityInvitationInvitationIdAcceptPutTest() throws ApiException {
        UUID invitationId = null;
        api.apiV10SecurityInvitationInvitationIdAcceptPut(invitationId);
        // TODO: test validations
    }

    /**
     * Declines an invitation.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityInvitationInvitationIdDeclinePutTest() throws ApiException {
        UUID invitationId = null;
        api.apiV10SecurityInvitationInvitationIdDeclinePut(invitationId);
        // TODO: test validations
    }

    /**
     * Deletes an existing invitation. If invitation does not exists, nothing happens.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityInvitationInvitationIdDeleteTest() throws ApiException {
        UUID invitationId = null;
        api.apiV10SecurityInvitationInvitationIdDelete(invitationId);
        // TODO: test validations
    }

    /**
     * Returns an information if the invitation exists, the email of the invited user and if the user already exists in the backend.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityInvitationInvitationIdInfoGetTest() throws ApiException {
        UUID invitationId = null;
        InvitationInfoModel response = api.apiV10SecurityInvitationInvitationIdInfoGet(invitationId);
        // TODO: test validations
    }

    /**
     * Resends an existing invitation.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityInvitationInvitationIdPutTest() throws ApiException {
        UUID invitationId = null;
        InvitationModel response = api.apiV10SecurityInvitationInvitationIdPut(invitationId);
        // TODO: test validations
    }

    /**
     * Returns all open invitations for the authenticated principal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityInvitationUserGetTest() throws ApiException {
        List<InvitationModel> response = api.apiV10SecurityInvitationUserGet();
        // TODO: test validations
    }

    /**
     * Send / Resend password reset email
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPasswordResetLinkPostTest() throws ApiException {
        String email = null;
        api.apiV10SecurityPasswordResetLinkPost(email);
        // TODO: test validations
    }

    /**
     * Send / Resend password reset email
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPasswordResetTokenHeadTest() throws ApiException {
        String token = null;
        api.apiV10SecurityPasswordResetTokenHead(token);
        // TODO: test validations
    }

    /**
     * Reset the password based on a reset password token
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPasswordResetTokenPostTest() throws ApiException {
        String token = null;
        String email = null;
        String newPassword = null;
        String response = api.apiV10SecurityPasswordResetTokenPost(token, email, newPassword);
        // TODO: test validations
    }

    /**
     * Get display permissions
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPermissionsDisplayGetTest() throws ApiException {
        UUID organizationId = null;
        List<EntityTypes> response = api.apiV10SecurityPermissionsDisplayGet(organizationId);
        // TODO: test validations
    }

    /**
     * Get entity permissions for requested type.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPermissionsGetTest() throws ApiException {
        EntityTypes type = null;
        String entityId = null;
        UUID organizationId = null;
        String entityVersion = null;
        List<EntityPermissions> response = api.apiV10SecurityPermissionsGet(type, entityId, organizationId, entityVersion);
        // TODO: test validations
    }

    /**
     * Returns the user information about the actual logged on user.
     *
     * You must be authenticated to use this method.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPrincipalGetTest() throws ApiException {
        Principal response = api.apiV10SecurityPrincipalGet();
        // TODO: test validations
    }

    /**
     * Possiblity to lock a principal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPrincipalLockPrincipalIdPutTest() throws ApiException {
        UUID principalId = null;
        api.apiV10SecurityPrincipalLockPrincipalIdPut(principalId);
        // TODO: test validations
    }

    /**
     * Returns all principals which are available for this organization.
     *
     * Returns only direct associated principals (not associated with distributor).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetTest() throws ApiException {
        UUID organisationId = null;
        List<GuidSimpleObject> response = api.apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet(organisationId);
        // TODO: test validations
    }

    /**
     * Loads the user profile image as url or dataUrl (can be optimized to return multiple sizes at once with retina, but then it would be better to use a CDN than generating all of them)
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPrincipalPictureGetTest() throws ApiException {
        Boolean thumbnail = null;
        ProfilePictureModel response = api.apiV10SecurityPrincipalPictureGet(thumbnail);
        // TODO: test validations
    }

    /**
     * Return profile picture url or data url
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPrincipalPicturePostTest() throws ApiException {
        File _file = null;
        ProfilePictureModel response = api.apiV10SecurityPrincipalPicturePost(_file);
        // TODO: test validations
    }

    /**
     * Creates the principal with the given registration model and does authentication (refresh token cookie).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPrincipalPostTest() throws ApiException {
        RegistrationModel registrationModel = null;
        String response = api.apiV10SecurityPrincipalPost(registrationModel);
        // TODO: test validations
    }

    /**
     * Updates principal master data in the data store.
     *
     * The principal must exist.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPrincipalPutTest() throws ApiException {
        Principal principal = null;
        Principal response = api.apiV10SecurityPrincipalPut(principal);
        // TODO: test validations
    }

    /**
     * Unlocks the given principal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecurityPrincipalUnlockPrincipalIdPutTest() throws ApiException {
        UUID principalId = null;
        api.apiV10SecurityPrincipalUnlockPrincipalIdPut(principalId);
        // TODO: test validations
    }

    /**
     * Changes the secret for the given authentication key.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SecuritySecretPostTest() throws ApiException {
        String authenticationKey = null;
        String oldSecret = null;
        String newSecret = null;
        String response = api.apiV10SecuritySecretPost(authenticationKey, oldSecret, newSecret);
        // TODO: test validations
    }

}
