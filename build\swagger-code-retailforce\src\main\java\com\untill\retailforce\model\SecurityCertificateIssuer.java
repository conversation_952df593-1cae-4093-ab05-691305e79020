/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.annotations.SerializedName;

import java.io.IOException;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

/**
 * Gets or Sets SecurityCertificateIssuer
 */
@JsonAdapter(SecurityCertificateIssuer.Adapter.class)
public enum SecurityCertificateIssuer {
  
  ATRUST("aTrust"),
  
  GLOBALTRUST("globalTrust"),
  
  PRIMESIGN("primeSign"),
  
  TEST("test"),
  
  UNKOWN("unkown");

  private String value;

  SecurityCertificateIssuer(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  public static SecurityCertificateIssuer fromValue(String value) {
    for (SecurityCertificateIssuer b : SecurityCertificateIssuer.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }

  public static class Adapter extends TypeAdapter<SecurityCertificateIssuer> {
    @Override
    public void write(final JsonWriter jsonWriter, final SecurityCertificateIssuer enumeration) throws IOException {
      jsonWriter.value(enumeration.getValue());
    }

    @Override
    public SecurityCertificateIssuer read(final JsonReader jsonReader) throws IOException {
      String value = jsonReader.nextString();
      return SecurityCertificateIssuer.fromValue(value);
    }
  }
}

