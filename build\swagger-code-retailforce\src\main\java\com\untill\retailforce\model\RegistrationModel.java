/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.AuthenticationType;
import java.io.IOException;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Data to create a new principal with security access to the given entity.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class RegistrationModel {
  public static final String SERIALIZED_NAME_NAME1 = "name1";
  @SerializedName(SERIALIZED_NAME_NAME1)
  private String name1;

  public static final String SERIALIZED_NAME_NAME2 = "name2";
  @SerializedName(SERIALIZED_NAME_NAME2)
  private String name2;

  public static final String SERIALIZED_NAME_CULTURE_INFO = "cultureInfo";
  @SerializedName(SERIALIZED_NAME_CULTURE_INFO)
  private String cultureInfo;

  public static final String SERIALIZED_NAME_EMAIL = "email";
  @SerializedName(SERIALIZED_NAME_EMAIL)
  private String email;

  public static final String SERIALIZED_NAME_TYPE = "type";
  @SerializedName(SERIALIZED_NAME_TYPE)
  private AuthenticationType type;

  public static final String SERIALIZED_NAME_SECRET = "secret";
  @SerializedName(SERIALIZED_NAME_SECRET)
  private String secret;

  public static final String SERIALIZED_NAME_INVITATION_ID = "invitationId";
  @SerializedName(SERIALIZED_NAME_INVITATION_ID)
  private UUID invitationId;

  public RegistrationModel() {
  }

  public RegistrationModel name1(String name1) {
    
    this.name1 = name1;
    return this;
  }

   /**
   * The name1 (firstname) of the new principal.
   * @return name1
  **/
  @javax.annotation.Nullable
  public String getName1() {
    return name1;
  }


  public void setName1(String name1) {
    this.name1 = name1;
  }


  public RegistrationModel name2(String name2) {
    
    this.name2 = name2;
    return this;
  }

   /**
   * The name2 (lastname) of the new principal.
   * @return name2
  **/
  @javax.annotation.Nullable
  public String getName2() {
    return name2;
  }


  public void setName2(String name2) {
    this.name2 = name2;
  }


  public RegistrationModel cultureInfo(String cultureInfo) {
    
    this.cultureInfo = cultureInfo;
    return this;
  }

   /**
   * The language of the new principal.
   * @return cultureInfo
  **/
  @javax.annotation.Nullable
  public String getCultureInfo() {
    return cultureInfo;
  }


  public void setCultureInfo(String cultureInfo) {
    this.cultureInfo = cultureInfo;
  }


  public RegistrationModel email(String email) {
    
    this.email = email;
    return this;
  }

   /**
   * The email address of the new principal.
   * @return email
  **/
  @javax.annotation.Nullable
  public String getEmail() {
    return email;
  }


  public void setEmail(String email) {
    this.email = email;
  }


  public RegistrationModel type(AuthenticationType type) {
    
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @javax.annotation.Nullable
  public AuthenticationType getType() {
    return type;
  }


  public void setType(AuthenticationType type) {
    this.type = type;
  }


  public RegistrationModel secret(String secret) {
    
    this.secret = secret;
    return this;
  }

   /**
   * The secret of the new logon.
   * @return secret
  **/
  @javax.annotation.Nullable
  public String getSecret() {
    return secret;
  }


  public void setSecret(String secret) {
    this.secret = secret;
  }


  public RegistrationModel invitationId(UUID invitationId) {
    
    this.invitationId = invitationId;
    return this;
  }

   /**
   * The id of the invitation.
   * @return invitationId
  **/
  @javax.annotation.Nullable
  public UUID getInvitationId() {
    return invitationId;
  }


  public void setInvitationId(UUID invitationId) {
    this.invitationId = invitationId;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RegistrationModel registrationModel = (RegistrationModel) o;
    return Objects.equals(this.name1, registrationModel.name1) &&
        Objects.equals(this.name2, registrationModel.name2) &&
        Objects.equals(this.cultureInfo, registrationModel.cultureInfo) &&
        Objects.equals(this.email, registrationModel.email) &&
        Objects.equals(this.type, registrationModel.type) &&
        Objects.equals(this.secret, registrationModel.secret) &&
        Objects.equals(this.invitationId, registrationModel.invitationId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name1, name2, cultureInfo, email, type, secret, invitationId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RegistrationModel {\n");
    sb.append("    name1: ").append(toIndentedString(name1)).append("\n");
    sb.append("    name2: ").append(toIndentedString(name2)).append("\n");
    sb.append("    cultureInfo: ").append(toIndentedString(cultureInfo)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    secret: ").append(toIndentedString(secret)).append("\n");
    sb.append("    invitationId: ").append(toIndentedString(invitationId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("name1");
    openapiFields.add("name2");
    openapiFields.add("cultureInfo");
    openapiFields.add("email");
    openapiFields.add("type");
    openapiFields.add("secret");
    openapiFields.add("invitationId");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to RegistrationModel
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!RegistrationModel.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in RegistrationModel is not found in the empty JSON string", RegistrationModel.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!RegistrationModel.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `RegistrationModel` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("name1") != null && !jsonObj.get("name1").isJsonNull()) && !jsonObj.get("name1").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `name1` to be a primitive type in the JSON string but got `%s`", jsonObj.get("name1").toString()));
      }
      if ((jsonObj.get("name2") != null && !jsonObj.get("name2").isJsonNull()) && !jsonObj.get("name2").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `name2` to be a primitive type in the JSON string but got `%s`", jsonObj.get("name2").toString()));
      }
      if ((jsonObj.get("cultureInfo") != null && !jsonObj.get("cultureInfo").isJsonNull()) && !jsonObj.get("cultureInfo").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cultureInfo` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cultureInfo").toString()));
      }
      if ((jsonObj.get("email") != null && !jsonObj.get("email").isJsonNull()) && !jsonObj.get("email").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `email` to be a primitive type in the JSON string but got `%s`", jsonObj.get("email").toString()));
      }
      if ((jsonObj.get("secret") != null && !jsonObj.get("secret").isJsonNull()) && !jsonObj.get("secret").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `secret` to be a primitive type in the JSON string but got `%s`", jsonObj.get("secret").toString()));
      }
      if ((jsonObj.get("invitationId") != null && !jsonObj.get("invitationId").isJsonNull()) && !jsonObj.get("invitationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `invitationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("invitationId").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!RegistrationModel.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'RegistrationModel' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<RegistrationModel> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(RegistrationModel.class));

       return (TypeAdapter<T>) new TypeAdapter<RegistrationModel>() {
           @Override
           public void write(JsonWriter out, RegistrationModel value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public RegistrationModel read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of RegistrationModel given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of RegistrationModel
  * @throws IOException if the JSON string is invalid with respect to RegistrationModel
  */
  public static RegistrationModel fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, RegistrationModel.class);
  }

 /**
  * Convert an instance of RegistrationModel to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

