/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.ReceiptData;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for ReceiptDataScrollResultModel
 */
public class ReceiptDataScrollResultModelTest {
    private final ReceiptDataScrollResultModel model = new ReceiptDataScrollResultModel();

    /**
     * Model tests for ReceiptDataScrollResultModel
     */
    @Test
    public void testReceiptDataScrollResultModel() {
        // TODO: test ReceiptDataScrollResultModel
    }

    /**
     * Test the property 'items'
     */
    @Test
    public void itemsTest() {
        // TODO: test items
    }

    /**
     * Test the property 'continuationToken'
     */
    @Test
    public void continuationTokenTest() {
        // TODO: test continuationToken
    }

    /**
     * Test the property 'query'
     */
    @Test
    public void queryTest() {
        // TODO: test query
    }

}
