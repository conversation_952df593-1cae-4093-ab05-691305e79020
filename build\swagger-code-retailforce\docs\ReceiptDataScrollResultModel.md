

# ReceiptDataScrollResultModel

Model for infinite scrolling at the backend.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**items** | [**List&lt;ReceiptData&gt;**](ReceiptData.md) | The returned list of items. |  [optional] |
|**continuationToken** | **String** | The token to continue search. |  [optional] |
|**query** | **String** | The query of the search. |  [optional] |



