# DocumentApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10DocumentsDocumentOrganizationIdPost**](DocumentApi.md#apiV10DocumentsDocumentOrganizationIdPost) | **POST** /api/v1.0/documents/document/{organizationId} | Stores a document / fiscal response tuple as RetailForce.Fiscalisation.Model.Helper.CosmosDocument. |


<a id="apiV10DocumentsDocumentOrganizationIdPost"></a>
# **apiV10DocumentsDocumentOrganizationIdPost**
> apiV10DocumentsDocumentOrganizationIdPost(organizationId, cosmosDocument)

Stores a document / fiscal response tuple as RetailForce.Fiscalisation.Model.Helper.CosmosDocument.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.DocumentApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    DocumentApi apiInstance = new DocumentApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization id of the organization to store this cosmosDocument.
    CosmosDocument cosmosDocument = new CosmosDocument(); // CosmosDocument | The document / fiscal response to store to the cosmos database.
    try {
      apiInstance.apiV10DocumentsDocumentOrganizationIdPost(organizationId, cosmosDocument);
    } catch (ApiException e) {
      System.err.println("Exception when calling DocumentApi#apiV10DocumentsDocumentOrganizationIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization id of the organization to store this cosmosDocument. | |
| **cosmosDocument** | [**CosmosDocument**](CosmosDocument.md)| The document / fiscal response to store to the cosmos database. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

