# MasterDataDistributorsApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MasterdataDistributorsDistributorIdContractContractIdDelete**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractContractIdDelete) | **DELETE** /api/v1.0/masterdata/distributors/{distributorId}/contract/{contractId} | Deletes a contract. |
| [**apiV10MasterdataDistributorsDistributorIdContractContractIdPut**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractContractIdPut) | **PUT** /api/v1.0/masterdata/distributors/{distributorId}/contract/{contractId} | Updates the given contract of the distributor. |
| [**apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut) | **PUT** /api/v1.0/masterdata/distributors/{distributorId}/contract/{distributorContractId}/onboarding/finish | Finishes onboarding for distributor |
| [**apiV10MasterdataDistributorsDistributorIdContractPost**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractPost) | **POST** /api/v1.0/masterdata/distributors/{distributorId}/contract | Creates a new contract for a distributor. |
| [**apiV10MasterdataDistributorsDistributorIdContractsContractIdGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractsContractIdGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/contracts/{contractId} | Returns the requested contract. |
| [**apiV10MasterdataDistributorsDistributorIdContractsGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractsGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/contracts | Returns all contracts for a distributor. |
| [**apiV10MasterdataDistributorsDistributorIdDelete**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdDelete) | **DELETE** /api/v1.0/masterdata/distributors/{distributorId} | Deletes an distributor from cloud store. |
| [**apiV10MasterdataDistributorsDistributorIdGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId} | Returns the distributor requested by the given id. |
| [**apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/licenseGroups | Returns the available license groups. |
| [**apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut) | **PUT** /api/v1.0/masterdata/distributors/{distributorId}/onboarding/finish | Finishes onboarding for distributor |
| [**apiV10MasterdataDistributorsDistributorIdPut**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdPut) | **PUT** /api/v1.0/masterdata/distributors/{distributorId} | Updates an distributor in the cloud store. |
| [**apiV10MasterdataDistributorsDistributorIdSimpleGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdSimpleGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/simple | Returns a simple distributorRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. |
| [**apiV10MasterdataDistributorsGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsGet) | **GET** /api/v1.0/masterdata/distributors | Returns all distributors for the authenticated user. |
| [**apiV10MasterdataDistributorsHierarchyAllGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsHierarchyAllGet) | **GET** /api/v1.0/masterdata/distributors/hierarchy/all | Returns all distributors based on the parent distributor (only retailforce users) |
| [**apiV10MasterdataDistributorsHierarchyBreadcrumbGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsHierarchyBreadcrumbGet) | **GET** /api/v1.0/masterdata/distributors/hierarchy/breadcrumb | Returns the hierarchy of the actual distributor as a breadcrumb object for navigation. |
| [**apiV10MasterdataDistributorsHierarchyGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsHierarchyGet) | **GET** /api/v1.0/masterdata/distributors/hierarchy | Returns distributor hierarchy. |
| [**apiV10MasterdataDistributorsLevelsGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsLevelsGet) | **GET** /api/v1.0/masterdata/distributors/levels | Returns the available distributor levels. |
| [**apiV10MasterdataDistributorsPost**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsPost) | **POST** /api/v1.0/masterdata/distributors | Creates a new distributor in the cloud store. |
| [**apiV10MasterdataDistributorsSimpleGet**](MasterDataDistributorsApi.md#apiV10MasterdataDistributorsSimpleGet) | **GET** /api/v1.0/masterdata/distributors/simple | Returns all distributors for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. |


<a id="apiV10MasterdataDistributorsDistributorIdContractContractIdDelete"></a>
# **apiV10MasterdataDistributorsDistributorIdContractContractIdDelete**
> apiV10MasterdataDistributorsDistributorIdContractContractIdDelete(distributorId, contractId)

Deletes a contract.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor id of the contract to delete.
    UUID contractId = UUID.randomUUID(); // UUID | The id of the contract to delete.
    try {
      apiInstance.apiV10MasterdataDistributorsDistributorIdContractContractIdDelete(distributorId, contractId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdContractContractIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The distributor id of the contract to delete. | |
| **contractId** | **UUID**| The id of the contract to delete. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdContractContractIdPut"></a>
# **apiV10MasterdataDistributorsDistributorIdContractContractIdPut**
> DistributorContractModel apiV10MasterdataDistributorsDistributorIdContractContractIdPut(distributorId, contractId, distributorContract)

Updates the given contract of the distributor.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the distributor where the contract will be updated.
    UUID contractId = UUID.randomUUID(); // UUID | The id of the contract to update.
    DistributorContract distributorContract = new DistributorContract(); // DistributorContract | The new contract data.
    try {
      DistributorContractModel result = apiInstance.apiV10MasterdataDistributorsDistributorIdContractContractIdPut(distributorId, contractId, distributorContract);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdContractContractIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the distributor where the contract will be updated. | |
| **contractId** | **UUID**| The id of the contract to update. | |
| **distributorContract** | [**DistributorContract**](DistributorContract.md)| The new contract data. | [optional] |

### Return type

[**DistributorContractModel**](DistributorContractModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut"></a>
# **apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut**
> apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut(distributorId, distributorContractId, onboardingFinishData)

Finishes onboarding for distributor

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the distributor
    UUID distributorContractId = UUID.randomUUID(); // UUID | The id of the distributor contract
    OnboardingFinishData onboardingFinishData = new OnboardingFinishData(); // OnboardingFinishData | Onboarding finish data
    try {
      apiInstance.apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut(distributorId, distributorContractId, onboardingFinishData);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the distributor | |
| **distributorContractId** | **UUID**| The id of the distributor contract | |
| **onboardingFinishData** | [**OnboardingFinishData**](OnboardingFinishData.md)| Onboarding finish data | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdContractPost"></a>
# **apiV10MasterdataDistributorsDistributorIdContractPost**
> DistributorContractModel apiV10MasterdataDistributorsDistributorIdContractPost(distributorId, distributorContract)

Creates a new contract for a distributor.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor id of the distributor where the contract has to be created.
    DistributorContract distributorContract = new DistributorContract(); // DistributorContract | The contract to create.
    try {
      DistributorContractModel result = apiInstance.apiV10MasterdataDistributorsDistributorIdContractPost(distributorId, distributorContract);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdContractPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The distributor id of the distributor where the contract has to be created. | |
| **distributorContract** | [**DistributorContract**](DistributorContract.md)| The contract to create. | [optional] |

### Return type

[**DistributorContractModel**](DistributorContractModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdContractsContractIdGet"></a>
# **apiV10MasterdataDistributorsDistributorIdContractsContractIdGet**
> DistributorContractModel apiV10MasterdataDistributorsDistributorIdContractsContractIdGet(distributorId, contractId)

Returns the requested contract.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The if of the distributor of the requested contract.
    UUID contractId = UUID.randomUUID(); // UUID | The id of the requested contract.
    try {
      DistributorContractModel result = apiInstance.apiV10MasterdataDistributorsDistributorIdContractsContractIdGet(distributorId, contractId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdContractsContractIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The if of the distributor of the requested contract. | |
| **contractId** | **UUID**| The id of the requested contract. | |

### Return type

[**DistributorContractModel**](DistributorContractModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdContractsGet"></a>
# **apiV10MasterdataDistributorsDistributorIdContractsGet**
> DistributorContractModelPageResultModel apiV10MasterdataDistributorsDistributorIdContractsGet(distributorId, pageOffset, pageSize, searchString)

Returns all contracts for a distributor.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor where the contacts are requested.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      DistributorContractModelPageResultModel result = apiInstance.apiV10MasterdataDistributorsDistributorIdContractsGet(distributorId, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdContractsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The distributor where the contacts are requested. | |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**DistributorContractModelPageResultModel**](DistributorContractModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdDelete"></a>
# **apiV10MasterdataDistributorsDistributorIdDelete**
> apiV10MasterdataDistributorsDistributorIdDelete(distributorId)

Deletes an distributor from cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the distributor which should be deleted.
    try {
      apiInstance.apiV10MasterdataDistributorsDistributorIdDelete(distributorId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the distributor which should be deleted. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdGet"></a>
# **apiV10MasterdataDistributorsDistributorIdGet**
> DistributorModel apiV10MasterdataDistributorsDistributorIdGet(distributorId)

Returns the distributor requested by the given id.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the requested distributor.
    try {
      DistributorModel result = apiInstance.apiV10MasterdataDistributorsDistributorIdGet(distributorId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the requested distributor. | |

### Return type

[**DistributorModel**](DistributorModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet"></a>
# **apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet**
> StringSimpleObjectPageResultModel apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet(distributorId)

Returns the available license groups.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the distributor for the available license groups.
    try {
      StringSimpleObjectPageResultModel result = apiInstance.apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet(distributorId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the distributor for the available license groups. | |

### Return type

[**StringSimpleObjectPageResultModel**](StringSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut"></a>
# **apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut**
> apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut(distributorId, onboardingFinishDistributorData)

Finishes onboarding for distributor

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the distributor
    OnboardingFinishDistributorData onboardingFinishDistributorData = new OnboardingFinishDistributorData(); // OnboardingFinishDistributorData | Onboarding finish data
    try {
      apiInstance.apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut(distributorId, onboardingFinishDistributorData);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the distributor | |
| **onboardingFinishDistributorData** | [**OnboardingFinishDistributorData**](OnboardingFinishDistributorData.md)| Onboarding finish data | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdPut"></a>
# **apiV10MasterdataDistributorsDistributorIdPut**
> DistributorModel apiV10MasterdataDistributorsDistributorIdPut(distributorId, distributor)

Updates an distributor in the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The id of the distributor to be updated.
    Distributor distributor = new Distributor(); // Distributor | The distributor object to update the distributor.
    try {
      DistributorModel result = apiInstance.apiV10MasterdataDistributorsDistributorIdPut(distributorId, distributor);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The id of the distributor to be updated. | |
| **distributor** | [**Distributor**](Distributor.md)| The distributor object to update the distributor. | [optional] |

### Return type

[**DistributorModel**](DistributorModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdSimpleGet"></a>
# **apiV10MasterdataDistributorsDistributorIdSimpleGet**
> GuidSimpleObject apiV10MasterdataDistributorsDistributorIdSimpleGet(distributorId)

Returns a simple distributorRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | 
    try {
      GuidSimpleObject result = apiInstance.apiV10MasterdataDistributorsDistributorIdSimpleGet(distributorId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsDistributorIdSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**|  | |

### Return type

[**GuidSimpleObject**](GuidSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsGet"></a>
# **apiV10MasterdataDistributorsGet**
> DistributorModelPageResultModel apiV10MasterdataDistributorsGet(pageOffset, pageSize, searchString)

Returns all distributors for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      DistributorModelPageResultModel result = apiInstance.apiV10MasterdataDistributorsGet(pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**DistributorModelPageResultModel**](DistributorModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsHierarchyAllGet"></a>
# **apiV10MasterdataDistributorsHierarchyAllGet**
> List&lt;DistributorModel&gt; apiV10MasterdataDistributorsHierarchyAllGet(parentDistributorId, searchString)

Returns all distributors based on the parent distributor (only retailforce users)

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID parentDistributorId = UUID.randomUUID(); // UUID | parent distributor id
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      List<DistributorModel> result = apiInstance.apiV10MasterdataDistributorsHierarchyAllGet(parentDistributorId, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsHierarchyAllGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **parentDistributorId** | **UUID**| parent distributor id | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**List&lt;DistributorModel&gt;**](DistributorModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsHierarchyBreadcrumbGet"></a>
# **apiV10MasterdataDistributorsHierarchyBreadcrumbGet**
> GuidBreadCrumb apiV10MasterdataDistributorsHierarchyBreadcrumbGet(distributorId, isClearingRun)

Returns the hierarchy of the actual distributor as a breadcrumb object for navigation.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The actual distributor, if not supplied first level is used.
    Boolean isClearingRun = true; // Boolean | Breadcrumb for Clearing Run.
    try {
      GuidBreadCrumb result = apiInstance.apiV10MasterdataDistributorsHierarchyBreadcrumbGet(distributorId, isClearingRun);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsHierarchyBreadcrumbGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The actual distributor, if not supplied first level is used. | [optional] |
| **isClearingRun** | **Boolean**| Breadcrumb for Clearing Run. | [optional] |

### Return type

[**GuidBreadCrumb**](GuidBreadCrumb.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsHierarchyGet"></a>
# **apiV10MasterdataDistributorsHierarchyGet**
> GuidHierarchicalSimpleObjectPageResultModel apiV10MasterdataDistributorsHierarchyGet(distributorId, pageOffset, pageSize)

Returns distributor hierarchy.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor id where the sub distributors should be loaded. If empty the first level (or top level) of the available distributors are loaded.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    try {
      GuidHierarchicalSimpleObjectPageResultModel result = apiInstance.apiV10MasterdataDistributorsHierarchyGet(distributorId, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsHierarchyGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The distributor id where the sub distributors should be loaded. If empty the first level (or top level) of the available distributors are loaded. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |

### Return type

[**GuidHierarchicalSimpleObjectPageResultModel**](GuidHierarchicalSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsLevelsGet"></a>
# **apiV10MasterdataDistributorsLevelsGet**
> GuidSimpleObjectPageResultModel apiV10MasterdataDistributorsLevelsGet()

Returns the available distributor levels.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    try {
      GuidSimpleObjectPageResultModel result = apiInstance.apiV10MasterdataDistributorsLevelsGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsLevelsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**GuidSimpleObjectPageResultModel**](GuidSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsPost"></a>
# **apiV10MasterdataDistributorsPost**
> DistributorModel apiV10MasterdataDistributorsPost(distributor)

Creates a new distributor in the cloud store.

If RetailForce.Cloud.Model.Distributor.DistributorId set to System.Guid.Empty, then the distributor id will be generated by the service.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    Distributor distributor = new Distributor(); // Distributor | The distributor to create.
    try {
      DistributorModel result = apiInstance.apiV10MasterdataDistributorsPost(distributor);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributor** | [**Distributor**](Distributor.md)| The distributor to create. | [optional] |

### Return type

[**DistributorModel**](DistributorModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsSimpleGet"></a>
# **apiV10MasterdataDistributorsSimpleGet**
> GuidSimpleObjectPageResultModel apiV10MasterdataDistributorsSimpleGet(pageOffset, pageSize, searchString, all)

Returns all distributors for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataDistributorsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataDistributorsApi apiInstance = new MasterDataDistributorsApi(defaultClient);
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    Boolean all = true; // Boolean | Include all distributors not only the distributors which are assigned to the user (only works for retailforce users).
    try {
      GuidSimpleObjectPageResultModel result = apiInstance.apiV10MasterdataDistributorsSimpleGet(pageOffset, pageSize, searchString, all);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataDistributorsApi#apiV10MasterdataDistributorsSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |
| **all** | **Boolean**| Include all distributors not only the distributors which are assigned to the user (only works for retailforce users). | [optional] |

### Return type

[**GuidSimpleObjectPageResultModel**](GuidSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

