/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.ReceiptDataScrollResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ReceiptsApi
 */
@Disabled
public class ReceiptsApiTest {

    private final ReceiptsApi api = new ReceiptsApi();

    /**
     * Method to search and display receipts from digital receipt store (digital receipt license necessary).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ReceiptsSearchOrganizationIdGetTest() throws ApiException {
        UUID organizationId = null;
        UUID companyId = null;
        UUID storeId = null;
        UUID terminalId = null;
        OffsetDateTime dateFrom = null;
        OffsetDateTime dateTill = null;
        Integer documentType = null;
        String amount = null;
        String partner = null;
        String users = null;
        String items = null;
        String documentNumber = null;
        String documentId = null;
        Boolean cancelled = null;
        String paymentCardNumberMasked = null;
        ReceiptDataScrollResultModel response = api.apiV10ReceiptsSearchOrganizationIdGet(organizationId, companyId, storeId, terminalId, dateFrom, dateTill, documentType, amount, partner, users, items, documentNumber, documentId, cancelled, paymentCardNumberMasked);
        // TODO: test validations
    }

    /**
     * Method to continue search by continuationToken.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ReceiptsSearchOrganizationIdPostTest() throws ApiException {
        UUID organizationId = null;
        String continuationToken = null;
        String query = null;
        ReceiptDataScrollResultModel response = api.apiV10ReceiptsSearchOrganizationIdPost(organizationId, continuationToken, query);
        // TODO: test validations
    }

    /**
     * Method to search connected receipts (connected with processId, for instance to search for cancelled receipts and cancelling receipt).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ReceiptsSearchOrganizationIdProcessIdGetTest() throws ApiException {
        UUID organizationId = null;
        String processId = null;
        ReceiptDataScrollResultModel response = api.apiV10ReceiptsSearchOrganizationIdProcessIdGet(organizationId, processId);
        // TODO: test validations
    }

}
