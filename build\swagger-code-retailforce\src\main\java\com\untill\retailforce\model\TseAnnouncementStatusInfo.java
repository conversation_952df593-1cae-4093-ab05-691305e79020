/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.TseAnnouncementStatus;
import java.io.IOException;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * TseAnnouncementStatusInfo
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TseAnnouncementStatusInfo {
  public static final String SERIALIZED_NAME_STATUS = "status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private TseAnnouncementStatus status;

  public static final String SERIALIZED_NAME_ORGANISATION_ID = "organisationId";
  @SerializedName(SERIALIZED_NAME_ORGANISATION_ID)
  private UUID organisationId;

  public TseAnnouncementStatusInfo() {
  }

  public TseAnnouncementStatusInfo status(TseAnnouncementStatus status) {
    
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @javax.annotation.Nullable
  public TseAnnouncementStatus getStatus() {
    return status;
  }


  public void setStatus(TseAnnouncementStatus status) {
    this.status = status;
  }


  public TseAnnouncementStatusInfo organisationId(UUID organisationId) {
    
    this.organisationId = organisationId;
    return this;
  }

   /**
   * Organisation Id
   * @return organisationId
  **/
  @javax.annotation.Nullable
  public UUID getOrganisationId() {
    return organisationId;
  }


  public void setOrganisationId(UUID organisationId) {
    this.organisationId = organisationId;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TseAnnouncementStatusInfo tseAnnouncementStatusInfo = (TseAnnouncementStatusInfo) o;
    return Objects.equals(this.status, tseAnnouncementStatusInfo.status) &&
        Objects.equals(this.organisationId, tseAnnouncementStatusInfo.organisationId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, organisationId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TseAnnouncementStatusInfo {\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    organisationId: ").append(toIndentedString(organisationId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("status");
    openapiFields.add("organisationId");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TseAnnouncementStatusInfo
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TseAnnouncementStatusInfo.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TseAnnouncementStatusInfo is not found in the empty JSON string", TseAnnouncementStatusInfo.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TseAnnouncementStatusInfo.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TseAnnouncementStatusInfo` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("organisationId") != null && !jsonObj.get("organisationId").isJsonNull()) && !jsonObj.get("organisationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `organisationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("organisationId").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TseAnnouncementStatusInfo.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TseAnnouncementStatusInfo' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TseAnnouncementStatusInfo> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TseAnnouncementStatusInfo.class));

       return (TypeAdapter<T>) new TypeAdapter<TseAnnouncementStatusInfo>() {
           @Override
           public void write(JsonWriter out, TseAnnouncementStatusInfo value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TseAnnouncementStatusInfo read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TseAnnouncementStatusInfo given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TseAnnouncementStatusInfo
  * @throws IOException if the JSON string is invalid with respect to TseAnnouncementStatusInfo
  */
  public static TseAnnouncementStatusInfo fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TseAnnouncementStatusInfo.class);
  }

 /**
  * Convert an instance of TseAnnouncementStatusInfo to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

