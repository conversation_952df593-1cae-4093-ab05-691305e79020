/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.EntityTypes;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for SearchResultModel
 */
public class SearchResultModelTest {
    private final SearchResultModel model = new SearchResultModel();

    /**
     * Model tests for SearchResultModel
     */
    @Test
    public void testSearchResultModel() {
        // TODO: test SearchResultModel
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'entityId'
     */
    @Test
    public void entityIdTest() {
        // TODO: test entityId
    }

    /**
     * Test the property 'entityType'
     */
    @Test
    public void entityTypeTest() {
        // TODO: test entityType
    }

    /**
     * Test the property 'link'
     */
    @Test
    public void linkTest() {
        // TODO: test link
    }

    /**
     * Test the property 'icon'
     */
    @Test
    public void iconTest() {
        // TODO: test icon
    }

}
