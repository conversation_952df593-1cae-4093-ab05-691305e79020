/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.FiscalCountry;
import com.untill.retailforce.model.PlatformType;
import com.untill.retailforce.model.TerminalType;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for Terminal
 */
public class TerminalTest {
    private final Terminal model = new Terminal();

    /**
     * Model tests for Terminal
     */
    @Test
    public void testTerminal() {
        // TODO: test Terminal
    }

    /**
     * Test the property 'terminalId'
     */
    @Test
    public void terminalIdTest() {
        // TODO: test terminalId
    }

    /**
     * Test the property 'storeId'
     */
    @Test
    public void storeIdTest() {
        // TODO: test storeId
    }

    /**
     * Test the property 'terminalNumber'
     */
    @Test
    public void terminalNumberTest() {
        // TODO: test terminalNumber
    }

    /**
     * Test the property 'fiscalCountry'
     */
    @Test
    public void fiscalCountryTest() {
        // TODO: test fiscalCountry
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'platformType'
     */
    @Test
    public void platformTypeTest() {
        // TODO: test platformType
    }

    /**
     * Test the property 'isTest'
     */
    @Test
    public void isTestTest() {
        // TODO: test isTest
    }

    /**
     * Test the property 'archiveDate'
     */
    @Test
    public void archiveDateTest() {
        // TODO: test archiveDate
    }

    /**
     * Test the property 'clientConfigurationId'
     */
    @Test
    public void clientConfigurationIdTest() {
        // TODO: test clientConfigurationId
    }

    /**
     * Test the property 'newClientConfigurationId'
     */
    @Test
    public void newClientConfigurationIdTest() {
        // TODO: test newClientConfigurationId
    }

    /**
     * Test the property 'newClientConfigurationValidFrom'
     */
    @Test
    public void newClientConfigurationValidFromTest() {
        // TODO: test newClientConfigurationValidFrom
    }

    /**
     * Test the property 'globalShortId'
     */
    @Test
    public void globalShortIdTest() {
        // TODO: test globalShortId
    }

    /**
     * Test the property 'purchaseDate'
     */
    @Test
    public void purchaseDateTest() {
        // TODO: test purchaseDate
    }

    /**
     * Test the property 'commissioningDate'
     */
    @Test
    public void commissioningDateTest() {
        // TODO: test commissioningDate
    }

    /**
     * Test the property 'decommissioningDate'
     */
    @Test
    public void decommissioningDateTest() {
        // TODO: test decommissioningDate
    }

    /**
     * Test the property 'terminalType'
     */
    @Test
    public void terminalTypeTest() {
        // TODO: test terminalType
    }

    /**
     * Test the property 'updatedByPrincipalId'
     */
    @Test
    public void updatedByPrincipalIdTest() {
        // TODO: test updatedByPrincipalId
    }

    /**
     * Test the property 'cashRegisterId'
     */
    @Test
    public void cashRegisterIdTest() {
        // TODO: test cashRegisterId
    }

    /**
     * Test the property 'cashRegisterBrand'
     */
    @Test
    public void cashRegisterBrandTest() {
        // TODO: test cashRegisterBrand
    }

    /**
     * Test the property 'cashRegisterModelname'
     */
    @Test
    public void cashRegisterModelnameTest() {
        // TODO: test cashRegisterModelname
    }

    /**
     * Test the property 'cashRegisterSoftwareBrand'
     */
    @Test
    public void cashRegisterSoftwareBrandTest() {
        // TODO: test cashRegisterSoftwareBrand
    }

    /**
     * Test the property 'cashRegisterSoftwareVersion'
     */
    @Test
    public void cashRegisterSoftwareVersionTest() {
        // TODO: test cashRegisterSoftwareVersion
    }

    /**
     * Test the property 'cashRegisterSoftwareCompany'
     */
    @Test
    public void cashRegisterSoftwareCompanyTest() {
        // TODO: test cashRegisterSoftwareCompany
    }

}
