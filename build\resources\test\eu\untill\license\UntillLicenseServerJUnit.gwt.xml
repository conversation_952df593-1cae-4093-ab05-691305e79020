<?xml version="1.0" encoding="UTF-8"?>
<!--
  When updating your version of GWT, you should also update this DTD reference,
  so that your app can take advantage of the latest GWT module capabilities.
-->
<!DOCTYPE module PUBLIC "-//Google Inc.//DTD Google Web Toolkit 2.8.0//EN"
  "http://gwtproject.org/doctype/2.8.0/gwt-module.dtd">
<module rename-to='untilllicenseserver'>
  <!-- Inherit our applications main module.                      -->
  <inherits name='eu.untill.license.UntillLicenseServer'/>

  <!-- Specify the path to any remote services.                   -->
  <servlet path="/untilllicenseserver/license" class="eu.untill.license.server.LicenseServiceImpl" />

</module>
