/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.FiscalCountry;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for StoreModel
 */
public class StoreModelTest {
    private final StoreModel model = new StoreModel();

    /**
     * Model tests for StoreModel
     */
    @Test
    public void testStoreModel() {
        // TODO: test StoreModel
    }

    /**
     * Test the property 'organisationCaption'
     */
    @Test
    public void organisationCaptionTest() {
        // TODO: test organisationCaption
    }

    /**
     * Test the property 'companyCaption'
     */
    @Test
    public void companyCaptionTest() {
        // TODO: test companyCaption
    }

    /**
     * Test the property 'companyIdentification'
     */
    @Test
    public void companyIdentificationTest() {
        // TODO: test companyIdentification
    }

    /**
     * Test the property 'fiscalCountry'
     */
    @Test
    public void fiscalCountryTest() {
        // TODO: test fiscalCountry
    }

    /**
     * Test the property 'terminalCount'
     */
    @Test
    public void terminalCountTest() {
        // TODO: test terminalCount
    }

    /**
     * Test the property 'alert'
     */
    @Test
    public void alertTest() {
        // TODO: test alert
    }

    /**
     * Test the property 'canEdit'
     */
    @Test
    public void canEditTest() {
        // TODO: test canEdit
    }

    /**
     * Test the property 'canDelete'
     */
    @Test
    public void canDeleteTest() {
        // TODO: test canDelete
    }

    /**
     * Test the property 'configurationCaption'
     */
    @Test
    public void configurationCaptionTest() {
        // TODO: test configurationCaption
    }

    /**
     * Test the property 'newConfigurationCaption'
     */
    @Test
    public void newConfigurationCaptionTest() {
        // TODO: test newConfigurationCaption
    }

    /**
     * Test the property 'isHidden'
     */
    @Test
    public void isHiddenTest() {
        // TODO: test isHidden
    }

    /**
     * Test the property 'version'
     */
    @Test
    public void versionTest() {
        // TODO: test version
    }

    /**
     * Test the property 'storeId'
     */
    @Test
    public void storeIdTest() {
        // TODO: test storeId
    }

    /**
     * Test the property 'organisationId'
     */
    @Test
    public void organisationIdTest() {
        // TODO: test organisationId
    }

    /**
     * Test the property 'companyId'
     */
    @Test
    public void companyIdTest() {
        // TODO: test companyId
    }

    /**
     * Test the property 'storeNumber'
     */
    @Test
    public void storeNumberTest() {
        // TODO: test storeNumber
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'fiscalRegion'
     */
    @Test
    public void fiscalRegionTest() {
        // TODO: test fiscalRegion
    }

    /**
     * Test the property 'telephone'
     */
    @Test
    public void telephoneTest() {
        // TODO: test telephone
    }

    /**
     * Test the property 'fax'
     */
    @Test
    public void faxTest() {
        // TODO: test fax
    }

    /**
     * Test the property 'eMail'
     */
    @Test
    public void eMailTest() {
        // TODO: test eMail
    }

    /**
     * Test the property 'openingDate'
     */
    @Test
    public void openingDateTest() {
        // TODO: test openingDate
    }

    /**
     * Test the property 'closingDate'
     */
    @Test
    public void closingDateTest() {
        // TODO: test closingDate
    }

    /**
     * Test the property 'hiddenDate'
     */
    @Test
    public void hiddenDateTest() {
        // TODO: test hiddenDate
    }

    /**
     * Test the property 'updatedByPrincipalId'
     */
    @Test
    public void updatedByPrincipalIdTest() {
        // TODO: test updatedByPrincipalId
    }

    /**
     * Test the property 'clientConfigurationId'
     */
    @Test
    public void clientConfigurationIdTest() {
        // TODO: test clientConfigurationId
    }

    /**
     * Test the property 'newClientConfigurationId'
     */
    @Test
    public void newClientConfigurationIdTest() {
        // TODO: test newClientConfigurationId
    }

    /**
     * Test the property 'newClientConfigurationValidFrom'
     */
    @Test
    public void newClientConfigurationValidFromTest() {
        // TODO: test newClientConfigurationValidFrom
    }

    /**
     * Test the property 'street'
     */
    @Test
    public void streetTest() {
        // TODO: test street
    }

    /**
     * Test the property 'streetNumber'
     */
    @Test
    public void streetNumberTest() {
        // TODO: test streetNumber
    }

    /**
     * Test the property 'postalCode'
     */
    @Test
    public void postalCodeTest() {
        // TODO: test postalCode
    }

    /**
     * Test the property 'city'
     */
    @Test
    public void cityTest() {
        // TODO: test city
    }

    /**
     * Test the property 'community'
     */
    @Test
    public void communityTest() {
        // TODO: test community
    }

    /**
     * Test the property 'countryCode'
     */
    @Test
    public void countryCodeTest() {
        // TODO: test countryCode
    }

}
