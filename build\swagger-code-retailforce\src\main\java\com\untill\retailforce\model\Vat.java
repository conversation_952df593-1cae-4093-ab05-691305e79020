/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Vat
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class Vat {
  public static final String SERIALIZED_NAME_VAT_PERCENT = "vatPercent";
  @SerializedName(SERIALIZED_NAME_VAT_PERCENT)
  private Double vatPercent;

  public static final String SERIALIZED_NAME_VAT_PERCENT2 = "vatPercent2";
  @SerializedName(SERIALIZED_NAME_VAT_PERCENT2)
  private Double vatPercent2;

  public static final String SERIALIZED_NAME_VAT_PERCENTS = "vatPercents";
  @Deprecated
  @SerializedName(SERIALIZED_NAME_VAT_PERCENTS)
  private List<Double> vatPercents;

  public static final String SERIALIZED_NAME_VAT_IDENTIFICATION = "vatIdentification";
  @SerializedName(SERIALIZED_NAME_VAT_IDENTIFICATION)
  private Integer vatIdentification;

  public static final String SERIALIZED_NAME_CAPTION = "caption";
  @SerializedName(SERIALIZED_NAME_CAPTION)
  private String caption;

  public static final String SERIALIZED_NAME_CAPTION_E_N = "caption_EN";
  @SerializedName(SERIALIZED_NAME_CAPTION_E_N)
  private String captionEN;

  public static final String SERIALIZED_NAME_SKIP_VAT_PERCENTAGE_VALIDATION = "skipVatPercentageValidation";
  @SerializedName(SERIALIZED_NAME_SKIP_VAT_PERCENTAGE_VALIDATION)
  private Boolean skipVatPercentageValidation;

  public static final String SERIALIZED_NAME_VALID_FROM = "validFrom";
  @SerializedName(SERIALIZED_NAME_VALID_FROM)
  private OffsetDateTime validFrom;

  public static final String SERIALIZED_NAME_VALID_TO = "validTo";
  @SerializedName(SERIALIZED_NAME_VALID_TO)
  private OffsetDateTime validTo;

  public Vat() {
  }

  
  public Vat(
     List<Double> vatPercents
  ) {
    this();
    this.vatPercents = vatPercents;
  }

  public Vat vatPercent(Double vatPercent) {
    
    this.vatPercent = vatPercent;
    return this;
  }

   /**
   * Get vatPercent
   * @return vatPercent
  **/
  @javax.annotation.Nonnull
  public Double getVatPercent() {
    return vatPercent;
  }


  public void setVatPercent(Double vatPercent) {
    this.vatPercent = vatPercent;
  }


  public Vat vatPercent2(Double vatPercent2) {
    
    this.vatPercent2 = vatPercent2;
    return this;
  }

   /**
   * Get vatPercent2
   * @return vatPercent2
  **/
  @javax.annotation.Nullable
  public Double getVatPercent2() {
    return vatPercent2;
  }


  public void setVatPercent2(Double vatPercent2) {
    this.vatPercent2 = vatPercent2;
  }


   /**
   * Get vatPercents
   * @return vatPercents
   * @deprecated
  **/
  @Deprecated
  @javax.annotation.Nullable
  public List<Double> getVatPercents() {
    return vatPercents;
  }




  public Vat vatIdentification(Integer vatIdentification) {
    
    this.vatIdentification = vatIdentification;
    return this;
  }

   /**
   * Get vatIdentification
   * @return vatIdentification
  **/
  @javax.annotation.Nonnull
  public Integer getVatIdentification() {
    return vatIdentification;
  }


  public void setVatIdentification(Integer vatIdentification) {
    this.vatIdentification = vatIdentification;
  }


  public Vat caption(String caption) {
    
    this.caption = caption;
    return this;
  }

   /**
   * Get caption
   * @return caption
  **/
  @javax.annotation.Nullable
  public String getCaption() {
    return caption;
  }


  public void setCaption(String caption) {
    this.caption = caption;
  }


  public Vat captionEN(String captionEN) {
    
    this.captionEN = captionEN;
    return this;
  }

   /**
   * Get captionEN
   * @return captionEN
  **/
  @javax.annotation.Nullable
  public String getCaptionEN() {
    return captionEN;
  }


  public void setCaptionEN(String captionEN) {
    this.captionEN = captionEN;
  }


  public Vat skipVatPercentageValidation(Boolean skipVatPercentageValidation) {
    
    this.skipVatPercentageValidation = skipVatPercentageValidation;
    return this;
  }

   /**
   * Get skipVatPercentageValidation
   * @return skipVatPercentageValidation
  **/
  @javax.annotation.Nonnull
  public Boolean getSkipVatPercentageValidation() {
    return skipVatPercentageValidation;
  }


  public void setSkipVatPercentageValidation(Boolean skipVatPercentageValidation) {
    this.skipVatPercentageValidation = skipVatPercentageValidation;
  }


  public Vat validFrom(OffsetDateTime validFrom) {
    
    this.validFrom = validFrom;
    return this;
  }

   /**
   * Get validFrom
   * @return validFrom
  **/
  @javax.annotation.Nonnull
  public OffsetDateTime getValidFrom() {
    return validFrom;
  }


  public void setValidFrom(OffsetDateTime validFrom) {
    this.validFrom = validFrom;
  }


  public Vat validTo(OffsetDateTime validTo) {
    
    this.validTo = validTo;
    return this;
  }

   /**
   * Get validTo
   * @return validTo
  **/
  @javax.annotation.Nonnull
  public OffsetDateTime getValidTo() {
    return validTo;
  }


  public void setValidTo(OffsetDateTime validTo) {
    this.validTo = validTo;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Vat vat = (Vat) o;
    return Objects.equals(this.vatPercent, vat.vatPercent) &&
        Objects.equals(this.vatPercent2, vat.vatPercent2) &&
        Objects.equals(this.vatPercents, vat.vatPercents) &&
        Objects.equals(this.vatIdentification, vat.vatIdentification) &&
        Objects.equals(this.caption, vat.caption) &&
        Objects.equals(this.captionEN, vat.captionEN) &&
        Objects.equals(this.skipVatPercentageValidation, vat.skipVatPercentageValidation) &&
        Objects.equals(this.validFrom, vat.validFrom) &&
        Objects.equals(this.validTo, vat.validTo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(vatPercent, vatPercent2, vatPercents, vatIdentification, caption, captionEN, skipVatPercentageValidation, validFrom, validTo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Vat {\n");
    sb.append("    vatPercent: ").append(toIndentedString(vatPercent)).append("\n");
    sb.append("    vatPercent2: ").append(toIndentedString(vatPercent2)).append("\n");
    sb.append("    vatPercents: ").append(toIndentedString(vatPercents)).append("\n");
    sb.append("    vatIdentification: ").append(toIndentedString(vatIdentification)).append("\n");
    sb.append("    caption: ").append(toIndentedString(caption)).append("\n");
    sb.append("    captionEN: ").append(toIndentedString(captionEN)).append("\n");
    sb.append("    skipVatPercentageValidation: ").append(toIndentedString(skipVatPercentageValidation)).append("\n");
    sb.append("    validFrom: ").append(toIndentedString(validFrom)).append("\n");
    sb.append("    validTo: ").append(toIndentedString(validTo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("vatPercent");
    openapiFields.add("vatPercent2");
    openapiFields.add("vatPercents");
    openapiFields.add("vatIdentification");
    openapiFields.add("caption");
    openapiFields.add("caption_EN");
    openapiFields.add("skipVatPercentageValidation");
    openapiFields.add("validFrom");
    openapiFields.add("validTo");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
    openapiRequiredFields.add("vatPercent");
    openapiRequiredFields.add("vatIdentification");
    openapiRequiredFields.add("skipVatPercentageValidation");
    openapiRequiredFields.add("validFrom");
    openapiRequiredFields.add("validTo");
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to Vat
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!Vat.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in Vat is not found in the empty JSON string", Vat.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!Vat.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `Vat` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }

      // check to make sure all required properties/fields are present in the JSON string
      for (String requiredField : Vat.openapiRequiredFields) {
        if (jsonObj.get(requiredField) == null) {
          throw new IllegalArgumentException(String.format("The required field `%s` is not found in the JSON string: %s", requiredField, jsonObj.toString()));
        }
      }
      // ensure the optional json data is an array if present
      if (jsonObj.get("vatPercents") != null && !jsonObj.get("vatPercents").isJsonArray()) {
        throw new IllegalArgumentException(String.format("Expected the field `vatPercents` to be an array in the JSON string but got `%s`", jsonObj.get("vatPercents").toString()));
      }
      if ((jsonObj.get("caption") != null && !jsonObj.get("caption").isJsonNull()) && !jsonObj.get("caption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption").toString()));
      }
      if ((jsonObj.get("caption_EN") != null && !jsonObj.get("caption_EN").isJsonNull()) && !jsonObj.get("caption_EN").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption_EN` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption_EN").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!Vat.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'Vat' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<Vat> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(Vat.class));

       return (TypeAdapter<T>) new TypeAdapter<Vat>() {
           @Override
           public void write(JsonWriter out, Vat value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public Vat read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of Vat given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of Vat
  * @throws IOException if the JSON string is invalid with respect to Vat
  */
  public static Vat fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, Vat.class);
  }

 /**
  * Convert an instance of Vat to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

