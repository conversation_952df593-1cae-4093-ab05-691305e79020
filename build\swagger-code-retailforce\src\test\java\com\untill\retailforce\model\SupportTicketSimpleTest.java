/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for SupportTicketSimple
 */
public class SupportTicketSimpleTest {
    private final SupportTicketSimple model = new SupportTicketSimple();

    /**
     * Model tests for SupportTicketSimple
     */
    @Test
    public void testSupportTicketSimple() {
        // TODO: test SupportTicketSimple
    }

    /**
     * Test the property 'ticketId'
     */
    @Test
    public void ticketIdTest() {
        // TODO: test ticketId
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'customer'
     */
    @Test
    public void customerTest() {
        // TODO: test customer
    }

}
