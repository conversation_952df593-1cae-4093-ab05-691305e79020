/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.TseAnnouncementProgress;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * TseAnnouncementOverview
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TseAnnouncementOverview {
  public static final String SERIALIZED_NAME_CAPTION = "caption";
  @SerializedName(SERIALIZED_NAME_CAPTION)
  private String caption;

  public static final String SERIALIZED_NAME_TSE_ANNOUNCEMENT_ID = "tseAnnouncementId";
  @SerializedName(SERIALIZED_NAME_TSE_ANNOUNCEMENT_ID)
  private UUID tseAnnouncementId;

  public static final String SERIALIZED_NAME_STORE_ID = "storeId";
  @SerializedName(SERIALIZED_NAME_STORE_ID)
  private UUID storeId;

  public static final String SERIALIZED_NAME_PROCESS_START = "processStart";
  @SerializedName(SERIALIZED_NAME_PROCESS_START)
  private OffsetDateTime processStart;

  public static final String SERIALIZED_NAME_ERROR_TEXT = "errorText";
  @SerializedName(SERIALIZED_NAME_ERROR_TEXT)
  private String errorText;

  public static final String SERIALIZED_NAME_IS_TEST = "isTest";
  @SerializedName(SERIALIZED_NAME_IS_TEST)
  private Boolean isTest;

  public static final String SERIALIZED_NAME_STATUS = "status";
  @SerializedName(SERIALIZED_NAME_STATUS)
  private TseAnnouncementProgress status;

  public TseAnnouncementOverview() {
  }

  public TseAnnouncementOverview caption(String caption) {
    
    this.caption = caption;
    return this;
  }

   /**
   * Get caption
   * @return caption
  **/
  @javax.annotation.Nullable
  public String getCaption() {
    return caption;
  }


  public void setCaption(String caption) {
    this.caption = caption;
  }


  public TseAnnouncementOverview tseAnnouncementId(UUID tseAnnouncementId) {
    
    this.tseAnnouncementId = tseAnnouncementId;
    return this;
  }

   /**
   * TSE Announcement Id
   * @return tseAnnouncementId
  **/
  @javax.annotation.Nullable
  public UUID getTseAnnouncementId() {
    return tseAnnouncementId;
  }


  public void setTseAnnouncementId(UUID tseAnnouncementId) {
    this.tseAnnouncementId = tseAnnouncementId;
  }


  public TseAnnouncementOverview storeId(UUID storeId) {
    
    this.storeId = storeId;
    return this;
  }

   /**
   * Store Id
   * @return storeId
  **/
  @javax.annotation.Nullable
  public UUID getStoreId() {
    return storeId;
  }


  public void setStoreId(UUID storeId) {
    this.storeId = storeId;
  }


  public TseAnnouncementOverview processStart(OffsetDateTime processStart) {
    
    this.processStart = processStart;
    return this;
  }

   /**
   * When the Announcement Process started
   * @return processStart
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getProcessStart() {
    return processStart;
  }


  public void setProcessStart(OffsetDateTime processStart) {
    this.processStart = processStart;
  }


  public TseAnnouncementOverview errorText(String errorText) {
    
    this.errorText = errorText;
    return this;
  }

   /**
   * If there was an error reporting to the authority
   * @return errorText
  **/
  @javax.annotation.Nullable
  public String getErrorText() {
    return errorText;
  }


  public void setErrorText(String errorText) {
    this.errorText = errorText;
  }


  public TseAnnouncementOverview isTest(Boolean isTest) {
    
    this.isTest = isTest;
    return this;
  }

   /**
   * Is Test (structure for terminals which are test)
   * @return isTest
  **/
  @javax.annotation.Nullable
  public Boolean getIsTest() {
    return isTest;
  }


  public void setIsTest(Boolean isTest) {
    this.isTest = isTest;
  }


  public TseAnnouncementOverview status(TseAnnouncementProgress status) {
    
    this.status = status;
    return this;
  }

   /**
   * Get status
   * @return status
  **/
  @javax.annotation.Nullable
  public TseAnnouncementProgress getStatus() {
    return status;
  }


  public void setStatus(TseAnnouncementProgress status) {
    this.status = status;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TseAnnouncementOverview tseAnnouncementOverview = (TseAnnouncementOverview) o;
    return Objects.equals(this.caption, tseAnnouncementOverview.caption) &&
        Objects.equals(this.tseAnnouncementId, tseAnnouncementOverview.tseAnnouncementId) &&
        Objects.equals(this.storeId, tseAnnouncementOverview.storeId) &&
        Objects.equals(this.processStart, tseAnnouncementOverview.processStart) &&
        Objects.equals(this.errorText, tseAnnouncementOverview.errorText) &&
        Objects.equals(this.isTest, tseAnnouncementOverview.isTest) &&
        Objects.equals(this.status, tseAnnouncementOverview.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(caption, tseAnnouncementId, storeId, processStart, errorText, isTest, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TseAnnouncementOverview {\n");
    sb.append("    caption: ").append(toIndentedString(caption)).append("\n");
    sb.append("    tseAnnouncementId: ").append(toIndentedString(tseAnnouncementId)).append("\n");
    sb.append("    storeId: ").append(toIndentedString(storeId)).append("\n");
    sb.append("    processStart: ").append(toIndentedString(processStart)).append("\n");
    sb.append("    errorText: ").append(toIndentedString(errorText)).append("\n");
    sb.append("    isTest: ").append(toIndentedString(isTest)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("caption");
    openapiFields.add("tseAnnouncementId");
    openapiFields.add("storeId");
    openapiFields.add("processStart");
    openapiFields.add("errorText");
    openapiFields.add("isTest");
    openapiFields.add("status");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TseAnnouncementOverview
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TseAnnouncementOverview.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TseAnnouncementOverview is not found in the empty JSON string", TseAnnouncementOverview.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TseAnnouncementOverview.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TseAnnouncementOverview` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("caption") != null && !jsonObj.get("caption").isJsonNull()) && !jsonObj.get("caption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption").toString()));
      }
      if ((jsonObj.get("tseAnnouncementId") != null && !jsonObj.get("tseAnnouncementId").isJsonNull()) && !jsonObj.get("tseAnnouncementId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `tseAnnouncementId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("tseAnnouncementId").toString()));
      }
      if ((jsonObj.get("storeId") != null && !jsonObj.get("storeId").isJsonNull()) && !jsonObj.get("storeId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeId").toString()));
      }
      if ((jsonObj.get("errorText") != null && !jsonObj.get("errorText").isJsonNull()) && !jsonObj.get("errorText").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `errorText` to be a primitive type in the JSON string but got `%s`", jsonObj.get("errorText").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TseAnnouncementOverview.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TseAnnouncementOverview' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TseAnnouncementOverview> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TseAnnouncementOverview.class));

       return (TypeAdapter<T>) new TypeAdapter<TseAnnouncementOverview>() {
           @Override
           public void write(JsonWriter out, TseAnnouncementOverview value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TseAnnouncementOverview read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TseAnnouncementOverview given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TseAnnouncementOverview
  * @throws IOException if the JSON string is invalid with respect to TseAnnouncementOverview
  */
  public static TseAnnouncementOverview fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TseAnnouncementOverview.class);
  }

 /**
  * Convert an instance of TseAnnouncementOverview to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

