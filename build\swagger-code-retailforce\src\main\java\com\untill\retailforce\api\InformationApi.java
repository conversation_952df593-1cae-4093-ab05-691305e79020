/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.BusinessTransactionTypeDocumentTypeMapping;
import com.untill.retailforce.model.DocumentType;
import com.untill.retailforce.model.FiscalCountry;
import com.untill.retailforce.model.HelpInformation;
import com.untill.retailforce.model.IFiscalCountryProperties;
import com.untill.retailforce.model.IFiscalCountryPropertiesPageResultModel;
import com.untill.retailforce.model.Int32SimpleObjectPageResultModel;
import com.untill.retailforce.model.MessageApiModel;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.Release;
import com.untill.retailforce.model.TerminalContactType;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class InformationApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public InformationApi() {
        this(Configuration.getDefaultApiClient());
    }

    public InformationApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10InformationDocumenttypesSimpleGet
     * @param organizationId  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationDocumenttypesSimpleGetCall(UUID organizationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/documenttypes/simple";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organizationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organizationId", organizationId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationDocumenttypesSimpleGetValidateBeforeCall(UUID organizationId, final ApiCallback _callback) throws ApiException {
        return apiV10InformationDocumenttypesSimpleGetCall(organizationId, _callback);

    }

    /**
     * Returns the available document types for fiscalisation and/or digital receipt.
     * 
     * @param organizationId  (optional)
     * @return Int32SimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public Int32SimpleObjectPageResultModel apiV10InformationDocumenttypesSimpleGet(UUID organizationId) throws ApiException {
        ApiResponse<Int32SimpleObjectPageResultModel> localVarResp = apiV10InformationDocumenttypesSimpleGetWithHttpInfo(organizationId);
        return localVarResp.getData();
    }

    /**
     * Returns the available document types for fiscalisation and/or digital receipt.
     * 
     * @param organizationId  (optional)
     * @return ApiResponse&lt;Int32SimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Int32SimpleObjectPageResultModel> apiV10InformationDocumenttypesSimpleGetWithHttpInfo(UUID organizationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationDocumenttypesSimpleGetValidateBeforeCall(organizationId, null);
        Type localVarReturnType = new TypeToken<Int32SimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the available document types for fiscalisation and/or digital receipt. (asynchronously)
     * 
     * @param organizationId  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationDocumenttypesSimpleGetAsync(UUID organizationId, final ApiCallback<Int32SimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationDocumenttypesSimpleGetValidateBeforeCall(organizationId, _callback);
        Type localVarReturnType = new TypeToken<Int32SimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet
     * @param fiscalCountry The fiscal country to fetch the fiscal regions. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetCall(FiscalCountry fiscalCountry, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/fiscalCountryProperties/{fiscalCountry}/fiscalregions"
            .replace("{" + "fiscalCountry" + "}", localVarApiClient.escapeString(fiscalCountry.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetValidateBeforeCall(FiscalCountry fiscalCountry, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'fiscalCountry' is set
        if (fiscalCountry == null) {
            throw new ApiException("Missing the required parameter 'fiscalCountry' when calling apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet(Async)");
        }

        return apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetCall(fiscalCountry, _callback);

    }

    /**
     * Returns the fiscal regions for the given fiscal country.
     * 
     * @param fiscalCountry The fiscal country to fetch the fiscal regions. (required)
     * @return List&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<String> apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet(FiscalCountry fiscalCountry) throws ApiException {
        ApiResponse<List<String>> localVarResp = apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetWithHttpInfo(fiscalCountry);
        return localVarResp.getData();
    }

    /**
     * Returns the fiscal regions for the given fiscal country.
     * 
     * @param fiscalCountry The fiscal country to fetch the fiscal regions. (required)
     * @return ApiResponse&lt;List&lt;String&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<String>> apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetWithHttpInfo(FiscalCountry fiscalCountry) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetValidateBeforeCall(fiscalCountry, null);
        Type localVarReturnType = new TypeToken<List<String>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the fiscal regions for the given fiscal country. (asynchronously)
     * 
     * @param fiscalCountry The fiscal country to fetch the fiscal regions. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetAsync(FiscalCountry fiscalCountry, final ApiCallback<List<String>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetValidateBeforeCall(fiscalCountry, _callback);
        Type localVarReturnType = new TypeToken<List<String>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationFiscalCountryPropertiesFiscalCountryGet
     * @param fiscalCountry The fiscal country where the fiscal country properties are requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationFiscalCountryPropertiesFiscalCountryGetCall(FiscalCountry fiscalCountry, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/fiscalCountryProperties/{fiscalCountry}"
            .replace("{" + "fiscalCountry" + "}", localVarApiClient.escapeString(fiscalCountry.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationFiscalCountryPropertiesFiscalCountryGetValidateBeforeCall(FiscalCountry fiscalCountry, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'fiscalCountry' is set
        if (fiscalCountry == null) {
            throw new ApiException("Missing the required parameter 'fiscalCountry' when calling apiV10InformationFiscalCountryPropertiesFiscalCountryGet(Async)");
        }

        return apiV10InformationFiscalCountryPropertiesFiscalCountryGetCall(fiscalCountry, _callback);

    }

    /**
     * Returns fiscal country properties for the given country.
     * 
     * @param fiscalCountry The fiscal country where the fiscal country properties are requested. (required)
     * @return IFiscalCountryProperties
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public IFiscalCountryProperties apiV10InformationFiscalCountryPropertiesFiscalCountryGet(FiscalCountry fiscalCountry) throws ApiException {
        ApiResponse<IFiscalCountryProperties> localVarResp = apiV10InformationFiscalCountryPropertiesFiscalCountryGetWithHttpInfo(fiscalCountry);
        return localVarResp.getData();
    }

    /**
     * Returns fiscal country properties for the given country.
     * 
     * @param fiscalCountry The fiscal country where the fiscal country properties are requested. (required)
     * @return ApiResponse&lt;IFiscalCountryProperties&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<IFiscalCountryProperties> apiV10InformationFiscalCountryPropertiesFiscalCountryGetWithHttpInfo(FiscalCountry fiscalCountry) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationFiscalCountryPropertiesFiscalCountryGetValidateBeforeCall(fiscalCountry, null);
        Type localVarReturnType = new TypeToken<IFiscalCountryProperties>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns fiscal country properties for the given country. (asynchronously)
     * 
     * @param fiscalCountry The fiscal country where the fiscal country properties are requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationFiscalCountryPropertiesFiscalCountryGetAsync(FiscalCountry fiscalCountry, final ApiCallback<IFiscalCountryProperties> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationFiscalCountryPropertiesFiscalCountryGetValidateBeforeCall(fiscalCountry, _callback);
        Type localVarReturnType = new TypeToken<IFiscalCountryProperties>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationFiscalCountryPropertiesGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationFiscalCountryPropertiesGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/fiscalCountryProperties";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationFiscalCountryPropertiesGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10InformationFiscalCountryPropertiesGetCall(_callback);

    }

    /**
     * Returns all fiscal countries properties for all available fiscal countries.
     * 
     * @return IFiscalCountryPropertiesPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public IFiscalCountryPropertiesPageResultModel apiV10InformationFiscalCountryPropertiesGet() throws ApiException {
        ApiResponse<IFiscalCountryPropertiesPageResultModel> localVarResp = apiV10InformationFiscalCountryPropertiesGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns all fiscal countries properties for all available fiscal countries.
     * 
     * @return ApiResponse&lt;IFiscalCountryPropertiesPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<IFiscalCountryPropertiesPageResultModel> apiV10InformationFiscalCountryPropertiesGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationFiscalCountryPropertiesGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<IFiscalCountryPropertiesPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all fiscal countries properties for all available fiscal countries. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationFiscalCountryPropertiesGetAsync(final ApiCallback<IFiscalCountryPropertiesPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationFiscalCountryPropertiesGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<IFiscalCountryPropertiesPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationHelpEnumTypeNameEnumValueGet
     * @param typeName The type name of the enum. Use it without namespace information. (required)
     * @param enumValue The name of the value where the help information is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename or enumValue was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationHelpEnumTypeNameEnumValueGetCall(String typeName, String enumValue, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/help/enum/{typeName}/{enumValue}"
            .replace("{" + "typeName" + "}", localVarApiClient.escapeString(typeName.toString()))
            .replace("{" + "enumValue" + "}", localVarApiClient.escapeString(enumValue.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationHelpEnumTypeNameEnumValueGetValidateBeforeCall(String typeName, String enumValue, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'typeName' is set
        if (typeName == null) {
            throw new ApiException("Missing the required parameter 'typeName' when calling apiV10InformationHelpEnumTypeNameEnumValueGet(Async)");
        }

        // verify the required parameter 'enumValue' is set
        if (enumValue == null) {
            throw new ApiException("Missing the required parameter 'enumValue' when calling apiV10InformationHelpEnumTypeNameEnumValueGet(Async)");
        }

        return apiV10InformationHelpEnumTypeNameEnumValueGetCall(typeName, enumValue, _callback);

    }

    /**
     * Returns type help information for the requested enum value.
     * Typename is without namespace information.
     * @param typeName The type name of the enum. Use it without namespace information. (required)
     * @param enumValue The name of the value where the help information is requested. (required)
     * @return HelpInformation
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename or enumValue was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public HelpInformation apiV10InformationHelpEnumTypeNameEnumValueGet(String typeName, String enumValue) throws ApiException {
        ApiResponse<HelpInformation> localVarResp = apiV10InformationHelpEnumTypeNameEnumValueGetWithHttpInfo(typeName, enumValue);
        return localVarResp.getData();
    }

    /**
     * Returns type help information for the requested enum value.
     * Typename is without namespace information.
     * @param typeName The type name of the enum. Use it without namespace information. (required)
     * @param enumValue The name of the value where the help information is requested. (required)
     * @return ApiResponse&lt;HelpInformation&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename or enumValue was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<HelpInformation> apiV10InformationHelpEnumTypeNameEnumValueGetWithHttpInfo(String typeName, String enumValue) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationHelpEnumTypeNameEnumValueGetValidateBeforeCall(typeName, enumValue, null);
        Type localVarReturnType = new TypeToken<HelpInformation>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns type help information for the requested enum value. (asynchronously)
     * Typename is without namespace information.
     * @param typeName The type name of the enum. Use it without namespace information. (required)
     * @param enumValue The name of the value where the help information is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename or enumValue was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationHelpEnumTypeNameEnumValueGetAsync(String typeName, String enumValue, final ApiCallback<HelpInformation> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationHelpEnumTypeNameEnumValueGetValidateBeforeCall(typeName, enumValue, _callback);
        Type localVarReturnType = new TypeToken<HelpInformation>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet
     * @param fiscalCountry The fiscal country where the mapping is requested. (required)
     * @param documentType The document type where the mapping is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Fiscal country properties not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetCall(FiscalCountry fiscalCountry, DocumentType documentType, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/help/fiscalCountryProperties/{fiscalCountry}/{documentType}"
            .replace("{" + "fiscalCountry" + "}", localVarApiClient.escapeString(fiscalCountry.toString()))
            .replace("{" + "documentType" + "}", localVarApiClient.escapeString(documentType.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetValidateBeforeCall(FiscalCountry fiscalCountry, DocumentType documentType, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'fiscalCountry' is set
        if (fiscalCountry == null) {
            throw new ApiException("Missing the required parameter 'fiscalCountry' when calling apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet(Async)");
        }

        // verify the required parameter 'documentType' is set
        if (documentType == null) {
            throw new ApiException("Missing the required parameter 'documentType' when calling apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet(Async)");
        }

        return apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetCall(fiscalCountry, documentType, _callback);

    }

    /**
     * Returns the mapping of business transaction types for the given document type and fiscal country.
     * 
     * @param fiscalCountry The fiscal country where the mapping is requested. (required)
     * @param documentType The document type where the mapping is requested. (required)
     * @return BusinessTransactionTypeDocumentTypeMapping
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Fiscal country properties not found. </td><td>  -  </td></tr>
     </table>
     */
    public BusinessTransactionTypeDocumentTypeMapping apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet(FiscalCountry fiscalCountry, DocumentType documentType) throws ApiException {
        ApiResponse<BusinessTransactionTypeDocumentTypeMapping> localVarResp = apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetWithHttpInfo(fiscalCountry, documentType);
        return localVarResp.getData();
    }

    /**
     * Returns the mapping of business transaction types for the given document type and fiscal country.
     * 
     * @param fiscalCountry The fiscal country where the mapping is requested. (required)
     * @param documentType The document type where the mapping is requested. (required)
     * @return ApiResponse&lt;BusinessTransactionTypeDocumentTypeMapping&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Fiscal country properties not found. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BusinessTransactionTypeDocumentTypeMapping> apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetWithHttpInfo(FiscalCountry fiscalCountry, DocumentType documentType) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetValidateBeforeCall(fiscalCountry, documentType, null);
        Type localVarReturnType = new TypeToken<BusinessTransactionTypeDocumentTypeMapping>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the mapping of business transaction types for the given document type and fiscal country. (asynchronously)
     * 
     * @param fiscalCountry The fiscal country where the mapping is requested. (required)
     * @param documentType The document type where the mapping is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Fiscal country properties not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetAsync(FiscalCountry fiscalCountry, DocumentType documentType, final ApiCallback<BusinessTransactionTypeDocumentTypeMapping> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetValidateBeforeCall(fiscalCountry, documentType, _callback);
        Type localVarReturnType = new TypeToken<BusinessTransactionTypeDocumentTypeMapping>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationHelpTypeNameGet
     * @param typeName The type where the help information is requested. Use it without namespace information. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationHelpTypeNameGetCall(String typeName, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/help/{typeName}"
            .replace("{" + "typeName" + "}", localVarApiClient.escapeString(typeName.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationHelpTypeNameGetValidateBeforeCall(String typeName, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'typeName' is set
        if (typeName == null) {
            throw new ApiException("Missing the required parameter 'typeName' when calling apiV10InformationHelpTypeNameGet(Async)");
        }

        return apiV10InformationHelpTypeNameGetCall(typeName, _callback);

    }

    /**
     * Returns help information for the given type.
     * Typename is without namespace information.
     * @param typeName The type where the help information is requested. Use it without namespace information. (required)
     * @return Map&lt;String, HelpInformation&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public Map<String, HelpInformation> apiV10InformationHelpTypeNameGet(String typeName) throws ApiException {
        ApiResponse<Map<String, HelpInformation>> localVarResp = apiV10InformationHelpTypeNameGetWithHttpInfo(typeName);
        return localVarResp.getData();
    }

    /**
     * Returns help information for the given type.
     * Typename is without namespace information.
     * @param typeName The type where the help information is requested. Use it without namespace information. (required)
     * @return ApiResponse&lt;Map&lt;String, HelpInformation&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Map<String, HelpInformation>> apiV10InformationHelpTypeNameGetWithHttpInfo(String typeName) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationHelpTypeNameGetValidateBeforeCall(typeName, null);
        Type localVarReturnType = new TypeToken<Map<String, HelpInformation>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns help information for the given type. (asynchronously)
     * Typename is without namespace information.
     * @param typeName The type where the help information is requested. Use it without namespace information. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationHelpTypeNameGetAsync(String typeName, final ApiCallback<Map<String, HelpInformation>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationHelpTypeNameGetValidateBeforeCall(typeName, _callback);
        Type localVarReturnType = new TypeToken<Map<String, HelpInformation>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationHelpTypeNamePropertyNameGet
     * @param typeName The type where the help information is requested. Use it without namespace information. (required)
     * @param propertyName The property name where the help information is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename or property name was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationHelpTypeNamePropertyNameGetCall(String typeName, String propertyName, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/help/{typeName}/{propertyName}"
            .replace("{" + "typeName" + "}", localVarApiClient.escapeString(typeName.toString()))
            .replace("{" + "propertyName" + "}", localVarApiClient.escapeString(propertyName.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationHelpTypeNamePropertyNameGetValidateBeforeCall(String typeName, String propertyName, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'typeName' is set
        if (typeName == null) {
            throw new ApiException("Missing the required parameter 'typeName' when calling apiV10InformationHelpTypeNamePropertyNameGet(Async)");
        }

        // verify the required parameter 'propertyName' is set
        if (propertyName == null) {
            throw new ApiException("Missing the required parameter 'propertyName' when calling apiV10InformationHelpTypeNamePropertyNameGet(Async)");
        }

        return apiV10InformationHelpTypeNamePropertyNameGetCall(typeName, propertyName, _callback);

    }

    /**
     * Returns the help information for the given type and property.
     * Typename is without namespace information.
     * @param typeName The type where the help information is requested. Use it without namespace information. (required)
     * @param propertyName The property name where the help information is requested. (required)
     * @return HelpInformation
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename or property name was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public HelpInformation apiV10InformationHelpTypeNamePropertyNameGet(String typeName, String propertyName) throws ApiException {
        ApiResponse<HelpInformation> localVarResp = apiV10InformationHelpTypeNamePropertyNameGetWithHttpInfo(typeName, propertyName);
        return localVarResp.getData();
    }

    /**
     * Returns the help information for the given type and property.
     * Typename is without namespace information.
     * @param typeName The type where the help information is requested. Use it without namespace information. (required)
     * @param propertyName The property name where the help information is requested. (required)
     * @return ApiResponse&lt;HelpInformation&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename or property name was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<HelpInformation> apiV10InformationHelpTypeNamePropertyNameGetWithHttpInfo(String typeName, String propertyName) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationHelpTypeNamePropertyNameGetValidateBeforeCall(typeName, propertyName, null);
        Type localVarReturnType = new TypeToken<HelpInformation>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the help information for the given type and property. (asynchronously)
     * Typename is without namespace information.
     * @param typeName The type where the help information is requested. Use it without namespace information. (required)
     * @param propertyName The property name where the help information is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Type not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Typename or property name was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationHelpTypeNamePropertyNameGetAsync(String typeName, String propertyName, final ApiCallback<HelpInformation> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationHelpTypeNamePropertyNameGetValidateBeforeCall(typeName, propertyName, _callback);
        Type localVarReturnType = new TypeToken<HelpInformation>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationMessageTerminalIdPost
     * @param terminalId The id of the terminal where the message should be sent (the contact of the terminal). (required)
     * @param contactType The type of the contact configured at organization level (primary or technical contact). If omitted primary contact is used; if technical contact is not configured, primary contact is used. (optional)
     * @param messageApiModel The message to send. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationMessageTerminalIdPostCall(UUID terminalId, TerminalContactType contactType, MessageApiModel messageApiModel, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = messageApiModel;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/message/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (contactType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("contactType", contactType));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationMessageTerminalIdPostValidateBeforeCall(UUID terminalId, TerminalContactType contactType, MessageApiModel messageApiModel, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10InformationMessageTerminalIdPost(Async)");
        }

        return apiV10InformationMessageTerminalIdPostCall(terminalId, contactType, messageApiModel, _callback);

    }

    /**
     * Send a message to the contact of the given terminal.
     * 
     * @param terminalId The id of the terminal where the message should be sent (the contact of the terminal). (required)
     * @param contactType The type of the contact configured at organization level (primary or technical contact). If omitted primary contact is used; if technical contact is not configured, primary contact is used. (optional)
     * @param messageApiModel The message to send. (optional)
     * @return Boolean
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public Boolean apiV10InformationMessageTerminalIdPost(UUID terminalId, TerminalContactType contactType, MessageApiModel messageApiModel) throws ApiException {
        ApiResponse<Boolean> localVarResp = apiV10InformationMessageTerminalIdPostWithHttpInfo(terminalId, contactType, messageApiModel);
        return localVarResp.getData();
    }

    /**
     * Send a message to the contact of the given terminal.
     * 
     * @param terminalId The id of the terminal where the message should be sent (the contact of the terminal). (required)
     * @param contactType The type of the contact configured at organization level (primary or technical contact). If omitted primary contact is used; if technical contact is not configured, primary contact is used. (optional)
     * @param messageApiModel The message to send. (optional)
     * @return ApiResponse&lt;Boolean&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Boolean> apiV10InformationMessageTerminalIdPostWithHttpInfo(UUID terminalId, TerminalContactType contactType, MessageApiModel messageApiModel) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationMessageTerminalIdPostValidateBeforeCall(terminalId, contactType, messageApiModel, null);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Send a message to the contact of the given terminal. (asynchronously)
     * 
     * @param terminalId The id of the terminal where the message should be sent (the contact of the terminal). (required)
     * @param contactType The type of the contact configured at organization level (primary or technical contact). If omitted primary contact is used; if technical contact is not configured, primary contact is used. (optional)
     * @param messageApiModel The message to send. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationMessageTerminalIdPostAsync(UUID terminalId, TerminalContactType contactType, MessageApiModel messageApiModel, final ApiCallback<Boolean> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationMessageTerminalIdPostValidateBeforeCall(terminalId, contactType, messageApiModel, _callback);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationReleasesGet
     * @param maxEntries Maximum count of entries (optional)
     * @param fromDate Filter for release date after from date. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationReleasesGetCall(Integer maxEntries, OffsetDateTime fromDate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/releases";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (maxEntries != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("maxEntries", maxEntries));
        }

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationReleasesGetValidateBeforeCall(Integer maxEntries, OffsetDateTime fromDate, final ApiCallback _callback) throws ApiException {
        return apiV10InformationReleasesGetCall(maxEntries, fromDate, _callback);

    }

    /**
     * Returns releases stored in the system.
     * 
     * @param maxEntries Maximum count of entries (optional)
     * @param fromDate Filter for release date after from date. (optional)
     * @return List&lt;Release&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<Release> apiV10InformationReleasesGet(Integer maxEntries, OffsetDateTime fromDate) throws ApiException {
        ApiResponse<List<Release>> localVarResp = apiV10InformationReleasesGetWithHttpInfo(maxEntries, fromDate);
        return localVarResp.getData();
    }

    /**
     * Returns releases stored in the system.
     * 
     * @param maxEntries Maximum count of entries (optional)
     * @param fromDate Filter for release date after from date. (optional)
     * @return ApiResponse&lt;List&lt;Release&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<Release>> apiV10InformationReleasesGetWithHttpInfo(Integer maxEntries, OffsetDateTime fromDate) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationReleasesGetValidateBeforeCall(maxEntries, fromDate, null);
        Type localVarReturnType = new TypeToken<List<Release>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns releases stored in the system. (asynchronously)
     * 
     * @param maxEntries Maximum count of entries (optional)
     * @param fromDate Filter for release date after from date. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationReleasesGetAsync(Integer maxEntries, OffsetDateTime fromDate, final ApiCallback<List<Release>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationReleasesGetValidateBeforeCall(maxEntries, fromDate, _callback);
        Type localVarReturnType = new TypeToken<List<Release>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationTerminalIdVersionGet
     * @param terminalId The terminal id where the sw version is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationTerminalIdVersionGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/{terminalId}/version"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationTerminalIdVersionGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10InformationTerminalIdVersionGet(Async)");
        }

        return apiV10InformationTerminalIdVersionGetCall(terminalId, _callback);

    }

    /**
     * Returns the actual version of the terminal (used sw version of client).
     * 
     * @param terminalId The terminal id where the sw version is requested. (required)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10InformationTerminalIdVersionGet(UUID terminalId) throws ApiException {
        ApiResponse<String> localVarResp = apiV10InformationTerminalIdVersionGetWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Returns the actual version of the terminal (used sw version of client).
     * 
     * @param terminalId The terminal id where the sw version is requested. (required)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10InformationTerminalIdVersionGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationTerminalIdVersionGetValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the actual version of the terminal (used sw version of client). (asynchronously)
     * 
     * @param terminalId The terminal id where the sw version is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationTerminalIdVersionGetAsync(UUID terminalId, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationTerminalIdVersionGetValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationTerminalIdVersionPut
     * @param terminalId The terminal id where the sw version should be set. (required)
     * @param clientVersion The version which should be set. (optional)
     * @param upgradeTime The time when the version upgrade was done. Format: !:RetailForce.Cloud.AzureBlob.Common.Constants.SERVICE_DATETIMEFORMATUTC. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationTerminalIdVersionPutCall(UUID terminalId, String clientVersion, String upgradeTime, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/{terminalId}/version"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (clientVersion != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("clientVersion", clientVersion));
        }

        if (upgradeTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("upgradeTime", upgradeTime));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationTerminalIdVersionPutValidateBeforeCall(UUID terminalId, String clientVersion, String upgradeTime, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10InformationTerminalIdVersionPut(Async)");
        }

        return apiV10InformationTerminalIdVersionPutCall(terminalId, clientVersion, upgradeTime, _callback);

    }

    /**
     * Sets the actual version of the terminal in the cloud.
     * 
     * @param terminalId The terminal id where the sw version should be set. (required)
     * @param clientVersion The version which should be set. (optional)
     * @param upgradeTime The time when the version upgrade was done. Format: !:RetailForce.Cloud.AzureBlob.Common.Constants.SERVICE_DATETIMEFORMATUTC. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10InformationTerminalIdVersionPut(UUID terminalId, String clientVersion, String upgradeTime) throws ApiException {
        apiV10InformationTerminalIdVersionPutWithHttpInfo(terminalId, clientVersion, upgradeTime);
    }

    /**
     * Sets the actual version of the terminal in the cloud.
     * 
     * @param terminalId The terminal id where the sw version should be set. (required)
     * @param clientVersion The version which should be set. (optional)
     * @param upgradeTime The time when the version upgrade was done. Format: !:RetailForce.Cloud.AzureBlob.Common.Constants.SERVICE_DATETIMEFORMATUTC. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10InformationTerminalIdVersionPutWithHttpInfo(UUID terminalId, String clientVersion, String upgradeTime) throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationTerminalIdVersionPutValidateBeforeCall(terminalId, clientVersion, upgradeTime, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Sets the actual version of the terminal in the cloud. (asynchronously)
     * 
     * @param terminalId The terminal id where the sw version should be set. (required)
     * @param clientVersion The version which should be set. (optional)
     * @param upgradeTime The time when the version upgrade was done. Format: !:RetailForce.Cloud.AzureBlob.Common.Constants.SERVICE_DATETIMEFORMATUTC. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationTerminalIdVersionPutAsync(UUID terminalId, String clientVersion, String upgradeTime, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationTerminalIdVersionPutValidateBeforeCall(terminalId, clientVersion, upgradeTime, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10InformationVersionGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationVersionGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/information/version";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10InformationVersionGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10InformationVersionGetCall(_callback);

    }

    /**
     * Returns the actual version (SW) of the software.
     * 
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10InformationVersionGet() throws ApiException {
        ApiResponse<String> localVarResp = apiV10InformationVersionGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns the actual version (SW) of the software.
     * 
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10InformationVersionGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10InformationVersionGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the actual version (SW) of the software. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10InformationVersionGetAsync(final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10InformationVersionGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
