/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for Software
 */
public class SoftwareTest {
    private final Software model = new Software();

    /**
     * Model tests for Software
     */
    @Test
    public void testSoftware() {
        // TODO: test Software
    }

    /**
     * Test the property 'brand'
     */
    @Test
    public void brandTest() {
        // TODO: test brand
    }

    /**
     * Test the property 'version'
     */
    @Test
    public void versionTest() {
        // TODO: test version
    }

    /**
     * Test the property 'softwareCompany'
     */
    @Test
    public void softwareCompanyTest() {
        // TODO: test softwareCompany
    }

}
