# BillingApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10BillingClearingClearingIdGet**](BillingApi.md#apiV10BillingClearingClearingIdGet) | **GET** /api/v1.0/billing/clearing/{clearingId} | Returns clearing license overview for distributor. |
| [**apiV10BillingClearingClearingIdOrganizationGet**](BillingApi.md#apiV10BillingClearingClearingIdOrganizationGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/organization | Returns clearing license overview for all organizations where the actual users is authorized. |
| [**apiV10BillingClearingClearingIdOrganizationIdDetailGet**](BillingApi.md#apiV10BillingClearingClearingIdOrganizationIdDetailGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/{organizationId}/detail | Returns clearing license details for the requested organization. |
| [**apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet**](BillingApi.md#apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/{organizationId}/usage/detail | returns the clearing detail usage for an organisation |
| [**apiV10BillingClearingClearingIdOrganizationUsageGet**](BillingApi.md#apiV10BillingClearingClearingIdOrganizationUsageGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/organization/usage | Returns clearing license overview usage for all organizations where the actual users is authorized. |
| [**apiV10BillingClearingClearingIdUsageGet**](BillingApi.md#apiV10BillingClearingClearingIdUsageGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/usage | Returns the billin license overview usage for the requested distributor |
| [**apiV10BillingClearingGet**](BillingApi.md#apiV10BillingClearingGet) | **GET** /api/v1.0/billing/clearing | Returns all available clearing runs. |
| [**apiV10BillingClearingPut**](BillingApi.md#apiV10BillingClearingPut) | **PUT** /api/v1.0/billing/clearing | Creates a new clearing run.  Clearing run will be created in background |
| [**apiV10BillingOverviewGet**](BillingApi.md#apiV10BillingOverviewGet) | **GET** /api/v1.0/billing/overview | Returns billing license overview for distributor. [preview] |
| [**apiV10BillingOverviewOrganizationGet**](BillingApi.md#apiV10BillingOverviewOrganizationGet) | **GET** /api/v1.0/billing/overview/organization | Returns billing license overview for all organizations where the actual users is authorized. [preview] |
| [**apiV10BillingOverviewOrganizationIdDetailGet**](BillingApi.md#apiV10BillingOverviewOrganizationIdDetailGet) | **GET** /api/v1.0/billing/overview/{organizationId}/detail | Returns license details for the requested organization. [preview] |
| [**apiV10BillingPaymentOutstandingGet**](BillingApi.md#apiV10BillingPaymentOutstandingGet) | **GET** /api/v1.0/billing/payment/outstanding | Log the acknowledgement of the outstanding payment message |


<a id="apiV10BillingClearingClearingIdGet"></a>
# **apiV10BillingClearingClearingIdGet**
> BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingClearingClearingIdGet(clearingId, distributorId, pageOffset, pageSize, searchText)

Returns clearing license overview for distributor.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID clearingId = UUID.randomUUID(); // UUID | The Id of the clearing.
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor to fetch the license overview. If null first level of distributor is queried.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: Caption of distributor).
    try {
      BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel result = apiInstance.apiV10BillingClearingClearingIdGet(clearingId, distributorId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingClearingClearingIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clearingId** | **UUID**| The Id of the clearing. | |
| **distributorId** | **UUID**| The distributor to fetch the license overview. If null first level of distributor is queried. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: Caption of distributor). | [optional] |

### Return type

[**BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel**](BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingClearingClearingIdOrganizationGet"></a>
# **apiV10BillingClearingClearingIdOrganizationGet**
> BillingLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingClearingClearingIdOrganizationGet(clearingId, distributorId, pageOffset, pageSize, searchText)

Returns clearing license overview for all organizations where the actual users is authorized.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID clearingId = UUID.randomUUID(); // UUID | The Id of the clearing.
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor to fetch the license overview. If null all organizations with authorization are fetched.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: Caption of organization).
    try {
      BillingLicenseCountBillingLicenseOverviewPageResultModel result = apiInstance.apiV10BillingClearingClearingIdOrganizationGet(clearingId, distributorId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingClearingClearingIdOrganizationGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clearingId** | **UUID**| The Id of the clearing. | |
| **distributorId** | **UUID**| The distributor to fetch the license overview. If null all organizations with authorization are fetched. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: Caption of organization). | [optional] |

### Return type

[**BillingLicenseCountBillingLicenseOverviewPageResultModel**](BillingLicenseCountBillingLicenseOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingClearingClearingIdOrganizationIdDetailGet"></a>
# **apiV10BillingClearingClearingIdOrganizationIdDetailGet**
> BillingLicenseDetailPageResultModel apiV10BillingClearingClearingIdOrganizationIdDetailGet(clearingId, organizationId, pageOffset, pageSize, searchText)

Returns clearing license details for the requested organization.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID clearingId = UUID.randomUUID(); // UUID | The Id of the clearing.
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization where the license details are requested.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption)
    try {
      BillingLicenseDetailPageResultModel result = apiInstance.apiV10BillingClearingClearingIdOrganizationIdDetailGet(clearingId, organizationId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingClearingClearingIdOrganizationIdDetailGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clearingId** | **UUID**| The Id of the clearing. | |
| **organizationId** | **UUID**| The id of the organization where the license details are requested. | |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) | [optional] |

### Return type

[**BillingLicenseDetailPageResultModel**](BillingLicenseDetailPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet"></a>
# **apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet**
> BillingLicenseDetailPageResultModel apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet(clearingId, organizationId, pageOffset, pageSize, searchText)

returns the clearing detail usage for an organisation

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID clearingId = UUID.randomUUID(); // UUID | The Id of the clearing.
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization where the license details are requested.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption)
    try {
      BillingLicenseDetailPageResultModel result = apiInstance.apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet(clearingId, organizationId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clearingId** | **UUID**| The Id of the clearing. | |
| **organizationId** | **UUID**| The id of the organization where the license details are requested. | |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) | [optional] |

### Return type

[**BillingLicenseDetailPageResultModel**](BillingLicenseDetailPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingClearingClearingIdOrganizationUsageGet"></a>
# **apiV10BillingClearingClearingIdOrganizationUsageGet**
> BillingLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingClearingClearingIdOrganizationUsageGet(clearingId, distributorId, pageOffset, pageSize, searchText)

Returns clearing license overview usage for all organizations where the actual users is authorized.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID clearingId = UUID.randomUUID(); // UUID | The Id of the clearing.
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor to fetch the license overview. If null first level of distributor is queried.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption)
    try {
      BillingLicenseCountBillingLicenseOverviewPageResultModel result = apiInstance.apiV10BillingClearingClearingIdOrganizationUsageGet(clearingId, distributorId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingClearingClearingIdOrganizationUsageGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clearingId** | **UUID**| The Id of the clearing. | |
| **distributorId** | **UUID**| The distributor to fetch the license overview. If null first level of distributor is queried. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) | [optional] |

### Return type

[**BillingLicenseCountBillingLicenseOverviewPageResultModel**](BillingLicenseCountBillingLicenseOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingClearingClearingIdUsageGet"></a>
# **apiV10BillingClearingClearingIdUsageGet**
> BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingClearingClearingIdUsageGet(clearingId, distributorId, pageOffset, pageSize, searchText)

Returns the billin license overview usage for the requested distributor

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID clearingId = UUID.randomUUID(); // UUID | The Id of the clearing.
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor to fetch the license overview. If null first level of distributor is queried.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption)
    try {
      BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel result = apiInstance.apiV10BillingClearingClearingIdUsageGet(clearingId, distributorId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingClearingClearingIdUsageGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **clearingId** | **UUID**| The Id of the clearing. | |
| **distributorId** | **UUID**| The distributor to fetch the license overview. If null first level of distributor is queried. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) | [optional] |

### Return type

[**BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel**](BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingClearingGet"></a>
# **apiV10BillingClearingGet**
> List&lt;ClearingRun&gt; apiV10BillingClearingGet()

Returns all available clearing runs.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    try {
      List<ClearingRun> result = apiInstance.apiV10BillingClearingGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingClearingGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;ClearingRun&gt;**](ClearingRun.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingClearingPut"></a>
# **apiV10BillingClearingPut**
> apiV10BillingClearingPut(caption, clearingRunDateTime)

Creates a new clearing run.  Clearing run will be created in background

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    String caption = "caption_example"; // String | The caption of the clearing run.
    OffsetDateTime clearingRunDateTime = OffsetDateTime.now(); // OffsetDateTime | 
    try {
      apiInstance.apiV10BillingClearingPut(caption, clearingRunDateTime);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingClearingPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **caption** | **String**| The caption of the clearing run. | |
| **clearingRunDateTime** | **OffsetDateTime**|  | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingOverviewGet"></a>
# **apiV10BillingOverviewGet**
> BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingOverviewGet(distributorId, pageOffset, pageSize, searchText)

Returns billing license overview for distributor. [preview]

This controller is in preview state and subject to change in future.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor to fetch the license overview. If null first level of distributor is queried.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: Caption of distributor).
    try {
      BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel result = apiInstance.apiV10BillingOverviewGet(distributorId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingOverviewGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The distributor to fetch the license overview. If null first level of distributor is queried. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: Caption of distributor). | [optional] |

### Return type

[**BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel**](BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingOverviewOrganizationGet"></a>
# **apiV10BillingOverviewOrganizationGet**
> BillingLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingOverviewOrganizationGet(distributorId, pageOffset, pageSize, searchText)

Returns billing license overview for all organizations where the actual users is authorized. [preview]

This controller is in preview state and subject to change in future.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | The distributor to fetch the license overview. If null all organizations with authorization are fetched.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: Caption of organization).
    try {
      BillingLicenseCountBillingLicenseOverviewPageResultModel result = apiInstance.apiV10BillingOverviewOrganizationGet(distributorId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingOverviewOrganizationGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**| The distributor to fetch the license overview. If null all organizations with authorization are fetched. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: Caption of organization). | [optional] |

### Return type

[**BillingLicenseCountBillingLicenseOverviewPageResultModel**](BillingLicenseCountBillingLicenseOverviewPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingOverviewOrganizationIdDetailGet"></a>
# **apiV10BillingOverviewOrganizationIdDetailGet**
> BillingLicenseDetailPageResultModel apiV10BillingOverviewOrganizationIdDetailGet(organizationId, pageOffset, pageSize, searchText)

Returns license details for the requested organization. [preview]

This controller is in preview state and subject to change in future.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization where the license details are requested.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchText = "searchText_example"; // String | An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption)
    try {
      BillingLicenseDetailPageResultModel result = apiInstance.apiV10BillingOverviewOrganizationIdDetailGet(organizationId, pageOffset, pageSize, searchText);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingOverviewOrganizationIdDetailGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of the organization where the license details are requested. | |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchText** | **String**| An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) | [optional] |

### Return type

[**BillingLicenseDetailPageResultModel**](BillingLicenseDetailPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BillingPaymentOutstandingGet"></a>
# **apiV10BillingPaymentOutstandingGet**
> List&lt;OutstandingPayment&gt; apiV10BillingPaymentOutstandingGet()

Log the acknowledgement of the outstanding payment message

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BillingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BillingApi apiInstance = new BillingApi(defaultClient);
    try {
      List<OutstandingPayment> result = apiInstance.apiV10BillingPaymentOutstandingGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BillingApi#apiV10BillingPaymentOutstandingGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;OutstandingPayment&gt;**](OutstandingPayment.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

