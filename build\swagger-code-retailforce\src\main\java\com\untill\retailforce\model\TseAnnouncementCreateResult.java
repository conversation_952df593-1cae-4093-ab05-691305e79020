/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Tse Announcement create result
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TseAnnouncementCreateResult {
  public static final String SERIALIZED_NAME_SUCCESSFUL_SUBMISSION_COUNT = "successfulSubmissionCount";
  @SerializedName(SERIALIZED_NAME_SUCCESSFUL_SUBMISSION_COUNT)
  private Integer successfulSubmissionCount;

  public static final String SERIALIZED_NAME_FAILED_SUBMISSION_COUNT = "failedSubmissionCount";
  @SerializedName(SERIALIZED_NAME_FAILED_SUBMISSION_COUNT)
  private Integer failedSubmissionCount;

  public static final String SERIALIZED_NAME_TAX_PAYER_VALIDATION_ERRORS = "taxPayerValidationErrors";
  @SerializedName(SERIALIZED_NAME_TAX_PAYER_VALIDATION_ERRORS)
  private List<String> taxPayerValidationErrors;

  public static final String SERIALIZED_NAME_SUBMISSION_VALIDATION_ERRORS = "submissionValidationErrors";
  @SerializedName(SERIALIZED_NAME_SUBMISSION_VALIDATION_ERRORS)
  private List<String> submissionValidationErrors;

  public TseAnnouncementCreateResult() {
  }

  public TseAnnouncementCreateResult successfulSubmissionCount(Integer successfulSubmissionCount) {
    
    this.successfulSubmissionCount = successfulSubmissionCount;
    return this;
  }

   /**
   * Number of successful created submissions
   * @return successfulSubmissionCount
  **/
  @javax.annotation.Nullable
  public Integer getSuccessfulSubmissionCount() {
    return successfulSubmissionCount;
  }


  public void setSuccessfulSubmissionCount(Integer successfulSubmissionCount) {
    this.successfulSubmissionCount = successfulSubmissionCount;
  }


  public TseAnnouncementCreateResult failedSubmissionCount(Integer failedSubmissionCount) {
    
    this.failedSubmissionCount = failedSubmissionCount;
    return this;
  }

   /**
   * Number of failed submissions
   * @return failedSubmissionCount
  **/
  @javax.annotation.Nullable
  public Integer getFailedSubmissionCount() {
    return failedSubmissionCount;
  }


  public void setFailedSubmissionCount(Integer failedSubmissionCount) {
    this.failedSubmissionCount = failedSubmissionCount;
  }


  public TseAnnouncementCreateResult taxPayerValidationErrors(List<String> taxPayerValidationErrors) {
    
    this.taxPayerValidationErrors = taxPayerValidationErrors;
    return this;
  }

  public TseAnnouncementCreateResult addTaxPayerValidationErrorsItem(String taxPayerValidationErrorsItem) {
    if (this.taxPayerValidationErrors == null) {
      this.taxPayerValidationErrors = new ArrayList<>();
    }
    this.taxPayerValidationErrors.add(taxPayerValidationErrorsItem);
    return this;
  }

   /**
   * Tax Payer validation error
   * @return taxPayerValidationErrors
  **/
  @javax.annotation.Nullable
  public List<String> getTaxPayerValidationErrors() {
    return taxPayerValidationErrors;
  }


  public void setTaxPayerValidationErrors(List<String> taxPayerValidationErrors) {
    this.taxPayerValidationErrors = taxPayerValidationErrors;
  }


  public TseAnnouncementCreateResult submissionValidationErrors(List<String> submissionValidationErrors) {
    
    this.submissionValidationErrors = submissionValidationErrors;
    return this;
  }

  public TseAnnouncementCreateResult addSubmissionValidationErrorsItem(String submissionValidationErrorsItem) {
    if (this.submissionValidationErrors == null) {
      this.submissionValidationErrors = new ArrayList<>();
    }
    this.submissionValidationErrors.add(submissionValidationErrorsItem);
    return this;
  }

   /**
   * Submission validation errors
   * @return submissionValidationErrors
  **/
  @javax.annotation.Nullable
  public List<String> getSubmissionValidationErrors() {
    return submissionValidationErrors;
  }


  public void setSubmissionValidationErrors(List<String> submissionValidationErrors) {
    this.submissionValidationErrors = submissionValidationErrors;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TseAnnouncementCreateResult tseAnnouncementCreateResult = (TseAnnouncementCreateResult) o;
    return Objects.equals(this.successfulSubmissionCount, tseAnnouncementCreateResult.successfulSubmissionCount) &&
        Objects.equals(this.failedSubmissionCount, tseAnnouncementCreateResult.failedSubmissionCount) &&
        Objects.equals(this.taxPayerValidationErrors, tseAnnouncementCreateResult.taxPayerValidationErrors) &&
        Objects.equals(this.submissionValidationErrors, tseAnnouncementCreateResult.submissionValidationErrors);
  }

  @Override
  public int hashCode() {
    return Objects.hash(successfulSubmissionCount, failedSubmissionCount, taxPayerValidationErrors, submissionValidationErrors);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TseAnnouncementCreateResult {\n");
    sb.append("    successfulSubmissionCount: ").append(toIndentedString(successfulSubmissionCount)).append("\n");
    sb.append("    failedSubmissionCount: ").append(toIndentedString(failedSubmissionCount)).append("\n");
    sb.append("    taxPayerValidationErrors: ").append(toIndentedString(taxPayerValidationErrors)).append("\n");
    sb.append("    submissionValidationErrors: ").append(toIndentedString(submissionValidationErrors)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("successfulSubmissionCount");
    openapiFields.add("failedSubmissionCount");
    openapiFields.add("taxPayerValidationErrors");
    openapiFields.add("submissionValidationErrors");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TseAnnouncementCreateResult
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TseAnnouncementCreateResult.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TseAnnouncementCreateResult is not found in the empty JSON string", TseAnnouncementCreateResult.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TseAnnouncementCreateResult.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TseAnnouncementCreateResult` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      // ensure the optional json data is an array if present
      if (jsonObj.get("taxPayerValidationErrors") != null && !jsonObj.get("taxPayerValidationErrors").isJsonArray()) {
        throw new IllegalArgumentException(String.format("Expected the field `taxPayerValidationErrors` to be an array in the JSON string but got `%s`", jsonObj.get("taxPayerValidationErrors").toString()));
      }
      // ensure the optional json data is an array if present
      if (jsonObj.get("submissionValidationErrors") != null && !jsonObj.get("submissionValidationErrors").isJsonArray()) {
        throw new IllegalArgumentException(String.format("Expected the field `submissionValidationErrors` to be an array in the JSON string but got `%s`", jsonObj.get("submissionValidationErrors").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TseAnnouncementCreateResult.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TseAnnouncementCreateResult' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TseAnnouncementCreateResult> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TseAnnouncementCreateResult.class));

       return (TypeAdapter<T>) new TypeAdapter<TseAnnouncementCreateResult>() {
           @Override
           public void write(JsonWriter out, TseAnnouncementCreateResult value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TseAnnouncementCreateResult read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TseAnnouncementCreateResult given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TseAnnouncementCreateResult
  * @throws IOException if the JSON string is invalid with respect to TseAnnouncementCreateResult
  */
  public static TseAnnouncementCreateResult fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TseAnnouncementCreateResult.class);
  }

 /**
  * Convert an instance of TseAnnouncementCreateResult to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

