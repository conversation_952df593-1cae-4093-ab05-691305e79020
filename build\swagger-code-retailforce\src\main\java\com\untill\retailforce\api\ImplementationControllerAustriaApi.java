/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.FonConnectionLogMessagePageResultModel;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.SignatureDevice;
import com.untill.retailforce.model.StringSimpleObject;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ImplementationControllerAustriaApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ImplementationControllerAustriaApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ImplementationControllerAustriaApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet
     * @param organizationId The organization where the annual year receipts are requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given organization. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetCall(UUID organizationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/at/organization/{organizationId}/missingAnnualReceipts"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetValidateBeforeCall(UUID organizationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet(Async)");
        }

        return apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetCall(organizationId, _callback);

    }

    /**
     * Returns all missing annual year receipts (annual year receipt check).
     * 
     * @param organizationId The organization where the annual year receipts are requested. (required)
     * @return List&lt;StringSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given organization. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization not found. </td><td>  -  </td></tr>
     </table>
     */
    public List<StringSimpleObject> apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet(UUID organizationId) throws ApiException {
        ApiResponse<List<StringSimpleObject>> localVarResp = apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetWithHttpInfo(organizationId);
        return localVarResp.getData();
    }

    /**
     * Returns all missing annual year receipts (annual year receipt check).
     * 
     * @param organizationId The organization where the annual year receipts are requested. (required)
     * @return ApiResponse&lt;List&lt;StringSimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given organization. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization not found. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<StringSimpleObject>> apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetWithHttpInfo(UUID organizationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetValidateBeforeCall(organizationId, null);
        Type localVarReturnType = new TypeToken<List<StringSimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all missing annual year receipts (annual year receipt check). (asynchronously)
     * 
     * @param organizationId The organization where the annual year receipts are requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given organization. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetAsync(UUID organizationId, final ApiCallback<List<StringSimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetValidateBeforeCall(organizationId, _callback);
        Type localVarReturnType = new TypeToken<List<StringSimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationAtSignatureDeviceEntityIdGet
     * @param entityId The id of the entity (organization or company) where the signature devices are requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtSignatureDeviceEntityIdGetCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/at/signatureDevice/{entityId}"
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationAtSignatureDeviceEntityIdGetValidateBeforeCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10ImplementationAtSignatureDeviceEntityIdGet(Async)");
        }

        return apiV10ImplementationAtSignatureDeviceEntityIdGetCall(entityId, _callback);

    }

    /**
     * Returns the signature devices for the given entity.
     * 
     * @param entityId The id of the entity (organization or company) where the signature devices are requested. (required)
     * @return List&lt;SignatureDevice&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<SignatureDevice> apiV10ImplementationAtSignatureDeviceEntityIdGet(UUID entityId) throws ApiException {
        ApiResponse<List<SignatureDevice>> localVarResp = apiV10ImplementationAtSignatureDeviceEntityIdGetWithHttpInfo(entityId);
        return localVarResp.getData();
    }

    /**
     * Returns the signature devices for the given entity.
     * 
     * @param entityId The id of the entity (organization or company) where the signature devices are requested. (required)
     * @return ApiResponse&lt;List&lt;SignatureDevice&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<SignatureDevice>> apiV10ImplementationAtSignatureDeviceEntityIdGetWithHttpInfo(UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationAtSignatureDeviceEntityIdGetValidateBeforeCall(entityId, null);
        Type localVarReturnType = new TypeToken<List<SignatureDevice>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the signature devices for the given entity. (asynchronously)
     * 
     * @param entityId The id of the entity (organization or company) where the signature devices are requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtSignatureDeviceEntityIdGetAsync(UUID entityId, final ApiCallback<List<SignatureDevice>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationAtSignatureDeviceEntityIdGetValidateBeforeCall(entityId, _callback);
        Type localVarReturnType = new TypeToken<List<SignatureDevice>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationAtTerminalIdAsitCryptoContainerGet
     * @param terminalId The terminal to fetch the container. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdAsitCryptoContainerGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/at/{terminalId}/asitCryptoContainer"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationAtTerminalIdAsitCryptoContainerGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationAtTerminalIdAsitCryptoContainerGet(Async)");
        }

        return apiV10ImplementationAtTerminalIdAsitCryptoContainerGetCall(terminalId, _callback);

    }

    /**
     * Returns the asit crypto container for asit check (as json string).
     * 
     * @param terminalId The terminal to fetch the container. (required)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10ImplementationAtTerminalIdAsitCryptoContainerGet(UUID terminalId) throws ApiException {
        ApiResponse<String> localVarResp = apiV10ImplementationAtTerminalIdAsitCryptoContainerGetWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Returns the asit crypto container for asit check (as json string).
     * 
     * @param terminalId The terminal to fetch the container. (required)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10ImplementationAtTerminalIdAsitCryptoContainerGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdAsitCryptoContainerGetValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the asit crypto container for asit check (as json string). (asynchronously)
     * 
     * @param terminalId The terminal to fetch the container. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdAsitCryptoContainerGetAsync(UUID terminalId, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdAsitCryptoContainerGetValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationAtTerminalIdDep131Get
     * @param terminalId The client id for which the export is requested. (required)
     * @param fromDate The start date of the requested receipts. (required)
     * @param tillDate The end date of the requested receipts. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdDep131GetCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/at/{terminalId}/dep131"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (tillDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tillDate", tillDate));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationAtTerminalIdDep131GetValidateBeforeCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationAtTerminalIdDep131Get(Async)");
        }

        // verify the required parameter 'fromDate' is set
        if (fromDate == null) {
            throw new ApiException("Missing the required parameter 'fromDate' when calling apiV10ImplementationAtTerminalIdDep131Get(Async)");
        }

        // verify the required parameter 'tillDate' is set
        if (tillDate == null) {
            throw new ApiException("Missing the required parameter 'tillDate' when calling apiV10ImplementationAtTerminalIdDep131Get(Async)");
        }

        return apiV10ImplementationAtTerminalIdDep131GetCall(terminalId, fromDate, tillDate, _callback);

    }

    /**
     * Exports all data requested for dep131 protocol out of stored data.
     * 
     * @param terminalId The client id for which the export is requested. (required)
     * @param fromDate The start date of the requested receipts. (required)
     * @param tillDate The end date of the requested receipts. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationAtTerminalIdDep131Get(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate) throws ApiException {
        apiV10ImplementationAtTerminalIdDep131GetWithHttpInfo(terminalId, fromDate, tillDate);
    }

    /**
     * Exports all data requested for dep131 protocol out of stored data.
     * 
     * @param terminalId The client id for which the export is requested. (required)
     * @param fromDate The start date of the requested receipts. (required)
     * @param tillDate The end date of the requested receipts. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationAtTerminalIdDep131GetWithHttpInfo(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdDep131GetValidateBeforeCall(terminalId, fromDate, tillDate, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Exports all data requested for dep131 protocol out of stored data. (asynchronously)
     * 
     * @param terminalId The client id for which the export is requested. (required)
     * @param fromDate The start date of the requested receipts. (required)
     * @param tillDate The end date of the requested receipts. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdDep131GetAsync(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdDep131GetValidateBeforeCall(terminalId, fromDate, tillDate, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationAtTerminalIdDepGet
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdDepGetCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/at/{terminalId}/dep"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (tillDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tillDate", tillDate));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationAtTerminalIdDepGetValidateBeforeCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationAtTerminalIdDepGet(Async)");
        }

        // verify the required parameter 'fromDate' is set
        if (fromDate == null) {
            throw new ApiException("Missing the required parameter 'fromDate' when calling apiV10ImplementationAtTerminalIdDepGet(Async)");
        }

        // verify the required parameter 'tillDate' is set
        if (tillDate == null) {
            throw new ApiException("Missing the required parameter 'tillDate' when calling apiV10ImplementationAtTerminalIdDepGet(Async)");
        }

        return apiV10ImplementationAtTerminalIdDepGetCall(terminalId, fromDate, tillDate, _callback);

    }

    /**
     * Exports austrian dep from cloud archive.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationAtTerminalIdDepGet(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate) throws ApiException {
        apiV10ImplementationAtTerminalIdDepGetWithHttpInfo(terminalId, fromDate, tillDate);
    }

    /**
     * Exports austrian dep from cloud archive.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationAtTerminalIdDepGetWithHttpInfo(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdDepGetValidateBeforeCall(terminalId, fromDate, tillDate, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Exports austrian dep from cloud archive. (asynchronously)
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdate/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdDepGetAsync(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdDepGetValidateBeforeCall(terminalId, fromDate, tillDate, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationAtTerminalIdFonGet
     * @param terminalId The terminalid of the requested client. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdFonGetCall(UUID terminalId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/at/{terminalId}/fon"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationAtTerminalIdFonGetValidateBeforeCall(UUID terminalId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationAtTerminalIdFonGet(Async)");
        }

        return apiV10ImplementationAtTerminalIdFonGetCall(terminalId, pageOffset, pageSize, _callback);

    }

    /**
     * Returns the pages fon connection log for the requested client.
     * 
     * @param terminalId The terminalid of the requested client. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit (optional)
     * @return FonConnectionLogMessagePageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public FonConnectionLogMessagePageResultModel apiV10ImplementationAtTerminalIdFonGet(UUID terminalId, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<FonConnectionLogMessagePageResultModel> localVarResp = apiV10ImplementationAtTerminalIdFonGetWithHttpInfo(terminalId, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Returns the pages fon connection log for the requested client.
     * 
     * @param terminalId The terminalid of the requested client. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit (optional)
     * @return ApiResponse&lt;FonConnectionLogMessagePageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<FonConnectionLogMessagePageResultModel> apiV10ImplementationAtTerminalIdFonGetWithHttpInfo(UUID terminalId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdFonGetValidateBeforeCall(terminalId, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<FonConnectionLogMessagePageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the pages fon connection log for the requested client. (asynchronously)
     * 
     * @param terminalId The terminalid of the requested client. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdFonGetAsync(UUID terminalId, Integer pageOffset, Integer pageSize, final ApiCallback<FonConnectionLogMessagePageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdFonGetValidateBeforeCall(terminalId, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<FonConnectionLogMessagePageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationAtTerminalIdYearReceiptPost
     * @param terminalId The terminalid of the terminal where the automatic year receipt should be created. (required)
     * @param year The year of the year receipt to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or year not between 2016 and current year. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdYearReceiptPostCall(UUID terminalId, Integer year, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/at/{terminalId}/yearReceipt"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (year != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("year", year));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationAtTerminalIdYearReceiptPostValidateBeforeCall(UUID terminalId, Integer year, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationAtTerminalIdYearReceiptPost(Async)");
        }

        return apiV10ImplementationAtTerminalIdYearReceiptPostCall(terminalId, year, _callback);

    }

    /**
     * Creates an automatic year receipt and validates this at fon.
     * In order to use this function it is necessary to have online connection to the terminal (signalr) or second dep validation channel (see documentation) is set.  The automatic year receipt will not be created if cloud store is not in sync with client dep store.
     * @param terminalId The terminalid of the terminal where the automatic year receipt should be created. (required)
     * @param year The year of the year receipt to create. (optional)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or year not between 2016 and current year. </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10ImplementationAtTerminalIdYearReceiptPost(UUID terminalId, Integer year) throws ApiException {
        ApiResponse<String> localVarResp = apiV10ImplementationAtTerminalIdYearReceiptPostWithHttpInfo(terminalId, year);
        return localVarResp.getData();
    }

    /**
     * Creates an automatic year receipt and validates this at fon.
     * In order to use this function it is necessary to have online connection to the terminal (signalr) or second dep validation channel (see documentation) is set.  The automatic year receipt will not be created if cloud store is not in sync with client dep store.
     * @param terminalId The terminalid of the terminal where the automatic year receipt should be created. (required)
     * @param year The year of the year receipt to create. (optional)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or year not between 2016 and current year. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10ImplementationAtTerminalIdYearReceiptPostWithHttpInfo(UUID terminalId, Integer year) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdYearReceiptPostValidateBeforeCall(terminalId, year, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates an automatic year receipt and validates this at fon. (asynchronously)
     * In order to use this function it is necessary to have online connection to the terminal (signalr) or second dep validation channel (see documentation) is set.  The automatic year receipt will not be created if cloud store is not in sync with client dep store.
     * @param terminalId The terminalid of the terminal where the automatic year receipt should be created. (required)
     * @param year The year of the year receipt to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or year not between 2016 and current year. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationAtTerminalIdYearReceiptPostAsync(UUID terminalId, Integer year, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationAtTerminalIdYearReceiptPostValidateBeforeCall(terminalId, year, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
