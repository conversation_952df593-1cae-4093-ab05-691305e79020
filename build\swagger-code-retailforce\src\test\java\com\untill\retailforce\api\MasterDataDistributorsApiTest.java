/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.Distributor;
import com.untill.retailforce.model.DistributorContract;
import com.untill.retailforce.model.DistributorContractModel;
import com.untill.retailforce.model.DistributorContractModelPageResultModel;
import com.untill.retailforce.model.DistributorModel;
import com.untill.retailforce.model.DistributorModelPageResultModel;
import com.untill.retailforce.model.GuidBreadCrumb;
import com.untill.retailforce.model.GuidHierarchicalSimpleObjectPageResultModel;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import com.untill.retailforce.model.OnboardingFinishData;
import com.untill.retailforce.model.OnboardingFinishDistributorData;
import com.untill.retailforce.model.StringSimpleObjectPageResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MasterDataDistributorsApi
 */
@Disabled
public class MasterDataDistributorsApiTest {

    private final MasterDataDistributorsApi api = new MasterDataDistributorsApi();

    /**
     * Deletes a contract.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteTest() throws ApiException {
        UUID distributorId = null;
        UUID contractId = null;
        api.apiV10MasterdataDistributorsDistributorIdContractContractIdDelete(distributorId, contractId);
        // TODO: test validations
    }

    /**
     * Updates the given contract of the distributor.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdContractContractIdPutTest() throws ApiException {
        UUID distributorId = null;
        UUID contractId = null;
        DistributorContract distributorContract = null;
        DistributorContractModel response = api.apiV10MasterdataDistributorsDistributorIdContractContractIdPut(distributorId, contractId, distributorContract);
        // TODO: test validations
    }

    /**
     * Finishes onboarding for distributor
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutTest() throws ApiException {
        UUID distributorId = null;
        UUID distributorContractId = null;
        OnboardingFinishData onboardingFinishData = null;
        api.apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut(distributorId, distributorContractId, onboardingFinishData);
        // TODO: test validations
    }

    /**
     * Creates a new contract for a distributor.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdContractPostTest() throws ApiException {
        UUID distributorId = null;
        DistributorContract distributorContract = null;
        DistributorContractModel response = api.apiV10MasterdataDistributorsDistributorIdContractPost(distributorId, distributorContract);
        // TODO: test validations
    }

    /**
     * Returns the requested contract.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdContractsContractIdGetTest() throws ApiException {
        UUID distributorId = null;
        UUID contractId = null;
        DistributorContractModel response = api.apiV10MasterdataDistributorsDistributorIdContractsContractIdGet(distributorId, contractId);
        // TODO: test validations
    }

    /**
     * Returns all contracts for a distributor.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdContractsGetTest() throws ApiException {
        UUID distributorId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        DistributorContractModelPageResultModel response = api.apiV10MasterdataDistributorsDistributorIdContractsGet(distributorId, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Deletes an distributor from cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdDeleteTest() throws ApiException {
        UUID distributorId = null;
        api.apiV10MasterdataDistributorsDistributorIdDelete(distributorId);
        // TODO: test validations
    }

    /**
     * Returns the distributor requested by the given id.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdGetTest() throws ApiException {
        UUID distributorId = null;
        DistributorModel response = api.apiV10MasterdataDistributorsDistributorIdGet(distributorId);
        // TODO: test validations
    }

    /**
     * Returns the available license groups.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetTest() throws ApiException {
        UUID distributorId = null;
        StringSimpleObjectPageResultModel response = api.apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet(distributorId);
        // TODO: test validations
    }

    /**
     * Finishes onboarding for distributor
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutTest() throws ApiException {
        UUID distributorId = null;
        OnboardingFinishDistributorData onboardingFinishDistributorData = null;
        api.apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut(distributorId, onboardingFinishDistributorData);
        // TODO: test validations
    }

    /**
     * Updates an distributor in the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdPutTest() throws ApiException {
        UUID distributorId = null;
        Distributor distributor = null;
        DistributorModel response = api.apiV10MasterdataDistributorsDistributorIdPut(distributorId, distributor);
        // TODO: test validations
    }

    /**
     * Returns a simple distributorRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsDistributorIdSimpleGetTest() throws ApiException {
        UUID distributorId = null;
        GuidSimpleObject response = api.apiV10MasterdataDistributorsDistributorIdSimpleGet(distributorId);
        // TODO: test validations
    }

    /**
     * Returns all distributors for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        DistributorModelPageResultModel response = api.apiV10MasterdataDistributorsGet(pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Returns all distributors based on the parent distributor (only retailforce users)
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsHierarchyAllGetTest() throws ApiException {
        UUID parentDistributorId = null;
        String searchString = null;
        List<DistributorModel> response = api.apiV10MasterdataDistributorsHierarchyAllGet(parentDistributorId, searchString);
        // TODO: test validations
    }

    /**
     * Returns the hierarchy of the actual distributor as a breadcrumb object for navigation.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsHierarchyBreadcrumbGetTest() throws ApiException {
        UUID distributorId = null;
        Boolean isClearingRun = null;
        GuidBreadCrumb response = api.apiV10MasterdataDistributorsHierarchyBreadcrumbGet(distributorId, isClearingRun);
        // TODO: test validations
    }

    /**
     * Returns distributor hierarchy.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsHierarchyGetTest() throws ApiException {
        UUID distributorId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        GuidHierarchicalSimpleObjectPageResultModel response = api.apiV10MasterdataDistributorsHierarchyGet(distributorId, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Returns the available distributor levels.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsLevelsGetTest() throws ApiException {
        GuidSimpleObjectPageResultModel response = api.apiV10MasterdataDistributorsLevelsGet();
        // TODO: test validations
    }

    /**
     * Creates a new distributor in the cloud store.
     *
     * If RetailForce.Cloud.Model.Distributor.DistributorId set to System.Guid.Empty, then the distributor id will be generated by the service.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsPostTest() throws ApiException {
        Distributor distributor = null;
        DistributorModel response = api.apiV10MasterdataDistributorsPost(distributor);
        // TODO: test validations
    }

    /**
     * Returns all distributors for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataDistributorsSimpleGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        Boolean all = null;
        GuidSimpleObjectPageResultModel response = api.apiV10MasterdataDistributorsSimpleGet(pageOffset, pageSize, searchString, all);
        // TODO: test validations
    }

}
