/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import java.io.File;
import java.time.OffsetDateTime;
import java.util.UUID;
import com.untill.retailforce.model.ValidationError;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ImplementationControllerDenmarkApi
 */
@Disabled
public class ImplementationControllerDenmarkApiTest {

    private final ImplementationControllerDenmarkApi api = new ImplementationControllerDenmarkApi();

    /**
     * Validates given zip file audit file (denmark saf-t) content.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDkSaftValidatePostTest() throws ApiException {
        File zipFile = null;
        List<ValidationError> response = api.apiV10ImplementationDkSaftValidatePost(zipFile);
        // TODO: test validations
    }

    /**
     * Exports denmark saf-t format for the given terminal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationDkTerminalIdSaftGetTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime tillDate = null;
        Boolean exportRawData = null;
        Boolean exportFilePerClosing = null;
        api.apiV10ImplementationDkTerminalIdSaftGet(terminalId, fromDate, tillDate, exportRawData, exportFilePerClosing);
        // TODO: test validations
    }

}
