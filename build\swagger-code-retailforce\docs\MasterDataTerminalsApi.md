# MasterDataTerminalsApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MasterdataTerminalsGet**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsGet) | **GET** /api/v1.0/masterdata/terminals | Returns all terminals for the requested store for the authenticated user. |
| [**apiV10MasterdataTerminalsPost**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsPost) | **POST** /api/v1.0/masterdata/terminals | Creates a terminal in the cloud store. |
| [**apiV10MasterdataTerminalsSimpleGet**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsSimpleGet) | **GET** /api/v1.0/masterdata/terminals/simple | Returns all terminals as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested store for the authenticated user. |
| [**apiV10MasterdataTerminalsTerminalIdArchivePost**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdArchivePost) | **POST** /api/v1.0/masterdata/terminals/{terminalId}/archive | Deactivates and archives the given terminal (cannot be undone). |
| [**apiV10MasterdataTerminalsTerminalIdDeactivatePut**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdDeactivatePut) | **PUT** /api/v1.0/masterdata/terminals/{terminalId}/deactivate | Deactivates a terminal temporarly (season functionality). |
| [**apiV10MasterdataTerminalsTerminalIdDecommissionDatePost**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdDecommissionDatePost) | **POST** /api/v1.0/masterdata/terminals/{terminalId}/decommissionDate | Set date of decommissioning for selected terminal in the cloud database. |
| [**apiV10MasterdataTerminalsTerminalIdDelete**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdDelete) | **DELETE** /api/v1.0/masterdata/terminals/{terminalId} | Deletes a terminal from the cloud store. |
| [**apiV10MasterdataTerminalsTerminalIdFiscaldataDelete**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdFiscaldataDelete) | **DELETE** /api/v1.0/masterdata/terminals/{terminalId}/fiscaldata | Deletes the cloud storage data of the test terminal. If called on a productive terminal exception / error will be raised. |
| [**apiV10MasterdataTerminalsTerminalIdGet**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId} | Returns the requested terminal for the authenticated user. |
| [**apiV10MasterdataTerminalsTerminalIdGet_0**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdGet_0) | **GET** /api/v1.0/masterdata/terminals/terminal/id | Returns the terminal id for the requested parameter. |
| [**apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost) | **POST** /api/v1.0/masterdata/terminals/{terminalId}/globalShortId | Generates a new or returns the already existing global short id for the terminal. |
| [**apiV10MasterdataTerminalsTerminalIdHead**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdHead) | **HEAD** /api/v1.0/masterdata/terminals/{terminalId} | Test if access to terminal is allowed. |
| [**apiV10MasterdataTerminalsTerminalIdInsightsGet**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdInsightsGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId}/insights | Returns terminal insights |
| [**apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId}/iotAccessKey | Generates a new or returns the already existing encrypted iot access key for the terminal. |
| [**apiV10MasterdataTerminalsTerminalIdPut**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdPut) | **PUT** /api/v1.0/masterdata/terminals/{terminalId} | Updates the terminal in the cloud store. |
| [**apiV10MasterdataTerminalsTerminalIdSupportPackagesGet**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdSupportPackagesGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId}/supportPackages | Returns the last 15 support packages if available. |
| [**apiV10MasterdataTerminalsTerminalIdVersionsGet**](MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdVersionsGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId}/versions | Get terminal versions |


<a id="apiV10MasterdataTerminalsGet"></a>
# **apiV10MasterdataTerminalsGet**
> TerminalModelPageResultModel apiV10MasterdataTerminalsGet(storeId, organizationId, companyId, pageOffset, pageSize, searchString)

Returns all terminals for the requested store for the authenticated user.

At least one of organizationId, companyId or storeId parameter must have a value.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID storeId = UUID.randomUUID(); // UUID | The store id of the requested terminals. If null all terminals are returned.
    UUID organizationId = UUID.randomUUID(); // UUID | The possible organization id of the requested terminals. If null all terminals are returned.
    UUID companyId = UUID.randomUUID(); // UUID | The possible company id of the requested terminals. If null all terminals are returned.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      TerminalModelPageResultModel result = apiInstance.apiV10MasterdataTerminalsGet(storeId, organizationId, companyId, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeId** | **UUID**| The store id of the requested terminals. If null all terminals are returned. | [optional] |
| **organizationId** | **UUID**| The possible organization id of the requested terminals. If null all terminals are returned. | [optional] |
| **companyId** | **UUID**| The possible company id of the requested terminals. If null all terminals are returned. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**TerminalModelPageResultModel**](TerminalModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsPost"></a>
# **apiV10MasterdataTerminalsPost**
> TerminalModel apiV10MasterdataTerminalsPost(terminal)

Creates a terminal in the cloud store.

If RetailForce.Cloud.Model.Terminal.TerminalId set to System.Guid.Empty, then the terminal id will be generated by the service.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    Terminal terminal = new Terminal(); // Terminal | The terminal to create.
    try {
      TerminalModel result = apiInstance.apiV10MasterdataTerminalsPost(terminal);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminal** | [**Terminal**](Terminal.md)| The terminal to create. | [optional] |

### Return type

[**TerminalModel**](TerminalModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsSimpleGet"></a>
# **apiV10MasterdataTerminalsSimpleGet**
> GuidSimpleObjectPageResultModel apiV10MasterdataTerminalsSimpleGet(storeId, pageOffset, pageSize, searchString)

Returns all terminals as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested store for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID storeId = UUID.randomUUID(); // UUID | The store id of the requested store.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    try {
      GuidSimpleObjectPageResultModel result = apiInstance.apiV10MasterdataTerminalsSimpleGet(storeId, pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **storeId** | **UUID**| The store id of the requested store. | [optional] |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |

### Return type

[**GuidSimpleObjectPageResultModel**](GuidSimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdArchivePost"></a>
# **apiV10MasterdataTerminalsTerminalIdArchivePost**
> TerminalModel apiV10MasterdataTerminalsTerminalIdArchivePost(terminalId, reactivate)

Deactivates and archives the given terminal (cannot be undone).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal which has to be archived.
    Boolean reactivate = false; // Boolean | Optional. True if the archived terminal should be reactivated. You have to be an administrative user.
    try {
      TerminalModel result = apiInstance.apiV10MasterdataTerminalsTerminalIdArchivePost(terminalId, reactivate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdArchivePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal which has to be archived. | |
| **reactivate** | **Boolean**| Optional. True if the archived terminal should be reactivated. You have to be an administrative user. | [optional] [default to false] |

### Return type

[**TerminalModel**](TerminalModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdDeactivatePut"></a>
# **apiV10MasterdataTerminalsTerminalIdDeactivatePut**
> TerminalModel apiV10MasterdataTerminalsTerminalIdDeactivatePut(terminalId)

Deactivates a terminal temporarly (season functionality).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal to deactivate.
    try {
      TerminalModel result = apiInstance.apiV10MasterdataTerminalsTerminalIdDeactivatePut(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdDeactivatePut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal to deactivate. | |

### Return type

[**TerminalModel**](TerminalModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdDecommissionDatePost"></a>
# **apiV10MasterdataTerminalsTerminalIdDecommissionDatePost**
> OffsetDateTime apiV10MasterdataTerminalsTerminalIdDecommissionDatePost(terminalId)

Set date of decommissioning for selected terminal in the cloud database.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | Terminal being decommissioned.
    try {
      OffsetDateTime result = apiInstance.apiV10MasterdataTerminalsTerminalIdDecommissionDatePost(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdDecommissionDatePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| Terminal being decommissioned. | |

### Return type

[**OffsetDateTime**](OffsetDateTime.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdDelete"></a>
# **apiV10MasterdataTerminalsTerminalIdDelete**
> apiV10MasterdataTerminalsTerminalIdDelete(terminalId)

Deletes a terminal from the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal to delete.
    try {
      apiInstance.apiV10MasterdataTerminalsTerminalIdDelete(terminalId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal to delete. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdFiscaldataDelete"></a>
# **apiV10MasterdataTerminalsTerminalIdFiscaldataDelete**
> apiV10MasterdataTerminalsTerminalIdFiscaldataDelete(terminalId)

Deletes the cloud storage data of the test terminal. If called on a productive terminal exception / error will be raised.

The terminal itself will not be deleted.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal where the data has to be cleaned.
    try {
      apiInstance.apiV10MasterdataTerminalsTerminalIdFiscaldataDelete(terminalId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdFiscaldataDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal where the data has to be cleaned. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdGet"></a>
# **apiV10MasterdataTerminalsTerminalIdGet**
> TerminalModel apiV10MasterdataTerminalsTerminalIdGet(terminalId, entityVersion)

Returns the requested terminal for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the requested terminal.
    String entityVersion = "entityVersion_example"; // String | load specific version
    try {
      TerminalModel result = apiInstance.apiV10MasterdataTerminalsTerminalIdGet(terminalId, entityVersion);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the requested terminal. | |
| **entityVersion** | **String**| load specific version | [optional] |

### Return type

[**TerminalModel**](TerminalModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdGet_0"></a>
# **apiV10MasterdataTerminalsTerminalIdGet_0**
> UUID apiV10MasterdataTerminalsTerminalIdGet_0(identificationType, identification, storeNumber, terminalNumber)

Returns the terminal id for the requested parameter.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    IdentificationType identificationType = IdentificationType.fromValue("[0] = VatNumber"); // IdentificationType | The type of the identification for the organisation id.
    String identification = "identification_example"; // String | The identification.
    String storeNumber = "storeNumber_example"; // String | The store number for the client configuration.
    String terminalNumber = "terminalNumber_example"; // String | The terminal number for the client configuration.
    try {
      UUID result = apiInstance.apiV10MasterdataTerminalsTerminalIdGet_0(identificationType, identification, storeNumber, terminalNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdGet_0");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **identificationType** | [**IdentificationType**](.md)| The type of the identification for the organisation id. | [optional] [enum: [0] = VatNumber, [1] = TaxNumber, [2] = GlnNumber, [3] = BusinessIdentificationNumber, [4] = Statistical classification, [5] = CommercialRegisterNumber, [6] = CommercialRegisterNumberOffice, [7] = TradeRegisterNumber, [8] = TaxOfficeNumber] |
| **identification** | **String**| The identification. | [optional] |
| **storeNumber** | **String**| The store number for the client configuration. | [optional] |
| **terminalNumber** | **String**| The terminal number for the client configuration. | [optional] |

### Return type

[**UUID**](UUID.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost"></a>
# **apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost**
> String apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost(terminalId)

Generates a new or returns the already existing global short id for the terminal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal where the global short id should be returned.
    try {
      String result = apiInstance.apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal where the global short id should be returned. | |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdHead"></a>
# **apiV10MasterdataTerminalsTerminalIdHead**
> apiV10MasterdataTerminalsTerminalIdHead(terminalId)

Test if access to terminal is allowed.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the requested terminal.
    try {
      apiInstance.apiV10MasterdataTerminalsTerminalIdHead(terminalId);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdHead");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the requested terminal. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdInsightsGet"></a>
# **apiV10MasterdataTerminalsTerminalIdInsightsGet**
> TerminalInsightModel apiV10MasterdataTerminalsTerminalIdInsightsGet(terminalId)

Returns terminal insights

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the requested terminal.
    try {
      TerminalInsightModel result = apiInstance.apiV10MasterdataTerminalsTerminalIdInsightsGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdInsightsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the requested terminal. | |

### Return type

[**TerminalInsightModel**](TerminalInsightModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet"></a>
# **apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet**
> String apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet(terminalId)

Generates a new or returns the already existing encrypted iot access key for the terminal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal where the global short id should be returned.
    try {
      String result = apiInstance.apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal where the global short id should be returned. | |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdPut"></a>
# **apiV10MasterdataTerminalsTerminalIdPut**
> TerminalModel apiV10MasterdataTerminalsTerminalIdPut(terminalId, terminal)

Updates the terminal in the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of terminal to update.
    Terminal terminal = new Terminal(); // Terminal | The data of the terminal to update.
    try {
      TerminalModel result = apiInstance.apiV10MasterdataTerminalsTerminalIdPut(terminalId, terminal);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of terminal to update. | |
| **terminal** | [**Terminal**](Terminal.md)| The data of the terminal to update. | [optional] |

### Return type

[**TerminalModel**](TerminalModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdSupportPackagesGet"></a>
# **apiV10MasterdataTerminalsTerminalIdSupportPackagesGet**
> DownloadLinkPageResultModel apiV10MasterdataTerminalsTerminalIdSupportPackagesGet(terminalId)

Returns the last 15 support packages if available.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal to request the support packages.
    try {
      DownloadLinkPageResultModel result = apiInstance.apiV10MasterdataTerminalsTerminalIdSupportPackagesGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdSupportPackagesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal to request the support packages. | |

### Return type

[**DownloadLinkPageResultModel**](DownloadLinkPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataTerminalsTerminalIdVersionsGet"></a>
# **apiV10MasterdataTerminalsTerminalIdVersionsGet**
> GuidEntityVersionPageResultModel apiV10MasterdataTerminalsTerminalIdVersionsGet(terminalId, pageOffset, pageSize)

Get terminal versions

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataTerminalsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataTerminalsApi apiInstance = new MasterDataTerminalsApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | 
    Integer pageOffset = 56; // Integer | 
    Integer pageSize = 56; // Integer | 
    try {
      GuidEntityVersionPageResultModel result = apiInstance.apiV10MasterdataTerminalsTerminalIdVersionsGet(terminalId, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataTerminalsApi#apiV10MasterdataTerminalsTerminalIdVersionsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**|  | |
| **pageOffset** | **Integer**|  | [optional] |
| **pageSize** | **Integer**|  | [optional] |

### Return type

[**GuidEntityVersionPageResultModel**](GuidEntityVersionPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

