/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.ConfigurationParameter;
import com.untill.retailforce.model.TseDriver;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * TseConfiguration
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TseConfiguration {
  public static final String SERIALIZED_NAME_TSE_DRIVER = "tseDriver";
  @SerializedName(SERIALIZED_NAME_TSE_DRIVER)
  private TseDriver tseDriver;

  public static final String SERIALIZED_NAME_TSE_ID = "tseId";
  @SerializedName(SERIALIZED_NAME_TSE_ID)
  private String tseId;

  public static final String SERIALIZED_NAME_TSE_GUID = "tseGuid";
  @Deprecated
  @SerializedName(SERIALIZED_NAME_TSE_GUID)
  private UUID tseGuid;

  public static final String SERIALIZED_NAME_USE_TSE_GUID = "useTseGuid";
  @Deprecated
  @SerializedName(SERIALIZED_NAME_USE_TSE_GUID)
  private Boolean useTseGuid;

  public static final String SERIALIZED_NAME_TSE_PARAMETER = "tseParameter";
  @SerializedName(SERIALIZED_NAME_TSE_PARAMETER)
  private List<ConfigurationParameter> tseParameter;

  public TseConfiguration() {
  }

  public TseConfiguration tseDriver(TseDriver tseDriver) {
    
    this.tseDriver = tseDriver;
    return this;
  }

   /**
   * Get tseDriver
   * @return tseDriver
  **/
  @javax.annotation.Nonnull
  public TseDriver getTseDriver() {
    return tseDriver;
  }


  public void setTseDriver(TseDriver tseDriver) {
    this.tseDriver = tseDriver;
  }


  public TseConfiguration tseId(String tseId) {
    
    this.tseId = tseId;
    return this;
  }

   /**
   * Get tseId
   * @return tseId
  **/
  @javax.annotation.Nullable
  public String getTseId() {
    return tseId;
  }


  public void setTseId(String tseId) {
    this.tseId = tseId;
  }


  @Deprecated
  public TseConfiguration tseGuid(UUID tseGuid) {
    
    this.tseGuid = tseGuid;
    return this;
  }

   /**
   * Get tseGuid
   * @return tseGuid
   * @deprecated
  **/
  @Deprecated
  @javax.annotation.Nullable
  public UUID getTseGuid() {
    return tseGuid;
  }


  @Deprecated
  public void setTseGuid(UUID tseGuid) {
    this.tseGuid = tseGuid;
  }


  @Deprecated
  public TseConfiguration useTseGuid(Boolean useTseGuid) {
    
    this.useTseGuid = useTseGuid;
    return this;
  }

   /**
   * Get useTseGuid
   * @return useTseGuid
   * @deprecated
  **/
  @Deprecated
  @javax.annotation.Nullable
  public Boolean getUseTseGuid() {
    return useTseGuid;
  }


  @Deprecated
  public void setUseTseGuid(Boolean useTseGuid) {
    this.useTseGuid = useTseGuid;
  }


  public TseConfiguration tseParameter(List<ConfigurationParameter> tseParameter) {
    
    this.tseParameter = tseParameter;
    return this;
  }

  public TseConfiguration addTseParameterItem(ConfigurationParameter tseParameterItem) {
    if (this.tseParameter == null) {
      this.tseParameter = new ArrayList<>();
    }
    this.tseParameter.add(tseParameterItem);
    return this;
  }

   /**
   * Get tseParameter
   * @return tseParameter
  **/
  @javax.annotation.Nullable
  public List<ConfigurationParameter> getTseParameter() {
    return tseParameter;
  }


  public void setTseParameter(List<ConfigurationParameter> tseParameter) {
    this.tseParameter = tseParameter;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TseConfiguration tseConfiguration = (TseConfiguration) o;
    return Objects.equals(this.tseDriver, tseConfiguration.tseDriver) &&
        Objects.equals(this.tseId, tseConfiguration.tseId) &&
        Objects.equals(this.tseGuid, tseConfiguration.tseGuid) &&
        Objects.equals(this.useTseGuid, tseConfiguration.useTseGuid) &&
        Objects.equals(this.tseParameter, tseConfiguration.tseParameter);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tseDriver, tseId, tseGuid, useTseGuid, tseParameter);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TseConfiguration {\n");
    sb.append("    tseDriver: ").append(toIndentedString(tseDriver)).append("\n");
    sb.append("    tseId: ").append(toIndentedString(tseId)).append("\n");
    sb.append("    tseGuid: ").append(toIndentedString(tseGuid)).append("\n");
    sb.append("    useTseGuid: ").append(toIndentedString(useTseGuid)).append("\n");
    sb.append("    tseParameter: ").append(toIndentedString(tseParameter)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("tseDriver");
    openapiFields.add("tseId");
    openapiFields.add("tseGuid");
    openapiFields.add("useTseGuid");
    openapiFields.add("tseParameter");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
    openapiRequiredFields.add("tseDriver");
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TseConfiguration
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TseConfiguration.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TseConfiguration is not found in the empty JSON string", TseConfiguration.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TseConfiguration.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TseConfiguration` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }

      // check to make sure all required properties/fields are present in the JSON string
      for (String requiredField : TseConfiguration.openapiRequiredFields) {
        if (jsonObj.get(requiredField) == null) {
          throw new IllegalArgumentException(String.format("The required field `%s` is not found in the JSON string: %s", requiredField, jsonObj.toString()));
        }
      }
      if ((jsonObj.get("tseId") != null && !jsonObj.get("tseId").isJsonNull()) && !jsonObj.get("tseId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `tseId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("tseId").toString()));
      }
      if ((jsonObj.get("tseGuid") != null && !jsonObj.get("tseGuid").isJsonNull()) && !jsonObj.get("tseGuid").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `tseGuid` to be a primitive type in the JSON string but got `%s`", jsonObj.get("tseGuid").toString()));
      }
      if (jsonObj.get("tseParameter") != null && !jsonObj.get("tseParameter").isJsonNull()) {
        JsonArray jsonArraytseParameter = jsonObj.getAsJsonArray("tseParameter");
        if (jsonArraytseParameter != null) {
          // ensure the json data is an array
          if (!jsonObj.get("tseParameter").isJsonArray()) {
            throw new IllegalArgumentException(String.format("Expected the field `tseParameter` to be an array in the JSON string but got `%s`", jsonObj.get("tseParameter").toString()));
          }

          // validate the optional field `tseParameter` (array)
          for (int i = 0; i < jsonArraytseParameter.size(); i++) {
            ConfigurationParameter.validateJsonObject(jsonArraytseParameter.get(i).getAsJsonObject());
          };
        }
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TseConfiguration.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TseConfiguration' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TseConfiguration> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TseConfiguration.class));

       return (TypeAdapter<T>) new TypeAdapter<TseConfiguration>() {
           @Override
           public void write(JsonWriter out, TseConfiguration value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TseConfiguration read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TseConfiguration given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TseConfiguration
  * @throws IOException if the JSON string is invalid with respect to TseConfiguration
  */
  public static TseConfiguration fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TseConfiguration.class);
  }

 /**
  * Convert an instance of TseConfiguration to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

