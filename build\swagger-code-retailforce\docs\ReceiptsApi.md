# ReceiptsApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ReceiptsSearchOrganizationIdGet**](ReceiptsApi.md#apiV10ReceiptsSearchOrganizationIdGet) | **GET** /api/v1.0/receipts/search/{organizationId} | Method to search and display receipts from digital receipt store (digital receipt license necessary). |
| [**apiV10ReceiptsSearchOrganizationIdPost**](ReceiptsApi.md#apiV10ReceiptsSearchOrganizationIdPost) | **POST** /api/v1.0/receipts/search/{organizationId} | Method to continue search by continuationToken. |
| [**apiV10ReceiptsSearchOrganizationIdProcessIdGet**](ReceiptsApi.md#apiV10ReceiptsSearchOrganizationIdProcessIdGet) | **GET** /api/v1.0/receipts/search/{organizationId}/{processId} | Method to search connected receipts (connected with processId, for instance to search for cancelled receipts and cancelling receipt). |


<a id="apiV10ReceiptsSearchOrganizationIdGet"></a>
# **apiV10ReceiptsSearchOrganizationIdGet**
> ReceiptDataScrollResultModel apiV10ReceiptsSearchOrganizationIdGet(organizationId, companyId, storeId, terminalId, dateFrom, dateTill, documentType, amount, partner, users, items, documentNumber, documentId, cancelled, paymentCardNumberMasked)

Method to search and display receipts from digital receipt store (digital receipt license necessary).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ReceiptsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ReceiptsApi apiInstance = new ReceiptsApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization id where the receipt have to searched.
    UUID companyId = UUID.randomUUID(); // UUID | The company id where the receipt have to searched.
    UUID storeId = UUID.randomUUID(); // UUID | Filter value to filter result for stores.
    UUID terminalId = UUID.randomUUID(); // UUID | Filter value to filter result for terminals.
    OffsetDateTime dateFrom = OffsetDateTime.now(); // OffsetDateTime | Filter value to filter result for book date (from).
    OffsetDateTime dateTill = OffsetDateTime.now(); // OffsetDateTime | Filter value to filter result for book date (till).
    Integer documentType = 56; // Integer | Filter value to filter result for special document types.
    String amount = "amount_example"; // String | Filter value to filter result for an amount (circa). range of 10 percentage will be added
    String partner = "partner_example"; // String | Filter value to filter result for customer/partner data.
    String users = "users_example"; // String | Filter value to filter for one or more users (caption, id), delimiter ','
    String items = "items_example"; // String | Filter value to filter for one or more items (itemId, itemcode), delimiter ','
    String documentNumber = "documentNumber_example"; // String | Filter value to filter for the document number.
    String documentId = "documentId_example"; // String | Filter value to filter for the document id.
    Boolean cancelled = true; // Boolean | Filter value to filter for cancelled documents.
    String paymentCardNumberMasked = "paymentCardNumberMasked_example"; // String | Filter value to filter for one paymentcardnumber (Creditcard, Google/Apple/Samsung Pay). It is not allowed to send clear readable full card number!
    try {
      ReceiptDataScrollResultModel result = apiInstance.apiV10ReceiptsSearchOrganizationIdGet(organizationId, companyId, storeId, terminalId, dateFrom, dateTill, documentType, amount, partner, users, items, documentNumber, documentId, cancelled, paymentCardNumberMasked);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ReceiptsApi#apiV10ReceiptsSearchOrganizationIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization id where the receipt have to searched. | |
| **companyId** | **UUID**| The company id where the receipt have to searched. | [optional] |
| **storeId** | **UUID**| Filter value to filter result for stores. | [optional] |
| **terminalId** | **UUID**| Filter value to filter result for terminals. | [optional] |
| **dateFrom** | **OffsetDateTime**| Filter value to filter result for book date (from). | [optional] |
| **dateTill** | **OffsetDateTime**| Filter value to filter result for book date (till). | [optional] |
| **documentType** | **Integer**| Filter value to filter result for special document types. | [optional] |
| **amount** | **String**| Filter value to filter result for an amount (circa). range of 10 percentage will be added | [optional] |
| **partner** | **String**| Filter value to filter result for customer/partner data. | [optional] |
| **users** | **String**| Filter value to filter for one or more users (caption, id), delimiter &#39;,&#39; | [optional] |
| **items** | **String**| Filter value to filter for one or more items (itemId, itemcode), delimiter &#39;,&#39; | [optional] |
| **documentNumber** | **String**| Filter value to filter for the document number. | [optional] |
| **documentId** | **String**| Filter value to filter for the document id. | [optional] |
| **cancelled** | **Boolean**| Filter value to filter for cancelled documents. | [optional] |
| **paymentCardNumberMasked** | **String**| Filter value to filter for one paymentcardnumber (Creditcard, Google/Apple/Samsung Pay). It is not allowed to send clear readable full card number! | [optional] |

### Return type

[**ReceiptDataScrollResultModel**](ReceiptDataScrollResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ReceiptsSearchOrganizationIdPost"></a>
# **apiV10ReceiptsSearchOrganizationIdPost**
> ReceiptDataScrollResultModel apiV10ReceiptsSearchOrganizationIdPost(organizationId, continuationToken, query)

Method to continue search by continuationToken.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ReceiptsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ReceiptsApi apiInstance = new ReceiptsApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization id where the receipt have to searched.
    String continuationToken = "continuationToken_example"; // String | 
    String query = "query_example"; // String | 
    try {
      ReceiptDataScrollResultModel result = apiInstance.apiV10ReceiptsSearchOrganizationIdPost(organizationId, continuationToken, query);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ReceiptsApi#apiV10ReceiptsSearchOrganizationIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization id where the receipt have to searched. | |
| **continuationToken** | **String**|  | [optional] |
| **query** | **String**|  | [optional] |

### Return type

[**ReceiptDataScrollResultModel**](ReceiptDataScrollResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ReceiptsSearchOrganizationIdProcessIdGet"></a>
# **apiV10ReceiptsSearchOrganizationIdProcessIdGet**
> ReceiptDataScrollResultModel apiV10ReceiptsSearchOrganizationIdProcessIdGet(organizationId, processId)

Method to search connected receipts (connected with processId, for instance to search for cancelled receipts and cancelling receipt).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ReceiptsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ReceiptsApi apiInstance = new ReceiptsApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization id where the receipt have to searched.
    String processId = "processId_example"; // String | The connecting process id. Must be sent by client.
    try {
      ReceiptDataScrollResultModel result = apiInstance.apiV10ReceiptsSearchOrganizationIdProcessIdGet(organizationId, processId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ReceiptsApi#apiV10ReceiptsSearchOrganizationIdProcessIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization id where the receipt have to searched. | |
| **processId** | **String**| The connecting process id. Must be sent by client. | |

### Return type

[**ReceiptDataScrollResultModel**](ReceiptDataScrollResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

