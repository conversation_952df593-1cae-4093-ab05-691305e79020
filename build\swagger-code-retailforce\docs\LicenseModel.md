

# LicenseModel

Represents a license model for a configuration or entity

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**licenseId** | **String** | The id of the license. |  [optional] |
|**caption** | **String** | The caption of the license. |  [optional] |
|**description** | **String** | The description of the license. |  [optional] |
|**unit** | **String** | The unit of the license. |  [optional] |
|**supplierName** | **String** | The name of the supplier. |  [optional] |
|**supplierId** | **UUID** | The guid of the supplier. |  [optional] |
|**signatureMissing** | **Boolean** | Returns if one or more contracts are not signed to use the license. See RetailForce.Cloud.Model.Licensing.LicenseModel.SignatureMissingContracts for missing signed contracts. |  [optional] |
|**options** | [**List&lt;LicenseOption&gt;**](LicenseOption.md) | A list of possible or effective options (access licenses) for this license. |  [optional] |
|**signatureMissingContracts** | **List&lt;UUID&gt;** | A list of all contracts which are not signed until yet. If empty nothing is to sign. |  [optional] |



