/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.annotations.SerializedName;

import java.io.IOException;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

/**
 * Gets or Sets SecurityCertificateDropoutReason
 */
@JsonAdapter(SecurityCertificateDropoutReason.Adapter.class)
public enum SecurityCertificateDropoutReason {
  
  STOLEN("stolen"),
  
  NOTWORKING("notWorking"),
  
  PLANEDDROPOUT("planedDropout"),
  
  UNREPAIRABLE("unrepairable"),
  
  OTHERS("others");

  private String value;

  SecurityCertificateDropoutReason(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  public static SecurityCertificateDropoutReason fromValue(String value) {
    for (SecurityCertificateDropoutReason b : SecurityCertificateDropoutReason.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }

  public static class Adapter extends TypeAdapter<SecurityCertificateDropoutReason> {
    @Override
    public void write(final JsonWriter jsonWriter, final SecurityCertificateDropoutReason enumeration) throws IOException {
      jsonWriter.value(enumeration.getValue());
    }

    @Override
    public SecurityCertificateDropoutReason read(final JsonReader jsonReader) throws IOException {
      String value = jsonReader.nextString();
      return SecurityCertificateDropoutReason.fromValue(value);
    }
  }
}

