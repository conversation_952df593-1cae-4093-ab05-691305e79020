/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.GuidEntityVersionPageResultModel;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import com.untill.retailforce.model.Store;
import com.untill.retailforce.model.StoreModel;
import com.untill.retailforce.model.StoreModelPageResultModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class MasterDataStoresApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public MasterDataStoresApi() {
        this(Configuration.getDefaultApiClient());
    }

    public MasterDataStoresApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10MasterdataStoresGet
     * @param organisationId The organization id for the requested stores. (optional)
     * @param companyId The company id for the requested stores. If set to null filter is not applied. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param showHiddenStores include hidden stores (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresGetCall(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, Boolean showHiddenStores, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (companyId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("companyId", companyId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        if (showHiddenStores != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("showHiddenStores", showHiddenStores));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresGetValidateBeforeCall(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, Boolean showHiddenStores, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataStoresGetCall(organisationId, companyId, pageOffset, pageSize, searchString, showHiddenStores, _callback);

    }

    /**
     * Returns all stores for the given organisation/company for the authenticated user.
     * 
     * @param organisationId The organization id for the requested stores. (optional)
     * @param companyId The company id for the requested stores. If set to null filter is not applied. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param showHiddenStores include hidden stores (optional)
     * @return StoreModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public StoreModelPageResultModel apiV10MasterdataStoresGet(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, Boolean showHiddenStores) throws ApiException {
        ApiResponse<StoreModelPageResultModel> localVarResp = apiV10MasterdataStoresGetWithHttpInfo(organisationId, companyId, pageOffset, pageSize, searchString, showHiddenStores);
        return localVarResp.getData();
    }

    /**
     * Returns all stores for the given organisation/company for the authenticated user.
     * 
     * @param organisationId The organization id for the requested stores. (optional)
     * @param companyId The company id for the requested stores. If set to null filter is not applied. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param showHiddenStores include hidden stores (optional)
     * @return ApiResponse&lt;StoreModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<StoreModelPageResultModel> apiV10MasterdataStoresGetWithHttpInfo(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, Boolean showHiddenStores) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresGetValidateBeforeCall(organisationId, companyId, pageOffset, pageSize, searchString, showHiddenStores, null);
        Type localVarReturnType = new TypeToken<StoreModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all stores for the given organisation/company for the authenticated user. (asynchronously)
     * 
     * @param organisationId The organization id for the requested stores. (optional)
     * @param companyId The company id for the requested stores. If set to null filter is not applied. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param showHiddenStores include hidden stores (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresGetAsync(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, Boolean showHiddenStores, final ApiCallback<StoreModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresGetValidateBeforeCall(organisationId, companyId, pageOffset, pageSize, searchString, showHiddenStores, _callback);
        Type localVarReturnType = new TypeToken<StoreModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresIdGet
     * @param organizationId The id of the organization of the store. (optional)
     * @param storeNumber The store number of the store. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresIdGetCall(UUID organizationId, String storeNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores/id";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organizationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organizationId", organizationId));
        }

        if (storeNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeNumber", storeNumber));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresIdGetValidateBeforeCall(UUID organizationId, String storeNumber, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataStoresIdGetCall(organizationId, storeNumber, _callback);

    }

    /**
     * Returns the store id of the requested store.
     * 
     * @param organizationId The id of the organization of the store. (optional)
     * @param storeNumber The store number of the store. (optional)
     * @return UUID
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public UUID apiV10MasterdataStoresIdGet(UUID organizationId, String storeNumber) throws ApiException {
        ApiResponse<UUID> localVarResp = apiV10MasterdataStoresIdGetWithHttpInfo(organizationId, storeNumber);
        return localVarResp.getData();
    }

    /**
     * Returns the store id of the requested store.
     * 
     * @param organizationId The id of the organization of the store. (optional)
     * @param storeNumber The store number of the store. (optional)
     * @return ApiResponse&lt;UUID&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<UUID> apiV10MasterdataStoresIdGetWithHttpInfo(UUID organizationId, String storeNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresIdGetValidateBeforeCall(organizationId, storeNumber, null);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the store id of the requested store. (asynchronously)
     * 
     * @param organizationId The id of the organization of the store. (optional)
     * @param storeNumber The store number of the store. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresIdGetAsync(UUID organizationId, String storeNumber, final ApiCallback<UUID> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresIdGetValidateBeforeCall(organizationId, storeNumber, _callback);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresPost
     * @param store The store to create. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresPostCall(Store store, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = store;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresPostValidateBeforeCall(Store store, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'store' is set
        if (store == null) {
            throw new ApiException("Missing the required parameter 'store' when calling apiV10MasterdataStoresPost(Async)");
        }

        return apiV10MasterdataStoresPostCall(store, _callback);

    }

    /**
     * Creates a store in the cloud store.
     * If RetailForce.Cloud.Model.Store.StoreId set to System.Guid.Empty, then the store id will be generated by the service.
     * @param store The store to create. (required)
     * @return StoreModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public StoreModel apiV10MasterdataStoresPost(Store store) throws ApiException {
        ApiResponse<StoreModel> localVarResp = apiV10MasterdataStoresPostWithHttpInfo(store);
        return localVarResp.getData();
    }

    /**
     * Creates a store in the cloud store.
     * If RetailForce.Cloud.Model.Store.StoreId set to System.Guid.Empty, then the store id will be generated by the service.
     * @param store The store to create. (required)
     * @return ApiResponse&lt;StoreModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<StoreModel> apiV10MasterdataStoresPostWithHttpInfo(Store store) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresPostValidateBeforeCall(store, null);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a store in the cloud store. (asynchronously)
     * If RetailForce.Cloud.Model.Store.StoreId set to System.Guid.Empty, then the store id will be generated by the service.
     * @param store The store to create. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresPostAsync(Store store, final ApiCallback<StoreModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresPostValidateBeforeCall(store, _callback);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresSimpleGet
     * @param organisationId The organisation id for the requested stores. (optional)
     * @param companyId The company id for the requested stores. If set to null filter is not applied. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresSimpleGetCall(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores/simple";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (companyId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("companyId", companyId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresSimpleGetValidateBeforeCall(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataStoresSimpleGetCall(organisationId, companyId, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all stores as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the given organisation/company for the authenticated user.
     * 
     * @param organisationId The organisation id for the requested stores. (optional)
     * @param companyId The company id for the requested stores. If set to null filter is not applied. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return GuidSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObjectPageResultModel apiV10MasterdataStoresSimpleGet(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<GuidSimpleObjectPageResultModel> localVarResp = apiV10MasterdataStoresSimpleGetWithHttpInfo(organisationId, companyId, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all stores as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the given organisation/company for the authenticated user.
     * 
     * @param organisationId The organisation id for the requested stores. (optional)
     * @param companyId The company id for the requested stores. If set to null filter is not applied. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;GuidSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObjectPageResultModel> apiV10MasterdataStoresSimpleGetWithHttpInfo(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresSimpleGetValidateBeforeCall(organisationId, companyId, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all stores as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the given organisation/company for the authenticated user. (asynchronously)
     * 
     * @param organisationId The organisation id for the requested stores. (optional)
     * @param companyId The company id for the requested stores. If set to null filter is not applied. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresSimpleGetAsync(UUID organisationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<GuidSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresSimpleGetValidateBeforeCall(organisationId, companyId, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresStoreIdDelete
     * @param storeId The id of the store to delete. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdDeleteCall(UUID storeId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores/{storeId}"
            .replace("{" + "storeId" + "}", localVarApiClient.escapeString(storeId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresStoreIdDeleteValidateBeforeCall(UUID storeId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'storeId' is set
        if (storeId == null) {
            throw new ApiException("Missing the required parameter 'storeId' when calling apiV10MasterdataStoresStoreIdDelete(Async)");
        }

        return apiV10MasterdataStoresStoreIdDeleteCall(storeId, _callback);

    }

    /**
     * Deletes a store from the cloud store.
     * 
     * @param storeId The id of the store to delete. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataStoresStoreIdDelete(UUID storeId) throws ApiException {
        apiV10MasterdataStoresStoreIdDeleteWithHttpInfo(storeId);
    }

    /**
     * Deletes a store from the cloud store.
     * 
     * @param storeId The id of the store to delete. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataStoresStoreIdDeleteWithHttpInfo(UUID storeId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdDeleteValidateBeforeCall(storeId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes a store from the cloud store. (asynchronously)
     * 
     * @param storeId The id of the store to delete. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdDeleteAsync(UUID storeId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdDeleteValidateBeforeCall(storeId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresStoreIdGet
     * @param storeId The id of the requested store. (required)
     * @param entityVersion load specific version (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdGetCall(UUID storeId, String entityVersion, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores/{storeId}"
            .replace("{" + "storeId" + "}", localVarApiClient.escapeString(storeId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (entityVersion != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityVersion", entityVersion));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresStoreIdGetValidateBeforeCall(UUID storeId, String entityVersion, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'storeId' is set
        if (storeId == null) {
            throw new ApiException("Missing the required parameter 'storeId' when calling apiV10MasterdataStoresStoreIdGet(Async)");
        }

        return apiV10MasterdataStoresStoreIdGetCall(storeId, entityVersion, _callback);

    }

    /**
     * Returns the requested store for the authenticated users.
     * 
     * @param storeId The id of the requested store. (required)
     * @param entityVersion load specific version (optional)
     * @return StoreModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public StoreModel apiV10MasterdataStoresStoreIdGet(UUID storeId, String entityVersion) throws ApiException {
        ApiResponse<StoreModel> localVarResp = apiV10MasterdataStoresStoreIdGetWithHttpInfo(storeId, entityVersion);
        return localVarResp.getData();
    }

    /**
     * Returns the requested store for the authenticated users.
     * 
     * @param storeId The id of the requested store. (required)
     * @param entityVersion load specific version (optional)
     * @return ApiResponse&lt;StoreModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<StoreModel> apiV10MasterdataStoresStoreIdGetWithHttpInfo(UUID storeId, String entityVersion) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdGetValidateBeforeCall(storeId, entityVersion, null);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the requested store for the authenticated users. (asynchronously)
     * 
     * @param storeId The id of the requested store. (required)
     * @param entityVersion load specific version (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdGetAsync(UUID storeId, String entityVersion, final ApiCallback<StoreModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdGetValidateBeforeCall(storeId, entityVersion, _callback);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresStoreIdHidePost
     * @param storeId The id of the store to hide. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdHidePostCall(UUID storeId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores/{storeId}/hide"
            .replace("{" + "storeId" + "}", localVarApiClient.escapeString(storeId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresStoreIdHidePostValidateBeforeCall(UUID storeId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'storeId' is set
        if (storeId == null) {
            throw new ApiException("Missing the required parameter 'storeId' when calling apiV10MasterdataStoresStoreIdHidePost(Async)");
        }

        return apiV10MasterdataStoresStoreIdHidePostCall(storeId, _callback);

    }

    /**
     * Hides the store from the standard list.
     * 
     * @param storeId The id of the store to hide. (required)
     * @return StoreModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public StoreModel apiV10MasterdataStoresStoreIdHidePost(UUID storeId) throws ApiException {
        ApiResponse<StoreModel> localVarResp = apiV10MasterdataStoresStoreIdHidePostWithHttpInfo(storeId);
        return localVarResp.getData();
    }

    /**
     * Hides the store from the standard list.
     * 
     * @param storeId The id of the store to hide. (required)
     * @return ApiResponse&lt;StoreModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<StoreModel> apiV10MasterdataStoresStoreIdHidePostWithHttpInfo(UUID storeId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdHidePostValidateBeforeCall(storeId, null);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Hides the store from the standard list. (asynchronously)
     * 
     * @param storeId The id of the store to hide. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdHidePostAsync(UUID storeId, final ApiCallback<StoreModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdHidePostValidateBeforeCall(storeId, _callback);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresStoreIdPut
     * @param storeId The id of the store to update. (required)
     * @param store The data of the store to update. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdPutCall(UUID storeId, Store store, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = store;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores/{storeId}"
            .replace("{" + "storeId" + "}", localVarApiClient.escapeString(storeId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresStoreIdPutValidateBeforeCall(UUID storeId, Store store, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'storeId' is set
        if (storeId == null) {
            throw new ApiException("Missing the required parameter 'storeId' when calling apiV10MasterdataStoresStoreIdPut(Async)");
        }

        // verify the required parameter 'store' is set
        if (store == null) {
            throw new ApiException("Missing the required parameter 'store' when calling apiV10MasterdataStoresStoreIdPut(Async)");
        }

        return apiV10MasterdataStoresStoreIdPutCall(storeId, store, _callback);

    }

    /**
     * Updates a store in the cloud store.
     * 
     * @param storeId The id of the store to update. (required)
     * @param store The data of the store to update. (required)
     * @return StoreModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public StoreModel apiV10MasterdataStoresStoreIdPut(UUID storeId, Store store) throws ApiException {
        ApiResponse<StoreModel> localVarResp = apiV10MasterdataStoresStoreIdPutWithHttpInfo(storeId, store);
        return localVarResp.getData();
    }

    /**
     * Updates a store in the cloud store.
     * 
     * @param storeId The id of the store to update. (required)
     * @param store The data of the store to update. (required)
     * @return ApiResponse&lt;StoreModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<StoreModel> apiV10MasterdataStoresStoreIdPutWithHttpInfo(UUID storeId, Store store) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdPutValidateBeforeCall(storeId, store, null);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates a store in the cloud store. (asynchronously)
     * 
     * @param storeId The id of the store to update. (required)
     * @param store The data of the store to update. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdPutAsync(UUID storeId, Store store, final ApiCallback<StoreModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdPutValidateBeforeCall(storeId, store, _callback);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresStoreIdUnHidePost
     * @param storeId The id of the store to un-hide. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdUnHidePostCall(UUID storeId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores/{storeId}/unHide"
            .replace("{" + "storeId" + "}", localVarApiClient.escapeString(storeId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresStoreIdUnHidePostValidateBeforeCall(UUID storeId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'storeId' is set
        if (storeId == null) {
            throw new ApiException("Missing the required parameter 'storeId' when calling apiV10MasterdataStoresStoreIdUnHidePost(Async)");
        }

        return apiV10MasterdataStoresStoreIdUnHidePostCall(storeId, _callback);

    }

    /**
     * Un-hides a previously hidden store to the standard list.
     * 
     * @param storeId The id of the store to un-hide. (required)
     * @return StoreModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public StoreModel apiV10MasterdataStoresStoreIdUnHidePost(UUID storeId) throws ApiException {
        ApiResponse<StoreModel> localVarResp = apiV10MasterdataStoresStoreIdUnHidePostWithHttpInfo(storeId);
        return localVarResp.getData();
    }

    /**
     * Un-hides a previously hidden store to the standard list.
     * 
     * @param storeId The id of the store to un-hide. (required)
     * @return ApiResponse&lt;StoreModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<StoreModel> apiV10MasterdataStoresStoreIdUnHidePostWithHttpInfo(UUID storeId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdUnHidePostValidateBeforeCall(storeId, null);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Un-hides a previously hidden store to the standard list. (asynchronously)
     * 
     * @param storeId The id of the store to un-hide. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdUnHidePostAsync(UUID storeId, final ApiCallback<StoreModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdUnHidePostValidateBeforeCall(storeId, _callback);
        Type localVarReturnType = new TypeToken<StoreModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataStoresStoreIdVersionsGet
     * @param storeId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdVersionsGetCall(UUID storeId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/stores/{storeId}/versions"
            .replace("{" + "storeId" + "}", localVarApiClient.escapeString(storeId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataStoresStoreIdVersionsGetValidateBeforeCall(UUID storeId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'storeId' is set
        if (storeId == null) {
            throw new ApiException("Missing the required parameter 'storeId' when calling apiV10MasterdataStoresStoreIdVersionsGet(Async)");
        }

        return apiV10MasterdataStoresStoreIdVersionsGetCall(storeId, pageOffset, pageSize, _callback);

    }

    /**
     * Get store versions
     * 
     * @param storeId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return GuidEntityVersionPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidEntityVersionPageResultModel apiV10MasterdataStoresStoreIdVersionsGet(UUID storeId, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<GuidEntityVersionPageResultModel> localVarResp = apiV10MasterdataStoresStoreIdVersionsGetWithHttpInfo(storeId, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Get store versions
     * 
     * @param storeId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return ApiResponse&lt;GuidEntityVersionPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidEntityVersionPageResultModel> apiV10MasterdataStoresStoreIdVersionsGetWithHttpInfo(UUID storeId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdVersionsGetValidateBeforeCall(storeId, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<GuidEntityVersionPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get store versions (asynchronously)
     * 
     * @param storeId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataStoresStoreIdVersionsGetAsync(UUID storeId, Integer pageOffset, Integer pageSize, final ApiCallback<GuidEntityVersionPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataStoresStoreIdVersionsGetValidateBeforeCall(storeId, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<GuidEntityVersionPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
