/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.BoolResponse;
import com.untill.retailforce.model.CashRegisterDropoutReason;
import com.untill.retailforce.model.FonCredentials;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.ResultResponse;
import com.untill.retailforce.model.SecurityCertificateDropoutReason;
import com.untill.retailforce.model.SecurityCertificateIssuer;
import com.untill.retailforce.model.SecurityCertificateType;
import com.untill.retailforce.model.SignDeviceDriverInfo;
import com.untill.retailforce.model.StringSimpleObject;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ConfigurationControllerAustriaApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ConfigurationControllerAustriaApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ConfigurationControllerAustriaApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ConfigurationAtFonCheckFonCredentialsPut
     * @param fonCredentials The fon credentials to logon. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> fonCredentials is null or any of the properties of fonCredentials is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonCheckFonCredentialsPutCall(FonCredentials fonCredentials, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = fonCredentials;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/checkFonCredentials";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonCheckFonCredentialsPutValidateBeforeCall(FonCredentials fonCredentials, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'fonCredentials' is set
        if (fonCredentials == null) {
            throw new ApiException("Missing the required parameter 'fonCredentials' when calling apiV10ConfigurationAtFonCheckFonCredentialsPut(Async)");
        }

        return apiV10ConfigurationAtFonCheckFonCredentialsPutCall(fonCredentials, _callback);

    }

    /**
     * Checks if an authentication can be done with the given fon credentials.
     * 
     * @param fonCredentials The fon credentials to logon. (required)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> fonCredentials is null or any of the properties of fonCredentials is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonCheckFonCredentialsPut(FonCredentials fonCredentials) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonCheckFonCredentialsPutWithHttpInfo(fonCredentials);
        return localVarResp.getData();
    }

    /**
     * Checks if an authentication can be done with the given fon credentials.
     * 
     * @param fonCredentials The fon credentials to logon. (required)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> fonCredentials is null or any of the properties of fonCredentials is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonCheckFonCredentialsPutWithHttpInfo(FonCredentials fonCredentials) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonCheckFonCredentialsPutValidateBeforeCall(fonCredentials, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Checks if an authentication can be done with the given fon credentials. (asynchronously)
     * 
     * @param fonCredentials The fon credentials to logon. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> fonCredentials is null or any of the properties of fonCredentials is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonCheckFonCredentialsPutAsync(FonCredentials fonCredentials, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonCheckFonCredentialsPutValidateBeforeCall(fonCredentials, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdAesKeyPost
     * @param clientId The client for which the request is done. (required)
     * @param body A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Aes key was not send with single quotes enclosing. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated or has no access to the given client. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or aesKey was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdAesKeyPostCall(UUID clientId, String body, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = body;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/aesKey"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdAesKeyPostValidateBeforeCall(UUID clientId, String body, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdAesKeyPost(Async)");
        }

        return apiV10ConfigurationAtFonClientIdAesKeyPostCall(clientId, body, _callback);

    }

    /**
     * Updates a the aes key for the given terminal in the retailforce database (or adds).
     * This method does not update the aes key at fon (Finanzonline).  Handle with care: Settings this value to a wrong value will result in an error when recovering fiscal client with RestoreByCloud and decrypting of turnover counter of dep of this client will no longer be possible.
     * @param clientId The client for which the request is done. (required)
     * @param body A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Aes key was not send with single quotes enclosing. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated or has no access to the given client. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or aesKey was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationAtFonClientIdAesKeyPost(UUID clientId, String body) throws ApiException {
        apiV10ConfigurationAtFonClientIdAesKeyPostWithHttpInfo(clientId, body);
    }

    /**
     * Updates a the aes key for the given terminal in the retailforce database (or adds).
     * This method does not update the aes key at fon (Finanzonline).  Handle with care: Settings this value to a wrong value will result in an error when recovering fiscal client with RestoreByCloud and decrypting of turnover counter of dep of this client will no longer be possible.
     * @param clientId The client for which the request is done. (required)
     * @param body A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Aes key was not send with single quotes enclosing. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated or has no access to the given client. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or aesKey was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationAtFonClientIdAesKeyPostWithHttpInfo(UUID clientId, String body) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdAesKeyPostValidateBeforeCall(clientId, body, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Updates a the aes key for the given terminal in the retailforce database (or adds). (asynchronously)
     * This method does not update the aes key at fon (Finanzonline).  Handle with care: Settings this value to a wrong value will result in an error when recovering fiscal client with RestoreByCloud and decrypting of turnover counter of dep of this client will no longer be possible.
     * @param clientId The client for which the request is done. (required)
     * @param body A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Aes key was not send with single quotes enclosing. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated or has no access to the given client. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or aesKey was set to null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdAesKeyPostAsync(UUID clientId, String body, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdAesKeyPostValidateBeforeCall(clientId, body, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCacheDelete
     * @param clientId The clientId where the cache should be removed. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCacheDeleteCall(UUID clientId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/cache"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCacheDeleteValidateBeforeCall(UUID clientId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCacheDelete(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCacheDeleteCall(clientId, _callback);

    }

    /**
     * Clears fon cache variable on server (for faster fon communication).
     * Removes both (Test and Productive) cache objects.
     * @param clientId The clientId where the cache should be removed. (required)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonClientIdCacheDelete(UUID clientId) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonClientIdCacheDeleteWithHttpInfo(clientId);
        return localVarResp.getData();
    }

    /**
     * Clears fon cache variable on server (for faster fon communication).
     * Removes both (Test and Productive) cache objects.
     * @param clientId The clientId where the cache should be removed. (required)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonClientIdCacheDeleteWithHttpInfo(UUID clientId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCacheDeleteValidateBeforeCall(clientId, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Clears fon cache variable on server (for faster fon communication). (asynchronously)
     * Removes both (Test and Productive) cache objects.
     * @param clientId The clientId where the cache should be removed. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCacheDeleteAsync(UUID clientId, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCacheDeleteValidateBeforeCall(clientId, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCashregisterDelete
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId The cash register id for which the request is done. (required)
     * @param reason Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. (required)
     * @param fromDate The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [5] NotWorking, [99] &#x3D; Others. Default is DateTime.Now. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterDeleteCall(UUID clientId, String cashRegisterId, CashRegisterDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/cashregister"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (cashRegisterId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("cashRegisterId", cashRegisterId));
        }

        if (reason != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("reason", reason));
        }

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (requestDateTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("requestDateTime", requestDateTime));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterDeleteValidateBeforeCall(UUID clientId, String cashRegisterId, CashRegisterDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCashregisterDelete(Async)");
        }

        // verify the required parameter 'cashRegisterId' is set
        if (cashRegisterId == null) {
            throw new ApiException("Missing the required parameter 'cashRegisterId' when calling apiV10ConfigurationAtFonClientIdCashregisterDelete(Async)");
        }

        // verify the required parameter 'reason' is set
        if (reason == null) {
            throw new ApiException("Missing the required parameter 'reason' when calling apiV10ConfigurationAtFonClientIdCashregisterDelete(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCashregisterDeleteCall(clientId, cashRegisterId, reason, fromDate, requestDateTime, isTest, _callback);

    }

    /**
     * Unregister cash register at fon service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId The cash register id for which the request is done. (required)
     * @param reason Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. (required)
     * @param fromDate The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [5] NotWorking, [99] &#x3D; Others. Default is DateTime.Now. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ResultResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ResultResponse apiV10ConfigurationAtFonClientIdCashregisterDelete(UUID clientId, String cashRegisterId, CashRegisterDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest) throws ApiException {
        ApiResponse<ResultResponse> localVarResp = apiV10ConfigurationAtFonClientIdCashregisterDeleteWithHttpInfo(clientId, cashRegisterId, reason, fromDate, requestDateTime, isTest);
        return localVarResp.getData();
    }

    /**
     * Unregister cash register at fon service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId The cash register id for which the request is done. (required)
     * @param reason Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. (required)
     * @param fromDate The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [5] NotWorking, [99] &#x3D; Others. Default is DateTime.Now. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ApiResponse&lt;ResultResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ResultResponse> apiV10ConfigurationAtFonClientIdCashregisterDeleteWithHttpInfo(UUID clientId, String cashRegisterId, CashRegisterDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterDeleteValidateBeforeCall(clientId, cashRegisterId, reason, fromDate, requestDateTime, isTest, null);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Unregister cash register at fon service. You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId The cash register id for which the request is done. (required)
     * @param reason Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. (required)
     * @param fromDate The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [5] NotWorking, [99] &#x3D; Others. Default is DateTime.Now. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterDeleteAsync(UUID clientId, String cashRegisterId, CashRegisterDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback<ResultResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterDeleteValidateBeforeCall(clientId, cashRegisterId, reason, fromDate, requestDateTime, isTest, _callback);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCashregisterGet
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId The client for which the request is done. (required)
     * @param expectedResponse A bool representing the expected response for this call. Default value is false. (optional, default to false)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterGetCall(UUID clientId, String cashRegisterId, Boolean expectedResponse, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/cashregister"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (cashRegisterId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("cashRegisterId", cashRegisterId));
        }

        if (expectedResponse != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("expectedResponse", expectedResponse));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterGetValidateBeforeCall(UUID clientId, String cashRegisterId, Boolean expectedResponse, Boolean isTest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCashregisterGet(Async)");
        }

        // verify the required parameter 'cashRegisterId' is set
        if (cashRegisterId == null) {
            throw new ApiException("Missing the required parameter 'cashRegisterId' when calling apiV10ConfigurationAtFonClientIdCashregisterGet(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCashregisterGetCall(clientId, cashRegisterId, expectedResponse, isTest, _callback);

    }

    /**
     * Checks the cash register state with the FON Service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId The client for which the request is done. (required)
     * @param expectedResponse A bool representing the expected response for this call. Default value is false. (optional, default to false)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonClientIdCashregisterGet(UUID clientId, String cashRegisterId, Boolean expectedResponse, Boolean isTest) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonClientIdCashregisterGetWithHttpInfo(clientId, cashRegisterId, expectedResponse, isTest);
        return localVarResp.getData();
    }

    /**
     * Checks the cash register state with the FON Service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId The client for which the request is done. (required)
     * @param expectedResponse A bool representing the expected response for this call. Default value is false. (optional, default to false)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonClientIdCashregisterGetWithHttpInfo(UUID clientId, String cashRegisterId, Boolean expectedResponse, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterGetValidateBeforeCall(clientId, cashRegisterId, expectedResponse, isTest, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Checks the cash register state with the FON Service. You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId The client for which the request is done. (required)
     * @param expectedResponse A bool representing the expected response for this call. Default value is false. (optional, default to false)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterGetAsync(UUID clientId, String cashRegisterId, Boolean expectedResponse, Boolean isTest, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterGetValidateBeforeCall(clientId, cashRegisterId, expectedResponse, isTest, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCashregisterPost
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId A string representing the unique cash register id. (required)
     * @param aesKey Do not use any more. Use request body instead. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param body A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated or has no access to the given client. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId or aesKey was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterPostCall(UUID clientId, String cashRegisterId, String aesKey, OffsetDateTime requestDateTime, Boolean isTest, String body, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = body;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/cashregister"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (cashRegisterId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("cashRegisterId", cashRegisterId));
        }

        if (aesKey != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("aesKey", aesKey));
        }

        if (requestDateTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("requestDateTime", requestDateTime));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterPostValidateBeforeCall(UUID clientId, String cashRegisterId, String aesKey, OffsetDateTime requestDateTime, Boolean isTest, String body, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCashregisterPost(Async)");
        }

        // verify the required parameter 'cashRegisterId' is set
        if (cashRegisterId == null) {
            throw new ApiException("Missing the required parameter 'cashRegisterId' when calling apiV10ConfigurationAtFonClientIdCashregisterPost(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCashregisterPostCall(clientId, cashRegisterId, aesKey, requestDateTime, isTest, body, _callback);

    }

    /**
     * Register of cash register system. You have to be authenticated.
     * Security patch: do not use longer query parameter aesKey. Use request body instead (You have to send the aeskeyBody with single quotes: &#39;{aesKey}&#39;).
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId A string representing the unique cash register id. (required)
     * @param aesKey Do not use any more. Use request body instead. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param body A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. (optional)
     * @return ResultResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated or has no access to the given client. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId or aesKey was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ResultResponse apiV10ConfigurationAtFonClientIdCashregisterPost(UUID clientId, String cashRegisterId, String aesKey, OffsetDateTime requestDateTime, Boolean isTest, String body) throws ApiException {
        ApiResponse<ResultResponse> localVarResp = apiV10ConfigurationAtFonClientIdCashregisterPostWithHttpInfo(clientId, cashRegisterId, aesKey, requestDateTime, isTest, body);
        return localVarResp.getData();
    }

    /**
     * Register of cash register system. You have to be authenticated.
     * Security patch: do not use longer query parameter aesKey. Use request body instead (You have to send the aeskeyBody with single quotes: &#39;{aesKey}&#39;).
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId A string representing the unique cash register id. (required)
     * @param aesKey Do not use any more. Use request body instead. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param body A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. (optional)
     * @return ApiResponse&lt;ResultResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated or has no access to the given client. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId or aesKey was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ResultResponse> apiV10ConfigurationAtFonClientIdCashregisterPostWithHttpInfo(UUID clientId, String cashRegisterId, String aesKey, OffsetDateTime requestDateTime, Boolean isTest, String body) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterPostValidateBeforeCall(clientId, cashRegisterId, aesKey, requestDateTime, isTest, body, null);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Register of cash register system. You have to be authenticated. (asynchronously)
     * Security patch: do not use longer query parameter aesKey. Use request body instead (You have to send the aeskeyBody with single quotes: &#39;{aesKey}&#39;).
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId A string representing the unique cash register id. (required)
     * @param aesKey Do not use any more. Use request body instead. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param body A string representing the base 64 encoded aes key security key of the cash register. You have to send the aeskey with single quotes: &#39;{aesKey}&#39;. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated or has no access to the given client. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or cashRegisterId or aesKey was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterPostAsync(UUID clientId, String cashRegisterId, String aesKey, OffsetDateTime requestDateTime, Boolean isTest, String body, final ApiCallback<ResultResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterPostValidateBeforeCall(clientId, cashRegisterId, aesKey, requestDateTime, isTest, body, _callback);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCashregisterPut
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId A string representing the unique cash register id. (required)
     * @param recommissionDate The date and time of recommissioning. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterPutCall(UUID clientId, String cashRegisterId, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/cashregister"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (cashRegisterId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("cashRegisterId", cashRegisterId));
        }

        if (recommissionDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("recommissionDate", recommissionDate));
        }

        if (requestDateTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("requestDateTime", requestDateTime));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterPutValidateBeforeCall(UUID clientId, String cashRegisterId, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCashregisterPut(Async)");
        }

        // verify the required parameter 'cashRegisterId' is set
        if (cashRegisterId == null) {
            throw new ApiException("Missing the required parameter 'cashRegisterId' when calling apiV10ConfigurationAtFonClientIdCashregisterPut(Async)");
        }

        // verify the required parameter 'recommissionDate' is set
        if (recommissionDate == null) {
            throw new ApiException("Missing the required parameter 'recommissionDate' when calling apiV10ConfigurationAtFonClientIdCashregisterPut(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCashregisterPutCall(clientId, cashRegisterId, recommissionDate, requestDateTime, isTest, _callback);

    }

    /**
     * Recommissioning of cash register system
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId A string representing the unique cash register id. (required)
     * @param recommissionDate The date and time of recommissioning. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ResultResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ResultResponse apiV10ConfigurationAtFonClientIdCashregisterPut(UUID clientId, String cashRegisterId, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest) throws ApiException {
        ApiResponse<ResultResponse> localVarResp = apiV10ConfigurationAtFonClientIdCashregisterPutWithHttpInfo(clientId, cashRegisterId, recommissionDate, requestDateTime, isTest);
        return localVarResp.getData();
    }

    /**
     * Recommissioning of cash register system
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId A string representing the unique cash register id. (required)
     * @param recommissionDate The date and time of recommissioning. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ApiResponse&lt;ResultResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ResultResponse> apiV10ConfigurationAtFonClientIdCashregisterPutWithHttpInfo(UUID clientId, String cashRegisterId, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterPutValidateBeforeCall(clientId, cashRegisterId, recommissionDate, requestDateTime, isTest, null);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Recommissioning of cash register system (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param cashRegisterId A string representing the unique cash register id. (required)
     * @param recommissionDate The date and time of recommissioning. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterPutAsync(UUID clientId, String cashRegisterId, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback<ResultResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterPutValidateBeforeCall(clientId, cashRegisterId, recommissionDate, requestDateTime, isTest, _callback);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete
     * @param clientId The client for which the request is done. (required)
     * @param reason Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteCall(UUID clientId, CashRegisterDropoutReason reason, OffsetDateTime requestDateTime, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/cashregister/simple"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (reason != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("reason", reason));
        }

        if (requestDateTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("requestDateTime", requestDateTime));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteValidateBeforeCall(UUID clientId, CashRegisterDropoutReason reason, OffsetDateTime requestDateTime, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete(Async)");
        }

        // verify the required parameter 'reason' is set
        if (reason == null) {
            throw new ApiException("Missing the required parameter 'reason' when calling apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteCall(clientId, reason, requestDateTime, _callback);

    }

    /**
     * Unregister cash register at fon service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param reason Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete(UUID clientId, CashRegisterDropoutReason reason, OffsetDateTime requestDateTime) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteWithHttpInfo(clientId, reason, requestDateTime);
        return localVarResp.getData();
    }

    /**
     * Unregister cash register at fon service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param reason Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteWithHttpInfo(UUID clientId, CashRegisterDropoutReason reason, OffsetDateTime requestDateTime) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteValidateBeforeCall(clientId, reason, requestDateTime, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Unregister cash register at fon service. You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param reason Reason for cash register dropout. Attention: just permanent dropout reasons are allowed. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteAsync(UUID clientId, CashRegisterDropoutReason reason, OffsetDateTime requestDateTime, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteValidateBeforeCall(clientId, reason, requestDateTime, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCashregisterSimpleGet
     * @param clientId The client for which the request is done. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterSimpleGetCall(UUID clientId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/cashregister/simple"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterSimpleGetValidateBeforeCall(UUID clientId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCashregisterSimpleGet(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCashregisterSimpleGetCall(clientId, _callback);

    }

    /**
     * Checks the cash register state with the FON Service. You have to be authenticated. The format for the cash registerid is storeNumber/terminalNumber.
     * The expected result is true.
     * @param clientId The client for which the request is done. (required)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonClientIdCashregisterSimpleGet(UUID clientId) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonClientIdCashregisterSimpleGetWithHttpInfo(clientId);
        return localVarResp.getData();
    }

    /**
     * Checks the cash register state with the FON Service. You have to be authenticated. The format for the cash registerid is storeNumber/terminalNumber.
     * The expected result is true.
     * @param clientId The client for which the request is done. (required)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonClientIdCashregisterSimpleGetWithHttpInfo(UUID clientId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterSimpleGetValidateBeforeCall(clientId, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Checks the cash register state with the FON Service. You have to be authenticated. The format for the cash registerid is storeNumber/terminalNumber. (asynchronously)
     * The expected result is true.
     * @param clientId The client for which the request is done. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCashregisterSimpleGetAsync(UUID clientId, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCashregisterSimpleGetValidateBeforeCall(clientId, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCertificateDelete
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to unregister. (required)
     * @param reason The reason of the dropout. (required)
     * @param fromDate The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [2] NotWorking, [99] &#x3D; Others. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param logMessage Additional log message for fon protocol log. (optional, default to )
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. Also returns if fromDate is set to null, when reason is set to  [1] Stolen, [2] NotWorking, [99] &#x3D; Others. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCertificateDeleteCall(UUID clientId, String securityCertificate, SecurityCertificateDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/certificate"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (securityCertificate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("securityCertificate", securityCertificate));
        }

        if (reason != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("reason", reason));
        }

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (requestDateTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("requestDateTime", requestDateTime));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        if (logMessage != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("logMessage", logMessage));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCertificateDeleteValidateBeforeCall(UUID clientId, String securityCertificate, SecurityCertificateDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCertificateDelete(Async)");
        }

        // verify the required parameter 'securityCertificate' is set
        if (securityCertificate == null) {
            throw new ApiException("Missing the required parameter 'securityCertificate' when calling apiV10ConfigurationAtFonClientIdCertificateDelete(Async)");
        }

        // verify the required parameter 'reason' is set
        if (reason == null) {
            throw new ApiException("Missing the required parameter 'reason' when calling apiV10ConfigurationAtFonClientIdCertificateDelete(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCertificateDeleteCall(clientId, securityCertificate, reason, fromDate, requestDateTime, isTest, logMessage, _callback);

    }

    /**
     * Unregister a security certificate at fon (Finanzonline). You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to unregister. (required)
     * @param reason The reason of the dropout. (required)
     * @param fromDate The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [2] NotWorking, [99] &#x3D; Others. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param logMessage Additional log message for fon protocol log. (optional, default to )
     * @return ResultResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. Also returns if fromDate is set to null, when reason is set to  [1] Stolen, [2] NotWorking, [99] &#x3D; Others. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ResultResponse apiV10ConfigurationAtFonClientIdCertificateDelete(UUID clientId, String securityCertificate, SecurityCertificateDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage) throws ApiException {
        ApiResponse<ResultResponse> localVarResp = apiV10ConfigurationAtFonClientIdCertificateDeleteWithHttpInfo(clientId, securityCertificate, reason, fromDate, requestDateTime, isTest, logMessage);
        return localVarResp.getData();
    }

    /**
     * Unregister a security certificate at fon (Finanzonline). You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to unregister. (required)
     * @param reason The reason of the dropout. (required)
     * @param fromDate The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [2] NotWorking, [99] &#x3D; Others. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param logMessage Additional log message for fon protocol log. (optional, default to )
     * @return ApiResponse&lt;ResultResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. Also returns if fromDate is set to null, when reason is set to  [1] Stolen, [2] NotWorking, [99] &#x3D; Others. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ResultResponse> apiV10ConfigurationAtFonClientIdCertificateDeleteWithHttpInfo(UUID clientId, String securityCertificate, SecurityCertificateDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCertificateDeleteValidateBeforeCall(clientId, securityCertificate, reason, fromDate, requestDateTime, isTest, logMessage, null);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Unregister a security certificate at fon (Finanzonline). You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to unregister. (required)
     * @param reason The reason of the dropout. (required)
     * @param fromDate The date of the dropout, has to be set at reason &#x3D; [1] Stolen, [2] NotWorking, [99] &#x3D; Others. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param logMessage Additional log message for fon protocol log. (optional, default to )
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. Also returns if fromDate is set to null, when reason is set to  [1] Stolen, [2] NotWorking, [99] &#x3D; Others. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCertificateDeleteAsync(UUID clientId, String securityCertificate, SecurityCertificateDropoutReason reason, OffsetDateTime fromDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage, final ApiCallback<ResultResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCertificateDeleteValidateBeforeCall(clientId, securityCertificate, reason, fromDate, requestDateTime, isTest, logMessage, _callback);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCertificateGet
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate id of the security certificate to check. (required)
     * @param expectedResponse A bool representing the expected response for this call. Default value is false. (optional, default to false)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCertificateGetCall(UUID clientId, String securityCertificate, Boolean expectedResponse, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/certificate"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (securityCertificate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("securityCertificate", securityCertificate));
        }

        if (expectedResponse != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("expectedResponse", expectedResponse));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCertificateGetValidateBeforeCall(UUID clientId, String securityCertificate, Boolean expectedResponse, Boolean isTest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCertificateGet(Async)");
        }

        // verify the required parameter 'securityCertificate' is set
        if (securityCertificate == null) {
            throw new ApiException("Missing the required parameter 'securityCertificate' when calling apiV10ConfigurationAtFonClientIdCertificateGet(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCertificateGetCall(clientId, securityCertificate, expectedResponse, isTest, _callback);

    }

    /**
     * Returns true if the given security certificate is activated (IN_BETRIEB) at fon (Finanzonline). You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate id of the security certificate to check. (required)
     * @param expectedResponse A bool representing the expected response for this call. Default value is false. (optional, default to false)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonClientIdCertificateGet(UUID clientId, String securityCertificate, Boolean expectedResponse, Boolean isTest) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonClientIdCertificateGetWithHttpInfo(clientId, securityCertificate, expectedResponse, isTest);
        return localVarResp.getData();
    }

    /**
     * Returns true if the given security certificate is activated (IN_BETRIEB) at fon (Finanzonline). You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate id of the security certificate to check. (required)
     * @param expectedResponse A bool representing the expected response for this call. Default value is false. (optional, default to false)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonClientIdCertificateGetWithHttpInfo(UUID clientId, String securityCertificate, Boolean expectedResponse, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCertificateGetValidateBeforeCall(clientId, securityCertificate, expectedResponse, isTest, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns true if the given security certificate is activated (IN_BETRIEB) at fon (Finanzonline). You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate id of the security certificate to check. (required)
     * @param expectedResponse A bool representing the expected response for this call. Default value is false. (optional, default to false)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCertificateGetAsync(UUID clientId, String securityCertificate, Boolean expectedResponse, Boolean isTest, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCertificateGetValidateBeforeCall(clientId, securityCertificate, expectedResponse, isTest, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCertificatePost
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to register. (required)
     * @param certificateType The type of the certificate. (required)
     * @param issuer The issuer of the certificate. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCertificatePostCall(UUID clientId, String securityCertificate, SecurityCertificateType certificateType, SecurityCertificateIssuer issuer, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/certificate"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (securityCertificate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("securityCertificate", securityCertificate));
        }

        if (certificateType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("certificateType", certificateType));
        }

        if (issuer != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("issuer", issuer));
        }

        if (requestDateTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("requestDateTime", requestDateTime));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCertificatePostValidateBeforeCall(UUID clientId, String securityCertificate, SecurityCertificateType certificateType, SecurityCertificateIssuer issuer, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCertificatePost(Async)");
        }

        // verify the required parameter 'securityCertificate' is set
        if (securityCertificate == null) {
            throw new ApiException("Missing the required parameter 'securityCertificate' when calling apiV10ConfigurationAtFonClientIdCertificatePost(Async)");
        }

        // verify the required parameter 'certificateType' is set
        if (certificateType == null) {
            throw new ApiException("Missing the required parameter 'certificateType' when calling apiV10ConfigurationAtFonClientIdCertificatePost(Async)");
        }

        // verify the required parameter 'issuer' is set
        if (issuer == null) {
            throw new ApiException("Missing the required parameter 'issuer' when calling apiV10ConfigurationAtFonClientIdCertificatePost(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCertificatePostCall(clientId, securityCertificate, certificateType, issuer, requestDateTime, isTest, _callback);

    }

    /**
     * Register a security certificate at fon (Finanzonline). You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to register. (required)
     * @param certificateType The type of the certificate. (required)
     * @param issuer The issuer of the certificate. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ResultResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ResultResponse apiV10ConfigurationAtFonClientIdCertificatePost(UUID clientId, String securityCertificate, SecurityCertificateType certificateType, SecurityCertificateIssuer issuer, OffsetDateTime requestDateTime, Boolean isTest) throws ApiException {
        ApiResponse<ResultResponse> localVarResp = apiV10ConfigurationAtFonClientIdCertificatePostWithHttpInfo(clientId, securityCertificate, certificateType, issuer, requestDateTime, isTest);
        return localVarResp.getData();
    }

    /**
     * Register a security certificate at fon (Finanzonline). You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to register. (required)
     * @param certificateType The type of the certificate. (required)
     * @param issuer The issuer of the certificate. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ApiResponse&lt;ResultResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ResultResponse> apiV10ConfigurationAtFonClientIdCertificatePostWithHttpInfo(UUID clientId, String securityCertificate, SecurityCertificateType certificateType, SecurityCertificateIssuer issuer, OffsetDateTime requestDateTime, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCertificatePostValidateBeforeCall(clientId, securityCertificate, certificateType, issuer, requestDateTime, isTest, null);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Register a security certificate at fon (Finanzonline). You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to register. (required)
     * @param certificateType The type of the certificate. (required)
     * @param issuer The issuer of the certificate. (required)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropriate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or securityCertificate was set to 0. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCertificatePostAsync(UUID clientId, String securityCertificate, SecurityCertificateType certificateType, SecurityCertificateIssuer issuer, OffsetDateTime requestDateTime, Boolean isTest, final ApiCallback<ResultResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCertificatePostValidateBeforeCall(clientId, securityCertificate, certificateType, issuer, requestDateTime, isTest, _callback);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdCertificatePut
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to register. (required)
     * @param recommissionDate The date and time of recommissioning. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param logMessage Additional log message for fon protocol log. (optional, default to )
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCertificatePutCall(UUID clientId, String securityCertificate, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/certificate"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (securityCertificate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("securityCertificate", securityCertificate));
        }

        if (recommissionDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("recommissionDate", recommissionDate));
        }

        if (requestDateTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("requestDateTime", requestDateTime));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        if (logMessage != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("logMessage", logMessage));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdCertificatePutValidateBeforeCall(UUID clientId, String securityCertificate, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdCertificatePut(Async)");
        }

        // verify the required parameter 'securityCertificate' is set
        if (securityCertificate == null) {
            throw new ApiException("Missing the required parameter 'securityCertificate' when calling apiV10ConfigurationAtFonClientIdCertificatePut(Async)");
        }

        return apiV10ConfigurationAtFonClientIdCertificatePutCall(clientId, securityCertificate, recommissionDate, requestDateTime, isTest, logMessage, _callback);

    }

    /**
     * Recommissioning a security certificate at fon (Finanzonline). You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to register. (required)
     * @param recommissionDate The date and time of recommissioning. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param logMessage Additional log message for fon protocol log. (optional, default to )
     * @return ResultResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ResultResponse apiV10ConfigurationAtFonClientIdCertificatePut(UUID clientId, String securityCertificate, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage) throws ApiException {
        ApiResponse<ResultResponse> localVarResp = apiV10ConfigurationAtFonClientIdCertificatePutWithHttpInfo(clientId, securityCertificate, recommissionDate, requestDateTime, isTest, logMessage);
        return localVarResp.getData();
    }

    /**
     * Recommissioning a security certificate at fon (Finanzonline). You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to register. (required)
     * @param recommissionDate The date and time of recommissioning. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param logMessage Additional log message for fon protocol log. (optional, default to )
     * @return ApiResponse&lt;ResultResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ResultResponse> apiV10ConfigurationAtFonClientIdCertificatePutWithHttpInfo(UUID clientId, String securityCertificate, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCertificatePutValidateBeforeCall(clientId, securityCertificate, recommissionDate, requestDateTime, isTest, logMessage, null);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Recommissioning a security certificate at fon (Finanzonline). You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param securityCertificate The certificate to register. (required)
     * @param recommissionDate The date and time of recommissioning. (optional)
     * @param requestDateTime The datetime of the request. (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param logMessage Additional log message for fon protocol log. (optional, default to )
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdCertificatePutAsync(UUID clientId, String securityCertificate, OffsetDateTime recommissionDate, OffsetDateTime requestDateTime, Boolean isTest, String logMessage, final ApiCallback<ResultResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdCertificatePutValidateBeforeCall(clientId, securityCertificate, recommissionDate, requestDateTime, isTest, logMessage, _callback);
        Type localVarReturnType = new TypeToken<ResultResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdDocumentValidateGet
     * @param clientId The client for which the request is done. (required)
     * @param depDocument A string representing the dep record of the document to check. (required)
     * @param annualReceiptYear String value. Set this value if the validated document is an annual receipt (check will be stored to database). (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropiate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or depDocument was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdDocumentValidateGetCall(UUID clientId, String depDocument, String annualReceiptYear, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/document/validate"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (depDocument != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("depDocument", depDocument));
        }

        if (annualReceiptYear != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("annualReceiptYear", annualReceiptYear));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdDocumentValidateGetValidateBeforeCall(UUID clientId, String depDocument, String annualReceiptYear, Boolean isTest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdDocumentValidateGet(Async)");
        }

        // verify the required parameter 'depDocument' is set
        if (depDocument == null) {
            throw new ApiException("Missing the required parameter 'depDocument' when calling apiV10ConfigurationAtFonClientIdDocumentValidateGet(Async)");
        }

        return apiV10ConfigurationAtFonClientIdDocumentValidateGetCall(clientId, depDocument, annualReceiptYear, isTest, _callback);

    }

    /**
     * Does document validation on fon service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param depDocument A string representing the dep record of the document to check. (required)
     * @param annualReceiptYear String value. Set this value if the validated document is an annual receipt (check will be stored to database). (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropiate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or depDocument was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonClientIdDocumentValidateGet(UUID clientId, String depDocument, String annualReceiptYear, Boolean isTest) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonClientIdDocumentValidateGetWithHttpInfo(clientId, depDocument, annualReceiptYear, isTest);
        return localVarResp.getData();
    }

    /**
     * Does document validation on fon service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @param depDocument A string representing the dep record of the document to check. (required)
     * @param annualReceiptYear String value. Set this value if the validated document is an annual receipt (check will be stored to database). (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropiate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or depDocument was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonClientIdDocumentValidateGetWithHttpInfo(UUID clientId, String depDocument, String annualReceiptYear, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdDocumentValidateGetValidateBeforeCall(clientId, depDocument, annualReceiptYear, isTest, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Does document validation on fon service. You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param depDocument A string representing the dep record of the document to check. (required)
     * @param annualReceiptYear String value. Set this value if the validated document is an annual receipt (check will be stored to database). (optional)
     * @param isTest True if the request should be done in test mode; otherwise false. Default is false. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropiate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty or depDocument was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdDocumentValidateGetAsync(UUID clientId, String depDocument, String annualReceiptYear, Boolean isTest, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdDocumentValidateGetValidateBeforeCall(clientId, depDocument, annualReceiptYear, isTest, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdDocumentValidateLastGet
     * @param clientId The client for which the request is done. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropiate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Client is not found or no dep is found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> Configured fon data is missing or not correct. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdDocumentValidateLastGetCall(UUID clientId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}/document/validate/last"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdDocumentValidateLastGetValidateBeforeCall(UUID clientId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdDocumentValidateLastGet(Async)");
        }

        return apiV10ConfigurationAtFonClientIdDocumentValidateLastGetCall(clientId, _callback);

    }

    /**
     * Does document validation on fon service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropiate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Client is not found or no dep is found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> Configured fon data is missing or not correct. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonClientIdDocumentValidateLastGet(UUID clientId) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonClientIdDocumentValidateLastGetWithHttpInfo(clientId);
        return localVarResp.getData();
    }

    /**
     * Does document validation on fon service. You have to be authenticated.
     * 
     * @param clientId The client for which the request is done. (required)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropiate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Client is not found or no dep is found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> Configured fon data is missing or not correct. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonClientIdDocumentValidateLastGetWithHttpInfo(UUID clientId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdDocumentValidateLastGetValidateBeforeCall(clientId, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Does document validation on fon service. You have to be authenticated. (asynchronously)
     * 
     * @param clientId The client for which the request is done. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Client has not appropiate license. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Client is not found or no dep is found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> Configured fon data is missing or not correct. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty was set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdDocumentValidateLastGetAsync(UUID clientId, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdDocumentValidateLastGetValidateBeforeCall(clientId, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonClientIdGet
     * @param clientId The client for which the access is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdGetCall(UUID clientId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/{clientId}"
            .replace("{" + "clientId" + "}", localVarApiClient.escapeString(clientId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonClientIdGetValidateBeforeCall(UUID clientId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new ApiException("Missing the required parameter 'clientId' when calling apiV10ConfigurationAtFonClientIdGet(Async)");
        }

        return apiV10ConfigurationAtFonClientIdGetCall(clientId, _callback);

    }

    /**
     * Returns whether the client has fon access (if the data is configured and license is exi
     * In possibility of having no license no exception is raised, but appropiate message is returned.
     * @param clientId The client for which the access is requested. (required)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10ConfigurationAtFonClientIdGet(UUID clientId) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10ConfigurationAtFonClientIdGetWithHttpInfo(clientId);
        return localVarResp.getData();
    }

    /**
     * Returns whether the client has fon access (if the data is configured and license is exi
     * In possibility of having no license no exception is raised, but appropiate message is returned.
     * @param clientId The client for which the access is requested. (required)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10ConfigurationAtFonClientIdGetWithHttpInfo(UUID clientId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdGetValidateBeforeCall(clientId, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns whether the client has fon access (if the data is configured and license is exi (asynchronously)
     * In possibility of having no license no exception is raised, but appropiate message is returned.
     * @param clientId The client for which the access is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> clientId was set to System.Guid.Empty. </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> Bad request. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Internal server error. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonClientIdGetAsync(UUID clientId, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonClientIdGetValidateBeforeCall(clientId, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtFonDropoutReasonsGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Forbidden (not authorized). </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonDropoutReasonsGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/fon/dropoutReasons";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtFonDropoutReasonsGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationAtFonDropoutReasonsGetCall(_callback);

    }

    /**
     * Returns the possible drop out reasons for cash register drop out.
     * 
     * @return List&lt;StringSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Forbidden (not authorized). </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
     </table>
     */
    public List<StringSimpleObject> apiV10ConfigurationAtFonDropoutReasonsGet() throws ApiException {
        ApiResponse<List<StringSimpleObject>> localVarResp = apiV10ConfigurationAtFonDropoutReasonsGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns the possible drop out reasons for cash register drop out.
     * 
     * @return ApiResponse&lt;List&lt;StringSimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Forbidden (not authorized). </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<StringSimpleObject>> apiV10ConfigurationAtFonDropoutReasonsGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtFonDropoutReasonsGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<StringSimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the possible drop out reasons for cash register drop out. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Forbidden (not authorized). </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Client is not authenticated. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtFonDropoutReasonsGetAsync(final ApiCallback<List<StringSimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtFonDropoutReasonsGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<StringSimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtHsmTerminalIdPatch
     * @param terminalId The terminalId of the terminal where the check has to be done. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> The requested terminal has no valid configuration configured. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to requested terminal is denied. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> The requested terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> The requested terminal has no valid hsm configuration to test. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> The requested terminal is not an austrian terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter terminalId is set to Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtHsmTerminalIdPatchCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/hsm/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PATCH", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtHsmTerminalIdPatchValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationAtHsmTerminalIdPatch(Async)");
        }

        return apiV10ConfigurationAtHsmTerminalIdPatchCall(terminalId, _callback);

    }

    /**
     * Tests configured hsm connections for the given client.
     * In normal case only one connection is configured.
     * @param terminalId The terminalId of the terminal where the check has to be done. (required)
     * @return Boolean
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> The requested terminal has no valid configuration configured. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to requested terminal is denied. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> The requested terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> The requested terminal has no valid hsm configuration to test. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> The requested terminal is not an austrian terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter terminalId is set to Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public Boolean apiV10ConfigurationAtHsmTerminalIdPatch(UUID terminalId) throws ApiException {
        ApiResponse<Boolean> localVarResp = apiV10ConfigurationAtHsmTerminalIdPatchWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Tests configured hsm connections for the given client.
     * In normal case only one connection is configured.
     * @param terminalId The terminalId of the terminal where the check has to be done. (required)
     * @return ApiResponse&lt;Boolean&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> The requested terminal has no valid configuration configured. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to requested terminal is denied. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> The requested terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> The requested terminal has no valid hsm configuration to test. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> The requested terminal is not an austrian terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter terminalId is set to Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Boolean> apiV10ConfigurationAtHsmTerminalIdPatchWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtHsmTerminalIdPatchValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Tests configured hsm connections for the given client. (asynchronously)
     * In normal case only one connection is configured.
     * @param terminalId The terminalId of the terminal where the check has to be done. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> The requested terminal has no valid configuration configured. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to requested terminal is denied. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> The requested terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> The requested terminal has no valid hsm configuration to test. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> The requested terminal is not an austrian terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter terminalId is set to Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtHsmTerminalIdPatchAsync(UUID terminalId, final ApiCallback<Boolean> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtHsmTerminalIdPatchValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationAtSignDeviceDriverInfoGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtSignDeviceDriverInfoGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/at/signDeviceDriverInfo";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAtSignDeviceDriverInfoGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationAtSignDeviceDriverInfoGetCall(_callback);

    }

    /**
     * Returns supported smart card drivers for configuration in the cloud user interface.
     * 
     * @return List&lt;SignDeviceDriverInfo&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<SignDeviceDriverInfo> apiV10ConfigurationAtSignDeviceDriverInfoGet() throws ApiException {
        ApiResponse<List<SignDeviceDriverInfo>> localVarResp = apiV10ConfigurationAtSignDeviceDriverInfoGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns supported smart card drivers for configuration in the cloud user interface.
     * 
     * @return ApiResponse&lt;List&lt;SignDeviceDriverInfo&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<SignDeviceDriverInfo>> apiV10ConfigurationAtSignDeviceDriverInfoGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAtSignDeviceDriverInfoGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<SignDeviceDriverInfo>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns supported smart card drivers for configuration in the cloud user interface. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAtSignDeviceDriverInfoGetAsync(final ApiCallback<List<SignDeviceDriverInfo>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAtSignDeviceDriverInfoGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<SignDeviceDriverInfo>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
