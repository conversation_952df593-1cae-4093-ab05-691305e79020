/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.Distributor;
import com.untill.retailforce.model.DistributorContract;
import com.untill.retailforce.model.DistributorContractModel;
import com.untill.retailforce.model.DistributorContractModelPageResultModel;
import com.untill.retailforce.model.DistributorModel;
import com.untill.retailforce.model.DistributorModelPageResultModel;
import com.untill.retailforce.model.GuidBreadCrumb;
import com.untill.retailforce.model.GuidHierarchicalSimpleObjectPageResultModel;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import com.untill.retailforce.model.OnboardingFinishData;
import com.untill.retailforce.model.OnboardingFinishDistributorData;
import com.untill.retailforce.model.StringSimpleObjectPageResultModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class MasterDataDistributorsApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public MasterDataDistributorsApi() {
        this(Configuration.getDefaultApiClient());
    }

    public MasterDataDistributorsApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdContractContractIdDelete
     * @param distributorId The distributor id of the contract to delete. (required)
     * @param contractId The id of the contract to delete. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteCall(UUID distributorId, UUID contractId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/contract/{contractId}"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()))
            .replace("{" + "contractId" + "}", localVarApiClient.escapeString(contractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteValidateBeforeCall(UUID distributorId, UUID contractId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdContractContractIdDelete(Async)");
        }

        // verify the required parameter 'contractId' is set
        if (contractId == null) {
            throw new ApiException("Missing the required parameter 'contractId' when calling apiV10MasterdataDistributorsDistributorIdContractContractIdDelete(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteCall(distributorId, contractId, _callback);

    }

    /**
     * Deletes a contract.
     * 
     * @param distributorId The distributor id of the contract to delete. (required)
     * @param contractId The id of the contract to delete. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataDistributorsDistributorIdContractContractIdDelete(UUID distributorId, UUID contractId) throws ApiException {
        apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteWithHttpInfo(distributorId, contractId);
    }

    /**
     * Deletes a contract.
     * 
     * @param distributorId The distributor id of the contract to delete. (required)
     * @param contractId The id of the contract to delete. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteWithHttpInfo(UUID distributorId, UUID contractId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteValidateBeforeCall(distributorId, contractId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes a contract. (asynchronously)
     * 
     * @param distributorId The distributor id of the contract to delete. (required)
     * @param contractId The id of the contract to delete. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteAsync(UUID distributorId, UUID contractId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractContractIdDeleteValidateBeforeCall(distributorId, contractId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdContractContractIdPut
     * @param distributorId The id of the distributor where the contract will be updated. (required)
     * @param contractId The id of the contract to update. (required)
     * @param distributorContract The new contract data. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractContractIdPutCall(UUID distributorId, UUID contractId, DistributorContract distributorContract, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = distributorContract;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/contract/{contractId}"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()))
            .replace("{" + "contractId" + "}", localVarApiClient.escapeString(contractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractContractIdPutValidateBeforeCall(UUID distributorId, UUID contractId, DistributorContract distributorContract, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdContractContractIdPut(Async)");
        }

        // verify the required parameter 'contractId' is set
        if (contractId == null) {
            throw new ApiException("Missing the required parameter 'contractId' when calling apiV10MasterdataDistributorsDistributorIdContractContractIdPut(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdContractContractIdPutCall(distributorId, contractId, distributorContract, _callback);

    }

    /**
     * Updates the given contract of the distributor.
     * 
     * @param distributorId The id of the distributor where the contract will be updated. (required)
     * @param contractId The id of the contract to update. (required)
     * @param distributorContract The new contract data. (optional)
     * @return DistributorContractModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DistributorContractModel apiV10MasterdataDistributorsDistributorIdContractContractIdPut(UUID distributorId, UUID contractId, DistributorContract distributorContract) throws ApiException {
        ApiResponse<DistributorContractModel> localVarResp = apiV10MasterdataDistributorsDistributorIdContractContractIdPutWithHttpInfo(distributorId, contractId, distributorContract);
        return localVarResp.getData();
    }

    /**
     * Updates the given contract of the distributor.
     * 
     * @param distributorId The id of the distributor where the contract will be updated. (required)
     * @param contractId The id of the contract to update. (required)
     * @param distributorContract The new contract data. (optional)
     * @return ApiResponse&lt;DistributorContractModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DistributorContractModel> apiV10MasterdataDistributorsDistributorIdContractContractIdPutWithHttpInfo(UUID distributorId, UUID contractId, DistributorContract distributorContract) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractContractIdPutValidateBeforeCall(distributorId, contractId, distributorContract, null);
        Type localVarReturnType = new TypeToken<DistributorContractModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates the given contract of the distributor. (asynchronously)
     * 
     * @param distributorId The id of the distributor where the contract will be updated. (required)
     * @param contractId The id of the contract to update. (required)
     * @param distributorContract The new contract data. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractContractIdPutAsync(UUID distributorId, UUID contractId, DistributorContract distributorContract, final ApiCallback<DistributorContractModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractContractIdPutValidateBeforeCall(distributorId, contractId, distributorContract, _callback);
        Type localVarReturnType = new TypeToken<DistributorContractModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut
     * @param distributorId The id of the distributor (required)
     * @param distributorContractId The id of the distributor contract (required)
     * @param onboardingFinishData Onboarding finish data (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutCall(UUID distributorId, UUID distributorContractId, OnboardingFinishData onboardingFinishData, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = onboardingFinishData;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/contract/{distributorContractId}/onboarding/finish"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()))
            .replace("{" + "distributorContractId" + "}", localVarApiClient.escapeString(distributorContractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutValidateBeforeCall(UUID distributorId, UUID distributorContractId, OnboardingFinishData onboardingFinishData, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut(Async)");
        }

        // verify the required parameter 'distributorContractId' is set
        if (distributorContractId == null) {
            throw new ApiException("Missing the required parameter 'distributorContractId' when calling apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutCall(distributorId, distributorContractId, onboardingFinishData, _callback);

    }

    /**
     * Finishes onboarding for distributor
     * 
     * @param distributorId The id of the distributor (required)
     * @param distributorContractId The id of the distributor contract (required)
     * @param onboardingFinishData Onboarding finish data (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut(UUID distributorId, UUID distributorContractId, OnboardingFinishData onboardingFinishData) throws ApiException {
        apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutWithHttpInfo(distributorId, distributorContractId, onboardingFinishData);
    }

    /**
     * Finishes onboarding for distributor
     * 
     * @param distributorId The id of the distributor (required)
     * @param distributorContractId The id of the distributor contract (required)
     * @param onboardingFinishData Onboarding finish data (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutWithHttpInfo(UUID distributorId, UUID distributorContractId, OnboardingFinishData onboardingFinishData) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutValidateBeforeCall(distributorId, distributorContractId, onboardingFinishData, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Finishes onboarding for distributor (asynchronously)
     * 
     * @param distributorId The id of the distributor (required)
     * @param distributorContractId The id of the distributor contract (required)
     * @param onboardingFinishData Onboarding finish data (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutAsync(UUID distributorId, UUID distributorContractId, OnboardingFinishData onboardingFinishData, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPutValidateBeforeCall(distributorId, distributorContractId, onboardingFinishData, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdContractPost
     * @param distributorId The distributor id of the distributor where the contract has to be created. (required)
     * @param distributorContract The contract to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractPostCall(UUID distributorId, DistributorContract distributorContract, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = distributorContract;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/contract"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractPostValidateBeforeCall(UUID distributorId, DistributorContract distributorContract, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdContractPost(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdContractPostCall(distributorId, distributorContract, _callback);

    }

    /**
     * Creates a new contract for a distributor.
     * 
     * @param distributorId The distributor id of the distributor where the contract has to be created. (required)
     * @param distributorContract The contract to create. (optional)
     * @return DistributorContractModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DistributorContractModel apiV10MasterdataDistributorsDistributorIdContractPost(UUID distributorId, DistributorContract distributorContract) throws ApiException {
        ApiResponse<DistributorContractModel> localVarResp = apiV10MasterdataDistributorsDistributorIdContractPostWithHttpInfo(distributorId, distributorContract);
        return localVarResp.getData();
    }

    /**
     * Creates a new contract for a distributor.
     * 
     * @param distributorId The distributor id of the distributor where the contract has to be created. (required)
     * @param distributorContract The contract to create. (optional)
     * @return ApiResponse&lt;DistributorContractModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DistributorContractModel> apiV10MasterdataDistributorsDistributorIdContractPostWithHttpInfo(UUID distributorId, DistributorContract distributorContract) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractPostValidateBeforeCall(distributorId, distributorContract, null);
        Type localVarReturnType = new TypeToken<DistributorContractModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a new contract for a distributor. (asynchronously)
     * 
     * @param distributorId The distributor id of the distributor where the contract has to be created. (required)
     * @param distributorContract The contract to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractPostAsync(UUID distributorId, DistributorContract distributorContract, final ApiCallback<DistributorContractModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractPostValidateBeforeCall(distributorId, distributorContract, _callback);
        Type localVarReturnType = new TypeToken<DistributorContractModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdContractsContractIdGet
     * @param distributorId The if of the distributor of the requested contract. (required)
     * @param contractId The id of the requested contract. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractsContractIdGetCall(UUID distributorId, UUID contractId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/contracts/{contractId}"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()))
            .replace("{" + "contractId" + "}", localVarApiClient.escapeString(contractId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractsContractIdGetValidateBeforeCall(UUID distributorId, UUID contractId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdContractsContractIdGet(Async)");
        }

        // verify the required parameter 'contractId' is set
        if (contractId == null) {
            throw new ApiException("Missing the required parameter 'contractId' when calling apiV10MasterdataDistributorsDistributorIdContractsContractIdGet(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdContractsContractIdGetCall(distributorId, contractId, _callback);

    }

    /**
     * Returns the requested contract.
     * 
     * @param distributorId The if of the distributor of the requested contract. (required)
     * @param contractId The id of the requested contract. (required)
     * @return DistributorContractModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DistributorContractModel apiV10MasterdataDistributorsDistributorIdContractsContractIdGet(UUID distributorId, UUID contractId) throws ApiException {
        ApiResponse<DistributorContractModel> localVarResp = apiV10MasterdataDistributorsDistributorIdContractsContractIdGetWithHttpInfo(distributorId, contractId);
        return localVarResp.getData();
    }

    /**
     * Returns the requested contract.
     * 
     * @param distributorId The if of the distributor of the requested contract. (required)
     * @param contractId The id of the requested contract. (required)
     * @return ApiResponse&lt;DistributorContractModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DistributorContractModel> apiV10MasterdataDistributorsDistributorIdContractsContractIdGetWithHttpInfo(UUID distributorId, UUID contractId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractsContractIdGetValidateBeforeCall(distributorId, contractId, null);
        Type localVarReturnType = new TypeToken<DistributorContractModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the requested contract. (asynchronously)
     * 
     * @param distributorId The if of the distributor of the requested contract. (required)
     * @param contractId The id of the requested contract. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractsContractIdGetAsync(UUID distributorId, UUID contractId, final ApiCallback<DistributorContractModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractsContractIdGetValidateBeforeCall(distributorId, contractId, _callback);
        Type localVarReturnType = new TypeToken<DistributorContractModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdContractsGet
     * @param distributorId The distributor where the contacts are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractsGetCall(UUID distributorId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/contracts"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractsGetValidateBeforeCall(UUID distributorId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdContractsGet(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdContractsGetCall(distributorId, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all contracts for a distributor.
     * 
     * @param distributorId The distributor where the contacts are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return DistributorContractModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DistributorContractModelPageResultModel apiV10MasterdataDistributorsDistributorIdContractsGet(UUID distributorId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<DistributorContractModelPageResultModel> localVarResp = apiV10MasterdataDistributorsDistributorIdContractsGetWithHttpInfo(distributorId, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all contracts for a distributor.
     * 
     * @param distributorId The distributor where the contacts are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;DistributorContractModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DistributorContractModelPageResultModel> apiV10MasterdataDistributorsDistributorIdContractsGetWithHttpInfo(UUID distributorId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractsGetValidateBeforeCall(distributorId, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<DistributorContractModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all contracts for a distributor. (asynchronously)
     * 
     * @param distributorId The distributor where the contacts are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdContractsGetAsync(UUID distributorId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<DistributorContractModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdContractsGetValidateBeforeCall(distributorId, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<DistributorContractModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdDelete
     * @param distributorId The id of the distributor which should be deleted. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdDeleteCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdDeleteValidateBeforeCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdDelete(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdDeleteCall(distributorId, _callback);

    }

    /**
     * Deletes an distributor from cloud store.
     * 
     * @param distributorId The id of the distributor which should be deleted. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataDistributorsDistributorIdDelete(UUID distributorId) throws ApiException {
        apiV10MasterdataDistributorsDistributorIdDeleteWithHttpInfo(distributorId);
    }

    /**
     * Deletes an distributor from cloud store.
     * 
     * @param distributorId The id of the distributor which should be deleted. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataDistributorsDistributorIdDeleteWithHttpInfo(UUID distributorId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdDeleteValidateBeforeCall(distributorId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes an distributor from cloud store. (asynchronously)
     * 
     * @param distributorId The id of the distributor which should be deleted. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdDeleteAsync(UUID distributorId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdDeleteValidateBeforeCall(distributorId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdGet
     * @param distributorId The id of the requested distributor. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdGetCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdGetValidateBeforeCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdGet(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdGetCall(distributorId, _callback);

    }

    /**
     * Returns the distributor requested by the given id.
     * 
     * @param distributorId The id of the requested distributor. (required)
     * @return DistributorModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DistributorModel apiV10MasterdataDistributorsDistributorIdGet(UUID distributorId) throws ApiException {
        ApiResponse<DistributorModel> localVarResp = apiV10MasterdataDistributorsDistributorIdGetWithHttpInfo(distributorId);
        return localVarResp.getData();
    }

    /**
     * Returns the distributor requested by the given id.
     * 
     * @param distributorId The id of the requested distributor. (required)
     * @return ApiResponse&lt;DistributorModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DistributorModel> apiV10MasterdataDistributorsDistributorIdGetWithHttpInfo(UUID distributorId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdGetValidateBeforeCall(distributorId, null);
        Type localVarReturnType = new TypeToken<DistributorModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the distributor requested by the given id. (asynchronously)
     * 
     * @param distributorId The id of the requested distributor. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdGetAsync(UUID distributorId, final ApiCallback<DistributorModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdGetValidateBeforeCall(distributorId, _callback);
        Type localVarReturnType = new TypeToken<DistributorModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet
     * @param distributorId The id of the distributor for the available license groups. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/licenseGroups"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetValidateBeforeCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetCall(distributorId, _callback);

    }

    /**
     * Returns the available license groups.
     * 
     * @param distributorId The id of the distributor for the available license groups. (required)
     * @return StringSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public StringSimpleObjectPageResultModel apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet(UUID distributorId) throws ApiException {
        ApiResponse<StringSimpleObjectPageResultModel> localVarResp = apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetWithHttpInfo(distributorId);
        return localVarResp.getData();
    }

    /**
     * Returns the available license groups.
     * 
     * @param distributorId The id of the distributor for the available license groups. (required)
     * @return ApiResponse&lt;StringSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<StringSimpleObjectPageResultModel> apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetWithHttpInfo(UUID distributorId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetValidateBeforeCall(distributorId, null);
        Type localVarReturnType = new TypeToken<StringSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the available license groups. (asynchronously)
     * 
     * @param distributorId The id of the distributor for the available license groups. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetAsync(UUID distributorId, final ApiCallback<StringSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdLicenseGroupsGetValidateBeforeCall(distributorId, _callback);
        Type localVarReturnType = new TypeToken<StringSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut
     * @param distributorId The id of the distributor (required)
     * @param onboardingFinishDistributorData Onboarding finish data (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutCall(UUID distributorId, OnboardingFinishDistributorData onboardingFinishDistributorData, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = onboardingFinishDistributorData;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/onboarding/finish"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutValidateBeforeCall(UUID distributorId, OnboardingFinishDistributorData onboardingFinishDistributorData, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutCall(distributorId, onboardingFinishDistributorData, _callback);

    }

    /**
     * Finishes onboarding for distributor
     * 
     * @param distributorId The id of the distributor (required)
     * @param onboardingFinishDistributorData Onboarding finish data (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut(UUID distributorId, OnboardingFinishDistributorData onboardingFinishDistributorData) throws ApiException {
        apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutWithHttpInfo(distributorId, onboardingFinishDistributorData);
    }

    /**
     * Finishes onboarding for distributor
     * 
     * @param distributorId The id of the distributor (required)
     * @param onboardingFinishDistributorData Onboarding finish data (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutWithHttpInfo(UUID distributorId, OnboardingFinishDistributorData onboardingFinishDistributorData) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutValidateBeforeCall(distributorId, onboardingFinishDistributorData, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Finishes onboarding for distributor (asynchronously)
     * 
     * @param distributorId The id of the distributor (required)
     * @param onboardingFinishDistributorData Onboarding finish data (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutAsync(UUID distributorId, OnboardingFinishDistributorData onboardingFinishDistributorData, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdOnboardingFinishPutValidateBeforeCall(distributorId, onboardingFinishDistributorData, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdPut
     * @param distributorId The id of the distributor to be updated. (required)
     * @param distributor The distributor object to update the distributor. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdPutCall(UUID distributorId, Distributor distributor, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = distributor;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdPutValidateBeforeCall(UUID distributorId, Distributor distributor, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdPut(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdPutCall(distributorId, distributor, _callback);

    }

    /**
     * Updates an distributor in the cloud store.
     * 
     * @param distributorId The id of the distributor to be updated. (required)
     * @param distributor The distributor object to update the distributor. (optional)
     * @return DistributorModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DistributorModel apiV10MasterdataDistributorsDistributorIdPut(UUID distributorId, Distributor distributor) throws ApiException {
        ApiResponse<DistributorModel> localVarResp = apiV10MasterdataDistributorsDistributorIdPutWithHttpInfo(distributorId, distributor);
        return localVarResp.getData();
    }

    /**
     * Updates an distributor in the cloud store.
     * 
     * @param distributorId The id of the distributor to be updated. (required)
     * @param distributor The distributor object to update the distributor. (optional)
     * @return ApiResponse&lt;DistributorModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DistributorModel> apiV10MasterdataDistributorsDistributorIdPutWithHttpInfo(UUID distributorId, Distributor distributor) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdPutValidateBeforeCall(distributorId, distributor, null);
        Type localVarReturnType = new TypeToken<DistributorModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates an distributor in the cloud store. (asynchronously)
     * 
     * @param distributorId The id of the distributor to be updated. (required)
     * @param distributor The distributor object to update the distributor. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdPutAsync(UUID distributorId, Distributor distributor, final ApiCallback<DistributorModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdPutValidateBeforeCall(distributorId, distributor, _callback);
        Type localVarReturnType = new TypeToken<DistributorModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdSimpleGet
     * @param distributorId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdSimpleGetCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/simple"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdSimpleGetValidateBeforeCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdSimpleGet(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdSimpleGetCall(distributorId, _callback);

    }

    /**
     * Returns a simple distributorRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     * 
     * @param distributorId  (required)
     * @return GuidSimpleObject
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObject apiV10MasterdataDistributorsDistributorIdSimpleGet(UUID distributorId) throws ApiException {
        ApiResponse<GuidSimpleObject> localVarResp = apiV10MasterdataDistributorsDistributorIdSimpleGetWithHttpInfo(distributorId);
        return localVarResp.getData();
    }

    /**
     * Returns a simple distributorRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     * 
     * @param distributorId  (required)
     * @return ApiResponse&lt;GuidSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObject> apiV10MasterdataDistributorsDistributorIdSimpleGetWithHttpInfo(UUID distributorId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdSimpleGetValidateBeforeCall(distributorId, null);
        Type localVarReturnType = new TypeToken<GuidSimpleObject>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a simple distributorRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. (asynchronously)
     * 
     * @param distributorId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdSimpleGetAsync(UUID distributorId, final ApiCallback<GuidSimpleObject> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdSimpleGetValidateBeforeCall(distributorId, _callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObject>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsGet
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsGetCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataDistributorsGetCall(pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all distributors for the authenticated user.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return DistributorModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DistributorModelPageResultModel apiV10MasterdataDistributorsGet(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<DistributorModelPageResultModel> localVarResp = apiV10MasterdataDistributorsGetWithHttpInfo(pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all distributors for the authenticated user.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;DistributorModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DistributorModelPageResultModel> apiV10MasterdataDistributorsGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsGetValidateBeforeCall(pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<DistributorModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all distributors for the authenticated user. (asynchronously)
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsGetAsync(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<DistributorModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsGetValidateBeforeCall(pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<DistributorModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsHierarchyAllGet
     * @param parentDistributorId parent distributor id (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsHierarchyAllGetCall(UUID parentDistributorId, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/hierarchy/all";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (parentDistributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("parentDistributorId", parentDistributorId));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsHierarchyAllGetValidateBeforeCall(UUID parentDistributorId, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataDistributorsHierarchyAllGetCall(parentDistributorId, searchString, _callback);

    }

    /**
     * Returns all distributors based on the parent distributor (only retailforce users)
     * 
     * @param parentDistributorId parent distributor id (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return List&lt;DistributorModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<DistributorModel> apiV10MasterdataDistributorsHierarchyAllGet(UUID parentDistributorId, String searchString) throws ApiException {
        ApiResponse<List<DistributorModel>> localVarResp = apiV10MasterdataDistributorsHierarchyAllGetWithHttpInfo(parentDistributorId, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all distributors based on the parent distributor (only retailforce users)
     * 
     * @param parentDistributorId parent distributor id (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;List&lt;DistributorModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<DistributorModel>> apiV10MasterdataDistributorsHierarchyAllGetWithHttpInfo(UUID parentDistributorId, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsHierarchyAllGetValidateBeforeCall(parentDistributorId, searchString, null);
        Type localVarReturnType = new TypeToken<List<DistributorModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all distributors based on the parent distributor (only retailforce users) (asynchronously)
     * 
     * @param parentDistributorId parent distributor id (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsHierarchyAllGetAsync(UUID parentDistributorId, String searchString, final ApiCallback<List<DistributorModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsHierarchyAllGetValidateBeforeCall(parentDistributorId, searchString, _callback);
        Type localVarReturnType = new TypeToken<List<DistributorModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsHierarchyBreadcrumbGet
     * @param distributorId The actual distributor, if not supplied first level is used. (optional)
     * @param isClearingRun Breadcrumb for Clearing Run. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsHierarchyBreadcrumbGetCall(UUID distributorId, Boolean isClearingRun, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/hierarchy/breadcrumb";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        if (isClearingRun != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isClearingRun", isClearingRun));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsHierarchyBreadcrumbGetValidateBeforeCall(UUID distributorId, Boolean isClearingRun, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataDistributorsHierarchyBreadcrumbGetCall(distributorId, isClearingRun, _callback);

    }

    /**
     * Returns the hierarchy of the actual distributor as a breadcrumb object for navigation.
     * 
     * @param distributorId The actual distributor, if not supplied first level is used. (optional)
     * @param isClearingRun Breadcrumb for Clearing Run. (optional)
     * @return GuidBreadCrumb
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidBreadCrumb apiV10MasterdataDistributorsHierarchyBreadcrumbGet(UUID distributorId, Boolean isClearingRun) throws ApiException {
        ApiResponse<GuidBreadCrumb> localVarResp = apiV10MasterdataDistributorsHierarchyBreadcrumbGetWithHttpInfo(distributorId, isClearingRun);
        return localVarResp.getData();
    }

    /**
     * Returns the hierarchy of the actual distributor as a breadcrumb object for navigation.
     * 
     * @param distributorId The actual distributor, if not supplied first level is used. (optional)
     * @param isClearingRun Breadcrumb for Clearing Run. (optional)
     * @return ApiResponse&lt;GuidBreadCrumb&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidBreadCrumb> apiV10MasterdataDistributorsHierarchyBreadcrumbGetWithHttpInfo(UUID distributorId, Boolean isClearingRun) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsHierarchyBreadcrumbGetValidateBeforeCall(distributorId, isClearingRun, null);
        Type localVarReturnType = new TypeToken<GuidBreadCrumb>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the hierarchy of the actual distributor as a breadcrumb object for navigation. (asynchronously)
     * 
     * @param distributorId The actual distributor, if not supplied first level is used. (optional)
     * @param isClearingRun Breadcrumb for Clearing Run. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsHierarchyBreadcrumbGetAsync(UUID distributorId, Boolean isClearingRun, final ApiCallback<GuidBreadCrumb> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsHierarchyBreadcrumbGetValidateBeforeCall(distributorId, isClearingRun, _callback);
        Type localVarReturnType = new TypeToken<GuidBreadCrumb>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsHierarchyGet
     * @param distributorId The distributor id where the sub distributors should be loaded. If empty the first level (or top level) of the available distributors are loaded. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsHierarchyGetCall(UUID distributorId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/hierarchy";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsHierarchyGetValidateBeforeCall(UUID distributorId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataDistributorsHierarchyGetCall(distributorId, pageOffset, pageSize, _callback);

    }

    /**
     * Returns distributor hierarchy.
     * 
     * @param distributorId The distributor id where the sub distributors should be loaded. If empty the first level (or top level) of the available distributors are loaded. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return GuidHierarchicalSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidHierarchicalSimpleObjectPageResultModel apiV10MasterdataDistributorsHierarchyGet(UUID distributorId, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<GuidHierarchicalSimpleObjectPageResultModel> localVarResp = apiV10MasterdataDistributorsHierarchyGetWithHttpInfo(distributorId, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Returns distributor hierarchy.
     * 
     * @param distributorId The distributor id where the sub distributors should be loaded. If empty the first level (or top level) of the available distributors are loaded. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return ApiResponse&lt;GuidHierarchicalSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidHierarchicalSimpleObjectPageResultModel> apiV10MasterdataDistributorsHierarchyGetWithHttpInfo(UUID distributorId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsHierarchyGetValidateBeforeCall(distributorId, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<GuidHierarchicalSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns distributor hierarchy. (asynchronously)
     * 
     * @param distributorId The distributor id where the sub distributors should be loaded. If empty the first level (or top level) of the available distributors are loaded. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsHierarchyGetAsync(UUID distributorId, Integer pageOffset, Integer pageSize, final ApiCallback<GuidHierarchicalSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsHierarchyGetValidateBeforeCall(distributorId, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<GuidHierarchicalSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsLevelsGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsLevelsGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/levels";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsLevelsGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataDistributorsLevelsGetCall(_callback);

    }

    /**
     * Returns the available distributor levels.
     * 
     * @return GuidSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObjectPageResultModel apiV10MasterdataDistributorsLevelsGet() throws ApiException {
        ApiResponse<GuidSimpleObjectPageResultModel> localVarResp = apiV10MasterdataDistributorsLevelsGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns the available distributor levels.
     * 
     * @return ApiResponse&lt;GuidSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObjectPageResultModel> apiV10MasterdataDistributorsLevelsGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsLevelsGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the available distributor levels. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsLevelsGetAsync(final ApiCallback<GuidSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsLevelsGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsPost
     * @param distributor The distributor to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsPostCall(Distributor distributor, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = distributor;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsPostValidateBeforeCall(Distributor distributor, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataDistributorsPostCall(distributor, _callback);

    }

    /**
     * Creates a new distributor in the cloud store.
     * If RetailForce.Cloud.Model.Distributor.DistributorId set to System.Guid.Empty, then the distributor id will be generated by the service.
     * @param distributor The distributor to create. (optional)
     * @return DistributorModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DistributorModel apiV10MasterdataDistributorsPost(Distributor distributor) throws ApiException {
        ApiResponse<DistributorModel> localVarResp = apiV10MasterdataDistributorsPostWithHttpInfo(distributor);
        return localVarResp.getData();
    }

    /**
     * Creates a new distributor in the cloud store.
     * If RetailForce.Cloud.Model.Distributor.DistributorId set to System.Guid.Empty, then the distributor id will be generated by the service.
     * @param distributor The distributor to create. (optional)
     * @return ApiResponse&lt;DistributorModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DistributorModel> apiV10MasterdataDistributorsPostWithHttpInfo(Distributor distributor) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsPostValidateBeforeCall(distributor, null);
        Type localVarReturnType = new TypeToken<DistributorModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a new distributor in the cloud store. (asynchronously)
     * If RetailForce.Cloud.Model.Distributor.DistributorId set to System.Guid.Empty, then the distributor id will be generated by the service.
     * @param distributor The distributor to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsPostAsync(Distributor distributor, final ApiCallback<DistributorModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsPostValidateBeforeCall(distributor, _callback);
        Type localVarReturnType = new TypeToken<DistributorModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsSimpleGet
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param all Include all distributors not only the distributors which are assigned to the user (only works for retailforce users). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsSimpleGetCall(Integer pageOffset, Integer pageSize, String searchString, Boolean all, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/simple";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        if (all != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("all", all));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsSimpleGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, Boolean all, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataDistributorsSimpleGetCall(pageOffset, pageSize, searchString, all, _callback);

    }

    /**
     * Returns all distributors for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param all Include all distributors not only the distributors which are assigned to the user (only works for retailforce users). (optional)
     * @return GuidSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObjectPageResultModel apiV10MasterdataDistributorsSimpleGet(Integer pageOffset, Integer pageSize, String searchString, Boolean all) throws ApiException {
        ApiResponse<GuidSimpleObjectPageResultModel> localVarResp = apiV10MasterdataDistributorsSimpleGetWithHttpInfo(pageOffset, pageSize, searchString, all);
        return localVarResp.getData();
    }

    /**
     * Returns all distributors for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param all Include all distributors not only the distributors which are assigned to the user (only works for retailforce users). (optional)
     * @return ApiResponse&lt;GuidSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObjectPageResultModel> apiV10MasterdataDistributorsSimpleGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString, Boolean all) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsSimpleGetValidateBeforeCall(pageOffset, pageSize, searchString, all, null);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all distributors for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. (asynchronously)
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param all Include all distributors not only the distributors which are assigned to the user (only works for retailforce users). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsSimpleGetAsync(Integer pageOffset, Integer pageSize, String searchString, Boolean all, final ApiCallback<GuidSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsSimpleGetValidateBeforeCall(pageOffset, pageSize, searchString, all, _callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
