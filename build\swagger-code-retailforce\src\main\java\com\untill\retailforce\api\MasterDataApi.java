/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.BoolResponse;
import com.untill.retailforce.model.CloudParameter;
import com.untill.retailforce.model.EntityTypes;
import java.io.File;
import com.untill.retailforce.model.ImportModel;
import com.untill.retailforce.model.ImportModelPageResultModel;
import com.untill.retailforce.model.ParameterInfo;
import com.untill.retailforce.model.TimelogOverviewModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class MasterDataApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public MasterDataApi() {
        this(Configuration.getDefaultApiClient());
    }

    public MasterDataApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdSupportHoursGet
     * @param distributorId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdSupportHoursGetCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/support-hours"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdSupportHoursGetValidateBeforeCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdSupportHoursGet(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdSupportHoursGetCall(distributorId, _callback);

    }

    /**
     * Get open support hours for distributor
     * 
     * @param distributorId  (required)
     * @return List&lt;TimelogOverviewModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<TimelogOverviewModel> apiV10MasterdataDistributorsDistributorIdSupportHoursGet(UUID distributorId) throws ApiException {
        ApiResponse<List<TimelogOverviewModel>> localVarResp = apiV10MasterdataDistributorsDistributorIdSupportHoursGetWithHttpInfo(distributorId);
        return localVarResp.getData();
    }

    /**
     * Get open support hours for distributor
     * 
     * @param distributorId  (required)
     * @return ApiResponse&lt;List&lt;TimelogOverviewModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<TimelogOverviewModel>> apiV10MasterdataDistributorsDistributorIdSupportHoursGetWithHttpInfo(UUID distributorId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdSupportHoursGetValidateBeforeCall(distributorId, null);
        Type localVarReturnType = new TypeToken<List<TimelogOverviewModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get open support hours for distributor (asynchronously)
     * 
     * @param distributorId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdSupportHoursGetAsync(UUID distributorId, final ApiCallback<List<TimelogOverviewModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdSupportHoursGetValidateBeforeCall(distributorId, _callback);
        Type localVarReturnType = new TypeToken<List<TimelogOverviewModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataDistributorsDistributorIdTimelogGet
     * @param distributorId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdTimelogGetCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/distributors/{distributorId}/timelog"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataDistributorsDistributorIdTimelogGetValidateBeforeCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10MasterdataDistributorsDistributorIdTimelogGet(Async)");
        }

        return apiV10MasterdataDistributorsDistributorIdTimelogGetCall(distributorId, _callback);

    }

    /**
     * Get onboarding or customer care timelog for distributor
     * 
     * @param distributorId  (required)
     * @return List&lt;TimelogOverviewModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<TimelogOverviewModel> apiV10MasterdataDistributorsDistributorIdTimelogGet(UUID distributorId) throws ApiException {
        ApiResponse<List<TimelogOverviewModel>> localVarResp = apiV10MasterdataDistributorsDistributorIdTimelogGetWithHttpInfo(distributorId);
        return localVarResp.getData();
    }

    /**
     * Get onboarding or customer care timelog for distributor
     * 
     * @param distributorId  (required)
     * @return ApiResponse&lt;List&lt;TimelogOverviewModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<TimelogOverviewModel>> apiV10MasterdataDistributorsDistributorIdTimelogGetWithHttpInfo(UUID distributorId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdTimelogGetValidateBeforeCall(distributorId, null);
        Type localVarReturnType = new TypeToken<List<TimelogOverviewModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get onboarding or customer care timelog for distributor (asynchronously)
     * 
     * @param distributorId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataDistributorsDistributorIdTimelogGetAsync(UUID distributorId, final ApiCallback<List<TimelogOverviewModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataDistributorsDistributorIdTimelogGetValidateBeforeCall(distributorId, _callback);
        Type localVarReturnType = new TypeToken<List<TimelogOverviewModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataEntityEntityIdHasOrganizationGet
     * @param entityId The id of the entity to check (distributor or supplier). (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataEntityEntityIdHasOrganizationGetCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/entity/{entityId}/hasOrganization"
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataEntityEntityIdHasOrganizationGetValidateBeforeCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10MasterdataEntityEntityIdHasOrganizationGet(Async)");
        }

        return apiV10MasterdataEntityEntityIdHasOrganizationGetCall(entityId, _callback);

    }

    /**
     * Returns true when entity has allocated organizations.
     * 
     * @param entityId The id of the entity to check (distributor or supplier). (required)
     * @return Boolean
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public Boolean apiV10MasterdataEntityEntityIdHasOrganizationGet(UUID entityId) throws ApiException {
        ApiResponse<Boolean> localVarResp = apiV10MasterdataEntityEntityIdHasOrganizationGetWithHttpInfo(entityId);
        return localVarResp.getData();
    }

    /**
     * Returns true when entity has allocated organizations.
     * 
     * @param entityId The id of the entity to check (distributor or supplier). (required)
     * @return ApiResponse&lt;Boolean&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Boolean> apiV10MasterdataEntityEntityIdHasOrganizationGetWithHttpInfo(UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataEntityEntityIdHasOrganizationGetValidateBeforeCall(entityId, null);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns true when entity has allocated organizations. (asynchronously)
     * 
     * @param entityId The id of the entity to check (distributor or supplier). (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataEntityEntityIdHasOrganizationGetAsync(UUID entityId, final ApiCallback<Boolean> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataEntityEntityIdHasOrganizationGetValidateBeforeCall(entityId, _callback);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataImportGet
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataImportGetCall(Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/import";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataImportGetValidateBeforeCall(Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataImportGetCall(pageOffset, pageSize, _callback);

    }

    /**
     * Load imports of Organizations/Companies/Terminals imports with pagination for the authenticated user.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return ImportModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ImportModelPageResultModel apiV10MasterdataImportGet(Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<ImportModelPageResultModel> localVarResp = apiV10MasterdataImportGetWithHttpInfo(pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Load imports of Organizations/Companies/Terminals imports with pagination for the authenticated user.
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return ApiResponse&lt;ImportModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ImportModelPageResultModel> apiV10MasterdataImportGetWithHttpInfo(Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataImportGetValidateBeforeCall(pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<ImportModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Load imports of Organizations/Companies/Terminals imports with pagination for the authenticated user. (asynchronously)
     * 
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataImportGetAsync(Integer pageOffset, Integer pageSize, final ApiCallback<ImportModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataImportGetValidateBeforeCall(pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<ImportModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataImportImportIdGet
     * @param importId The id of the requested import. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataImportImportIdGetCall(UUID importId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/import/{importId}"
            .replace("{" + "importId" + "}", localVarApiClient.escapeString(importId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataImportImportIdGetValidateBeforeCall(UUID importId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'importId' is set
        if (importId == null) {
            throw new ApiException("Missing the required parameter 'importId' when calling apiV10MasterdataImportImportIdGet(Async)");
        }

        return apiV10MasterdataImportImportIdGetCall(importId, pageOffset, pageSize, _callback);

    }

    /**
     * Returns the detail information of the requested import log.
     * 
     * @param importId The id of the requested import. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit (optional)
     * @return ImportModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ImportModelPageResultModel apiV10MasterdataImportImportIdGet(UUID importId, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<ImportModelPageResultModel> localVarResp = apiV10MasterdataImportImportIdGetWithHttpInfo(importId, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Returns the detail information of the requested import log.
     * 
     * @param importId The id of the requested import. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit (optional)
     * @return ApiResponse&lt;ImportModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ImportModelPageResultModel> apiV10MasterdataImportImportIdGetWithHttpInfo(UUID importId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataImportImportIdGetValidateBeforeCall(importId, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<ImportModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the detail information of the requested import log. (asynchronously)
     * 
     * @param importId The id of the requested import. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataImportImportIdGetAsync(UUID importId, Integer pageOffset, Integer pageSize, final ApiCallback<ImportModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataImportImportIdGetValidateBeforeCall(importId, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<ImportModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataImportPost
     * @param _file  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataImportPostCall(File _file, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/import";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (_file != null) {
            localVarFormParams.put("file", _file);
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataImportPostValidateBeforeCall(File _file, final ApiCallback _callback) throws ApiException {
        // verify the required parameter '_file' is set
        if (_file == null) {
            throw new ApiException("Missing the required parameter '_file' when calling apiV10MasterdataImportPost(Async)");
        }

        return apiV10MasterdataImportPostCall(_file, _callback);

    }

    /**
     * Import Organizations/Companies/Terminals as CSV
     * 
     * @param _file  (required)
     * @return ImportModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ImportModel apiV10MasterdataImportPost(File _file) throws ApiException {
        ApiResponse<ImportModel> localVarResp = apiV10MasterdataImportPostWithHttpInfo(_file);
        return localVarResp.getData();
    }

    /**
     * Import Organizations/Companies/Terminals as CSV
     * 
     * @param _file  (required)
     * @return ApiResponse&lt;ImportModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ImportModel> apiV10MasterdataImportPostWithHttpInfo(File _file) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataImportPostValidateBeforeCall(_file, null);
        Type localVarReturnType = new TypeToken<ImportModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Import Organizations/Companies/Terminals as CSV (asynchronously)
     * 
     * @param _file  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataImportPostAsync(File _file, final ApiCallback<ImportModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataImportPostValidateBeforeCall(_file, _callback);
        Type localVarReturnType = new TypeToken<ImportModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataParameterEntityTypeEntityIdGet
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdGetCall(String entityType, UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/parameter/{entityType}/{entityId}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdGetValidateBeforeCall(String entityType, UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10MasterdataParameterEntityTypeEntityIdGet(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10MasterdataParameterEntityTypeEntityIdGet(Async)");
        }

        return apiV10MasterdataParameterEntityTypeEntityIdGetCall(entityType, entityId, _callback);

    }

    /**
     * Returns all stored parameters for the given entity.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @return List&lt;CloudParameter&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<CloudParameter> apiV10MasterdataParameterEntityTypeEntityIdGet(String entityType, UUID entityId) throws ApiException {
        ApiResponse<List<CloudParameter>> localVarResp = apiV10MasterdataParameterEntityTypeEntityIdGetWithHttpInfo(entityType, entityId);
        return localVarResp.getData();
    }

    /**
     * Returns all stored parameters for the given entity.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @return ApiResponse&lt;List&lt;CloudParameter&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<CloudParameter>> apiV10MasterdataParameterEntityTypeEntityIdGetWithHttpInfo(String entityType, UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdGetValidateBeforeCall(entityType, entityId, null);
        Type localVarReturnType = new TypeToken<List<CloudParameter>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all stored parameters for the given entity. (asynchronously)
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdGetAsync(String entityType, UUID entityId, final ApiCallback<List<CloudParameter>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdGetValidateBeforeCall(entityType, entityId, _callback);
        Type localVarReturnType = new TypeToken<List<CloudParameter>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataParameterEntityTypeEntityIdInfoGet
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdInfoGetCall(String entityType, UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/parameter/{entityType}/{entityId}/info"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdInfoGetValidateBeforeCall(String entityType, UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10MasterdataParameterEntityTypeEntityIdInfoGet(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10MasterdataParameterEntityTypeEntityIdInfoGet(Async)");
        }

        return apiV10MasterdataParameterEntityTypeEntityIdInfoGetCall(entityType, entityId, _callback);

    }

    /**
     * Get parameter infos (available parameters) for the given entity.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @return List&lt;ParameterInfo&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<ParameterInfo> apiV10MasterdataParameterEntityTypeEntityIdInfoGet(String entityType, UUID entityId) throws ApiException {
        ApiResponse<List<ParameterInfo>> localVarResp = apiV10MasterdataParameterEntityTypeEntityIdInfoGetWithHttpInfo(entityType, entityId);
        return localVarResp.getData();
    }

    /**
     * Get parameter infos (available parameters) for the given entity.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @return ApiResponse&lt;List&lt;ParameterInfo&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ParameterInfo>> apiV10MasterdataParameterEntityTypeEntityIdInfoGetWithHttpInfo(String entityType, UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdInfoGetValidateBeforeCall(entityType, entityId, null);
        Type localVarReturnType = new TypeToken<List<ParameterInfo>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get parameter infos (available parameters) for the given entity. (asynchronously)
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdInfoGetAsync(String entityType, UUID entityId, final ApiCallback<List<ParameterInfo>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdInfoGetValidateBeforeCall(entityType, entityId, _callback);
        Type localVarReturnType = new TypeToken<List<ParameterInfo>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The guid of the parameter to delete. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteCall(String entityType, UUID entityId, UUID parameterGuid, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()))
            .replace("{" + "parameterGuid" + "}", localVarApiClient.escapeString(parameterGuid.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteValidateBeforeCall(String entityType, UUID entityId, UUID parameterGuid, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete(Async)");
        }

        // verify the required parameter 'parameterGuid' is set
        if (parameterGuid == null) {
            throw new ApiException("Missing the required parameter 'parameterGuid' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete(Async)");
        }

        return apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteCall(entityType, entityId, parameterGuid, _callback);

    }

    /**
     * Deletes an existing entity parameter.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The guid of the parameter to delete. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete(String entityType, UUID entityId, UUID parameterGuid) throws ApiException {
        apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteWithHttpInfo(entityType, entityId, parameterGuid);
    }

    /**
     * Deletes an existing entity parameter.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The guid of the parameter to delete. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteWithHttpInfo(String entityType, UUID entityId, UUID parameterGuid) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteValidateBeforeCall(entityType, entityId, parameterGuid, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes an existing entity parameter. (asynchronously)
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The guid of the parameter to delete. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteAsync(String entityType, UUID entityId, UUID parameterGuid, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDeleteValidateBeforeCall(entityType, entityId, parameterGuid, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The id of the parameter to load. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetCall(String entityType, UUID entityId, UUID parameterGuid, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()))
            .replace("{" + "parameterGuid" + "}", localVarApiClient.escapeString(parameterGuid.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetValidateBeforeCall(String entityType, UUID entityId, UUID parameterGuid, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet(Async)");
        }

        // verify the required parameter 'parameterGuid' is set
        if (parameterGuid == null) {
            throw new ApiException("Missing the required parameter 'parameterGuid' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet(Async)");
        }

        return apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetCall(entityType, entityId, parameterGuid, _callback);

    }

    /**
     * Returns requested stored parameter for the given entity and parameterId.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The id of the parameter to load. (required)
     * @return CloudParameter
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CloudParameter apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet(String entityType, UUID entityId, UUID parameterGuid) throws ApiException {
        ApiResponse<CloudParameter> localVarResp = apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetWithHttpInfo(entityType, entityId, parameterGuid);
        return localVarResp.getData();
    }

    /**
     * Returns requested stored parameter for the given entity and parameterId.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The id of the parameter to load. (required)
     * @return ApiResponse&lt;CloudParameter&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CloudParameter> apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetWithHttpInfo(String entityType, UUID entityId, UUID parameterGuid) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetValidateBeforeCall(entityType, entityId, parameterGuid, null);
        Type localVarReturnType = new TypeToken<CloudParameter>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns requested stored parameter for the given entity and parameterId. (asynchronously)
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The id of the parameter to load. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetAsync(String entityType, UUID entityId, UUID parameterGuid, final ApiCallback<CloudParameter> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGetValidateBeforeCall(entityType, entityId, parameterGuid, _callback);
        Type localVarReturnType = new TypeToken<CloudParameter>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The guid of the parameter to change. (required)
     * @param cloudParameter The parameter to update. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutCall(String entityType, UUID entityId, UUID parameterGuid, CloudParameter cloudParameter, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = cloudParameter;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()))
            .replace("{" + "parameterGuid" + "}", localVarApiClient.escapeString(parameterGuid.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutValidateBeforeCall(String entityType, UUID entityId, UUID parameterGuid, CloudParameter cloudParameter, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut(Async)");
        }

        // verify the required parameter 'parameterGuid' is set
        if (parameterGuid == null) {
            throw new ApiException("Missing the required parameter 'parameterGuid' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut(Async)");
        }

        // verify the required parameter 'cloudParameter' is set
        if (cloudParameter == null) {
            throw new ApiException("Missing the required parameter 'cloudParameter' when calling apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut(Async)");
        }

        return apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutCall(entityType, entityId, parameterGuid, cloudParameter, _callback);

    }

    /**
     * Updates an existing entity parameter.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The guid of the parameter to change. (required)
     * @param cloudParameter The parameter to update. (required)
     * @return CloudParameter
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CloudParameter apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut(String entityType, UUID entityId, UUID parameterGuid, CloudParameter cloudParameter) throws ApiException {
        ApiResponse<CloudParameter> localVarResp = apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutWithHttpInfo(entityType, entityId, parameterGuid, cloudParameter);
        return localVarResp.getData();
    }

    /**
     * Updates an existing entity parameter.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The guid of the parameter to change. (required)
     * @param cloudParameter The parameter to update. (required)
     * @return ApiResponse&lt;CloudParameter&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CloudParameter> apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutWithHttpInfo(String entityType, UUID entityId, UUID parameterGuid, CloudParameter cloudParameter) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutValidateBeforeCall(entityType, entityId, parameterGuid, cloudParameter, null);
        Type localVarReturnType = new TypeToken<CloudParameter>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates an existing entity parameter. (asynchronously)
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param parameterGuid The guid of the parameter to change. (required)
     * @param cloudParameter The parameter to update. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutAsync(String entityType, UUID entityId, UUID parameterGuid, CloudParameter cloudParameter, final ApiCallback<CloudParameter> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPutValidateBeforeCall(entityType, entityId, parameterGuid, cloudParameter, _callback);
        Type localVarReturnType = new TypeToken<CloudParameter>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataParameterEntityTypeEntityIdPost
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param cloudParameter The RetailForce.Cloud.Model.Configuration.CloudParameter to create. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdPostCall(String entityType, UUID entityId, CloudParameter cloudParameter, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = cloudParameter;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/parameter/{entityType}/{entityId}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdPostValidateBeforeCall(String entityType, UUID entityId, CloudParameter cloudParameter, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10MasterdataParameterEntityTypeEntityIdPost(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10MasterdataParameterEntityTypeEntityIdPost(Async)");
        }

        // verify the required parameter 'cloudParameter' is set
        if (cloudParameter == null) {
            throw new ApiException("Missing the required parameter 'cloudParameter' when calling apiV10MasterdataParameterEntityTypeEntityIdPost(Async)");
        }

        return apiV10MasterdataParameterEntityTypeEntityIdPostCall(entityType, entityId, cloudParameter, _callback);

    }

    /**
     * Creates a new entity parameter.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param cloudParameter The RetailForce.Cloud.Model.Configuration.CloudParameter to create. (required)
     * @return CloudParameter
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CloudParameter apiV10MasterdataParameterEntityTypeEntityIdPost(String entityType, UUID entityId, CloudParameter cloudParameter) throws ApiException {
        ApiResponse<CloudParameter> localVarResp = apiV10MasterdataParameterEntityTypeEntityIdPostWithHttpInfo(entityType, entityId, cloudParameter);
        return localVarResp.getData();
    }

    /**
     * Creates a new entity parameter.
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param cloudParameter The RetailForce.Cloud.Model.Configuration.CloudParameter to create. (required)
     * @return ApiResponse&lt;CloudParameter&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CloudParameter> apiV10MasterdataParameterEntityTypeEntityIdPostWithHttpInfo(String entityType, UUID entityId, CloudParameter cloudParameter) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdPostValidateBeforeCall(entityType, entityId, cloudParameter, null);
        Type localVarReturnType = new TypeToken<CloudParameter>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a new entity parameter. (asynchronously)
     * 
     * @param entityType The type of the entity for the request. (required)
     * @param entityId The id of the entity for the request. (required)
     * @param cloudParameter The RetailForce.Cloud.Model.Configuration.CloudParameter to create. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataParameterEntityTypeEntityIdPostAsync(String entityType, UUID entityId, CloudParameter cloudParameter, final ApiCallback<CloudParameter> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataParameterEntityTypeEntityIdPostValidateBeforeCall(entityType, entityId, cloudParameter, _callback);
        Type localVarReturnType = new TypeToken<CloudParameter>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataVatCheckSimpleGet
     * @param vatNumber The vatNumber to check. (optional)
     * @param entityType  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataVatCheckSimpleGetCall(String vatNumber, EntityTypes entityType, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/vatCheck/simple";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (vatNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("vatNumber", vatNumber));
        }

        if (entityType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityType", entityType));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataVatCheckSimpleGetValidateBeforeCall(String vatNumber, EntityTypes entityType, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataVatCheckSimpleGetCall(vatNumber, entityType, _callback);

    }

    /**
     * Returns whether the given vatNumber is ok or not.
     * 
     * @param vatNumber The vatNumber to check. (optional)
     * @param entityType  (optional)
     * @return BoolResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BoolResponse apiV10MasterdataVatCheckSimpleGet(String vatNumber, EntityTypes entityType) throws ApiException {
        ApiResponse<BoolResponse> localVarResp = apiV10MasterdataVatCheckSimpleGetWithHttpInfo(vatNumber, entityType);
        return localVarResp.getData();
    }

    /**
     * Returns whether the given vatNumber is ok or not.
     * 
     * @param vatNumber The vatNumber to check. (optional)
     * @param entityType  (optional)
     * @return ApiResponse&lt;BoolResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BoolResponse> apiV10MasterdataVatCheckSimpleGetWithHttpInfo(String vatNumber, EntityTypes entityType) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataVatCheckSimpleGetValidateBeforeCall(vatNumber, entityType, null);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns whether the given vatNumber is ok or not. (asynchronously)
     * 
     * @param vatNumber The vatNumber to check. (optional)
     * @param entityType  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataVatCheckSimpleGetAsync(String vatNumber, EntityTypes entityType, final ApiCallback<BoolResponse> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataVatCheckSimpleGetValidateBeforeCall(vatNumber, entityType, _callback);
        Type localVarReturnType = new TypeToken<BoolResponse>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
