# SupportTicketApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10SupportTicketChargePut**](SupportTicketApi.md#apiV10SupportTicketChargePut) | **PUT** /api/v1.0/support-ticket/charge | Charge tickets |
| [**apiV10SupportTicketGet**](SupportTicketApi.md#apiV10SupportTicketGet) | **GET** /api/v1.0/support-ticket | Get SupportTickets |
| [**apiV10SupportTicketOnboardingGet**](SupportTicketApi.md#apiV10SupportTicketOnboardingGet) | **GET** /api/v1.0/support-ticket/onboarding | Returns a list of onboarding tickets to move support ticket to onboarding. |
| [**apiV10SupportTicketOrderGet**](SupportTicketApi.md#apiV10SupportTicketOrderGet) | **GET** /api/v1.0/support-ticket/order | Returns possible orders to map support tickets. |
| [**apiV10SupportTicketSupportTicketNumberCustomerPut**](SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberCustomerPut) | **PUT** /api/v1.0/support-ticket/{supportTicketNumber}/customer | AssignCustomer |
| [**apiV10SupportTicketSupportTicketNumberOnboardingPost**](SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberOnboardingPost) | **POST** /api/v1.0/support-ticket/{supportTicketNumber}/onboarding | Map a support ticket to onboarding of a customer. |
| [**apiV10SupportTicketSupportTicketNumberOrderPost**](SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberOrderPost) | **POST** /api/v1.0/support-ticket/{supportTicketNumber}/order | Map a support ticket to order of a customer. |
| [**apiV10SupportTicketSupportTicketNumberStatusPut**](SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberStatusPut) | **PUT** /api/v1.0/support-ticket/{supportTicketNumber}/status | UpdateChargeable status of ticket |
| [**apiV10SupportTicketSupportTicketNumberTimelogGet**](SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberTimelogGet) | **GET** /api/v1.0/support-ticket/{supportTicketNumber}/timelog | Get Timelog for a support ticket |


<a id="apiV10SupportTicketChargePut"></a>
# **apiV10SupportTicketChargePut**
> DownloadLink apiV10SupportTicketChargePut()

Charge tickets

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    try {
      DownloadLink result = apiInstance.apiV10SupportTicketChargePut();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketChargePut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**DownloadLink**](DownloadLink.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SupportTicketGet"></a>
# **apiV10SupportTicketGet**
> SupportTicketModelPageResultModel apiV10SupportTicketGet(pageOffset, pageSize, statusFilter, searchString)

Get SupportTickets

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    Integer pageOffset = 56; // Integer | page offset
    Integer pageSize = 56; // Integer | page size
    SupportTicketStatus statusFilter = SupportTicketStatus.fromValue("TicketsWithHoursNoCustomer"); // SupportTicketStatus | ticket status filter
    String searchString = "searchString_example"; // String | search text
    try {
      SupportTicketModelPageResultModel result = apiInstance.apiV10SupportTicketGet(pageOffset, pageSize, statusFilter, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page offset | [optional] |
| **pageSize** | **Integer**| page size | [optional] |
| **statusFilter** | [**SupportTicketStatus**](.md)| ticket status filter | [optional] [enum: TicketsWithHoursNoCustomer, TicketsWithHoursCustomerNoBillingStatus, TicketsWithHoursCustomerBillable, TicketsWithHoursCustomerNotBillable, TicketsWithHoursCharged] |
| **searchString** | **String**| search text | [optional] |

### Return type

[**SupportTicketModelPageResultModel**](SupportTicketModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SupportTicketOnboardingGet"></a>
# **apiV10SupportTicketOnboardingGet**
> List&lt;SupportTicketSimple&gt; apiV10SupportTicketOnboardingGet()

Returns a list of onboarding tickets to move support ticket to onboarding.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    try {
      List<SupportTicketSimple> result = apiInstance.apiV10SupportTicketOnboardingGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketOnboardingGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;SupportTicketSimple&gt;**](SupportTicketSimple.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SupportTicketOrderGet"></a>
# **apiV10SupportTicketOrderGet**
> List&lt;GuidSimpleObject&gt; apiV10SupportTicketOrderGet(supportTicketNumber)

Returns possible orders to map support tickets.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    String supportTicketNumber = "supportTicketNumber_example"; // String | The support ticket number of the support ticket to move to an order.
    try {
      List<GuidSimpleObject> result = apiInstance.apiV10SupportTicketOrderGet(supportTicketNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketOrderGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supportTicketNumber** | **String**| The support ticket number of the support ticket to move to an order. | [optional] |

### Return type

[**List&lt;GuidSimpleObject&gt;**](GuidSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SupportTicketSupportTicketNumberCustomerPut"></a>
# **apiV10SupportTicketSupportTicketNumberCustomerPut**
> SupportTicketModel apiV10SupportTicketSupportTicketNumberCustomerPut(supportTicketNumber, customerId)

AssignCustomer

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    String supportTicketNumber = "supportTicketNumber_example"; // String | supportTicketNumber
    UUID customerId = UUID.randomUUID(); // UUID | customer id
    try {
      SupportTicketModel result = apiInstance.apiV10SupportTicketSupportTicketNumberCustomerPut(supportTicketNumber, customerId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketSupportTicketNumberCustomerPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supportTicketNumber** | **String**| supportTicketNumber | |
| **customerId** | **UUID**| customer id | [optional] |

### Return type

[**SupportTicketModel**](SupportTicketModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SupportTicketSupportTicketNumberOnboardingPost"></a>
# **apiV10SupportTicketSupportTicketNumberOnboardingPost**
> SupportTicketModel apiV10SupportTicketSupportTicketNumberOnboardingPost(supportTicketNumber, onboardingTicketNumber)

Map a support ticket to onboarding of a customer.

Only hours in time tracking were moved to onboarding which are not already charged.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    String supportTicketNumber = "supportTicketNumber_example"; // String | The support ticket number to move time tracking entries to onboarding ticket.
    UUID onboardingTicketNumber = UUID.randomUUID(); // UUID | The guid of the onboarding ticket.
    try {
      SupportTicketModel result = apiInstance.apiV10SupportTicketSupportTicketNumberOnboardingPost(supportTicketNumber, onboardingTicketNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketSupportTicketNumberOnboardingPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supportTicketNumber** | **String**| The support ticket number to move time tracking entries to onboarding ticket. | |
| **onboardingTicketNumber** | **UUID**| The guid of the onboarding ticket. | [optional] |

### Return type

[**SupportTicketModel**](SupportTicketModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SupportTicketSupportTicketNumberOrderPost"></a>
# **apiV10SupportTicketSupportTicketNumberOrderPost**
> SupportTicketModel apiV10SupportTicketSupportTicketNumberOrderPost(supportTicketNumber, orderTicketNumber)

Map a support ticket to order of a customer.

Only hours in time tracking were moved to order which are not already charged.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    String supportTicketNumber = "supportTicketNumber_example"; // String | The support ticket number to move time tracking entries to order ticket.
    UUID orderTicketNumber = UUID.randomUUID(); // UUID | The guid of the order ticket.
    try {
      SupportTicketModel result = apiInstance.apiV10SupportTicketSupportTicketNumberOrderPost(supportTicketNumber, orderTicketNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketSupportTicketNumberOrderPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supportTicketNumber** | **String**| The support ticket number to move time tracking entries to order ticket. | |
| **orderTicketNumber** | **UUID**| The guid of the order ticket. | [optional] |

### Return type

[**SupportTicketModel**](SupportTicketModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SupportTicketSupportTicketNumberStatusPut"></a>
# **apiV10SupportTicketSupportTicketNumberStatusPut**
> SupportTicketModel apiV10SupportTicketSupportTicketNumberStatusPut(supportTicketNumber, chargeable)

UpdateChargeable status of ticket

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    String supportTicketNumber = "supportTicketNumber_example"; // String | support ticket number
    Boolean chargeable = true; // Boolean | sets the charable status of the ticket
    try {
      SupportTicketModel result = apiInstance.apiV10SupportTicketSupportTicketNumberStatusPut(supportTicketNumber, chargeable);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketSupportTicketNumberStatusPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supportTicketNumber** | **String**| support ticket number | |
| **chargeable** | **Boolean**| sets the charable status of the ticket | [optional] |

### Return type

[**SupportTicketModel**](SupportTicketModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SupportTicketSupportTicketNumberTimelogGet"></a>
# **apiV10SupportTicketSupportTicketNumberTimelogGet**
> List&lt;TimelogOverviewModel&gt; apiV10SupportTicketSupportTicketNumberTimelogGet(supportTicketNumber)

Get Timelog for a support ticket

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SupportTicketApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SupportTicketApi apiInstance = new SupportTicketApi(defaultClient);
    String supportTicketNumber = "supportTicketNumber_example"; // String | 
    try {
      List<TimelogOverviewModel> result = apiInstance.apiV10SupportTicketSupportTicketNumberTimelogGet(supportTicketNumber);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SupportTicketApi#apiV10SupportTicketSupportTicketNumberTimelogGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **supportTicketNumber** | **String**|  | |

### Return type

[**List&lt;TimelogOverviewModel&gt;**](TimelogOverviewModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

