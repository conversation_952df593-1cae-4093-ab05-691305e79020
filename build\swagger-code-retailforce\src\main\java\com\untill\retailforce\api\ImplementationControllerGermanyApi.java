/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.EntityTypes;
import java.io.File;
import com.untill.retailforce.model.FiscalClient;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.TseAnnouncementCreateResult;
import com.untill.retailforce.model.TseAnnouncementOverview;
import com.untill.retailforce.model.TseAnnouncementOverviewPageResultModel;
import com.untill.retailforce.model.TseAnnouncementStatusInfo;
import com.untill.retailforce.model.TseInformationOverview;
import com.untill.retailforce.model.TseInformationOverviewPageResultModel;
import com.untill.retailforce.model.TseType;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ImplementationControllerGermanyApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ImplementationControllerGermanyApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ImplementationControllerGermanyApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ImplementationDeDistributorIdTseInformation2Post
     * @param distributorId The id of the distributor. (required)
     * @param organizationId A possible guid of a organization id of the tse. Optional. (optional)
     * @param terminalId A possible guid of a terminal id of the tse. Optional. (optional)
     * @param serialNumber The serial number of the tse. (optional)
     * @param certificateExpirationDate The expiration date of the tse&#39;s certificate. (optional)
     * @param bsiCertificationId BSI Certificate ID (-), without the prefix BSI-K-TR- (e.g.: 1234-1234) Optional. (optional)
     * @param initializationDate The date when the tse was activated. Optional. (optional)
     * @param type The type of the tse. Optional. (optional)
     * @param tseId The id of the tse. Optional. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given distributor. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> tseData was sent is null. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeDistributorIdTseInformation2PostCall(UUID distributorId, UUID organizationId, UUID terminalId, String serialNumber, OffsetDateTime certificateExpirationDate, String bsiCertificationId, OffsetDateTime initializationDate, TseType type, UUID tseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/{distributorId}/tse/information2"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organizationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organizationId", organizationId));
        }

        if (terminalId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("terminalId", terminalId));
        }

        if (serialNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("serialNumber", serialNumber));
        }

        if (certificateExpirationDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("certificateExpirationDate", certificateExpirationDate));
        }

        if (bsiCertificationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("bsiCertificationId", bsiCertificationId));
        }

        if (initializationDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("initializationDate", initializationDate));
        }

        if (type != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("type", type));
        }

        if (tseId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tseId", tseId));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeDistributorIdTseInformation2PostValidateBeforeCall(UUID distributorId, UUID organizationId, UUID terminalId, String serialNumber, OffsetDateTime certificateExpirationDate, String bsiCertificationId, OffsetDateTime initializationDate, TseType type, UUID tseId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10ImplementationDeDistributorIdTseInformation2Post(Async)");
        }

        return apiV10ImplementationDeDistributorIdTseInformation2PostCall(distributorId, organizationId, terminalId, serialNumber, certificateExpirationDate, bsiCertificationId, initializationDate, type, tseId, _callback);

    }

    /**
     * Store the given tse information
     * 
     * @param distributorId The id of the distributor. (required)
     * @param organizationId A possible guid of a organization id of the tse. Optional. (optional)
     * @param terminalId A possible guid of a terminal id of the tse. Optional. (optional)
     * @param serialNumber The serial number of the tse. (optional)
     * @param certificateExpirationDate The expiration date of the tse&#39;s certificate. (optional)
     * @param bsiCertificationId BSI Certificate ID (-), without the prefix BSI-K-TR- (e.g.: 1234-1234) Optional. (optional)
     * @param initializationDate The date when the tse was activated. Optional. (optional)
     * @param type The type of the tse. Optional. (optional)
     * @param tseId The id of the tse. Optional. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given distributor. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> tseData was sent is null. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationDeDistributorIdTseInformation2Post(UUID distributorId, UUID organizationId, UUID terminalId, String serialNumber, OffsetDateTime certificateExpirationDate, String bsiCertificationId, OffsetDateTime initializationDate, TseType type, UUID tseId) throws ApiException {
        apiV10ImplementationDeDistributorIdTseInformation2PostWithHttpInfo(distributorId, organizationId, terminalId, serialNumber, certificateExpirationDate, bsiCertificationId, initializationDate, type, tseId);
    }

    /**
     * Store the given tse information
     * 
     * @param distributorId The id of the distributor. (required)
     * @param organizationId A possible guid of a organization id of the tse. Optional. (optional)
     * @param terminalId A possible guid of a terminal id of the tse. Optional. (optional)
     * @param serialNumber The serial number of the tse. (optional)
     * @param certificateExpirationDate The expiration date of the tse&#39;s certificate. (optional)
     * @param bsiCertificationId BSI Certificate ID (-), without the prefix BSI-K-TR- (e.g.: 1234-1234) Optional. (optional)
     * @param initializationDate The date when the tse was activated. Optional. (optional)
     * @param type The type of the tse. Optional. (optional)
     * @param tseId The id of the tse. Optional. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given distributor. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> tseData was sent is null. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationDeDistributorIdTseInformation2PostWithHttpInfo(UUID distributorId, UUID organizationId, UUID terminalId, String serialNumber, OffsetDateTime certificateExpirationDate, String bsiCertificationId, OffsetDateTime initializationDate, TseType type, UUID tseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeDistributorIdTseInformation2PostValidateBeforeCall(distributorId, organizationId, terminalId, serialNumber, certificateExpirationDate, bsiCertificationId, initializationDate, type, tseId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Store the given tse information (asynchronously)
     * 
     * @param distributorId The id of the distributor. (required)
     * @param organizationId A possible guid of a organization id of the tse. Optional. (optional)
     * @param terminalId A possible guid of a terminal id of the tse. Optional. (optional)
     * @param serialNumber The serial number of the tse. (optional)
     * @param certificateExpirationDate The expiration date of the tse&#39;s certificate. (optional)
     * @param bsiCertificationId BSI Certificate ID (-), without the prefix BSI-K-TR- (e.g.: 1234-1234) Optional. (optional)
     * @param initializationDate The date when the tse was activated. Optional. (optional)
     * @param type The type of the tse. Optional. (optional)
     * @param tseId The id of the tse. Optional. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given distributor. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> tseData was sent is null. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeDistributorIdTseInformation2PostAsync(UUID distributorId, UUID organizationId, UUID terminalId, String serialNumber, OffsetDateTime certificateExpirationDate, String bsiCertificationId, OffsetDateTime initializationDate, TseType type, UUID tseId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeDistributorIdTseInformation2PostValidateBeforeCall(distributorId, organizationId, terminalId, serialNumber, certificateExpirationDate, bsiCertificationId, initializationDate, type, tseId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeDistributorIdTseInformationPost
     * @param distributorId The id of the distributor. (required)
     * @param organizationId A possible guid of a organization id of the tse. Optional. (optional)
     * @param terminalId A possible guid of a terminal id of the tse. Optional. (optional)
     * @param body Data of the tse (512 bytes) (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given distributor. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> tseData was sent is null. </td><td>  -  </td></tr>
     </table>
     * @deprecated
     */
    @Deprecated
    public okhttp3.Call apiV10ImplementationDeDistributorIdTseInformationPostCall(UUID distributorId, UUID organizationId, UUID terminalId, byte[] body, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = body;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/{distributorId}/tse/information"
            .replace("{" + "distributorId" + "}", localVarApiClient.escapeString(distributorId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organizationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organizationId", organizationId));
        }

        if (terminalId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("terminalId", terminalId));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @Deprecated
    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeDistributorIdTseInformationPostValidateBeforeCall(UUID distributorId, UUID organizationId, UUID terminalId, byte[] body, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'distributorId' is set
        if (distributorId == null) {
            throw new ApiException("Missing the required parameter 'distributorId' when calling apiV10ImplementationDeDistributorIdTseInformationPost(Async)");
        }

        return apiV10ImplementationDeDistributorIdTseInformationPostCall(distributorId, organizationId, terminalId, body, _callback);

    }

    /**
     * Store the given tse information
     * 
     * @param distributorId The id of the distributor. (required)
     * @param organizationId A possible guid of a organization id of the tse. Optional. (optional)
     * @param terminalId A possible guid of a terminal id of the tse. Optional. (optional)
     * @param body Data of the tse (512 bytes) (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given distributor. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> tseData was sent is null. </td><td>  -  </td></tr>
     </table>
     * @deprecated
     */
    @Deprecated
    public void apiV10ImplementationDeDistributorIdTseInformationPost(UUID distributorId, UUID organizationId, UUID terminalId, byte[] body) throws ApiException {
        apiV10ImplementationDeDistributorIdTseInformationPostWithHttpInfo(distributorId, organizationId, terminalId, body);
    }

    /**
     * Store the given tse information
     * 
     * @param distributorId The id of the distributor. (required)
     * @param organizationId A possible guid of a organization id of the tse. Optional. (optional)
     * @param terminalId A possible guid of a terminal id of the tse. Optional. (optional)
     * @param body Data of the tse (512 bytes) (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given distributor. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> tseData was sent is null. </td><td>  -  </td></tr>
     </table>
     * @deprecated
     */
    @Deprecated
    public ApiResponse<Void> apiV10ImplementationDeDistributorIdTseInformationPostWithHttpInfo(UUID distributorId, UUID organizationId, UUID terminalId, byte[] body) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeDistributorIdTseInformationPostValidateBeforeCall(distributorId, organizationId, terminalId, body, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Store the given tse information (asynchronously)
     * 
     * @param distributorId The id of the distributor. (required)
     * @param organizationId A possible guid of a organization id of the tse. Optional. (optional)
     * @param terminalId A possible guid of a terminal id of the tse. Optional. (optional)
     * @param body Data of the tse (512 bytes) (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given distributor. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> tseData was sent is null. </td><td>  -  </td></tr>
     </table>
     * @deprecated
     */
    @Deprecated
    public okhttp3.Call apiV10ImplementationDeDistributorIdTseInformationPostAsync(UUID distributorId, UUID organizationId, UUID terminalId, byte[] body, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeDistributorIdTseInformationPostValidateBeforeCall(distributorId, organizationId, terminalId, body, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTerminalIdTarGet
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTerminalIdTarGetCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/{terminalId}/tar"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (tillDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tillDate", tillDate));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTerminalIdTarGetValidateBeforeCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationDeTerminalIdTarGet(Async)");
        }

        // verify the required parameter 'fromDate' is set
        if (fromDate == null) {
            throw new ApiException("Missing the required parameter 'fromDate' when calling apiV10ImplementationDeTerminalIdTarGet(Async)");
        }

        // verify the required parameter 'tillDate' is set
        if (tillDate == null) {
            throw new ApiException("Missing the required parameter 'tillDate' when calling apiV10ImplementationDeTerminalIdTarGet(Async)");
        }

        return apiV10ImplementationDeTerminalIdTarGetCall(terminalId, fromDate, tillDate, _callback);

    }

    /**
     * Exports germany tar data (tse) from cloud archive.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationDeTerminalIdTarGet(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate) throws ApiException {
        apiV10ImplementationDeTerminalIdTarGetWithHttpInfo(terminalId, fromDate, tillDate);
    }

    /**
     * Exports germany tar data (tse) from cloud archive.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationDeTerminalIdTarGetWithHttpInfo(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTerminalIdTarGetValidateBeforeCall(terminalId, fromDate, tillDate, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Exports germany tar data (tse) from cloud archive. (asynchronously)
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTerminalIdTarGetAsync(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTerminalIdTarGetValidateBeforeCall(terminalId, fromDate, tillDate, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTerminalIdTaxonomyGet
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param type The type of the export (json &#x3D; 0, csv &#x3D; 1). (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTerminalIdTaxonomyGetCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Integer type, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/{terminalId}/taxonomy"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (tillDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tillDate", tillDate));
        }

        if (type != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("type", type));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTerminalIdTaxonomyGetValidateBeforeCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Integer type, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationDeTerminalIdTaxonomyGet(Async)");
        }

        // verify the required parameter 'fromDate' is set
        if (fromDate == null) {
            throw new ApiException("Missing the required parameter 'fromDate' when calling apiV10ImplementationDeTerminalIdTaxonomyGet(Async)");
        }

        // verify the required parameter 'tillDate' is set
        if (tillDate == null) {
            throw new ApiException("Missing the required parameter 'tillDate' when calling apiV10ImplementationDeTerminalIdTaxonomyGet(Async)");
        }

        // verify the required parameter 'type' is set
        if (type == null) {
            throw new ApiException("Missing the required parameter 'type' when calling apiV10ImplementationDeTerminalIdTaxonomyGet(Async)");
        }

        return apiV10ImplementationDeTerminalIdTaxonomyGetCall(terminalId, fromDate, tillDate, type, _callback);

    }

    /**
     * Exports germany taxonomy data from cloud archive.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param type The type of the export (json &#x3D; 0, csv &#x3D; 1). (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationDeTerminalIdTaxonomyGet(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Integer type) throws ApiException {
        apiV10ImplementationDeTerminalIdTaxonomyGetWithHttpInfo(terminalId, fromDate, tillDate, type);
    }

    /**
     * Exports germany taxonomy data from cloud archive.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param type The type of the export (json &#x3D; 0, csv &#x3D; 1). (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationDeTerminalIdTaxonomyGetWithHttpInfo(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Integer type) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTerminalIdTaxonomyGetValidateBeforeCall(terminalId, fromDate, tillDate, type, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Exports germany taxonomy data from cloud archive. (asynchronously)
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param type The type of the export (json &#x3D; 0, csv &#x3D; 1). (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTerminalIdTaxonomyGetAsync(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Integer type, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTerminalIdTaxonomyGetValidateBeforeCall(terminalId, fromDate, tillDate, type, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTerminalIdTseProvisioningPatch
     * @param terminalId The id of the terminal to start the provisioning. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> License for provisioning not found. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId was sent with Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTerminalIdTseProvisioningPatchCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/{terminalId}/tse/provisioning"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PATCH", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTerminalIdTseProvisioningPatchValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationDeTerminalIdTseProvisioningPatch(Async)");
        }

        return apiV10ImplementationDeTerminalIdTseProvisioningPatchCall(terminalId, _callback);

    }

    /**
     * Starts tse provisioning for the given terminal.
     * 
     * @param terminalId The id of the terminal to start the provisioning. (required)
     * @return FiscalClient
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> License for provisioning not found. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId was sent with Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public FiscalClient apiV10ImplementationDeTerminalIdTseProvisioningPatch(UUID terminalId) throws ApiException {
        ApiResponse<FiscalClient> localVarResp = apiV10ImplementationDeTerminalIdTseProvisioningPatchWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Starts tse provisioning for the given terminal.
     * 
     * @param terminalId The id of the terminal to start the provisioning. (required)
     * @return ApiResponse&lt;FiscalClient&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> License for provisioning not found. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId was sent with Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<FiscalClient> apiV10ImplementationDeTerminalIdTseProvisioningPatchWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTerminalIdTseProvisioningPatchValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<FiscalClient>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Starts tse provisioning for the given terminal. (asynchronously)
     * 
     * @param terminalId The id of the terminal to start the provisioning. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> License for provisioning not found. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId was sent with Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTerminalIdTseProvisioningPatchAsync(UUID terminalId, final ApiCallback<FiscalClient> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTerminalIdTseProvisioningPatchValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<FiscalClient>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsGet
     * @param organisationId organisation id (required)
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param isTest if true only test otherwise not test (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsGetCall(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsGetValidateBeforeCall(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organisationId' is set
        if (organisationId == null) {
            throw new ApiException("Missing the required parameter 'organisationId' when calling apiV10ImplementationDeTseAnnouncementsGet(Async)");
        }

        return apiV10ImplementationDeTseAnnouncementsGetCall(organisationId, pageOffset, pageSize, isTest, _callback);

    }

    /**
     * Get Tse Announcements
     * 
     * @param organisationId organisation id (required)
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param isTest if true only test otherwise not test (optional)
     * @return TseAnnouncementOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TseAnnouncementOverviewPageResultModel apiV10ImplementationDeTseAnnouncementsGet(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest) throws ApiException {
        ApiResponse<TseAnnouncementOverviewPageResultModel> localVarResp = apiV10ImplementationDeTseAnnouncementsGetWithHttpInfo(organisationId, pageOffset, pageSize, isTest);
        return localVarResp.getData();
    }

    /**
     * Get Tse Announcements
     * 
     * @param organisationId organisation id (required)
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param isTest if true only test otherwise not test (optional)
     * @return ApiResponse&lt;TseAnnouncementOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TseAnnouncementOverviewPageResultModel> apiV10ImplementationDeTseAnnouncementsGetWithHttpInfo(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsGetValidateBeforeCall(organisationId, pageOffset, pageSize, isTest, null);
        Type localVarReturnType = new TypeToken<TseAnnouncementOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get Tse Announcements (asynchronously)
     * 
     * @param organisationId organisation id (required)
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param isTest if true only test otherwise not test (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsGetAsync(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest, final ApiCallback<TseAnnouncementOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsGetValidateBeforeCall(organisationId, pageOffset, pageSize, isTest, _callback);
        Type localVarReturnType = new TypeToken<TseAnnouncementOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsPost
     * @param storeIds  (optional)
     * @param isTest  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsPostCall(List<UUID> storeIds, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (storeIds != null) {
            localVarFormParams.put("storeIds", storeIds);
        }

        if (isTest != null) {
            localVarFormParams.put("isTest", isTest);
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsPostValidateBeforeCall(List<UUID> storeIds, Boolean isTest, final ApiCallback _callback) throws ApiException {
        return apiV10ImplementationDeTseAnnouncementsPostCall(storeIds, isTest, _callback);

    }

    /**
     * Create TseAnnouncement  starts a new tse announcement progress. An email per store will be sent to the user, containing the announcemnt data and a verification link.
     * 
     * @param storeIds  (optional)
     * @param isTest  (optional)
     * @return TseAnnouncementCreateResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TseAnnouncementCreateResult apiV10ImplementationDeTseAnnouncementsPost(List<UUID> storeIds, Boolean isTest) throws ApiException {
        ApiResponse<TseAnnouncementCreateResult> localVarResp = apiV10ImplementationDeTseAnnouncementsPostWithHttpInfo(storeIds, isTest);
        return localVarResp.getData();
    }

    /**
     * Create TseAnnouncement  starts a new tse announcement progress. An email per store will be sent to the user, containing the announcemnt data and a verification link.
     * 
     * @param storeIds  (optional)
     * @param isTest  (optional)
     * @return ApiResponse&lt;TseAnnouncementCreateResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TseAnnouncementCreateResult> apiV10ImplementationDeTseAnnouncementsPostWithHttpInfo(List<UUID> storeIds, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsPostValidateBeforeCall(storeIds, isTest, null);
        Type localVarReturnType = new TypeToken<TseAnnouncementCreateResult>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Create TseAnnouncement  starts a new tse announcement progress. An email per store will be sent to the user, containing the announcemnt data and a verification link. (asynchronously)
     * 
     * @param storeIds  (optional)
     * @param isTest  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsPostAsync(List<UUID> storeIds, Boolean isTest, final ApiCallback<TseAnnouncementCreateResult> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsPostValidateBeforeCall(storeIds, isTest, _callback);
        Type localVarReturnType = new TypeToken<TseAnnouncementCreateResult>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsStatusGet
     * @param entityId id of organisation / company, store or terminal (optional)
     * @param entityType entity type (optional)
     * @param isTest filter for test or not test (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsStatusGetCall(UUID entityId, EntityTypes entityType, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements/status";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (entityId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityId", entityId));
        }

        if (entityType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityType", entityType));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsStatusGetValidateBeforeCall(UUID entityId, EntityTypes entityType, Boolean isTest, final ApiCallback _callback) throws ApiException {
        return apiV10ImplementationDeTseAnnouncementsStatusGetCall(entityId, entityType, isTest, _callback);

    }

    /**
     * Get TSE Announcement status:  if there are tse changes or an open tse announcement process (changes in announcemnt structure store,terminal,tseInformation) which need to be reported
     * 
     * @param entityId id of organisation / company, store or terminal (optional)
     * @param entityType entity type (optional)
     * @param isTest filter for test or not test (optional)
     * @return TseAnnouncementStatusInfo
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TseAnnouncementStatusInfo apiV10ImplementationDeTseAnnouncementsStatusGet(UUID entityId, EntityTypes entityType, Boolean isTest) throws ApiException {
        ApiResponse<TseAnnouncementStatusInfo> localVarResp = apiV10ImplementationDeTseAnnouncementsStatusGetWithHttpInfo(entityId, entityType, isTest);
        return localVarResp.getData();
    }

    /**
     * Get TSE Announcement status:  if there are tse changes or an open tse announcement process (changes in announcemnt structure store,terminal,tseInformation) which need to be reported
     * 
     * @param entityId id of organisation / company, store or terminal (optional)
     * @param entityType entity type (optional)
     * @param isTest filter for test or not test (optional)
     * @return ApiResponse&lt;TseAnnouncementStatusInfo&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TseAnnouncementStatusInfo> apiV10ImplementationDeTseAnnouncementsStatusGetWithHttpInfo(UUID entityId, EntityTypes entityType, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsStatusGetValidateBeforeCall(entityId, entityType, isTest, null);
        Type localVarReturnType = new TypeToken<TseAnnouncementStatusInfo>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get TSE Announcement status:  if there are tse changes or an open tse announcement process (changes in announcemnt structure store,terminal,tseInformation) which need to be reported (asynchronously)
     * 
     * @param entityId id of organisation / company, store or terminal (optional)
     * @param entityType entity type (optional)
     * @param isTest filter for test or not test (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsStatusGetAsync(UUID entityId, EntityTypes entityType, Boolean isTest, final ApiCallback<TseAnnouncementStatusInfo> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsStatusGetValidateBeforeCall(entityId, entityType, isTest, _callback);
        Type localVarReturnType = new TypeToken<TseAnnouncementStatusInfo>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost
     * @param tseAnnouncementId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostCall(UUID tseAnnouncementId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/cancel"
            .replace("{" + "tseAnnouncementId" + "}", localVarApiClient.escapeString(tseAnnouncementId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostValidateBeforeCall(UUID tseAnnouncementId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'tseAnnouncementId' is set
        if (tseAnnouncementId == null) {
            throw new ApiException("Missing the required parameter 'tseAnnouncementId' when calling apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost(Async)");
        }

        return apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostCall(tseAnnouncementId, _callback);

    }

    /**
     * Cancel TseAnnouncement
     * 
     * @param tseAnnouncementId  (required)
     * @return TseAnnouncementOverview
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TseAnnouncementOverview apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost(UUID tseAnnouncementId) throws ApiException {
        ApiResponse<TseAnnouncementOverview> localVarResp = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostWithHttpInfo(tseAnnouncementId);
        return localVarResp.getData();
    }

    /**
     * Cancel TseAnnouncement
     * 
     * @param tseAnnouncementId  (required)
     * @return ApiResponse&lt;TseAnnouncementOverview&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TseAnnouncementOverview> apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostWithHttpInfo(UUID tseAnnouncementId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostValidateBeforeCall(tseAnnouncementId, null);
        Type localVarReturnType = new TypeToken<TseAnnouncementOverview>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Cancel TseAnnouncement (asynchronously)
     * 
     * @param tseAnnouncementId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostAsync(UUID tseAnnouncementId, final ApiCallback<TseAnnouncementOverview> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPostValidateBeforeCall(tseAnnouncementId, _callback);
        Type localVarReturnType = new TypeToken<TseAnnouncementOverview>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet
     * @param tseAnnouncementId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetCall(UUID tseAnnouncementId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/data/pdf"
            .replace("{" + "tseAnnouncementId" + "}", localVarApiClient.escapeString(tseAnnouncementId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetValidateBeforeCall(UUID tseAnnouncementId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'tseAnnouncementId' is set
        if (tseAnnouncementId == null) {
            throw new ApiException("Missing the required parameter 'tseAnnouncementId' when calling apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet(Async)");
        }

        return apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetCall(tseAnnouncementId, _callback);

    }

    /**
     * Returns the tse announcement data as pdf
     * 
     * @param tseAnnouncementId  (required)
     * @return File
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public File apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet(UUID tseAnnouncementId) throws ApiException {
        ApiResponse<File> localVarResp = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetWithHttpInfo(tseAnnouncementId);
        return localVarResp.getData();
    }

    /**
     * Returns the tse announcement data as pdf
     * 
     * @param tseAnnouncementId  (required)
     * @return ApiResponse&lt;File&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<File> apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetWithHttpInfo(UUID tseAnnouncementId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetValidateBeforeCall(tseAnnouncementId, null);
        Type localVarReturnType = new TypeToken<File>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the tse announcement data as pdf (asynchronously)
     * 
     * @param tseAnnouncementId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetAsync(UUID tseAnnouncementId, final ApiCallback<File> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGetValidateBeforeCall(tseAnnouncementId, _callback);
        Type localVarReturnType = new TypeToken<File>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet
     * @param tseAnnouncementId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetCall(UUID tseAnnouncementId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}"
            .replace("{" + "tseAnnouncementId" + "}", localVarApiClient.escapeString(tseAnnouncementId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetValidateBeforeCall(UUID tseAnnouncementId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'tseAnnouncementId' is set
        if (tseAnnouncementId == null) {
            throw new ApiException("Missing the required parameter 'tseAnnouncementId' when calling apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet(Async)");
        }

        return apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetCall(tseAnnouncementId, _callback);

    }

    /**
     * Get TseAnnouncement
     * 
     * @param tseAnnouncementId  (required)
     * @return TseAnnouncementOverview
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TseAnnouncementOverview apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet(UUID tseAnnouncementId) throws ApiException {
        ApiResponse<TseAnnouncementOverview> localVarResp = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetWithHttpInfo(tseAnnouncementId);
        return localVarResp.getData();
    }

    /**
     * Get TseAnnouncement
     * 
     * @param tseAnnouncementId  (required)
     * @return ApiResponse&lt;TseAnnouncementOverview&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TseAnnouncementOverview> apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetWithHttpInfo(UUID tseAnnouncementId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetValidateBeforeCall(tseAnnouncementId, null);
        Type localVarReturnType = new TypeToken<TseAnnouncementOverview>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get TseAnnouncement (asynchronously)
     * 
     * @param tseAnnouncementId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetAsync(UUID tseAnnouncementId, final ApiCallback<TseAnnouncementOverview> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGetValidateBeforeCall(tseAnnouncementId, _callback);
        Type localVarReturnType = new TypeToken<TseAnnouncementOverview>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut
     * @param tseAnnouncementId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutCall(UUID tseAnnouncementId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/verification/email"
            .replace("{" + "tseAnnouncementId" + "}", localVarApiClient.escapeString(tseAnnouncementId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutValidateBeforeCall(UUID tseAnnouncementId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'tseAnnouncementId' is set
        if (tseAnnouncementId == null) {
            throw new ApiException("Missing the required parameter 'tseAnnouncementId' when calling apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut(Async)");
        }

        return apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutCall(tseAnnouncementId, _callback);

    }

    /**
     * Resend Verification Email
     * 
     * @param tseAnnouncementId  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut(UUID tseAnnouncementId) throws ApiException {
        apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutWithHttpInfo(tseAnnouncementId);
    }

    /**
     * Resend Verification Email
     * 
     * @param tseAnnouncementId  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutWithHttpInfo(UUID tseAnnouncementId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutValidateBeforeCall(tseAnnouncementId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Resend Verification Email (asynchronously)
     * 
     * @param tseAnnouncementId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutAsync(UUID tseAnnouncementId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPutValidateBeforeCall(tseAnnouncementId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost
     * @param tseAnnouncementId  (required)
     * @param token  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostCall(UUID tseAnnouncementId, String token, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/verification"
            .replace("{" + "tseAnnouncementId" + "}", localVarApiClient.escapeString(tseAnnouncementId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (token != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("token", token));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostValidateBeforeCall(UUID tseAnnouncementId, String token, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'tseAnnouncementId' is set
        if (tseAnnouncementId == null) {
            throw new ApiException("Missing the required parameter 'tseAnnouncementId' when calling apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost(Async)");
        }

        // verify the required parameter 'token' is set
        if (token == null) {
            throw new ApiException("Missing the required parameter 'token' when calling apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost(Async)");
        }

        return apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostCall(tseAnnouncementId, token, _callback);

    }

    /**
     * 
     * 
     * @param tseAnnouncementId  (required)
     * @param token  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost(UUID tseAnnouncementId, String token) throws ApiException {
        apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostWithHttpInfo(tseAnnouncementId, token);
    }

    /**
     * 
     * 
     * @param tseAnnouncementId  (required)
     * @param token  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostWithHttpInfo(UUID tseAnnouncementId, String token) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostValidateBeforeCall(tseAnnouncementId, token, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     *  (asynchronously)
     * 
     * @param tseAnnouncementId  (required)
     * @param token  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostAsync(UUID tseAnnouncementId, String token, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPostValidateBeforeCall(tseAnnouncementId, token, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseAnnouncementsUnannouncedGet
     * @param organisationId organisation id (required)
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param isTest is test (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsUnannouncedGetCall(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/announcements/unannounced";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (isTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("isTest", isTest));
        }

        final String[] localVarAccepts = {
            "application/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseAnnouncementsUnannouncedGetValidateBeforeCall(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organisationId' is set
        if (organisationId == null) {
            throw new ApiException("Missing the required parameter 'organisationId' when calling apiV10ImplementationDeTseAnnouncementsUnannouncedGet(Async)");
        }

        return apiV10ImplementationDeTseAnnouncementsUnannouncedGetCall(organisationId, pageOffset, pageSize, isTest, _callback);

    }

    /**
     * Get all unannounced TseAnnouncements  This include all TseAnnouncements which has never been announced or include new changes, but not tseAnnouncements which are open (verificationPending or inProgress)
     * 
     * @param organisationId organisation id (required)
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param isTest is test (optional)
     * @return GuidSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObjectPageResultModel apiV10ImplementationDeTseAnnouncementsUnannouncedGet(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest) throws ApiException {
        ApiResponse<GuidSimpleObjectPageResultModel> localVarResp = apiV10ImplementationDeTseAnnouncementsUnannouncedGetWithHttpInfo(organisationId, pageOffset, pageSize, isTest);
        return localVarResp.getData();
    }

    /**
     * Get all unannounced TseAnnouncements  This include all TseAnnouncements which has never been announced or include new changes, but not tseAnnouncements which are open (verificationPending or inProgress)
     * 
     * @param organisationId organisation id (required)
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param isTest is test (optional)
     * @return ApiResponse&lt;GuidSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObjectPageResultModel> apiV10ImplementationDeTseAnnouncementsUnannouncedGetWithHttpInfo(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsUnannouncedGetValidateBeforeCall(organisationId, pageOffset, pageSize, isTest, null);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get all unannounced TseAnnouncements  This include all TseAnnouncements which has never been announced or include new changes, but not tseAnnouncements which are open (verificationPending or inProgress) (asynchronously)
     * 
     * @param organisationId organisation id (required)
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param isTest is test (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseAnnouncementsUnannouncedGetAsync(UUID organisationId, Integer pageOffset, Integer pageSize, Boolean isTest, final ApiCallback<GuidSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseAnnouncementsUnannouncedGetValidateBeforeCall(organisationId, pageOffset, pageSize, isTest, _callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseFinderDownloadGet
     * @param distributorId  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseFinderDownloadGetCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/finder/download";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseFinderDownloadGetValidateBeforeCall(UUID distributorId, final ApiCallback _callback) throws ApiException {
        return apiV10ImplementationDeTseFinderDownloadGetCall(distributorId, _callback);

    }

    /**
     * Download TSE Finder
     * 
     * @param distributorId  (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationDeTseFinderDownloadGet(UUID distributorId) throws ApiException {
        apiV10ImplementationDeTseFinderDownloadGetWithHttpInfo(distributorId);
    }

    /**
     * Download TSE Finder
     * 
     * @param distributorId  (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationDeTseFinderDownloadGetWithHttpInfo(UUID distributorId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseFinderDownloadGetValidateBeforeCall(distributorId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Download TSE Finder (asynchronously)
     * 
     * @param distributorId  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseFinderDownloadGetAsync(UUID distributorId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseFinderDownloadGetValidateBeforeCall(distributorId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseInformationExportGet
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param searchString search string (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseInformationExportGetCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/information/export";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseInformationExportGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10ImplementationDeTseInformationExportGetCall(pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Export TseInformations as CSV
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param searchString search string (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationDeTseInformationExportGet(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        apiV10ImplementationDeTseInformationExportGetWithHttpInfo(pageOffset, pageSize, searchString);
    }

    /**
     * Export TseInformations as CSV
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param searchString search string (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationDeTseInformationExportGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseInformationExportGetValidateBeforeCall(pageOffset, pageSize, searchString, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Export TseInformations as CSV (asynchronously)
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param searchString search string (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseInformationExportGetAsync(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseInformationExportGetValidateBeforeCall(pageOffset, pageSize, searchString, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseInformationGet
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param searchString search text search for serial hex (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseInformationGetCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/information";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseInformationGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10ImplementationDeTseInformationGetCall(pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Get tse informations for the given distributor
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param searchString search text search for serial hex (optional)
     * @return TseInformationOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TseInformationOverviewPageResultModel apiV10ImplementationDeTseInformationGet(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<TseInformationOverviewPageResultModel> localVarResp = apiV10ImplementationDeTseInformationGetWithHttpInfo(pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Get tse informations for the given distributor
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param searchString search text search for serial hex (optional)
     * @return ApiResponse&lt;TseInformationOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TseInformationOverviewPageResultModel> apiV10ImplementationDeTseInformationGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseInformationGetValidateBeforeCall(pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<TseInformationOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get tse informations for the given distributor (asynchronously)
     * 
     * @param pageOffset page offset (optional)
     * @param pageSize page size (optional)
     * @param searchString search text search for serial hex (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseInformationGetAsync(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<TseInformationOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseInformationGetValidateBeforeCall(pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<TseInformationOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDeTseInformationTseSerialHexAssignPut
     * @param tseSerialHex tse serial hex (required)
     * @param organisationId new orgnaisationId (optional)
     * @param terminalId new terminalId (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseInformationTseSerialHexAssignPutCall(String tseSerialHex, UUID organisationId, UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/de/tse/information/{tseSerialHex}/assign"
            .replace("{" + "tseSerialHex" + "}", localVarApiClient.escapeString(tseSerialHex.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (terminalId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("terminalId", terminalId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDeTseInformationTseSerialHexAssignPutValidateBeforeCall(String tseSerialHex, UUID organisationId, UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'tseSerialHex' is set
        if (tseSerialHex == null) {
            throw new ApiException("Missing the required parameter 'tseSerialHex' when calling apiV10ImplementationDeTseInformationTseSerialHexAssignPut(Async)");
        }

        return apiV10ImplementationDeTseInformationTseSerialHexAssignPutCall(tseSerialHex, organisationId, terminalId, _callback);

    }

    /**
     * Assign OrganisationId and TerminalId to Tse
     * 
     * @param tseSerialHex tse serial hex (required)
     * @param organisationId new orgnaisationId (optional)
     * @param terminalId new terminalId (optional)
     * @return TseInformationOverview
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TseInformationOverview apiV10ImplementationDeTseInformationTseSerialHexAssignPut(String tseSerialHex, UUID organisationId, UUID terminalId) throws ApiException {
        ApiResponse<TseInformationOverview> localVarResp = apiV10ImplementationDeTseInformationTseSerialHexAssignPutWithHttpInfo(tseSerialHex, organisationId, terminalId);
        return localVarResp.getData();
    }

    /**
     * Assign OrganisationId and TerminalId to Tse
     * 
     * @param tseSerialHex tse serial hex (required)
     * @param organisationId new orgnaisationId (optional)
     * @param terminalId new terminalId (optional)
     * @return ApiResponse&lt;TseInformationOverview&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TseInformationOverview> apiV10ImplementationDeTseInformationTseSerialHexAssignPutWithHttpInfo(String tseSerialHex, UUID organisationId, UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDeTseInformationTseSerialHexAssignPutValidateBeforeCall(tseSerialHex, organisationId, terminalId, null);
        Type localVarReturnType = new TypeToken<TseInformationOverview>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Assign OrganisationId and TerminalId to Tse (asynchronously)
     * 
     * @param tseSerialHex tse serial hex (required)
     * @param organisationId new orgnaisationId (optional)
     * @param terminalId new terminalId (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDeTseInformationTseSerialHexAssignPutAsync(String tseSerialHex, UUID organisationId, UUID terminalId, final ApiCallback<TseInformationOverview> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDeTseInformationTseSerialHexAssignPutValidateBeforeCall(tseSerialHex, organisationId, terminalId, _callback);
        Type localVarReturnType = new TypeToken<TseInformationOverview>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
