/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for Supplier
 */
public class SupplierTest {
    private final Supplier model = new Supplier();

    /**
     * Model tests for Supplier
     */
    @Test
    public void testSupplier() {
        // TODO: test Supplier
    }

    /**
     * Test the property 'supplierId'
     */
    @Test
    public void supplierIdTest() {
        // TODO: test supplierId
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'caption2'
     */
    @Test
    public void caption2Test() {
        // TODO: test caption2
    }

    /**
     * Test the property 'webUrl'
     */
    @Test
    public void webUrlTest() {
        // TODO: test webUrl
    }

    /**
     * Test the property 'vatNumber'
     */
    @Test
    public void vatNumberTest() {
        // TODO: test vatNumber
    }

    /**
     * Test the property 'eMail'
     */
    @Test
    public void eMailTest() {
        // TODO: test eMail
    }

    /**
     * Test the property 'street'
     */
    @Test
    public void streetTest() {
        // TODO: test street
    }

    /**
     * Test the property 'streetNumber'
     */
    @Test
    public void streetNumberTest() {
        // TODO: test streetNumber
    }

    /**
     * Test the property 'postalCode'
     */
    @Test
    public void postalCodeTest() {
        // TODO: test postalCode
    }

    /**
     * Test the property 'city'
     */
    @Test
    public void cityTest() {
        // TODO: test city
    }

    /**
     * Test the property 'community'
     */
    @Test
    public void communityTest() {
        // TODO: test community
    }

    /**
     * Test the property 'countryCode'
     */
    @Test
    public void countryCodeTest() {
        // TODO: test countryCode
    }

}
