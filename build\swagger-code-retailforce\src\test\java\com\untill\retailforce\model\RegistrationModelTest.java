/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.AuthenticationType;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for RegistrationModel
 */
public class RegistrationModelTest {
    private final RegistrationModel model = new RegistrationModel();

    /**
     * Model tests for RegistrationModel
     */
    @Test
    public void testRegistrationModel() {
        // TODO: test RegistrationModel
    }

    /**
     * Test the property 'name1'
     */
    @Test
    public void name1Test() {
        // TODO: test name1
    }

    /**
     * Test the property 'name2'
     */
    @Test
    public void name2Test() {
        // TODO: test name2
    }

    /**
     * Test the property 'cultureInfo'
     */
    @Test
    public void cultureInfoTest() {
        // TODO: test cultureInfo
    }

    /**
     * Test the property 'email'
     */
    @Test
    public void emailTest() {
        // TODO: test email
    }

    /**
     * Test the property 'type'
     */
    @Test
    public void typeTest() {
        // TODO: test type
    }

    /**
     * Test the property 'secret'
     */
    @Test
    public void secretTest() {
        // TODO: test secret
    }

    /**
     * Test the property 'invitationId'
     */
    @Test
    public void invitationIdTest() {
        // TODO: test invitationId
    }

}
