/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.SupportTicketStatus;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for TimelogOverviewModel
 */
public class TimelogOverviewModelTest {
    private final TimelogOverviewModel model = new TimelogOverviewModel();

    /**
     * Model tests for TimelogOverviewModel
     */
    @Test
    public void testTimelogOverviewModel() {
        // TODO: test TimelogOverviewModel
    }

    /**
     * Test the property 'id'
     */
    @Test
    public void idTest() {
        // TODO: test id
    }

    /**
     * Test the property 'username'
     */
    @Test
    public void usernameTest() {
        // TODO: test username
    }

    /**
     * Test the property 'email'
     */
    @Test
    public void emailTest() {
        // TODO: test email
    }

    /**
     * Test the property 'categoryId'
     */
    @Test
    public void categoryIdTest() {
        // TODO: test categoryId
    }

    /**
     * Test the property 'activityText'
     */
    @Test
    public void activityTextTest() {
        // TODO: test activityText
    }

    /**
     * Test the property 'fromDate'
     */
    @Test
    public void fromDateTest() {
        // TODO: test fromDate
    }

    /**
     * Test the property 'tillDate'
     */
    @Test
    public void tillDateTest() {
        // TODO: test tillDate
    }

    /**
     * Test the property 'isHomeoffice'
     */
    @Test
    public void isHomeofficeTest() {
        // TODO: test isHomeoffice
    }

    /**
     * Test the property 'ticketNr'
     */
    @Test
    public void ticketNrTest() {
        // TODO: test ticketNr
    }

    /**
     * Test the property 'ticketCaption'
     */
    @Test
    public void ticketCaptionTest() {
        // TODO: test ticketCaption
    }

    /**
     * Test the property 'ticketStatus'
     */
    @Test
    public void ticketStatusTest() {
        // TODO: test ticketStatus
    }

    /**
     * Test the property 'ticketUrl'
     */
    @Test
    public void ticketUrlTest() {
        // TODO: test ticketUrl
    }

    /**
     * Test the property 'isReplaced'
     */
    @Test
    public void isReplacedTest() {
        // TODO: test isReplaced
    }

}
