# StaticApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ConfigurationBase64Post**](StaticApi.md#apiV10ConfigurationBase64Post) | **POST** /api/v1.0/configuration/base64 | Returns the file containing the content from the base64 string. |
| [**apiV10ConfigurationCountriesGet**](StaticApi.md#apiV10ConfigurationCountriesGet) | **GET** /api/v1.0/configuration/countries | Get Countries |
| [**apiV10ConfigurationCultureInfosGet**](StaticApi.md#apiV10ConfigurationCultureInfosGet) | **GET** /api/v1.0/configuration/cultureInfos | Returns the available culture infos. |


<a id="apiV10ConfigurationBase64Post"></a>
# **apiV10ConfigurationBase64Post**
> apiV10ConfigurationBase64Post(body)

Returns the file containing the content from the base64 string.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.StaticApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    StaticApi apiInstance = new StaticApi(defaultClient);
    String body = "body_example"; // String | The base64 string to be decoded to bytes.
    try {
      apiInstance.apiV10ConfigurationBase64Post(body);
    } catch (ApiException e) {
      System.err.println("Exception when calling StaticApi#apiV10ConfigurationBase64Post");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **body** | **String**| The base64 string to be decoded to bytes. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCountriesGet"></a>
# **apiV10ConfigurationCountriesGet**
> List&lt;StringSimpleObject&gt; apiV10ConfigurationCountriesGet()

Get Countries

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.StaticApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    StaticApi apiInstance = new StaticApi(defaultClient);
    try {
      List<StringSimpleObject> result = apiInstance.apiV10ConfigurationCountriesGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StaticApi#apiV10ConfigurationCountriesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;StringSimpleObject&gt;**](StringSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationCultureInfosGet"></a>
# **apiV10ConfigurationCultureInfosGet**
> List&lt;String&gt; apiV10ConfigurationCultureInfosGet()

Returns the available culture infos.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.StaticApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    StaticApi apiInstance = new StaticApi(defaultClient);
    try {
      List<String> result = apiInstance.apiV10ConfigurationCultureInfosGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling StaticApi#apiV10ConfigurationCultureInfosGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**List&lt;String&gt;**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

