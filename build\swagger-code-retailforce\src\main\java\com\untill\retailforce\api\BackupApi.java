/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.BackupDataPageResultModel;
import java.time.OffsetDateTime;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class BackupApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public BackupApi() {
        this(Configuration.getDefaultApiClient());
    }

    public BackupApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10BackupDataTerminalIdDelete
     * @param terminalId The terminal id of the terminal to delete a backup. (required)
     * @param backupDate The date of the backup to delete. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BackupDataTerminalIdDeleteCall(UUID terminalId, OffsetDateTime backupDate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/backup/data/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (backupDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("backupDate", backupDate));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BackupDataTerminalIdDeleteValidateBeforeCall(UUID terminalId, OffsetDateTime backupDate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10BackupDataTerminalIdDelete(Async)");
        }

        return apiV10BackupDataTerminalIdDeleteCall(terminalId, backupDate, _callback);

    }

    /**
     * Deletes a backup from the storage.
     * 
     * @param terminalId The terminal id of the terminal to delete a backup. (required)
     * @param backupDate The date of the backup to delete. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10BackupDataTerminalIdDelete(UUID terminalId, OffsetDateTime backupDate) throws ApiException {
        apiV10BackupDataTerminalIdDeleteWithHttpInfo(terminalId, backupDate);
    }

    /**
     * Deletes a backup from the storage.
     * 
     * @param terminalId The terminal id of the terminal to delete a backup. (required)
     * @param backupDate The date of the backup to delete. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10BackupDataTerminalIdDeleteWithHttpInfo(UUID terminalId, OffsetDateTime backupDate) throws ApiException {
        okhttp3.Call localVarCall = apiV10BackupDataTerminalIdDeleteValidateBeforeCall(terminalId, backupDate, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes a backup from the storage. (asynchronously)
     * 
     * @param terminalId The terminal id of the terminal to delete a backup. (required)
     * @param backupDate The date of the backup to delete. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BackupDataTerminalIdDeleteAsync(UUID terminalId, OffsetDateTime backupDate, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BackupDataTerminalIdDeleteValidateBeforeCall(terminalId, backupDate, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BackupDataTerminalIdGet
     * @param terminalId The id of the terminal where the backup list is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BackupDataTerminalIdGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/backup/data/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BackupDataTerminalIdGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10BackupDataTerminalIdGet(Async)");
        }

        return apiV10BackupDataTerminalIdGetCall(terminalId, _callback);

    }

    /**
     * Returns the backups which are available for this terminal.
     * 
     * @param terminalId The id of the terminal where the backup list is requested. (required)
     * @return BackupDataPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BackupDataPageResultModel apiV10BackupDataTerminalIdGet(UUID terminalId) throws ApiException {
        ApiResponse<BackupDataPageResultModel> localVarResp = apiV10BackupDataTerminalIdGetWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Returns the backups which are available for this terminal.
     * 
     * @param terminalId The id of the terminal where the backup list is requested. (required)
     * @return ApiResponse&lt;BackupDataPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BackupDataPageResultModel> apiV10BackupDataTerminalIdGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10BackupDataTerminalIdGetValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<BackupDataPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the backups which are available for this terminal. (asynchronously)
     * 
     * @param terminalId The id of the terminal where the backup list is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BackupDataTerminalIdGetAsync(UUID terminalId, final ApiCallback<BackupDataPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BackupDataTerminalIdGetValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<BackupDataPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
