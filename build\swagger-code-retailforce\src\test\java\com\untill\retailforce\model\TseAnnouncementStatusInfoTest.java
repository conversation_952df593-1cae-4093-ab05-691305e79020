/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.TseAnnouncementStatus;
import java.io.IOException;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for TseAnnouncementStatusInfo
 */
public class TseAnnouncementStatusInfoTest {
    private final TseAnnouncementStatusInfo model = new TseAnnouncementStatusInfo();

    /**
     * Model tests for TseAnnouncementStatusInfo
     */
    @Test
    public void testTseAnnouncementStatusInfo() {
        // TODO: test TseAnnouncementStatusInfo
    }

    /**
     * Test the property 'status'
     */
    @Test
    public void statusTest() {
        // TODO: test status
    }

    /**
     * Test the property 'organisationId'
     */
    @Test
    public void organisationIdTest() {
        // TODO: test organisationId
    }

}
