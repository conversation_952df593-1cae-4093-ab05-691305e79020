/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * TaxonomyFileStoreConfiguration
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TaxonomyFileStoreConfiguration {
  public static final String SERIALIZED_NAME_LOCAL_STORE_PATH = "localStorePath";
  @SerializedName(SERIALIZED_NAME_LOCAL_STORE_PATH)
  private String localStorePath;

  public static final String SERIALIZED_NAME_COMPRESS = "compress";
  @SerializedName(SERIALIZED_NAME_COMPRESS)
  private Boolean compress;

  public TaxonomyFileStoreConfiguration() {
  }

  public TaxonomyFileStoreConfiguration localStorePath(String localStorePath) {
    
    this.localStorePath = localStorePath;
    return this;
  }

   /**
   * Get localStorePath
   * @return localStorePath
  **/
  @javax.annotation.Nullable
  public String getLocalStorePath() {
    return localStorePath;
  }


  public void setLocalStorePath(String localStorePath) {
    this.localStorePath = localStorePath;
  }


  public TaxonomyFileStoreConfiguration compress(Boolean compress) {
    
    this.compress = compress;
    return this;
  }

   /**
   * Get compress
   * @return compress
  **/
  @javax.annotation.Nullable
  public Boolean getCompress() {
    return compress;
  }


  public void setCompress(Boolean compress) {
    this.compress = compress;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaxonomyFileStoreConfiguration taxonomyFileStoreConfiguration = (TaxonomyFileStoreConfiguration) o;
    return Objects.equals(this.localStorePath, taxonomyFileStoreConfiguration.localStorePath) &&
        Objects.equals(this.compress, taxonomyFileStoreConfiguration.compress);
  }

  @Override
  public int hashCode() {
    return Objects.hash(localStorePath, compress);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaxonomyFileStoreConfiguration {\n");
    sb.append("    localStorePath: ").append(toIndentedString(localStorePath)).append("\n");
    sb.append("    compress: ").append(toIndentedString(compress)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("localStorePath");
    openapiFields.add("compress");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TaxonomyFileStoreConfiguration
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TaxonomyFileStoreConfiguration.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TaxonomyFileStoreConfiguration is not found in the empty JSON string", TaxonomyFileStoreConfiguration.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TaxonomyFileStoreConfiguration.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TaxonomyFileStoreConfiguration` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("localStorePath") != null && !jsonObj.get("localStorePath").isJsonNull()) && !jsonObj.get("localStorePath").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `localStorePath` to be a primitive type in the JSON string but got `%s`", jsonObj.get("localStorePath").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TaxonomyFileStoreConfiguration.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TaxonomyFileStoreConfiguration' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TaxonomyFileStoreConfiguration> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TaxonomyFileStoreConfiguration.class));

       return (TypeAdapter<T>) new TypeAdapter<TaxonomyFileStoreConfiguration>() {
           @Override
           public void write(JsonWriter out, TaxonomyFileStoreConfiguration value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TaxonomyFileStoreConfiguration read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TaxonomyFileStoreConfiguration given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TaxonomyFileStoreConfiguration
  * @throws IOException if the JSON string is invalid with respect to TaxonomyFileStoreConfiguration
  */
  public static TaxonomyFileStoreConfiguration fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TaxonomyFileStoreConfiguration.class);
  }

 /**
  * Convert an instance of TaxonomyFileStoreConfiguration to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

