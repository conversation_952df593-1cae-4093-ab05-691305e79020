# LicencingApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete**](LicencingApi.md#apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete) | **DELETE** /api/v1.0/licensing/license/accessLicense/{accessLicenseId} |  |
| [**apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet**](LicencingApi.md#apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet) | **GET** /api/v1.0/licensing/license/accessLicense/{accessLicenseId} |  |
| [**apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut**](LicencingApi.md#apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut) | **PUT** /api/v1.0/licensing/license/accessLicense/{accessLicenseId} |  |
| [**apiV10LicensingLicenseAccessLicenseGet**](LicencingApi.md#apiV10LicensingLicenseAccessLicenseGet) | **GET** /api/v1.0/licensing/license/accessLicense |  |
| [**apiV10LicensingLicenseAccessLicensePost**](LicencingApi.md#apiV10LicensingLicenseAccessLicensePost) | **POST** /api/v1.0/licensing/license/accessLicense |  |
| [**apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete**](LicencingApi.md#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete) | **DELETE** /api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract |  |
| [**apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet**](LicencingApi.md#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet) | **GET** /api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract |  |
| [**apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost**](LicencingApi.md#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost) | **POST** /api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract |  |
| [**apiV10LicensingLicenseAdminCreateallcontainersPatch**](LicencingApi.md#apiV10LicensingLicenseAdminCreateallcontainersPatch) | **PATCH** /api/v1.0/licensing/license/admin/createallcontainers |  |
| [**apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete**](LicencingApi.md#apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete) | **DELETE** /api/v1.0/licensing/license/allocation/{licenseId}/{accessLicenseId} |  |
| [**apiV10LicensingLicenseAllocationLicenseIdGet**](LicencingApi.md#apiV10LicensingLicenseAllocationLicenseIdGet) | **GET** /api/v1.0/licensing/license/allocation/{licenseId} |  |
| [**apiV10LicensingLicenseAllocationLicenseIdPost**](LicencingApi.md#apiV10LicensingLicenseAllocationLicenseIdPost) | **POST** /api/v1.0/licensing/license/allocation/{licenseId} |  |
| [**apiV10LicensingLicenseConfigurationConfigurationIdGet**](LicencingApi.md#apiV10LicensingLicenseConfigurationConfigurationIdGet) | **GET** /api/v1.0/licensing/license/configuration/{configurationId} | Returns the possible licenses for the requested organisation. |
| [**apiV10LicensingLicenseConfigurationConfigurationIdPut**](LicencingApi.md#apiV10LicensingLicenseConfigurationConfigurationIdPut) | **PUT** /api/v1.0/licensing/license/configuration/{configurationId} | Updates licenses to the given configuration. |
| [**apiV10LicensingLicenseConfigurationConfigurationIdUsageGet**](LicencingApi.md#apiV10LicensingLicenseConfigurationConfigurationIdUsageGet) | **GET** /api/v1.0/licensing/license/configuration/{configurationId}/usage | Returns the used licenses by this configuration. |
| [**apiV10LicensingLicenseConfigurationGroupsGet**](LicencingApi.md#apiV10LicensingLicenseConfigurationGroupsGet) | **GET** /api/v1.0/licensing/license/configuration/groups |  |
| [**apiV10LicensingLicenseConfigurationUnitsGet**](LicencingApi.md#apiV10LicensingLicenseConfigurationUnitsGet) | **GET** /api/v1.0/licensing/license/configuration/units | Returns all available license units. |
| [**apiV10LicensingLicenseEffectiveEntityIdGet**](LicencingApi.md#apiV10LicensingLicenseEffectiveEntityIdGet) | **GET** /api/v1.0/licensing/license/effective/{entityId} | Returns the effective licenses for the given entity. |
| [**apiV10LicensingLicenseGet**](LicencingApi.md#apiV10LicensingLicenseGet) | **GET** /api/v1.0/licensing/license |  |
| [**apiV10LicensingLicenseLicenseIdDelete**](LicencingApi.md#apiV10LicensingLicenseLicenseIdDelete) | **DELETE** /api/v1.0/licensing/license/{licenseId} |  |
| [**apiV10LicensingLicenseLicenseIdGet**](LicencingApi.md#apiV10LicensingLicenseLicenseIdGet) | **GET** /api/v1.0/licensing/license/{licenseId} |  |
| [**apiV10LicensingLicenseLicenseIdPut**](LicencingApi.md#apiV10LicensingLicenseLicenseIdPut) | **PUT** /api/v1.0/licensing/license/{licenseId} |  |
| [**apiV10LicensingLicensePost**](LicencingApi.md#apiV10LicensingLicensePost) | **POST** /api/v1.0/licensing/license |  |
| [**apiV10LicensingTokenAccessCounterGet**](LicencingApi.md#apiV10LicensingTokenAccessCounterGet) | **GET** /api/v1.0/licensing/tokenAccessCounter | Returns a license token (jwt) to use for requests for azure stateless functions. |
| [**apiV10LicensingTokenGet**](LicencingApi.md#apiV10LicensingTokenGet) | **GET** /api/v1.0/licensing/token | Returns a license token (jwt) to use for requests for azure stateless functions. |
| [**apiV10LicensingTokenTerminalIdGet**](LicencingApi.md#apiV10LicensingTokenTerminalIdGet) | **GET** /api/v1.0/licensing/token/{terminalId} | Returns the license token (jwt) including all licenses for the requested terminal. |
| [**apiV10LicensingTokenValidateGet**](LicencingApi.md#apiV10LicensingTokenValidateGet) | **GET** /api/v1.0/licensing/token/validate | Validates the given license token and returns a RetailForce.Cloud.Functions.Entities.JwtLicenseClaim object containing the license of the token. |


<a id="apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete"></a>
# **apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete**
> apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete(accessLicenseId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String accessLicenseId = "accessLicenseId_example"; // String | 
    try {
      apiInstance.apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete(accessLicenseId);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **accessLicenseId** | **String**|  | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet"></a>
# **apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet**
> AccessLicense apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet(accessLicenseId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String accessLicenseId = "accessLicenseId_example"; // String | 
    try {
      AccessLicense result = apiInstance.apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet(accessLicenseId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **accessLicenseId** | **String**|  | |

### Return type

[**AccessLicense**](AccessLicense.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut"></a>
# **apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut**
> AccessLicense apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut(accessLicenseId, accessLicense)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String accessLicenseId = "accessLicenseId_example"; // String | 
    AccessLicense accessLicense = new AccessLicense(); // AccessLicense | 
    try {
      AccessLicense result = apiInstance.apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut(accessLicenseId, accessLicense);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **accessLicenseId** | **String**|  | |
| **accessLicense** | [**AccessLicense**](AccessLicense.md)|  | [optional] |

### Return type

[**AccessLicense**](AccessLicense.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAccessLicenseGet"></a>
# **apiV10LicensingLicenseAccessLicenseGet**
> AccessLicensePageResultModel apiV10LicensingLicenseAccessLicenseGet(pageOffset, pageSize, searchString)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    Integer pageOffset = 56; // Integer | 
    Integer pageSize = 56; // Integer | 
    String searchString = "searchString_example"; // String | 
    try {
      AccessLicensePageResultModel result = apiInstance.apiV10LicensingLicenseAccessLicenseGet(pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAccessLicenseGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**|  | [optional] |
| **pageSize** | **Integer**|  | [optional] |
| **searchString** | **String**|  | [optional] |

### Return type

[**AccessLicensePageResultModel**](AccessLicensePageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAccessLicensePost"></a>
# **apiV10LicensingLicenseAccessLicensePost**
> AccessLicense apiV10LicensingLicenseAccessLicensePost(accessLicense)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    AccessLicense accessLicense = new AccessLicense(); // AccessLicense | 
    try {
      AccessLicense result = apiInstance.apiV10LicensingLicenseAccessLicensePost(accessLicense);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAccessLicensePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **accessLicense** | [**AccessLicense**](AccessLicense.md)|  | [optional] |

### Return type

[**AccessLicense**](AccessLicense.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete"></a>
# **apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete**
> List&lt;AccessLicenseContract&gt; apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete(accessLicenseId, supplierId, contractId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String accessLicenseId = "accessLicenseId_example"; // String | 
    UUID supplierId = UUID.randomUUID(); // UUID | 
    UUID contractId = UUID.randomUUID(); // UUID | 
    try {
      List<AccessLicenseContract> result = apiInstance.apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete(accessLicenseId, supplierId, contractId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **accessLicenseId** | **String**|  | |
| **supplierId** | **UUID**|  | [optional] |
| **contractId** | **UUID**|  | [optional] |

### Return type

[**List&lt;AccessLicenseContract&gt;**](AccessLicenseContract.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet"></a>
# **apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet**
> List&lt;AccessLicenseContract&gt; apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet(accessLicenseId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String accessLicenseId = "accessLicenseId_example"; // String | 
    try {
      List<AccessLicenseContract> result = apiInstance.apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet(accessLicenseId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **accessLicenseId** | **String**|  | |

### Return type

[**List&lt;AccessLicenseContract&gt;**](AccessLicenseContract.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost"></a>
# **apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost**
> List&lt;AccessLicenseContract&gt; apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost(accessLicenseId, supplierId, contractId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String accessLicenseId = "accessLicenseId_example"; // String | 
    UUID supplierId = UUID.randomUUID(); // UUID | 
    UUID contractId = UUID.randomUUID(); // UUID | 
    try {
      List<AccessLicenseContract> result = apiInstance.apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost(accessLicenseId, supplierId, contractId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **accessLicenseId** | **String**|  | |
| **supplierId** | **UUID**|  | [optional] |
| **contractId** | **UUID**|  | [optional] |

### Return type

[**List&lt;AccessLicenseContract&gt;**](AccessLicenseContract.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAdminCreateallcontainersPatch"></a>
# **apiV10LicensingLicenseAdminCreateallcontainersPatch**
> apiV10LicensingLicenseAdminCreateallcontainersPatch()



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    try {
      apiInstance.apiV10LicensingLicenseAdminCreateallcontainersPatch();
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAdminCreateallcontainersPatch");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete"></a>
# **apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete**
> List&lt;AccessLicenseAllocation&gt; apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete(licenseId, accessLicenseId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String licenseId = "licenseId_example"; // String | 
    String accessLicenseId = "accessLicenseId_example"; // String | 
    try {
      List<AccessLicenseAllocation> result = apiInstance.apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete(licenseId, accessLicenseId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **licenseId** | **String**|  | |
| **accessLicenseId** | **String**|  | |

### Return type

[**List&lt;AccessLicenseAllocation&gt;**](AccessLicenseAllocation.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAllocationLicenseIdGet"></a>
# **apiV10LicensingLicenseAllocationLicenseIdGet**
> List&lt;AccessLicenseAllocation&gt; apiV10LicensingLicenseAllocationLicenseIdGet(licenseId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String licenseId = "licenseId_example"; // String | 
    try {
      List<AccessLicenseAllocation> result = apiInstance.apiV10LicensingLicenseAllocationLicenseIdGet(licenseId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAllocationLicenseIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **licenseId** | **String**|  | |

### Return type

[**List&lt;AccessLicenseAllocation&gt;**](AccessLicenseAllocation.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseAllocationLicenseIdPost"></a>
# **apiV10LicensingLicenseAllocationLicenseIdPost**
> List&lt;AccessLicenseAllocation&gt; apiV10LicensingLicenseAllocationLicenseIdPost(licenseId, accessLicenseAllocation)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String licenseId = "licenseId_example"; // String | 
    AccessLicenseAllocation accessLicenseAllocation = new AccessLicenseAllocation(); // AccessLicenseAllocation | 
    try {
      List<AccessLicenseAllocation> result = apiInstance.apiV10LicensingLicenseAllocationLicenseIdPost(licenseId, accessLicenseAllocation);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseAllocationLicenseIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **licenseId** | **String**|  | |
| **accessLicenseAllocation** | [**AccessLicenseAllocation**](AccessLicenseAllocation.md)|  | [optional] |

### Return type

[**List&lt;AccessLicenseAllocation&gt;**](AccessLicenseAllocation.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseConfigurationConfigurationIdGet"></a>
# **apiV10LicensingLicenseConfigurationConfigurationIdGet**
> List&lt;ConfigLicenseModel&gt; apiV10LicensingLicenseConfigurationConfigurationIdGet(configurationId)

Returns the possible licenses for the requested organisation.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration where the possible licenses are requested.
    try {
      List<ConfigLicenseModel> result = apiInstance.apiV10LicensingLicenseConfigurationConfigurationIdGet(configurationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseConfigurationConfigurationIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration where the possible licenses are requested. | |

### Return type

[**List&lt;ConfigLicenseModel&gt;**](ConfigLicenseModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseConfigurationConfigurationIdPut"></a>
# **apiV10LicensingLicenseConfigurationConfigurationIdPut**
> List&lt;LicenseModel&gt; apiV10LicensingLicenseConfigurationConfigurationIdPut(configurationId, licenseModel)

Updates licenses to the given configuration.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of the configuration to add the licenses.
    List<LicenseModel> licenseModel = Arrays.asList(); // List<LicenseModel> | A list of licenseIds to add / update.
    try {
      List<LicenseModel> result = apiInstance.apiV10LicensingLicenseConfigurationConfigurationIdPut(configurationId, licenseModel);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseConfigurationConfigurationIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of the configuration to add the licenses. | |
| **licenseModel** | [**List&lt;LicenseModel&gt;**](LicenseModel.md)| A list of licenseIds to add / update. | [optional] |

### Return type

[**List&lt;LicenseModel&gt;**](LicenseModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseConfigurationConfigurationIdUsageGet"></a>
# **apiV10LicensingLicenseConfigurationConfigurationIdUsageGet**
> List&lt;BillingLicenseCount&gt; apiV10LicensingLicenseConfigurationConfigurationIdUsageGet(configurationId)

Returns the used licenses by this configuration.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    UUID configurationId = UUID.randomUUID(); // UUID | The id of teh configuration where the used licenses are requested.
    try {
      List<BillingLicenseCount> result = apiInstance.apiV10LicensingLicenseConfigurationConfigurationIdUsageGet(configurationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseConfigurationConfigurationIdUsageGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **configurationId** | **UUID**| The id of teh configuration where the used licenses are requested. | |

### Return type

[**List&lt;BillingLicenseCount&gt;**](BillingLicenseCount.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseConfigurationGroupsGet"></a>
# **apiV10LicensingLicenseConfigurationGroupsGet**
> List&lt;StringSimpleObject&gt; apiV10LicensingLicenseConfigurationGroupsGet()



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    try {
      List<StringSimpleObject> result = apiInstance.apiV10LicensingLicenseConfigurationGroupsGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseConfigurationGroupsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;StringSimpleObject&gt;**](StringSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseConfigurationUnitsGet"></a>
# **apiV10LicensingLicenseConfigurationUnitsGet**
> List&lt;StringSimpleObject&gt; apiV10LicensingLicenseConfigurationUnitsGet()

Returns all available license units.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    try {
      List<StringSimpleObject> result = apiInstance.apiV10LicensingLicenseConfigurationUnitsGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseConfigurationUnitsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;StringSimpleObject&gt;**](StringSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseEffectiveEntityIdGet"></a>
# **apiV10LicensingLicenseEffectiveEntityIdGet**
> List&lt;LicenseModel&gt; apiV10LicensingLicenseEffectiveEntityIdGet(entityId)

Returns the effective licenses for the given entity.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | The entity where the licenses are requested. Can be of type Configuration, Organisation, Company, Store or Terminal.
    try {
      List<LicenseModel> result = apiInstance.apiV10LicensingLicenseEffectiveEntityIdGet(entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseEffectiveEntityIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| The entity where the licenses are requested. Can be of type Configuration, Organisation, Company, Store or Terminal. | |

### Return type

[**List&lt;LicenseModel&gt;**](LicenseModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseGet"></a>
# **apiV10LicensingLicenseGet**
> LicenseModelPageResultModel apiV10LicensingLicenseGet(pageOffset, pageSize, searchString)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    Integer pageOffset = 56; // Integer | 
    Integer pageSize = 56; // Integer | 
    String searchString = "searchString_example"; // String | 
    try {
      LicenseModelPageResultModel result = apiInstance.apiV10LicensingLicenseGet(pageOffset, pageSize, searchString);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**|  | [optional] |
| **pageSize** | **Integer**|  | [optional] |
| **searchString** | **String**|  | [optional] |

### Return type

[**LicenseModelPageResultModel**](LicenseModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseLicenseIdDelete"></a>
# **apiV10LicensingLicenseLicenseIdDelete**
> apiV10LicensingLicenseLicenseIdDelete(licenseId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String licenseId = "licenseId_example"; // String | 
    try {
      apiInstance.apiV10LicensingLicenseLicenseIdDelete(licenseId);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseLicenseIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **licenseId** | **String**|  | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseLicenseIdGet"></a>
# **apiV10LicensingLicenseLicenseIdGet**
> License apiV10LicensingLicenseLicenseIdGet(licenseId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String licenseId = "licenseId_example"; // String | 
    try {
      License result = apiInstance.apiV10LicensingLicenseLicenseIdGet(licenseId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseLicenseIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **licenseId** | **String**|  | |

### Return type

[**License**](License.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicenseLicenseIdPut"></a>
# **apiV10LicensingLicenseLicenseIdPut**
> License apiV10LicensingLicenseLicenseIdPut(licenseId, license)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    String licenseId = "licenseId_example"; // String | 
    License license = new License(); // License | 
    try {
      License result = apiInstance.apiV10LicensingLicenseLicenseIdPut(licenseId, license);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicenseLicenseIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **licenseId** | **String**|  | |
| **license** | [**License**](License.md)|  | [optional] |

### Return type

[**License**](License.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingLicensePost"></a>
# **apiV10LicensingLicensePost**
> License apiV10LicensingLicensePost(license)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    License license = new License(); // License | 
    try {
      License result = apiInstance.apiV10LicensingLicensePost(license);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingLicensePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **license** | [**License**](License.md)|  | [optional] |

### Return type

[**License**](License.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingTokenAccessCounterGet"></a>
# **apiV10LicensingTokenAccessCounterGet**
> String apiV10LicensingTokenAccessCounterGet(uniqueClientId, accesslicenseId)

Returns a license token (jwt) to use for requests for azure stateless functions.

The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    UUID uniqueClientId = UUID.randomUUID(); // UUID | The clientId for which the license token is requested.
    String accesslicenseId = "accesslicenseId_example"; // String | The requested access license (technical license).
    try {
      String result = apiInstance.apiV10LicensingTokenAccessCounterGet(uniqueClientId, accesslicenseId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingTokenAccessCounterGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **uniqueClientId** | **UUID**| The clientId for which the license token is requested. | [optional] |
| **accesslicenseId** | **String**| The requested access license (technical license). | [optional] |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingTokenGet"></a>
# **apiV10LicensingTokenGet**
> String apiV10LicensingTokenGet(uniqueClientId, accesslicenseId)

Returns a license token (jwt) to use for requests for azure stateless functions.

The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    UUID uniqueClientId = UUID.randomUUID(); // UUID | The clientId for which the license token is requested.
    String accesslicenseId = "accesslicenseId_example"; // String | The requested access license (technical license).
    try {
      String result = apiInstance.apiV10LicensingTokenGet(uniqueClientId, accesslicenseId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingTokenGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **uniqueClientId** | **UUID**| The clientId for which the license token is requested. | [optional] |
| **accesslicenseId** | **String**| The requested access license (technical license). | [optional] |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingTokenTerminalIdGet"></a>
# **apiV10LicensingTokenTerminalIdGet**
> String apiV10LicensingTokenTerminalIdGet(terminalId, clientVersion)

Returns the license token (jwt) including all licenses for the requested terminal.

The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id for which terminal the license is requested.
    String clientVersion = "clientVersion_example"; // String | The software version of the client.
    try {
      String result = apiInstance.apiV10LicensingTokenTerminalIdGet(terminalId, clientVersion);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingTokenTerminalIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id for which terminal the license is requested. | |
| **clientVersion** | **String**| The software version of the client. | [optional] |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10LicensingTokenValidateGet"></a>
# **apiV10LicensingTokenValidateGet**
> JwtLicenseClaim apiV10LicensingTokenValidateGet(uniqueClientId, licenseToken)

Validates the given license token and returns a RetailForce.Cloud.Functions.Entities.JwtLicenseClaim object containing the license of the token.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.LicencingApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    LicencingApi apiInstance = new LicencingApi(defaultClient);
    UUID uniqueClientId = UUID.randomUUID(); // UUID | The clientid of requested license token.
    String licenseToken = "licenseToken_example"; // String | The license token to validate and read.
    try {
      JwtLicenseClaim result = apiInstance.apiV10LicensingTokenValidateGet(uniqueClientId, licenseToken);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling LicencingApi#apiV10LicensingTokenValidateGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **uniqueClientId** | **UUID**| The clientid of requested license token. | [optional] |
| **licenseToken** | **String**| The license token to validate and read. | [optional] |

### Return type

[**JwtLicenseClaim**](JwtLicenseClaim.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

