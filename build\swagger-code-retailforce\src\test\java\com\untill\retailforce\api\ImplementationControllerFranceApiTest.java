/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import java.io.File;
import java.time.OffsetDateTime;
import java.util.UUID;
import com.untill.retailforce.model.ValidationError;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ImplementationControllerFranceApi
 */
@Disabled
public class ImplementationControllerFranceApiTest {

    private final ImplementationControllerFranceApi api = new ImplementationControllerFranceApi();

    /**
     * Stores the french archive to the french archive store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationFrTerminalIdStoreArchivePostTest() throws ApiException {
        UUID terminalId = null;
        String archiveFilename = null;
        File frenchArchiveFile = null;
        api.apiV10ImplementationFrTerminalIdStoreArchivePost(terminalId, archiveFilename, frenchArchiveFile);
        // TODO: test validations
    }

    /**
     * Exports one or more french fiscal archives according to date parameter.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationFrTerminalIdTaxArchiveGetTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime tillDate = null;
        api.apiV10ImplementationFrTerminalIdTaxArchiveGet(terminalId, fromDate, tillDate);
        // TODO: test validations
    }

    /**
     * Method to verify the french archive.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostTest() throws ApiException {
        UUID terminalId = null;
        File frenchArchiveFile = null;
        List<ValidationError> response = api.apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost(terminalId, frenchArchiveFile);
        // TODO: test validations
    }

}
