/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.ParameterInfo;
import com.untill.retailforce.model.SignDeviceDriver;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for SignDeviceDriverInfo
 */
public class SignDeviceDriverInfoTest {
    private final SignDeviceDriverInfo model = new SignDeviceDriverInfo();

    /**
     * Model tests for SignDeviceDriverInfo
     */
    @Test
    public void testSignDeviceDriverInfo() {
        // TODO: test SignDeviceDriverInfo
    }

    /**
     * Test the property 'signDeviceDriver'
     */
    @Test
    public void signDeviceDriverTest() {
        // TODO: test signDeviceDriver
    }

    /**
     * Test the property 'parameters'
     */
    @Test
    public void parametersTest() {
        // TODO: test parameters
    }

}
