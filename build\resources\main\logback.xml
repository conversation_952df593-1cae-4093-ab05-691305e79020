<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
		<encoder>
			<pattern>%date [%thread] %-5level %logger{36} %method %mdc: %message%n</pattern>
		</encoder>
	</appender>     

 	<appender name="BUFFER" class="eu.untill.license.server.LogBufferAppender">
		<encoder>
			<pattern>%date [%thread] %-5level %logger{0} %method %mdc: %message%n</pattern>
		</encoder>
	</appender>

	<appender name="FILE_TRACE" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>logs/eu.untill.license.trace.log</file>
		<encoder>
			<pattern>%date [%thread] %-5level %logger{0} %method %mdc: %message%n</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>logs/eu.untill.license.trace.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
			<maxFileSize>500MB</maxFileSize>
			<maxHistory>10</maxHistory>
			<totalSizeCap>10GB</totalSizeCap>
		</rollingPolicy>
	</appender>

	<appender name="FILE_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>logs/eu.untill.license.info.log</file>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>INFO</level>
		</filter>
		<encoder>
			<pattern>%date [%thread] %-5level %logger{0} %mdc: %message%n</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>logs/eu.untill.license.info.%d{yyyy-MM}.%i.log</fileNamePattern>
			<maxFileSize>500MB</maxFileSize>
			<maxHistory>10</maxHistory>
			<totalSizeCap>10GB</totalSizeCap>
		</rollingPolicy>
	</appender>

	<appender name="FILE_WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>logs/eu.untill.license.warn.log</file>
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>WARN</level>
		</filter>
		<encoder>
			<pattern>%date [%thread] %-5level %logger{0} %mdc: %message%n</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
			<fileNamePattern>logs/eu.untill.license.warn.%d{yyyy}.%i.log</fileNamePattern>
			<maxFileSize>500MB</maxFileSize>
			<maxHistory>10</maxHistory>
			<totalSizeCap>10GB</totalSizeCap>
		</rollingPolicy>
	</appender>

	<logger name="eu.untill.license" level="TRACE">
		<appender-ref ref="BUFFER" />
		<appender-ref ref="FILE_TRACE" />
		<appender-ref ref="FILE_INFO" />
		<appender-ref ref="FILE_WARN" />
	</logger>

	<root level="INFO">
		<appender-ref ref="STDOUT" />
	</root>

</configuration>
