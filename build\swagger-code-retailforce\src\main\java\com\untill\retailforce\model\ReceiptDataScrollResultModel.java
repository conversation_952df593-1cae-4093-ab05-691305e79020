/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.ReceiptData;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Model for infinite scrolling at the backend.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class ReceiptDataScrollResultModel {
  public static final String SERIALIZED_NAME_ITEMS = "items";
  @SerializedName(SERIALIZED_NAME_ITEMS)
  private List<ReceiptData> items;

  public static final String SERIALIZED_NAME_CONTINUATION_TOKEN = "continuationToken";
  @SerializedName(SERIALIZED_NAME_CONTINUATION_TOKEN)
  private String continuationToken;

  public static final String SERIALIZED_NAME_QUERY = "query";
  @SerializedName(SERIALIZED_NAME_QUERY)
  private String query;

  public ReceiptDataScrollResultModel() {
  }

  public ReceiptDataScrollResultModel items(List<ReceiptData> items) {
    
    this.items = items;
    return this;
  }

  public ReceiptDataScrollResultModel addItemsItem(ReceiptData itemsItem) {
    if (this.items == null) {
      this.items = new ArrayList<>();
    }
    this.items.add(itemsItem);
    return this;
  }

   /**
   * The returned list of items.
   * @return items
  **/
  @javax.annotation.Nullable
  public List<ReceiptData> getItems() {
    return items;
  }


  public void setItems(List<ReceiptData> items) {
    this.items = items;
  }


  public ReceiptDataScrollResultModel continuationToken(String continuationToken) {
    
    this.continuationToken = continuationToken;
    return this;
  }

   /**
   * The token to continue search.
   * @return continuationToken
  **/
  @javax.annotation.Nullable
  public String getContinuationToken() {
    return continuationToken;
  }


  public void setContinuationToken(String continuationToken) {
    this.continuationToken = continuationToken;
  }


  public ReceiptDataScrollResultModel query(String query) {
    
    this.query = query;
    return this;
  }

   /**
   * The query of the search.
   * @return query
  **/
  @javax.annotation.Nullable
  public String getQuery() {
    return query;
  }


  public void setQuery(String query) {
    this.query = query;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReceiptDataScrollResultModel receiptDataScrollResultModel = (ReceiptDataScrollResultModel) o;
    return Objects.equals(this.items, receiptDataScrollResultModel.items) &&
        Objects.equals(this.continuationToken, receiptDataScrollResultModel.continuationToken) &&
        Objects.equals(this.query, receiptDataScrollResultModel.query);
  }

  @Override
  public int hashCode() {
    return Objects.hash(items, continuationToken, query);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReceiptDataScrollResultModel {\n");
    sb.append("    items: ").append(toIndentedString(items)).append("\n");
    sb.append("    continuationToken: ").append(toIndentedString(continuationToken)).append("\n");
    sb.append("    query: ").append(toIndentedString(query)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("items");
    openapiFields.add("continuationToken");
    openapiFields.add("query");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to ReceiptDataScrollResultModel
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!ReceiptDataScrollResultModel.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in ReceiptDataScrollResultModel is not found in the empty JSON string", ReceiptDataScrollResultModel.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!ReceiptDataScrollResultModel.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `ReceiptDataScrollResultModel` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if (jsonObj.get("items") != null && !jsonObj.get("items").isJsonNull()) {
        JsonArray jsonArrayitems = jsonObj.getAsJsonArray("items");
        if (jsonArrayitems != null) {
          // ensure the json data is an array
          if (!jsonObj.get("items").isJsonArray()) {
            throw new IllegalArgumentException(String.format("Expected the field `items` to be an array in the JSON string but got `%s`", jsonObj.get("items").toString()));
          }

          // validate the optional field `items` (array)
          for (int i = 0; i < jsonArrayitems.size(); i++) {
            ReceiptData.validateJsonObject(jsonArrayitems.get(i).getAsJsonObject());
          };
        }
      }
      if ((jsonObj.get("continuationToken") != null && !jsonObj.get("continuationToken").isJsonNull()) && !jsonObj.get("continuationToken").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `continuationToken` to be a primitive type in the JSON string but got `%s`", jsonObj.get("continuationToken").toString()));
      }
      if ((jsonObj.get("query") != null && !jsonObj.get("query").isJsonNull()) && !jsonObj.get("query").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `query` to be a primitive type in the JSON string but got `%s`", jsonObj.get("query").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!ReceiptDataScrollResultModel.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'ReceiptDataScrollResultModel' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<ReceiptDataScrollResultModel> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(ReceiptDataScrollResultModel.class));

       return (TypeAdapter<T>) new TypeAdapter<ReceiptDataScrollResultModel>() {
           @Override
           public void write(JsonWriter out, ReceiptDataScrollResultModel value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public ReceiptDataScrollResultModel read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of ReceiptDataScrollResultModel given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of ReceiptDataScrollResultModel
  * @throws IOException if the JSON string is invalid with respect to ReceiptDataScrollResultModel
  */
  public static ReceiptDataScrollResultModel fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, ReceiptDataScrollResultModel.class);
  }

 /**
  * Convert an instance of ReceiptDataScrollResultModel to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

