/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.ClientConfigurationGermany;
import com.untill.retailforce.model.TseDriverInfo;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ConfigurationControllerGermanyApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ConfigurationControllerGermanyApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ConfigurationControllerGermanyApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ConfigurationDeClientConfigurationTerminalIdPost
     * @param terminalId The terminalId of the configuration. (required)
     * @param clientConfigurationGermany The configuration to store. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationDeClientConfigurationTerminalIdPostCall(UUID terminalId, ClientConfigurationGermany clientConfigurationGermany, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = clientConfigurationGermany;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/de/clientConfiguration/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationDeClientConfigurationTerminalIdPostValidateBeforeCall(UUID terminalId, ClientConfigurationGermany clientConfigurationGermany, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationDeClientConfigurationTerminalIdPost(Async)");
        }

        return apiV10ConfigurationDeClientConfigurationTerminalIdPostCall(terminalId, clientConfigurationGermany, _callback);

    }

    /**
     * Stores the necessary fields of the client configuration to the insights tables for germany.
     * At the moment only primary tse configuration is stored to the system.
     * @param terminalId The terminalId of the configuration. (required)
     * @param clientConfigurationGermany The configuration to store. (optional)
     * @return Boolean
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public Boolean apiV10ConfigurationDeClientConfigurationTerminalIdPost(UUID terminalId, ClientConfigurationGermany clientConfigurationGermany) throws ApiException {
        ApiResponse<Boolean> localVarResp = apiV10ConfigurationDeClientConfigurationTerminalIdPostWithHttpInfo(terminalId, clientConfigurationGermany);
        return localVarResp.getData();
    }

    /**
     * Stores the necessary fields of the client configuration to the insights tables for germany.
     * At the moment only primary tse configuration is stored to the system.
     * @param terminalId The terminalId of the configuration. (required)
     * @param clientConfigurationGermany The configuration to store. (optional)
     * @return ApiResponse&lt;Boolean&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Boolean> apiV10ConfigurationDeClientConfigurationTerminalIdPostWithHttpInfo(UUID terminalId, ClientConfigurationGermany clientConfigurationGermany) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationDeClientConfigurationTerminalIdPostValidateBeforeCall(terminalId, clientConfigurationGermany, null);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Stores the necessary fields of the client configuration to the insights tables for germany. (asynchronously)
     * At the moment only primary tse configuration is stored to the system.
     * @param terminalId The terminalId of the configuration. (required)
     * @param clientConfigurationGermany The configuration to store. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationDeClientConfigurationTerminalIdPostAsync(UUID terminalId, ClientConfigurationGermany clientConfigurationGermany, final ApiCallback<Boolean> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationDeClientConfigurationTerminalIdPostValidateBeforeCall(terminalId, clientConfigurationGermany, _callback);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost
     * @param terminalId The terminalId of the configuration. (required)
     * @param tseVersionInformation The tse version information of the client. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostCall(UUID terminalId, String tseVersionInformation, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/de/clientConfiguration/{terminalId}/tseVersion"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (tseVersionInformation != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tseVersionInformation", tseVersionInformation));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostValidateBeforeCall(UUID terminalId, String tseVersionInformation, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost(Async)");
        }

        return apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostCall(terminalId, tseVersionInformation, _callback);

    }

    /**
     * Stores the actual tse version information to the terminal insights table.
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @param tseVersionInformation The tse version information of the client. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost(UUID terminalId, String tseVersionInformation) throws ApiException {
        apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostWithHttpInfo(terminalId, tseVersionInformation);
    }

    /**
     * Stores the actual tse version information to the terminal insights table.
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @param tseVersionInformation The tse version information of the client. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostWithHttpInfo(UUID terminalId, String tseVersionInformation) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostValidateBeforeCall(terminalId, tseVersionInformation, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Stores the actual tse version information to the terminal insights table. (asynchronously)
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @param tseVersionInformation The tse version information of the client. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostAsync(UUID terminalId, String tseVersionInformation, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPostValidateBeforeCall(terminalId, tseVersionInformation, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationDeTseDriverInfoGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationDeTseDriverInfoGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/de/tseDriverInfo";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationDeTseDriverInfoGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationDeTseDriverInfoGetCall(_callback);

    }

    /**
     * Returns supported tse drivers for configuration in the cloud user interface.
     * This method does not return parameters marked with RetailForce.Fiscalisation.Configuration.ParameterInfo.PortalHidden &#x3D; true.
     * @return List&lt;TseDriverInfo&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<TseDriverInfo> apiV10ConfigurationDeTseDriverInfoGet() throws ApiException {
        ApiResponse<List<TseDriverInfo>> localVarResp = apiV10ConfigurationDeTseDriverInfoGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns supported tse drivers for configuration in the cloud user interface.
     * This method does not return parameters marked with RetailForce.Fiscalisation.Configuration.ParameterInfo.PortalHidden &#x3D; true.
     * @return ApiResponse&lt;List&lt;TseDriverInfo&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<TseDriverInfo>> apiV10ConfigurationDeTseDriverInfoGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationDeTseDriverInfoGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<TseDriverInfo>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns supported tse drivers for configuration in the cloud user interface. (asynchronously)
     * This method does not return parameters marked with RetailForce.Fiscalisation.Configuration.ParameterInfo.PortalHidden &#x3D; true.
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationDeTseDriverInfoGetAsync(final ApiCallback<List<TseDriverInfo>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationDeTseDriverInfoGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<TseDriverInfo>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
