/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.FiscalCountry;
import com.untill.retailforce.model.PlatformType;
import com.untill.retailforce.model.TerminalType;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Represents a terminal and it&#39;s configuration.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class Terminal {
  public static final String SERIALIZED_NAME_TERMINAL_ID = "terminalId";
  @SerializedName(SERIALIZED_NAME_TERMINAL_ID)
  private UUID terminalId;

  public static final String SERIALIZED_NAME_STORE_ID = "storeId";
  @SerializedName(SERIALIZED_NAME_STORE_ID)
  private UUID storeId;

  public static final String SERIALIZED_NAME_TERMINAL_NUMBER = "terminalNumber";
  @SerializedName(SERIALIZED_NAME_TERMINAL_NUMBER)
  private String terminalNumber;

  public static final String SERIALIZED_NAME_FISCAL_COUNTRY = "fiscalCountry";
  @SerializedName(SERIALIZED_NAME_FISCAL_COUNTRY)
  private FiscalCountry fiscalCountry;

  public static final String SERIALIZED_NAME_CAPTION = "caption";
  @SerializedName(SERIALIZED_NAME_CAPTION)
  private String caption;

  public static final String SERIALIZED_NAME_PLATFORM_TYPE = "platformType";
  @SerializedName(SERIALIZED_NAME_PLATFORM_TYPE)
  private PlatformType platformType;

  public static final String SERIALIZED_NAME_IS_TEST = "isTest";
  @SerializedName(SERIALIZED_NAME_IS_TEST)
  private Boolean isTest;

  public static final String SERIALIZED_NAME_ARCHIVE_DATE = "archiveDate";
  @SerializedName(SERIALIZED_NAME_ARCHIVE_DATE)
  private OffsetDateTime archiveDate;

  public static final String SERIALIZED_NAME_CLIENT_CONFIGURATION_ID = "clientConfigurationId";
  @SerializedName(SERIALIZED_NAME_CLIENT_CONFIGURATION_ID)
  private UUID clientConfigurationId;

  public static final String SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_ID = "newClientConfigurationId";
  @SerializedName(SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_ID)
  private UUID newClientConfigurationId;

  public static final String SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_VALID_FROM = "newClientConfigurationValidFrom";
  @SerializedName(SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_VALID_FROM)
  private OffsetDateTime newClientConfigurationValidFrom;

  public static final String SERIALIZED_NAME_GLOBAL_SHORT_ID = "globalShortId";
  @SerializedName(SERIALIZED_NAME_GLOBAL_SHORT_ID)
  private String globalShortId;

  public static final String SERIALIZED_NAME_PURCHASE_DATE = "purchaseDate";
  @SerializedName(SERIALIZED_NAME_PURCHASE_DATE)
  private OffsetDateTime purchaseDate;

  public static final String SERIALIZED_NAME_COMMISSIONING_DATE = "commissioningDate";
  @SerializedName(SERIALIZED_NAME_COMMISSIONING_DATE)
  private OffsetDateTime commissioningDate;

  public static final String SERIALIZED_NAME_DECOMMISSIONING_DATE = "decommissioningDate";
  @SerializedName(SERIALIZED_NAME_DECOMMISSIONING_DATE)
  private OffsetDateTime decommissioningDate;

  public static final String SERIALIZED_NAME_TERMINAL_TYPE = "terminalType";
  @SerializedName(SERIALIZED_NAME_TERMINAL_TYPE)
  private TerminalType terminalType;

  public static final String SERIALIZED_NAME_UPDATED_BY_PRINCIPAL_ID = "updatedByPrincipalId";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY_PRINCIPAL_ID)
  private UUID updatedByPrincipalId;

  public static final String SERIALIZED_NAME_CASH_REGISTER_ID = "cashRegisterId";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_ID)
  private String cashRegisterId;

  public static final String SERIALIZED_NAME_CASH_REGISTER_BRAND = "cashRegisterBrand";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_BRAND)
  private String cashRegisterBrand;

  public static final String SERIALIZED_NAME_CASH_REGISTER_MODELNAME = "cashRegisterModelname";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_MODELNAME)
  private String cashRegisterModelname;

  public static final String SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_BRAND = "cashRegisterSoftwareBrand";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_BRAND)
  private String cashRegisterSoftwareBrand;

  public static final String SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_VERSION = "cashRegisterSoftwareVersion";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_VERSION)
  private String cashRegisterSoftwareVersion;

  public static final String SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_COMPANY = "cashRegisterSoftwareCompany";
  @SerializedName(SERIALIZED_NAME_CASH_REGISTER_SOFTWARE_COMPANY)
  private String cashRegisterSoftwareCompany;

  public Terminal() {
  }

  public Terminal terminalId(UUID terminalId) {
    
    this.terminalId = terminalId;
    return this;
  }

   /**
   * The id of the terminal.
   * @return terminalId
  **/
  @javax.annotation.Nullable
  public UUID getTerminalId() {
    return terminalId;
  }


  public void setTerminalId(UUID terminalId) {
    this.terminalId = terminalId;
  }


  public Terminal storeId(UUID storeId) {
    
    this.storeId = storeId;
    return this;
  }

   /**
   * The store of the terminal.
   * @return storeId
  **/
  @javax.annotation.Nullable
  public UUID getStoreId() {
    return storeId;
  }


  public void setStoreId(UUID storeId) {
    this.storeId = storeId;
  }


  public Terminal terminalNumber(String terminalNumber) {
    
    this.terminalNumber = terminalNumber;
    return this;
  }

   /**
   * The terminal number.
   * @return terminalNumber
  **/
  @javax.annotation.Nullable
  public String getTerminalNumber() {
    return terminalNumber;
  }


  public void setTerminalNumber(String terminalNumber) {
    this.terminalNumber = terminalNumber;
  }


  public Terminal fiscalCountry(FiscalCountry fiscalCountry) {
    
    this.fiscalCountry = fiscalCountry;
    return this;
  }

   /**
   * Get fiscalCountry
   * @return fiscalCountry
  **/
  @javax.annotation.Nullable
  public FiscalCountry getFiscalCountry() {
    return fiscalCountry;
  }


  public void setFiscalCountry(FiscalCountry fiscalCountry) {
    this.fiscalCountry = fiscalCountry;
  }


  public Terminal caption(String caption) {
    
    this.caption = caption;
    return this;
  }

   /**
   * Gets or sets a possible caption for a terminal.
   * @return caption
  **/
  @javax.annotation.Nullable
  public String getCaption() {
    return caption;
  }


  public void setCaption(String caption) {
    this.caption = caption;
  }


  public Terminal platformType(PlatformType platformType) {
    
    this.platformType = platformType;
    return this;
  }

   /**
   * Get platformType
   * @return platformType
  **/
  @javax.annotation.Nullable
  public PlatformType getPlatformType() {
    return platformType;
  }


  public void setPlatformType(PlatformType platformType) {
    this.platformType = platformType;
  }


  public Terminal isTest(Boolean isTest) {
    
    this.isTest = isTest;
    return this;
  }

   /**
   * True if it is a test terminal.
   * @return isTest
  **/
  @javax.annotation.Nullable
  public Boolean getIsTest() {
    return isTest;
  }


  public void setIsTest(Boolean isTest) {
    this.isTest = isTest;
  }


  public Terminal archiveDate(OffsetDateTime archiveDate) {
    
    this.archiveDate = archiveDate;
    return this;
  }

   /**
   * The date when the terminal was archived (decommissioned).
   * @return archiveDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getArchiveDate() {
    return archiveDate;
  }


  public void setArchiveDate(OffsetDateTime archiveDate) {
    this.archiveDate = archiveDate;
  }


  public Terminal clientConfigurationId(UUID clientConfigurationId) {
    
    this.clientConfigurationId = clientConfigurationId;
    return this;
  }

   /**
   * The configuration for this terminal.
   * @return clientConfigurationId
  **/
  @javax.annotation.Nullable
  public UUID getClientConfigurationId() {
    return clientConfigurationId;
  }


  public void setClientConfigurationId(UUID clientConfigurationId) {
    this.clientConfigurationId = clientConfigurationId;
  }


  public Terminal newClientConfigurationId(UUID newClientConfigurationId) {
    
    this.newClientConfigurationId = newClientConfigurationId;
    return this;
  }

   /**
   * The new configuration for this terminal valid from RetailForce.Cloud.Model.Terminal.NewClientConfigurationValidFrom.
   * @return newClientConfigurationId
  **/
  @javax.annotation.Nullable
  public UUID getNewClientConfigurationId() {
    return newClientConfigurationId;
  }


  public void setNewClientConfigurationId(UUID newClientConfigurationId) {
    this.newClientConfigurationId = newClientConfigurationId;
  }


  public Terminal newClientConfigurationValidFrom(OffsetDateTime newClientConfigurationValidFrom) {
    
    this.newClientConfigurationValidFrom = newClientConfigurationValidFrom;
    return this;
  }

   /**
   * The validity date for the new configuration.
   * @return newClientConfigurationValidFrom
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getNewClientConfigurationValidFrom() {
    return newClientConfigurationValidFrom;
  }


  public void setNewClientConfigurationValidFrom(OffsetDateTime newClientConfigurationValidFrom) {
    this.newClientConfigurationValidFrom = newClientConfigurationValidFrom;
  }


  public Terminal globalShortId(String globalShortId) {
    
    this.globalShortId = globalShortId;
    return this;
  }

   /**
   * Represents a 4 digit (alphanumeric, case-sensitive) value representing a global unique short id for the terminal.
   * @return globalShortId
  **/
  @javax.annotation.Nullable
  public String getGlobalShortId() {
    return globalShortId;
  }


  public void setGlobalShortId(String globalShortId) {
    this.globalShortId = globalShortId;
  }


  public Terminal purchaseDate(OffsetDateTime purchaseDate) {
    
    this.purchaseDate = purchaseDate;
    return this;
  }

   /**
   * Purchase date
   * @return purchaseDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getPurchaseDate() {
    return purchaseDate;
  }


  public void setPurchaseDate(OffsetDateTime purchaseDate) {
    this.purchaseDate = purchaseDate;
  }


  public Terminal commissioningDate(OffsetDateTime commissioningDate) {
    
    this.commissioningDate = commissioningDate;
    return this;
  }

   /**
   * Commissioning date
   * @return commissioningDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getCommissioningDate() {
    return commissioningDate;
  }


  public void setCommissioningDate(OffsetDateTime commissioningDate) {
    this.commissioningDate = commissioningDate;
  }


  public Terminal decommissioningDate(OffsetDateTime decommissioningDate) {
    
    this.decommissioningDate = decommissioningDate;
    return this;
  }

   /**
   * Decommissioning date
   * @return decommissioningDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getDecommissioningDate() {
    return decommissioningDate;
  }


  public void setDecommissioningDate(OffsetDateTime decommissioningDate) {
    this.decommissioningDate = decommissioningDate;
  }


  public Terminal terminalType(TerminalType terminalType) {
    
    this.terminalType = terminalType;
    return this;
  }

   /**
   * Get terminalType
   * @return terminalType
  **/
  @javax.annotation.Nullable
  public TerminalType getTerminalType() {
    return terminalType;
  }


  public void setTerminalType(TerminalType terminalType) {
    this.terminalType = terminalType;
  }


  public Terminal updatedByPrincipalId(UUID updatedByPrincipalId) {
    
    this.updatedByPrincipalId = updatedByPrincipalId;
    return this;
  }

   /**
   * Updated by PrincipalId
   * @return updatedByPrincipalId
  **/
  @javax.annotation.Nullable
  public UUID getUpdatedByPrincipalId() {
    return updatedByPrincipalId;
  }


  public void setUpdatedByPrincipalId(UUID updatedByPrincipalId) {
    this.updatedByPrincipalId = updatedByPrincipalId;
  }


  public Terminal cashRegisterId(String cashRegisterId) {
    
    this.cashRegisterId = cashRegisterId;
    return this;
  }

   /**
   * The id of the cash register hardware.
   * @return cashRegisterId
  **/
  @javax.annotation.Nullable
  public String getCashRegisterId() {
    return cashRegisterId;
  }


  public void setCashRegisterId(String cashRegisterId) {
    this.cashRegisterId = cashRegisterId;
  }


  public Terminal cashRegisterBrand(String cashRegisterBrand) {
    
    this.cashRegisterBrand = cashRegisterBrand;
    return this;
  }

   /**
   * The brand of the cash register hardware.
   * @return cashRegisterBrand
  **/
  @javax.annotation.Nullable
  public String getCashRegisterBrand() {
    return cashRegisterBrand;
  }


  public void setCashRegisterBrand(String cashRegisterBrand) {
    this.cashRegisterBrand = cashRegisterBrand;
  }


  public Terminal cashRegisterModelname(String cashRegisterModelname) {
    
    this.cashRegisterModelname = cashRegisterModelname;
    return this;
  }

   /**
   * The model name of the cash register hardware.
   * @return cashRegisterModelname
  **/
  @javax.annotation.Nullable
  public String getCashRegisterModelname() {
    return cashRegisterModelname;
  }


  public void setCashRegisterModelname(String cashRegisterModelname) {
    this.cashRegisterModelname = cashRegisterModelname;
  }


  public Terminal cashRegisterSoftwareBrand(String cashRegisterSoftwareBrand) {
    
    this.cashRegisterSoftwareBrand = cashRegisterSoftwareBrand;
    return this;
  }

   /**
   * The software of the cash register.
   * @return cashRegisterSoftwareBrand
  **/
  @javax.annotation.Nullable
  public String getCashRegisterSoftwareBrand() {
    return cashRegisterSoftwareBrand;
  }


  public void setCashRegisterSoftwareBrand(String cashRegisterSoftwareBrand) {
    this.cashRegisterSoftwareBrand = cashRegisterSoftwareBrand;
  }


  public Terminal cashRegisterSoftwareVersion(String cashRegisterSoftwareVersion) {
    
    this.cashRegisterSoftwareVersion = cashRegisterSoftwareVersion;
    return this;
  }

   /**
   * The version of the software of the cash register.
   * @return cashRegisterSoftwareVersion
  **/
  @javax.annotation.Nullable
  public String getCashRegisterSoftwareVersion() {
    return cashRegisterSoftwareVersion;
  }


  public void setCashRegisterSoftwareVersion(String cashRegisterSoftwareVersion) {
    this.cashRegisterSoftwareVersion = cashRegisterSoftwareVersion;
  }


  public Terminal cashRegisterSoftwareCompany(String cashRegisterSoftwareCompany) {
    
    this.cashRegisterSoftwareCompany = cashRegisterSoftwareCompany;
    return this;
  }

   /**
   * The company name of the creator of the cash register software.
   * @return cashRegisterSoftwareCompany
  **/
  @javax.annotation.Nullable
  public String getCashRegisterSoftwareCompany() {
    return cashRegisterSoftwareCompany;
  }


  public void setCashRegisterSoftwareCompany(String cashRegisterSoftwareCompany) {
    this.cashRegisterSoftwareCompany = cashRegisterSoftwareCompany;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Terminal terminal = (Terminal) o;
    return Objects.equals(this.terminalId, terminal.terminalId) &&
        Objects.equals(this.storeId, terminal.storeId) &&
        Objects.equals(this.terminalNumber, terminal.terminalNumber) &&
        Objects.equals(this.fiscalCountry, terminal.fiscalCountry) &&
        Objects.equals(this.caption, terminal.caption) &&
        Objects.equals(this.platformType, terminal.platformType) &&
        Objects.equals(this.isTest, terminal.isTest) &&
        Objects.equals(this.archiveDate, terminal.archiveDate) &&
        Objects.equals(this.clientConfigurationId, terminal.clientConfigurationId) &&
        Objects.equals(this.newClientConfigurationId, terminal.newClientConfigurationId) &&
        Objects.equals(this.newClientConfigurationValidFrom, terminal.newClientConfigurationValidFrom) &&
        Objects.equals(this.globalShortId, terminal.globalShortId) &&
        Objects.equals(this.purchaseDate, terminal.purchaseDate) &&
        Objects.equals(this.commissioningDate, terminal.commissioningDate) &&
        Objects.equals(this.decommissioningDate, terminal.decommissioningDate) &&
        Objects.equals(this.terminalType, terminal.terminalType) &&
        Objects.equals(this.updatedByPrincipalId, terminal.updatedByPrincipalId) &&
        Objects.equals(this.cashRegisterId, terminal.cashRegisterId) &&
        Objects.equals(this.cashRegisterBrand, terminal.cashRegisterBrand) &&
        Objects.equals(this.cashRegisterModelname, terminal.cashRegisterModelname) &&
        Objects.equals(this.cashRegisterSoftwareBrand, terminal.cashRegisterSoftwareBrand) &&
        Objects.equals(this.cashRegisterSoftwareVersion, terminal.cashRegisterSoftwareVersion) &&
        Objects.equals(this.cashRegisterSoftwareCompany, terminal.cashRegisterSoftwareCompany);
  }

  @Override
  public int hashCode() {
    return Objects.hash(terminalId, storeId, terminalNumber, fiscalCountry, caption, platformType, isTest, archiveDate, clientConfigurationId, newClientConfigurationId, newClientConfigurationValidFrom, globalShortId, purchaseDate, commissioningDate, decommissioningDate, terminalType, updatedByPrincipalId, cashRegisterId, cashRegisterBrand, cashRegisterModelname, cashRegisterSoftwareBrand, cashRegisterSoftwareVersion, cashRegisterSoftwareCompany);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Terminal {\n");
    sb.append("    terminalId: ").append(toIndentedString(terminalId)).append("\n");
    sb.append("    storeId: ").append(toIndentedString(storeId)).append("\n");
    sb.append("    terminalNumber: ").append(toIndentedString(terminalNumber)).append("\n");
    sb.append("    fiscalCountry: ").append(toIndentedString(fiscalCountry)).append("\n");
    sb.append("    caption: ").append(toIndentedString(caption)).append("\n");
    sb.append("    platformType: ").append(toIndentedString(platformType)).append("\n");
    sb.append("    isTest: ").append(toIndentedString(isTest)).append("\n");
    sb.append("    archiveDate: ").append(toIndentedString(archiveDate)).append("\n");
    sb.append("    clientConfigurationId: ").append(toIndentedString(clientConfigurationId)).append("\n");
    sb.append("    newClientConfigurationId: ").append(toIndentedString(newClientConfigurationId)).append("\n");
    sb.append("    newClientConfigurationValidFrom: ").append(toIndentedString(newClientConfigurationValidFrom)).append("\n");
    sb.append("    globalShortId: ").append(toIndentedString(globalShortId)).append("\n");
    sb.append("    purchaseDate: ").append(toIndentedString(purchaseDate)).append("\n");
    sb.append("    commissioningDate: ").append(toIndentedString(commissioningDate)).append("\n");
    sb.append("    decommissioningDate: ").append(toIndentedString(decommissioningDate)).append("\n");
    sb.append("    terminalType: ").append(toIndentedString(terminalType)).append("\n");
    sb.append("    updatedByPrincipalId: ").append(toIndentedString(updatedByPrincipalId)).append("\n");
    sb.append("    cashRegisterId: ").append(toIndentedString(cashRegisterId)).append("\n");
    sb.append("    cashRegisterBrand: ").append(toIndentedString(cashRegisterBrand)).append("\n");
    sb.append("    cashRegisterModelname: ").append(toIndentedString(cashRegisterModelname)).append("\n");
    sb.append("    cashRegisterSoftwareBrand: ").append(toIndentedString(cashRegisterSoftwareBrand)).append("\n");
    sb.append("    cashRegisterSoftwareVersion: ").append(toIndentedString(cashRegisterSoftwareVersion)).append("\n");
    sb.append("    cashRegisterSoftwareCompany: ").append(toIndentedString(cashRegisterSoftwareCompany)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("terminalId");
    openapiFields.add("storeId");
    openapiFields.add("terminalNumber");
    openapiFields.add("fiscalCountry");
    openapiFields.add("caption");
    openapiFields.add("platformType");
    openapiFields.add("isTest");
    openapiFields.add("archiveDate");
    openapiFields.add("clientConfigurationId");
    openapiFields.add("newClientConfigurationId");
    openapiFields.add("newClientConfigurationValidFrom");
    openapiFields.add("globalShortId");
    openapiFields.add("purchaseDate");
    openapiFields.add("commissioningDate");
    openapiFields.add("decommissioningDate");
    openapiFields.add("terminalType");
    openapiFields.add("updatedByPrincipalId");
    openapiFields.add("cashRegisterId");
    openapiFields.add("cashRegisterBrand");
    openapiFields.add("cashRegisterModelname");
    openapiFields.add("cashRegisterSoftwareBrand");
    openapiFields.add("cashRegisterSoftwareVersion");
    openapiFields.add("cashRegisterSoftwareCompany");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to Terminal
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!Terminal.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in Terminal is not found in the empty JSON string", Terminal.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!Terminal.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `Terminal` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("terminalId") != null && !jsonObj.get("terminalId").isJsonNull()) && !jsonObj.get("terminalId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `terminalId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("terminalId").toString()));
      }
      if ((jsonObj.get("storeId") != null && !jsonObj.get("storeId").isJsonNull()) && !jsonObj.get("storeId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeId").toString()));
      }
      if ((jsonObj.get("terminalNumber") != null && !jsonObj.get("terminalNumber").isJsonNull()) && !jsonObj.get("terminalNumber").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `terminalNumber` to be a primitive type in the JSON string but got `%s`", jsonObj.get("terminalNumber").toString()));
      }
      if ((jsonObj.get("caption") != null && !jsonObj.get("caption").isJsonNull()) && !jsonObj.get("caption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption").toString()));
      }
      if ((jsonObj.get("clientConfigurationId") != null && !jsonObj.get("clientConfigurationId").isJsonNull()) && !jsonObj.get("clientConfigurationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `clientConfigurationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("clientConfigurationId").toString()));
      }
      if ((jsonObj.get("newClientConfigurationId") != null && !jsonObj.get("newClientConfigurationId").isJsonNull()) && !jsonObj.get("newClientConfigurationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `newClientConfigurationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("newClientConfigurationId").toString()));
      }
      if ((jsonObj.get("globalShortId") != null && !jsonObj.get("globalShortId").isJsonNull()) && !jsonObj.get("globalShortId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `globalShortId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("globalShortId").toString()));
      }
      if ((jsonObj.get("updatedByPrincipalId") != null && !jsonObj.get("updatedByPrincipalId").isJsonNull()) && !jsonObj.get("updatedByPrincipalId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `updatedByPrincipalId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("updatedByPrincipalId").toString()));
      }
      if ((jsonObj.get("cashRegisterId") != null && !jsonObj.get("cashRegisterId").isJsonNull()) && !jsonObj.get("cashRegisterId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterId").toString()));
      }
      if ((jsonObj.get("cashRegisterBrand") != null && !jsonObj.get("cashRegisterBrand").isJsonNull()) && !jsonObj.get("cashRegisterBrand").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterBrand` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterBrand").toString()));
      }
      if ((jsonObj.get("cashRegisterModelname") != null && !jsonObj.get("cashRegisterModelname").isJsonNull()) && !jsonObj.get("cashRegisterModelname").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterModelname` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterModelname").toString()));
      }
      if ((jsonObj.get("cashRegisterSoftwareBrand") != null && !jsonObj.get("cashRegisterSoftwareBrand").isJsonNull()) && !jsonObj.get("cashRegisterSoftwareBrand").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterSoftwareBrand` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterSoftwareBrand").toString()));
      }
      if ((jsonObj.get("cashRegisterSoftwareVersion") != null && !jsonObj.get("cashRegisterSoftwareVersion").isJsonNull()) && !jsonObj.get("cashRegisterSoftwareVersion").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterSoftwareVersion` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterSoftwareVersion").toString()));
      }
      if ((jsonObj.get("cashRegisterSoftwareCompany") != null && !jsonObj.get("cashRegisterSoftwareCompany").isJsonNull()) && !jsonObj.get("cashRegisterSoftwareCompany").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `cashRegisterSoftwareCompany` to be a primitive type in the JSON string but got `%s`", jsonObj.get("cashRegisterSoftwareCompany").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!Terminal.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'Terminal' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<Terminal> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(Terminal.class));

       return (TypeAdapter<T>) new TypeAdapter<Terminal>() {
           @Override
           public void write(JsonWriter out, Terminal value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public Terminal read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of Terminal given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of Terminal
  * @throws IOException if the JSON string is invalid with respect to Terminal
  */
  public static Terminal fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, Terminal.class);
  }

 /**
  * Convert an instance of Terminal to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

