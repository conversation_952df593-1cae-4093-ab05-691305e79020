/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.FonConnectionLogMessagePageResultModel;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.SignatureDevice;
import com.untill.retailforce.model.StringSimpleObject;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ImplementationControllerAustriaApi
 */
@Disabled
public class ImplementationControllerAustriaApiTest {

    private final ImplementationControllerAustriaApi api = new ImplementationControllerAustriaApi();

    /**
     * Returns all missing annual year receipts (annual year receipt check).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGetTest() throws ApiException {
        UUID organizationId = null;
        List<StringSimpleObject> response = api.apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet(organizationId);
        // TODO: test validations
    }

    /**
     * Returns the signature devices for the given entity.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationAtSignatureDeviceEntityIdGetTest() throws ApiException {
        UUID entityId = null;
        List<SignatureDevice> response = api.apiV10ImplementationAtSignatureDeviceEntityIdGet(entityId);
        // TODO: test validations
    }

    /**
     * Returns the asit crypto container for asit check (as json string).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationAtTerminalIdAsitCryptoContainerGetTest() throws ApiException {
        UUID terminalId = null;
        String response = api.apiV10ImplementationAtTerminalIdAsitCryptoContainerGet(terminalId);
        // TODO: test validations
    }

    /**
     * Exports all data requested for dep131 protocol out of stored data.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationAtTerminalIdDep131GetTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime tillDate = null;
        api.apiV10ImplementationAtTerminalIdDep131Get(terminalId, fromDate, tillDate);
        // TODO: test validations
    }

    /**
     * Exports austrian dep from cloud archive.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationAtTerminalIdDepGetTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime tillDate = null;
        api.apiV10ImplementationAtTerminalIdDepGet(terminalId, fromDate, tillDate);
        // TODO: test validations
    }

    /**
     * Returns the pages fon connection log for the requested client.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationAtTerminalIdFonGetTest() throws ApiException {
        UUID terminalId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        FonConnectionLogMessagePageResultModel response = api.apiV10ImplementationAtTerminalIdFonGet(terminalId, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Creates an automatic year receipt and validates this at fon.
     *
     * In order to use this function it is necessary to have online connection to the terminal (signalr) or second dep validation channel (see documentation) is set.  The automatic year receipt will not be created if cloud store is not in sync with client dep store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationAtTerminalIdYearReceiptPostTest() throws ApiException {
        UUID terminalId = null;
        Integer year = null;
        String response = api.apiV10ImplementationAtTerminalIdYearReceiptPost(terminalId, year);
        // TODO: test validations
    }

}
