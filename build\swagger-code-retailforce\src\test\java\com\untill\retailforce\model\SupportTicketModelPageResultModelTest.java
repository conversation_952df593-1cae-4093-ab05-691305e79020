/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.SupportTicketModel;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for SupportTicketModelPageResultModel
 */
public class SupportTicketModelPageResultModelTest {
    private final SupportTicketModelPageResultModel model = new SupportTicketModelPageResultModel();

    /**
     * Model tests for SupportTicketModelPageResultModel
     */
    @Test
    public void testSupportTicketModelPageResultModel() {
        // TODO: test SupportTicketModelPageResultModel
    }

    /**
     * Test the property 'pageSize'
     */
    @Test
    public void pageSizeTest() {
        // TODO: test pageSize
    }

    /**
     * Test the property 'pageOffset'
     */
    @Test
    public void pageOffsetTest() {
        // TODO: test pageOffset
    }

    /**
     * Test the property 'count'
     */
    @Test
    public void countTest() {
        // TODO: test count
    }

    /**
     * Test the property 'items'
     */
    @Test
    public void itemsTest() {
        // TODO: test items
    }

}
