

# Principal

Represents user information for the retail force cloud.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**principalId** | **UUID** | The id of the principal. |  [optional] |
|**caption** | **String** | The caption of the principal. |  [optional] |
|**name1** | **String** | The first name of the principal. |  [optional] |
|**name2** | **String** | The last (second) name of the principal. |  [optional] |
|**telephone** | **String** | Gets or sets the telephon nummber of the principal. |  [optional] |
|**mobile** | **String** | Gets or sets the mobile number of the principal. |  [optional] |
|**eMail** | **String** | Gets or sets the email of the principal. |  [optional] |



