/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Represents user information for the retail force cloud.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class Principal {
  public static final String SERIALIZED_NAME_PRINCIPAL_ID = "principalId";
  @SerializedName(SERIALIZED_NAME_PRINCIPAL_ID)
  private UUID principalId;

  public static final String SERIALIZED_NAME_CAPTION = "caption";
  @SerializedName(SERIALIZED_NAME_CAPTION)
  private String caption;

  public static final String SERIALIZED_NAME_NAME1 = "name1";
  @SerializedName(SERIALIZED_NAME_NAME1)
  private String name1;

  public static final String SERIALIZED_NAME_NAME2 = "name2";
  @SerializedName(SERIALIZED_NAME_NAME2)
  private String name2;

  public static final String SERIALIZED_NAME_TELEPHONE = "telephone";
  @SerializedName(SERIALIZED_NAME_TELEPHONE)
  private String telephone;

  public static final String SERIALIZED_NAME_MOBILE = "mobile";
  @SerializedName(SERIALIZED_NAME_MOBILE)
  private String mobile;

  public static final String SERIALIZED_NAME_E_MAIL = "eMail";
  @SerializedName(SERIALIZED_NAME_E_MAIL)
  private String eMail;

  public Principal() {
  }

  public Principal principalId(UUID principalId) {
    
    this.principalId = principalId;
    return this;
  }

   /**
   * The id of the principal.
   * @return principalId
  **/
  @javax.annotation.Nullable
  public UUID getPrincipalId() {
    return principalId;
  }


  public void setPrincipalId(UUID principalId) {
    this.principalId = principalId;
  }


  public Principal caption(String caption) {
    
    this.caption = caption;
    return this;
  }

   /**
   * The caption of the principal.
   * @return caption
  **/
  @javax.annotation.Nullable
  public String getCaption() {
    return caption;
  }


  public void setCaption(String caption) {
    this.caption = caption;
  }


  public Principal name1(String name1) {
    
    this.name1 = name1;
    return this;
  }

   /**
   * The first name of the principal.
   * @return name1
  **/
  @javax.annotation.Nullable
  public String getName1() {
    return name1;
  }


  public void setName1(String name1) {
    this.name1 = name1;
  }


  public Principal name2(String name2) {
    
    this.name2 = name2;
    return this;
  }

   /**
   * The last (second) name of the principal.
   * @return name2
  **/
  @javax.annotation.Nullable
  public String getName2() {
    return name2;
  }


  public void setName2(String name2) {
    this.name2 = name2;
  }


  public Principal telephone(String telephone) {
    
    this.telephone = telephone;
    return this;
  }

   /**
   * Gets or sets the telephon nummber of the principal.
   * @return telephone
  **/
  @javax.annotation.Nullable
  public String getTelephone() {
    return telephone;
  }


  public void setTelephone(String telephone) {
    this.telephone = telephone;
  }


  public Principal mobile(String mobile) {
    
    this.mobile = mobile;
    return this;
  }

   /**
   * Gets or sets the mobile number of the principal.
   * @return mobile
  **/
  @javax.annotation.Nullable
  public String getMobile() {
    return mobile;
  }


  public void setMobile(String mobile) {
    this.mobile = mobile;
  }


  public Principal eMail(String eMail) {
    
    this.eMail = eMail;
    return this;
  }

   /**
   * Gets or sets the email of the principal.
   * @return eMail
  **/
  @javax.annotation.Nullable
  public String geteMail() {
    return eMail;
  }


  public void seteMail(String eMail) {
    this.eMail = eMail;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Principal principal = (Principal) o;
    return Objects.equals(this.principalId, principal.principalId) &&
        Objects.equals(this.caption, principal.caption) &&
        Objects.equals(this.name1, principal.name1) &&
        Objects.equals(this.name2, principal.name2) &&
        Objects.equals(this.telephone, principal.telephone) &&
        Objects.equals(this.mobile, principal.mobile) &&
        Objects.equals(this.eMail, principal.eMail);
  }

  @Override
  public int hashCode() {
    return Objects.hash(principalId, caption, name1, name2, telephone, mobile, eMail);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Principal {\n");
    sb.append("    principalId: ").append(toIndentedString(principalId)).append("\n");
    sb.append("    caption: ").append(toIndentedString(caption)).append("\n");
    sb.append("    name1: ").append(toIndentedString(name1)).append("\n");
    sb.append("    name2: ").append(toIndentedString(name2)).append("\n");
    sb.append("    telephone: ").append(toIndentedString(telephone)).append("\n");
    sb.append("    mobile: ").append(toIndentedString(mobile)).append("\n");
    sb.append("    eMail: ").append(toIndentedString(eMail)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("principalId");
    openapiFields.add("caption");
    openapiFields.add("name1");
    openapiFields.add("name2");
    openapiFields.add("telephone");
    openapiFields.add("mobile");
    openapiFields.add("eMail");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to Principal
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!Principal.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in Principal is not found in the empty JSON string", Principal.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!Principal.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `Principal` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("principalId") != null && !jsonObj.get("principalId").isJsonNull()) && !jsonObj.get("principalId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `principalId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("principalId").toString()));
      }
      if ((jsonObj.get("caption") != null && !jsonObj.get("caption").isJsonNull()) && !jsonObj.get("caption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption").toString()));
      }
      if ((jsonObj.get("name1") != null && !jsonObj.get("name1").isJsonNull()) && !jsonObj.get("name1").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `name1` to be a primitive type in the JSON string but got `%s`", jsonObj.get("name1").toString()));
      }
      if ((jsonObj.get("name2") != null && !jsonObj.get("name2").isJsonNull()) && !jsonObj.get("name2").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `name2` to be a primitive type in the JSON string but got `%s`", jsonObj.get("name2").toString()));
      }
      if ((jsonObj.get("telephone") != null && !jsonObj.get("telephone").isJsonNull()) && !jsonObj.get("telephone").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `telephone` to be a primitive type in the JSON string but got `%s`", jsonObj.get("telephone").toString()));
      }
      if ((jsonObj.get("mobile") != null && !jsonObj.get("mobile").isJsonNull()) && !jsonObj.get("mobile").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `mobile` to be a primitive type in the JSON string but got `%s`", jsonObj.get("mobile").toString()));
      }
      if ((jsonObj.get("eMail") != null && !jsonObj.get("eMail").isJsonNull()) && !jsonObj.get("eMail").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `eMail` to be a primitive type in the JSON string but got `%s`", jsonObj.get("eMail").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!Principal.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'Principal' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<Principal> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(Principal.class));

       return (TypeAdapter<T>) new TypeAdapter<Principal>() {
           @Override
           public void write(JsonWriter out, Principal value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public Principal read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of Principal given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of Principal
  * @throws IOException if the JSON string is invalid with respect to Principal
  */
  public static Principal fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, Principal.class);
  }

 /**
  * Convert an instance of Principal to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

