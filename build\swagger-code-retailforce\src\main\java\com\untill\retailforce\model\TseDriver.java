/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.annotations.SerializedName;

import java.io.IOException;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

/**
 * Gets or Sets TseDriver
 */
@JsonAdapter(TseDriver.Adapter.class)
public enum TseDriver {
  
  _0_FISKALY("[0] = Fiskaly"),
  
  _1_SWISSBIT("[1] = Swissbit"),
  
  _2_SWISSBIT_CLOUD("[2] = Swissbit Cloud"),
  
  _3_SWISSBIT_CLOUD_2_0("[3] = Swissbit Cloud 2.0"),
  
  _1000_TESTTSE("[1000] = TestTse");

  private String value;

  TseDriver(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  public static TseDriver fromValue(String value) {
    for (TseDriver b : TseDriver.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }

  public static class Adapter extends TypeAdapter<TseDriver> {
    @Override
    public void write(final JsonWriter jsonWriter, final TseDriver enumeration) throws IOException {
      jsonWriter.value(enumeration.getValue());
    }

    @Override
    public TseDriver read(final JsonReader jsonReader) throws IOException {
      String value = jsonReader.nextString();
      return TseDriver.fromValue(value);
    }
  }
}

