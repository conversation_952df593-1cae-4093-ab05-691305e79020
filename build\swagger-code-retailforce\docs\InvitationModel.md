

# InvitationModel

Represents the view model for an inviation.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**invitationId** | **UUID** | The id of the invitation. |  [optional] |
|**invitationDate** | **OffsetDateTime** | The date of the invitation. |  [optional] |
|**validTill** | **OffsetDateTime** | The date until the invitation is valid. |  [optional] |
|**entityId** | **UUID** | The entity for which the invitation was sent. Possible types: Organisation/Distributor. |  [optional] |
|**entityName** | **String** | The name of the entity to display. |  [optional] |
|**entityType** | **String** | The type of the entity. Can be Organisation or Distributor. |  [optional] |
|**eMail** | **String** | The email of the invited person. |  [optional] |
|**inviterPrincipalId** | **UUID** | The principalId of the inviter. |  [optional] |



