/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.SupportTicketStatus;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * TimelogOverviewModel
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TimelogOverviewModel {
  public static final String SERIALIZED_NAME_ID = "id";
  @SerializedName(SERIALIZED_NAME_ID)
  private UUID id;

  public static final String SERIALIZED_NAME_USERNAME = "username";
  @SerializedName(SERIALIZED_NAME_USERNAME)
  private String username;

  public static final String SERIALIZED_NAME_EMAIL = "email";
  @SerializedName(SERIALIZED_NAME_EMAIL)
  private String email;

  public static final String SERIALIZED_NAME_CATEGORY_ID = "categoryId";
  @SerializedName(SERIALIZED_NAME_CATEGORY_ID)
  private String categoryId;

  public static final String SERIALIZED_NAME_ACTIVITY_TEXT = "activityText";
  @SerializedName(SERIALIZED_NAME_ACTIVITY_TEXT)
  private String activityText;

  public static final String SERIALIZED_NAME_FROM_DATE = "fromDate";
  @SerializedName(SERIALIZED_NAME_FROM_DATE)
  private OffsetDateTime fromDate;

  public static final String SERIALIZED_NAME_TILL_DATE = "tillDate";
  @SerializedName(SERIALIZED_NAME_TILL_DATE)
  private OffsetDateTime tillDate;

  public static final String SERIALIZED_NAME_IS_HOMEOFFICE = "isHomeoffice";
  @SerializedName(SERIALIZED_NAME_IS_HOMEOFFICE)
  private Boolean isHomeoffice;

  public static final String SERIALIZED_NAME_TICKET_NR = "ticketNr";
  @SerializedName(SERIALIZED_NAME_TICKET_NR)
  private String ticketNr;

  public static final String SERIALIZED_NAME_TICKET_CAPTION = "ticketCaption";
  @SerializedName(SERIALIZED_NAME_TICKET_CAPTION)
  private String ticketCaption;

  public static final String SERIALIZED_NAME_TICKET_STATUS = "ticketStatus";
  @SerializedName(SERIALIZED_NAME_TICKET_STATUS)
  private SupportTicketStatus ticketStatus;

  public static final String SERIALIZED_NAME_TICKET_URL = "ticketUrl";
  @SerializedName(SERIALIZED_NAME_TICKET_URL)
  private String ticketUrl;

  public static final String SERIALIZED_NAME_IS_REPLACED = "isReplaced";
  @SerializedName(SERIALIZED_NAME_IS_REPLACED)
  private Boolean isReplaced;

  public TimelogOverviewModel() {
  }

  public TimelogOverviewModel id(UUID id) {
    
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @javax.annotation.Nullable
  public UUID getId() {
    return id;
  }


  public void setId(UUID id) {
    this.id = id;
  }


  public TimelogOverviewModel username(String username) {
    
    this.username = username;
    return this;
  }

   /**
   * Get username
   * @return username
  **/
  @javax.annotation.Nullable
  public String getUsername() {
    return username;
  }


  public void setUsername(String username) {
    this.username = username;
  }


  public TimelogOverviewModel email(String email) {
    
    this.email = email;
    return this;
  }

   /**
   * Email is only set for retailforce users
   * @return email
  **/
  @javax.annotation.Nullable
  public String getEmail() {
    return email;
  }


  public void setEmail(String email) {
    this.email = email;
  }


  public TimelogOverviewModel categoryId(String categoryId) {
    
    this.categoryId = categoryId;
    return this;
  }

   /**
   * Get categoryId
   * @return categoryId
  **/
  @javax.annotation.Nullable
  public String getCategoryId() {
    return categoryId;
  }


  public void setCategoryId(String categoryId) {
    this.categoryId = categoryId;
  }


  public TimelogOverviewModel activityText(String activityText) {
    
    this.activityText = activityText;
    return this;
  }

   /**
   * Get activityText
   * @return activityText
  **/
  @javax.annotation.Nullable
  public String getActivityText() {
    return activityText;
  }


  public void setActivityText(String activityText) {
    this.activityText = activityText;
  }


  public TimelogOverviewModel fromDate(OffsetDateTime fromDate) {
    
    this.fromDate = fromDate;
    return this;
  }

   /**
   * Get fromDate
   * @return fromDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getFromDate() {
    return fromDate;
  }


  public void setFromDate(OffsetDateTime fromDate) {
    this.fromDate = fromDate;
  }


  public TimelogOverviewModel tillDate(OffsetDateTime tillDate) {
    
    this.tillDate = tillDate;
    return this;
  }

   /**
   * Get tillDate
   * @return tillDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getTillDate() {
    return tillDate;
  }


  public void setTillDate(OffsetDateTime tillDate) {
    this.tillDate = tillDate;
  }


  public TimelogOverviewModel isHomeoffice(Boolean isHomeoffice) {
    
    this.isHomeoffice = isHomeoffice;
    return this;
  }

   /**
   * Get isHomeoffice
   * @return isHomeoffice
  **/
  @javax.annotation.Nullable
  public Boolean getIsHomeoffice() {
    return isHomeoffice;
  }


  public void setIsHomeoffice(Boolean isHomeoffice) {
    this.isHomeoffice = isHomeoffice;
  }


  public TimelogOverviewModel ticketNr(String ticketNr) {
    
    this.ticketNr = ticketNr;
    return this;
  }

   /**
   * Get ticketNr
   * @return ticketNr
  **/
  @javax.annotation.Nullable
  public String getTicketNr() {
    return ticketNr;
  }


  public void setTicketNr(String ticketNr) {
    this.ticketNr = ticketNr;
  }


  public TimelogOverviewModel ticketCaption(String ticketCaption) {
    
    this.ticketCaption = ticketCaption;
    return this;
  }

   /**
   * Get ticketCaption
   * @return ticketCaption
  **/
  @javax.annotation.Nullable
  public String getTicketCaption() {
    return ticketCaption;
  }


  public void setTicketCaption(String ticketCaption) {
    this.ticketCaption = ticketCaption;
  }


  public TimelogOverviewModel ticketStatus(SupportTicketStatus ticketStatus) {
    
    this.ticketStatus = ticketStatus;
    return this;
  }

   /**
   * Get ticketStatus
   * @return ticketStatus
  **/
  @javax.annotation.Nullable
  public SupportTicketStatus getTicketStatus() {
    return ticketStatus;
  }


  public void setTicketStatus(SupportTicketStatus ticketStatus) {
    this.ticketStatus = ticketStatus;
  }


  public TimelogOverviewModel ticketUrl(String ticketUrl) {
    
    this.ticketUrl = ticketUrl;
    return this;
  }

   /**
   * Only set for support tickets
   * @return ticketUrl
  **/
  @javax.annotation.Nullable
  public String getTicketUrl() {
    return ticketUrl;
  }


  public void setTicketUrl(String ticketUrl) {
    this.ticketUrl = ticketUrl;
  }


  public TimelogOverviewModel isReplaced(Boolean isReplaced) {
    
    this.isReplaced = isReplaced;
    return this;
  }

   /**
   * If replaced (replacement ticket id is set &#x3D;&gt; support ticket is booked to onboarding)
   * @return isReplaced
  **/
  @javax.annotation.Nullable
  public Boolean getIsReplaced() {
    return isReplaced;
  }


  public void setIsReplaced(Boolean isReplaced) {
    this.isReplaced = isReplaced;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TimelogOverviewModel timelogOverviewModel = (TimelogOverviewModel) o;
    return Objects.equals(this.id, timelogOverviewModel.id) &&
        Objects.equals(this.username, timelogOverviewModel.username) &&
        Objects.equals(this.email, timelogOverviewModel.email) &&
        Objects.equals(this.categoryId, timelogOverviewModel.categoryId) &&
        Objects.equals(this.activityText, timelogOverviewModel.activityText) &&
        Objects.equals(this.fromDate, timelogOverviewModel.fromDate) &&
        Objects.equals(this.tillDate, timelogOverviewModel.tillDate) &&
        Objects.equals(this.isHomeoffice, timelogOverviewModel.isHomeoffice) &&
        Objects.equals(this.ticketNr, timelogOverviewModel.ticketNr) &&
        Objects.equals(this.ticketCaption, timelogOverviewModel.ticketCaption) &&
        Objects.equals(this.ticketStatus, timelogOverviewModel.ticketStatus) &&
        Objects.equals(this.ticketUrl, timelogOverviewModel.ticketUrl) &&
        Objects.equals(this.isReplaced, timelogOverviewModel.isReplaced);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, username, email, categoryId, activityText, fromDate, tillDate, isHomeoffice, ticketNr, ticketCaption, ticketStatus, ticketUrl, isReplaced);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TimelogOverviewModel {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    categoryId: ").append(toIndentedString(categoryId)).append("\n");
    sb.append("    activityText: ").append(toIndentedString(activityText)).append("\n");
    sb.append("    fromDate: ").append(toIndentedString(fromDate)).append("\n");
    sb.append("    tillDate: ").append(toIndentedString(tillDate)).append("\n");
    sb.append("    isHomeoffice: ").append(toIndentedString(isHomeoffice)).append("\n");
    sb.append("    ticketNr: ").append(toIndentedString(ticketNr)).append("\n");
    sb.append("    ticketCaption: ").append(toIndentedString(ticketCaption)).append("\n");
    sb.append("    ticketStatus: ").append(toIndentedString(ticketStatus)).append("\n");
    sb.append("    ticketUrl: ").append(toIndentedString(ticketUrl)).append("\n");
    sb.append("    isReplaced: ").append(toIndentedString(isReplaced)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("id");
    openapiFields.add("username");
    openapiFields.add("email");
    openapiFields.add("categoryId");
    openapiFields.add("activityText");
    openapiFields.add("fromDate");
    openapiFields.add("tillDate");
    openapiFields.add("isHomeoffice");
    openapiFields.add("ticketNr");
    openapiFields.add("ticketCaption");
    openapiFields.add("ticketStatus");
    openapiFields.add("ticketUrl");
    openapiFields.add("isReplaced");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TimelogOverviewModel
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TimelogOverviewModel.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TimelogOverviewModel is not found in the empty JSON string", TimelogOverviewModel.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TimelogOverviewModel.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TimelogOverviewModel` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("id") != null && !jsonObj.get("id").isJsonNull()) && !jsonObj.get("id").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `id` to be a primitive type in the JSON string but got `%s`", jsonObj.get("id").toString()));
      }
      if ((jsonObj.get("username") != null && !jsonObj.get("username").isJsonNull()) && !jsonObj.get("username").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `username` to be a primitive type in the JSON string but got `%s`", jsonObj.get("username").toString()));
      }
      if ((jsonObj.get("email") != null && !jsonObj.get("email").isJsonNull()) && !jsonObj.get("email").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `email` to be a primitive type in the JSON string but got `%s`", jsonObj.get("email").toString()));
      }
      if ((jsonObj.get("categoryId") != null && !jsonObj.get("categoryId").isJsonNull()) && !jsonObj.get("categoryId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `categoryId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("categoryId").toString()));
      }
      if ((jsonObj.get("activityText") != null && !jsonObj.get("activityText").isJsonNull()) && !jsonObj.get("activityText").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `activityText` to be a primitive type in the JSON string but got `%s`", jsonObj.get("activityText").toString()));
      }
      if ((jsonObj.get("ticketNr") != null && !jsonObj.get("ticketNr").isJsonNull()) && !jsonObj.get("ticketNr").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `ticketNr` to be a primitive type in the JSON string but got `%s`", jsonObj.get("ticketNr").toString()));
      }
      if ((jsonObj.get("ticketCaption") != null && !jsonObj.get("ticketCaption").isJsonNull()) && !jsonObj.get("ticketCaption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `ticketCaption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("ticketCaption").toString()));
      }
      if ((jsonObj.get("ticketUrl") != null && !jsonObj.get("ticketUrl").isJsonNull()) && !jsonObj.get("ticketUrl").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `ticketUrl` to be a primitive type in the JSON string but got `%s`", jsonObj.get("ticketUrl").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TimelogOverviewModel.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TimelogOverviewModel' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TimelogOverviewModel> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TimelogOverviewModel.class));

       return (TypeAdapter<T>) new TypeAdapter<TimelogOverviewModel>() {
           @Override
           public void write(JsonWriter out, TimelogOverviewModel value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TimelogOverviewModel read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TimelogOverviewModel given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TimelogOverviewModel
  * @throws IOException if the JSON string is invalid with respect to TimelogOverviewModel
  */
  public static TimelogOverviewModel fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TimelogOverviewModel.class);
  }

 /**
  * Convert an instance of TimelogOverviewModel to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

