/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.DashboardTerminalDetailPageResultModel;
import com.untill.retailforce.model.DashboardTerminalOverviewPageResultModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class DashboardApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public DashboardApi() {
        this(Configuration.getDefaultApiClient());
    }

    public DashboardApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet
     * @param organizationId The organization of the terminals where the annual year check should be shown. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetCall(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/dashboard/terminal/annualYearCheck/{organizationId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (includeTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("includeTest", includeTest));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetValidateBeforeCall(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet(Async)");
        }

        return apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetCall(organizationId, includeTest, pageOffset, pageSize, _callback);

    }

    /**
     * Returns an overview of all annual year checks for all active terminals (archived terminals will not be returned).
     * If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.
     * @param organizationId The organization of the terminals where the annual year check should be shown. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return DashboardTerminalOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DashboardTerminalOverviewPageResultModel apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<DashboardTerminalOverviewPageResultModel> localVarResp = apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetWithHttpInfo(organizationId, includeTest, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Returns an overview of all annual year checks for all active terminals (archived terminals will not be returned).
     * If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.
     * @param organizationId The organization of the terminals where the annual year check should be shown. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return ApiResponse&lt;DashboardTerminalOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DashboardTerminalOverviewPageResultModel> apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetWithHttpInfo(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetValidateBeforeCall(organizationId, includeTest, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<DashboardTerminalOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns an overview of all annual year checks for all active terminals (archived terminals will not be returned). (asynchronously)
     * If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.
     * @param organizationId The organization of the terminals where the annual year check should be shown. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetAsync(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize, final ApiCallback<DashboardTerminalOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetValidateBeforeCall(organizationId, includeTest, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<DashboardTerminalOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet
     * @param organizationId The organization of the terminals where the annual year check should be shown. (required)
     * @param year The requested year. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString Search for terminal or store number (or caption). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetCall(UUID organizationId, String year, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/dashboard/terminal/annualYearCheck/{organizationId}/{year}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()))
            .replace("{" + "year" + "}", localVarApiClient.escapeString(year.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (includeTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("includeTest", includeTest));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetValidateBeforeCall(UUID organizationId, String year, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet(Async)");
        }

        // verify the required parameter 'year' is set
        if (year == null) {
            throw new ApiException("Missing the required parameter 'year' when calling apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet(Async)");
        }

        return apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetCall(organizationId, year, includeTest, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns a detail list of terminals belonging to the requested organization and containing the requested annual year check.
     * If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.
     * @param organizationId The organization of the terminals where the annual year check should be shown. (required)
     * @param year The requested year. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString Search for terminal or store number (or caption). (optional)
     * @return DashboardTerminalDetailPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DashboardTerminalDetailPageResultModel apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet(UUID organizationId, String year, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<DashboardTerminalDetailPageResultModel> localVarResp = apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetWithHttpInfo(organizationId, year, includeTest, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns a detail list of terminals belonging to the requested organization and containing the requested annual year check.
     * If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.
     * @param organizationId The organization of the terminals where the annual year check should be shown. (required)
     * @param year The requested year. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString Search for terminal or store number (or caption). (optional)
     * @return ApiResponse&lt;DashboardTerminalDetailPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DashboardTerminalDetailPageResultModel> apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetWithHttpInfo(UUID organizationId, String year, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetValidateBeforeCall(organizationId, year, includeTest, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<DashboardTerminalDetailPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a detail list of terminals belonging to the requested organization and containing the requested annual year check. (asynchronously)
     * If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.
     * @param organizationId The organization of the terminals where the annual year check should be shown. (required)
     * @param year The requested year. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString Search for terminal or store number (or caption). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetAsync(UUID organizationId, String year, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<DashboardTerminalDetailPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetValidateBeforeCall(organizationId, year, includeTest, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<DashboardTerminalDetailPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet
     * @param organizationId The organization where the terminal versions are requested. (required)
     * @param clientVersion The requested client version. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString Search for terminal or store number (or caption). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetCall(UUID organizationId, String clientVersion, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/dashboard/terminal/clientVersion/{organizationId}/{clientVersion}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()))
            .replace("{" + "clientVersion" + "}", localVarApiClient.escapeString(clientVersion.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (includeTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("includeTest", includeTest));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetValidateBeforeCall(UUID organizationId, String clientVersion, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet(Async)");
        }

        // verify the required parameter 'clientVersion' is set
        if (clientVersion == null) {
            throw new ApiException("Missing the required parameter 'clientVersion' when calling apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet(Async)");
        }

        return apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetCall(organizationId, clientVersion, includeTest, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns a detail list of terminals belonging to the requested organization and having requested client version.
     * 
     * @param organizationId The organization where the terminal versions are requested. (required)
     * @param clientVersion The requested client version. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString Search for terminal or store number (or caption). (optional)
     * @return DashboardTerminalDetailPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DashboardTerminalDetailPageResultModel apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet(UUID organizationId, String clientVersion, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<DashboardTerminalDetailPageResultModel> localVarResp = apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetWithHttpInfo(organizationId, clientVersion, includeTest, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns a detail list of terminals belonging to the requested organization and having requested client version.
     * 
     * @param organizationId The organization where the terminal versions are requested. (required)
     * @param clientVersion The requested client version. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString Search for terminal or store number (or caption). (optional)
     * @return ApiResponse&lt;DashboardTerminalDetailPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DashboardTerminalDetailPageResultModel> apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetWithHttpInfo(UUID organizationId, String clientVersion, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetValidateBeforeCall(organizationId, clientVersion, includeTest, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<DashboardTerminalDetailPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a detail list of terminals belonging to the requested organization and having requested client version. (asynchronously)
     * 
     * @param organizationId The organization where the terminal versions are requested. (required)
     * @param clientVersion The requested client version. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString Search for terminal or store number (or caption). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetAsync(UUID organizationId, String clientVersion, Boolean includeTest, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<DashboardTerminalDetailPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetValidateBeforeCall(organizationId, clientVersion, includeTest, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<DashboardTerminalDetailPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10DashboardTerminalClientVersionOrganizationIdGet
     * @param organizationId The organization where the terminal versions are requested. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DashboardTerminalClientVersionOrganizationIdGetCall(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/dashboard/terminal/clientVersion/{organizationId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (includeTest != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("includeTest", includeTest));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10DashboardTerminalClientVersionOrganizationIdGetValidateBeforeCall(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10DashboardTerminalClientVersionOrganizationIdGet(Async)");
        }

        return apiV10DashboardTerminalClientVersionOrganizationIdGetCall(organizationId, includeTest, pageOffset, pageSize, _callback);

    }

    /**
     * Returns an overview of all terminals and their versions.
     * 
     * @param organizationId The organization where the terminal versions are requested. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return DashboardTerminalOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DashboardTerminalOverviewPageResultModel apiV10DashboardTerminalClientVersionOrganizationIdGet(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<DashboardTerminalOverviewPageResultModel> localVarResp = apiV10DashboardTerminalClientVersionOrganizationIdGetWithHttpInfo(organizationId, includeTest, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Returns an overview of all terminals and their versions.
     * 
     * @param organizationId The organization where the terminal versions are requested. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return ApiResponse&lt;DashboardTerminalOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DashboardTerminalOverviewPageResultModel> apiV10DashboardTerminalClientVersionOrganizationIdGetWithHttpInfo(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10DashboardTerminalClientVersionOrganizationIdGetValidateBeforeCall(organizationId, includeTest, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<DashboardTerminalOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns an overview of all terminals and their versions. (asynchronously)
     * 
     * @param organizationId The organization where the terminal versions are requested. (required)
     * @param includeTest True if test terminals should also be returned. Default is false. (optional, default to false)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DashboardTerminalClientVersionOrganizationIdGetAsync(UUID organizationId, Boolean includeTest, Integer pageOffset, Integer pageSize, final ApiCallback<DashboardTerminalOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10DashboardTerminalClientVersionOrganizationIdGetValidateBeforeCall(organizationId, includeTest, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<DashboardTerminalOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
