# BackupApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10BackupDataTerminalIdDelete**](BackupApi.md#apiV10BackupDataTerminalIdDelete) | **DELETE** /api/v1.0/backup/data/{terminalId} | Deletes a backup from the storage. |
| [**apiV10BackupDataTerminalIdGet**](BackupApi.md#apiV10BackupDataTerminalIdGet) | **GET** /api/v1.0/backup/data/{terminalId} | Returns the backups which are available for this terminal. |


<a id="apiV10BackupDataTerminalIdDelete"></a>
# **apiV10BackupDataTerminalIdDelete**
> apiV10BackupDataTerminalIdDelete(terminalId, backupDate)

Deletes a backup from the storage.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BackupApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BackupApi apiInstance = new BackupApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id of the terminal to delete a backup.
    OffsetDateTime backupDate = OffsetDateTime.now(); // OffsetDateTime | The date of the backup to delete.
    try {
      apiInstance.apiV10BackupDataTerminalIdDelete(terminalId, backupDate);
    } catch (ApiException e) {
      System.err.println("Exception when calling BackupApi#apiV10BackupDataTerminalIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id of the terminal to delete a backup. | |
| **backupDate** | **OffsetDateTime**| The date of the backup to delete. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10BackupDataTerminalIdGet"></a>
# **apiV10BackupDataTerminalIdGet**
> BackupDataPageResultModel apiV10BackupDataTerminalIdGet(terminalId)

Returns the backups which are available for this terminal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.BackupApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    BackupApi apiInstance = new BackupApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal where the backup list is requested.
    try {
      BackupDataPageResultModel result = apiInstance.apiV10BackupDataTerminalIdGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling BackupApi#apiV10BackupDataTerminalIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal where the backup list is requested. | |

### Return type

[**BackupDataPageResultModel**](BackupDataPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

