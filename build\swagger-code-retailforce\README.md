# openapi-java-client

RetailForce Cloud v1.0
- API version: v1.0

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)


*Automatically generated by the [OpenAPI Generator](https://openapi-generator.tech)*


## Requirements

Building the API client library requires:
1. Java 1.8+
2. <PERSON><PERSON> (3.8.3+)/Gradle (7.2+)

## Installation

To install the API client library to your local Maven repository, simply execute:

```shell
mvn clean install
```

To deploy it to a remote Maven repository instead, configure the settings of the repository and execute:

```shell
mvn clean deploy
```

Refer to the [OSSRH Guide](http://central.sonatype.org/pages/ossrh-guide.html) for more information.

### Maven users

Add this dependency to your project's POM:

```xml
<dependency>
  <groupId>org.openapitools</groupId>
  <artifactId>openapi-java-client</artifactId>
  <version>v1.0</version>
  <scope>compile</scope>
</dependency>
```

### Gradle users

Add this dependency to your project's build file:

```groovy
  repositories {
    mavenCentral()     // Needed if the 'openapi-java-client' jar has been published to maven central.
    mavenLocal()       // Needed if the 'openapi-java-client' jar has been published to the local maven repo.
  }

  dependencies {
     implementation "org.openapitools:openapi-java-client:v1.0"
  }
```

### Others

At first generate the JAR by executing:

```shell
mvn clean package
```

Then manually install the following JARs:

* `target/openapi-java-client-v1.0.jar`
* `target/lib/*.jar`

## Getting Started

Please follow the [installation](#installation) instruction and execute the following Java code:

```java

// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.AuthenticationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    AuthenticationApi apiInstance = new AuthenticationApi(defaultClient);
    String key = "key_example"; // String | 
    String secret = "secret_example"; // String | 
    try {
      String result = apiInstance.apiV10AuthenticateLogon2Post(key, secret);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling AuthenticationApi#apiV10AuthenticateLogon2Post");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}

```

## Documentation for API Endpoints

All URIs are relative to *http://localhost*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
*AuthenticationApi* | [**apiV10AuthenticateLogon2Post**](docs/AuthenticationApi.md#apiV10AuthenticateLogon2Post) | **POST** /api/v1.0/authenticate/logon2 | 
*AuthenticationApi* | [**apiV10AuthenticateLogonPost**](docs/AuthenticationApi.md#apiV10AuthenticateLogonPost) | **POST** /api/v1.0/authenticate/logon | Logon on the service and returns web token for service communication.
*AuthenticationApi* | [**apiV10AuthenticateLogoutPost**](docs/AuthenticationApi.md#apiV10AuthenticateLogoutPost) | **POST** /api/v1.0/authenticate/logout | 
*AuthenticationApi* | [**apiV10AuthenticateSessionGet**](docs/AuthenticationApi.md#apiV10AuthenticateSessionGet) | **GET** /api/v1.0/authenticate/session | Returns user session data.
*AuthenticationApi* | [**apiV10AuthenticateTokenGet**](docs/AuthenticationApi.md#apiV10AuthenticateTokenGet) | **GET** /api/v1.0/authenticate/token | 
*BackupApi* | [**apiV10BackupDataTerminalIdDelete**](docs/BackupApi.md#apiV10BackupDataTerminalIdDelete) | **DELETE** /api/v1.0/backup/data/{terminalId} | Deletes a backup from the storage.
*BackupApi* | [**apiV10BackupDataTerminalIdGet**](docs/BackupApi.md#apiV10BackupDataTerminalIdGet) | **GET** /api/v1.0/backup/data/{terminalId} | Returns the backups which are available for this terminal.
*BillingApi* | [**apiV10BillingClearingClearingIdGet**](docs/BillingApi.md#apiV10BillingClearingClearingIdGet) | **GET** /api/v1.0/billing/clearing/{clearingId} | Returns clearing license overview for distributor.
*BillingApi* | [**apiV10BillingClearingClearingIdOrganizationGet**](docs/BillingApi.md#apiV10BillingClearingClearingIdOrganizationGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/organization | Returns clearing license overview for all organizations where the actual users is authorized.
*BillingApi* | [**apiV10BillingClearingClearingIdOrganizationIdDetailGet**](docs/BillingApi.md#apiV10BillingClearingClearingIdOrganizationIdDetailGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/{organizationId}/detail | Returns clearing license details for the requested organization.
*BillingApi* | [**apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet**](docs/BillingApi.md#apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/{organizationId}/usage/detail | returns the clearing detail usage for an organisation
*BillingApi* | [**apiV10BillingClearingClearingIdOrganizationUsageGet**](docs/BillingApi.md#apiV10BillingClearingClearingIdOrganizationUsageGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/organization/usage | Returns clearing license overview usage for all organizations where the actual users is authorized.
*BillingApi* | [**apiV10BillingClearingClearingIdUsageGet**](docs/BillingApi.md#apiV10BillingClearingClearingIdUsageGet) | **GET** /api/v1.0/billing/clearing/{clearingId}/usage | Returns the billin license overview usage for the requested distributor
*BillingApi* | [**apiV10BillingClearingGet**](docs/BillingApi.md#apiV10BillingClearingGet) | **GET** /api/v1.0/billing/clearing | Returns all available clearing runs.
*BillingApi* | [**apiV10BillingClearingPut**](docs/BillingApi.md#apiV10BillingClearingPut) | **PUT** /api/v1.0/billing/clearing | Creates a new clearing run.  Clearing run will be created in background
*BillingApi* | [**apiV10BillingOverviewGet**](docs/BillingApi.md#apiV10BillingOverviewGet) | **GET** /api/v1.0/billing/overview | Returns billing license overview for distributor. [preview]
*BillingApi* | [**apiV10BillingOverviewOrganizationGet**](docs/BillingApi.md#apiV10BillingOverviewOrganizationGet) | **GET** /api/v1.0/billing/overview/organization | Returns billing license overview for all organizations where the actual users is authorized. [preview]
*BillingApi* | [**apiV10BillingOverviewOrganizationIdDetailGet**](docs/BillingApi.md#apiV10BillingOverviewOrganizationIdDetailGet) | **GET** /api/v1.0/billing/overview/{organizationId}/detail | Returns license details for the requested organization. [preview]
*BillingApi* | [**apiV10BillingPaymentOutstandingGet**](docs/BillingApi.md#apiV10BillingPaymentOutstandingGet) | **GET** /api/v1.0/billing/payment/outstanding | Log the acknowledgement of the outstanding payment message
*ConfigurationApi* | [**apiV10ConfigurationAccessLicenseConfigurationInfoGet**](docs/ConfigurationApi.md#apiV10ConfigurationAccessLicenseConfigurationInfoGet) | **GET** /api/v1.0/configuration/access-license/configuration/info | Returns supported access license configuration parameters in the cloud user interface.
*ConfigurationApi* | [**apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost**](docs/ConfigurationApi.md#apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost) | **POST** /api/v1.0/configuration/certificate/csr/{entityType}/{entityId}/{csrRequestId} | Stores external certificate (with prior created certificate signing request (csr)).
*ConfigurationApi* | [**apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet**](docs/ConfigurationApi.md#apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet) | **GET** /api/v1.0/configuration/certificate/csr/{entityType}/{entityId} | Creates a certificate request file (csr) for the given distributor.
*ConfigurationApi* | [**apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet**](docs/ConfigurationApi.md#apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet) | **GET** /api/v1.0/configuration/certificate/csr/{entityType}/{entityId}/open | Get open csrs
*ConfigurationApi* | [**apiV10ConfigurationCertificateEntityTypeEntityIdDelete**](docs/ConfigurationApi.md#apiV10ConfigurationCertificateEntityTypeEntityIdDelete) | **DELETE** /api/v1.0/configuration/certificate/{entityType}/{entityId} | Removes the given external certificate from the store.
*ConfigurationApi* | [**apiV10ConfigurationCertificateEntityTypeEntityIdFilePost**](docs/ConfigurationApi.md#apiV10ConfigurationCertificateEntityTypeEntityIdFilePost) | **POST** /api/v1.0/configuration/certificate/{entityType}/{entityId}/file | Returns the external certificate for download.
*ConfigurationApi* | [**apiV10ConfigurationCertificateEntityTypeEntityIdGet**](docs/ConfigurationApi.md#apiV10ConfigurationCertificateEntityTypeEntityIdGet) | **GET** /api/v1.0/configuration/certificate/{entityType}/{entityId} | Returns all external certificates for the given entity.
*ConfigurationApi* | [**apiV10ConfigurationCertificateEntityTypeEntityIdPost**](docs/ConfigurationApi.md#apiV10ConfigurationCertificateEntityTypeEntityIdPost) | **POST** /api/v1.0/configuration/certificate/{entityType}/{entityId} | Stores the given certificate to the certificate store.
*ConfigurationApi* | [**apiV10ConfigurationCertificateTerminalIdGet**](docs/ConfigurationApi.md#apiV10ConfigurationCertificateTerminalIdGet) | **GET** /api/v1.0/configuration/certificate/{terminalId} | Returns the certificate structure for an external certificate for the client.
*ConfigurationApi* | [**apiV10ConfigurationConfigurationIdDelete**](docs/ConfigurationApi.md#apiV10ConfigurationConfigurationIdDelete) | **DELETE** /api/v1.0/configuration/{configurationId} | Deletes a configuration in the cloud.
*ConfigurationApi* | [**apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions**](docs/ConfigurationApi.md#apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions) | **OPTIONS** /api/v1.0/configuration/{configurationId}/digitalReceipt/mail | If special configuration for digital receipt mail sending is configured, you can test this here.
*ConfigurationApi* | [**apiV10ConfigurationConfigurationIdGet**](docs/ConfigurationApi.md#apiV10ConfigurationConfigurationIdGet) | **GET** /api/v1.0/configuration/{configurationId} | Returns a configuration requested by id for the authenticated user.
*ConfigurationApi* | [**apiV10ConfigurationConfigurationIdPut**](docs/ConfigurationApi.md#apiV10ConfigurationConfigurationIdPut) | **PUT** /api/v1.0/configuration/{configurationId} | Updates a configuration in the retail cloud service.
*ConfigurationApi* | [**apiV10ConfigurationFiscalClientGet**](docs/ConfigurationApi.md#apiV10ConfigurationFiscalClientGet) | **GET** /api/v1.0/configuration/fiscalClient | Function for TrustedFiscalModule to download fiscal client information (CreateClientByCloud).
*ConfigurationApi* | [**apiV10ConfigurationFiscalClientPatch**](docs/ConfigurationApi.md#apiV10ConfigurationFiscalClientPatch) | **PATCH** /api/v1.0/configuration/fiscalClient | Function for TrustedFiscalModule to commit fiscal client configuration download.
*ConfigurationApi* | [**apiV10ConfigurationFiscalClientValidateGet**](docs/ConfigurationApi.md#apiV10ConfigurationFiscalClientValidateGet) | **GET** /api/v1.0/configuration/fiscalClient/validate | Function to validate the fiscal client configured in the cloud.
*ConfigurationApi* | [**apiV10ConfigurationFiscalClientValidateTerminalIdGet**](docs/ConfigurationApi.md#apiV10ConfigurationFiscalClientValidateTerminalIdGet) | **GET** /api/v1.0/configuration/fiscalClient/validate/{terminalId} | Function to validate the fiscal client configured in the cloud.
*ConfigurationApi* | [**apiV10ConfigurationFiscalclientPost**](docs/ConfigurationApi.md#apiV10ConfigurationFiscalclientPost) | **POST** /api/v1.0/configuration/fiscalclient | Creates a client configuration based on fiscal client data.
*ConfigurationApi* | [**apiV10ConfigurationGet**](docs/ConfigurationApi.md#apiV10ConfigurationGet) | **GET** /api/v1.0/configuration | Returns all configurations for the given organisation for the authenticated user.
*ConfigurationApi* | [**apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet**](docs/ConfigurationApi.md#apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet) | **GET** /api/v1.0/configuration/id/{organisationId}/{storeNumber}/{terminalNumber} | Returns the configuration id for the requested terminal.
*ConfigurationApi* | [**apiV10ConfigurationOrganisationCompanyGet**](docs/ConfigurationApi.md#apiV10ConfigurationOrganisationCompanyGet) | **GET** /api/v1.0/configuration/organisation/company | Returns the guid for a organization by company companyidentification.
*ConfigurationApi* | [**apiV10ConfigurationOrganisationGet**](docs/ConfigurationApi.md#apiV10ConfigurationOrganisationGet) | **GET** /api/v1.0/configuration/organisation | Returns the guid for a organization.
*ConfigurationApi* | [**apiV10ConfigurationPost**](docs/ConfigurationApi.md#apiV10ConfigurationPost) | **POST** /api/v1.0/configuration | Create configuration in the retail cloud service.
*ConfigurationApi* | [**apiV10ConfigurationSignatureGet**](docs/ConfigurationApi.md#apiV10ConfigurationSignatureGet) | **GET** /api/v1.0/configuration/signature | Returns the public certificate for retailforce signature CA certificate (CER Format - base64 encoded, key length 4096 bit).
*ConfigurationApi* | [**apiV10ConfigurationSignatureOrganizationIdGet**](docs/ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdGet) | **GET** /api/v1.0/configuration/signature/{organizationId} | Returns the public certificate generated for the organization (CER Format - base64 encoded, key length 2048 bit).
*ConfigurationApi* | [**apiV10ConfigurationSignatureOrganizationIdPatch**](docs/ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdPatch) | **PATCH** /api/v1.0/configuration/signature/{organizationId} | Validates the given signature data with the signature using the organization certificate.
*ConfigurationApi* | [**apiV10ConfigurationSignatureOrganizationIdPost**](docs/ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdPost) | **POST** /api/v1.0/configuration/signature/{organizationId} | Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).
*ConfigurationApi* | [**apiV10ConfigurationSignatureOrganizationIdTerminalIdGet**](docs/ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdTerminalIdGet) | **GET** /api/v1.0/configuration/signature/{organizationId}/{terminalId} | Returns the certificate for the terminal (pfx Format - base64 encoded, key length 1024 bit).
*ConfigurationApi* | [**apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch**](docs/ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch) | **PATCH** /api/v1.0/configuration/signature/{organizationId}/{terminalId} | Validates the given signature data with the signature using the organization certificate.
*ConfigurationApi* | [**apiV10ConfigurationSignatureOrganizationIdTerminalIdPost**](docs/ConfigurationApi.md#apiV10ConfigurationSignatureOrganizationIdTerminalIdPost) | **POST** /api/v1.0/configuration/signature/{organizationId}/{terminalId} | Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonCheckFonCredentialsPut**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonCheckFonCredentialsPut) | **PUT** /api/v1.0/configuration/at/fon/checkFonCredentials | Checks if an authentication can be done with the given fon credentials.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdAesKeyPost**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdAesKeyPost) | **POST** /api/v1.0/configuration/at/fon/{clientId}/aesKey | Updates a the aes key for the given terminal in the retailforce database (or adds).
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCacheDelete**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCacheDelete) | **DELETE** /api/v1.0/configuration/at/fon/{clientId}/cache | Clears fon cache variable on server (for faster fon communication).
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCashregisterDelete**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterDelete) | **DELETE** /api/v1.0/configuration/at/fon/{clientId}/cashregister | Unregister cash register at fon service. You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCashregisterGet**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/cashregister | Checks the cash register state with the FON Service. You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCashregisterPost**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterPost) | **POST** /api/v1.0/configuration/at/fon/{clientId}/cashregister | Register of cash register system. You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCashregisterPut**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterPut) | **PUT** /api/v1.0/configuration/at/fon/{clientId}/cashregister | Recommissioning of cash register system
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete) | **DELETE** /api/v1.0/configuration/at/fon/{clientId}/cashregister/simple | Unregister cash register at fon service. You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCashregisterSimpleGet**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCashregisterSimpleGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/cashregister/simple | Checks the cash register state with the FON Service. You have to be authenticated. The format for the cash registerid is storeNumber/terminalNumber.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCertificateDelete**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCertificateDelete) | **DELETE** /api/v1.0/configuration/at/fon/{clientId}/certificate | Unregister a security certificate at fon (Finanzonline). You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCertificateGet**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCertificateGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/certificate | Returns true if the given security certificate is activated (IN_BETRIEB) at fon (Finanzonline). You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCertificatePost**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCertificatePost) | **POST** /api/v1.0/configuration/at/fon/{clientId}/certificate | Register a security certificate at fon (Finanzonline). You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdCertificatePut**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdCertificatePut) | **PUT** /api/v1.0/configuration/at/fon/{clientId}/certificate | Recommissioning a security certificate at fon (Finanzonline). You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdDocumentValidateGet**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdDocumentValidateGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/document/validate | Does document validation on fon service. You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdDocumentValidateLastGet**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdDocumentValidateLastGet) | **GET** /api/v1.0/configuration/at/fon/{clientId}/document/validate/last | Does document validation on fon service. You have to be authenticated.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonClientIdGet**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonClientIdGet) | **GET** /api/v1.0/configuration/at/fon/{clientId} | Returns whether the client has fon access (if the data is configured and license is exi
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtFonDropoutReasonsGet**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtFonDropoutReasonsGet) | **GET** /api/v1.0/configuration/at/fon/dropoutReasons | Returns the possible drop out reasons for cash register drop out.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtHsmTerminalIdPatch**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtHsmTerminalIdPatch) | **PATCH** /api/v1.0/configuration/at/hsm/{terminalId} | Tests configured hsm connections for the given client.
*ConfigurationControllerAustriaApi* | [**apiV10ConfigurationAtSignDeviceDriverInfoGet**](docs/ConfigurationControllerAustriaApi.md#apiV10ConfigurationAtSignDeviceDriverInfoGet) | **GET** /api/v1.0/configuration/at/signDeviceDriverInfo | Returns supported smart card drivers for configuration in the cloud user interface.
*ConfigurationControllerGermanyApi* | [**apiV10ConfigurationDeClientConfigurationTerminalIdPost**](docs/ConfigurationControllerGermanyApi.md#apiV10ConfigurationDeClientConfigurationTerminalIdPost) | **POST** /api/v1.0/configuration/de/clientConfiguration/{terminalId} | Stores the necessary fields of the client configuration to the insights tables for germany.
*ConfigurationControllerGermanyApi* | [**apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost**](docs/ConfigurationControllerGermanyApi.md#apiV10ConfigurationDeClientConfigurationTerminalIdTseVersionPost) | **POST** /api/v1.0/configuration/de/clientConfiguration/{terminalId}/tseVersion | Stores the actual tse version information to the terminal insights table.
*ConfigurationControllerGermanyApi* | [**apiV10ConfigurationDeTseDriverInfoGet**](docs/ConfigurationControllerGermanyApi.md#apiV10ConfigurationDeTseDriverInfoGet) | **GET** /api/v1.0/configuration/de/tseDriverInfo | Returns supported tse drivers for configuration in the cloud user interface.
*ConfigurationControllerItalyApi* | [**apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet**](docs/ConfigurationControllerItalyApi.md#apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet) | **GET** /api/v1.0/configuration/it/clientConfiguration/{terminalId}/printerImages | Returns stored printer image ids
*ConfigurationControllerItalyApi* | [**apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet**](docs/ConfigurationControllerItalyApi.md#apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet) | **GET** /api/v1.0/configuration/it/clientConfiguration/{terminalId}/printerImages/{order} | returns the requeste image
*ConfigurationControllerItalyApi* | [**apiV10ConfigurationItConfigurationIdPrinterImagesGet**](docs/ConfigurationControllerItalyApi.md#apiV10ConfigurationItConfigurationIdPrinterImagesGet) | **GET** /api/v1.0/configuration/it/{configurationId}/printerImages | Returns stored printer image ids
*ConfigurationControllerItalyApi* | [**apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete**](docs/ConfigurationControllerItalyApi.md#apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete) | **DELETE** /api/v1.0/configuration/it/{configurationId}/printerImages/{order} | Delete printer image
*ConfigurationControllerItalyApi* | [**apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet**](docs/ConfigurationControllerItalyApi.md#apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet) | **GET** /api/v1.0/configuration/it/{configurationId}/printerImages/{order} | returns the requeste image
*ConfigurationControllerItalyApi* | [**apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost**](docs/ConfigurationControllerItalyApi.md#apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost) | **POST** /api/v1.0/configuration/it/{configurationId}/printerImages/{order} | Upload printer image
*ConfigurationControllerItalyApi* | [**apiV10ConfigurationItPrinterDriverInfosGet**](docs/ConfigurationControllerItalyApi.md#apiV10ConfigurationItPrinterDriverInfosGet) | **GET** /api/v1.0/configuration/it/printerDriverInfos | Returns the printer driver infos.
*ConfigurationControllerLithuaniaApi* | [**apiV10ConfigurationLtCertificatesDistributorIdPost**](docs/ConfigurationControllerLithuaniaApi.md#apiV10ConfigurationLtCertificatesDistributorIdPost) | **POST** /api/v1.0/configuration/lt/certificates/{distributorId} | 
*ConfigurationControllerLithuaniaApi* | [**apiV10ConfigurationLtCertificatesTerminalIdSecurityGet**](docs/ConfigurationControllerLithuaniaApi.md#apiV10ConfigurationLtCertificatesTerminalIdSecurityGet) | **GET** /api/v1.0/configuration/lt/certificates/{terminalId}/security | 
*ConfigurationControllerLithuaniaApi* | [**apiV10ConfigurationLtCertificatesTerminalIdTransportGet**](docs/ConfigurationControllerLithuaniaApi.md#apiV10ConfigurationLtCertificatesTerminalIdTransportGet) | **GET** /api/v1.0/configuration/lt/certificates/{terminalId}/transport | 
*ConfigurationControllerSwedenApi* | [**apiV10ConfigurationSeControlUnitDriverInfoGet**](docs/ConfigurationControllerSwedenApi.md#apiV10ConfigurationSeControlUnitDriverInfoGet) | **GET** /api/v1.0/configuration/se/controlUnitDriverInfo | Returns supported smart card drivers for configuration in the cloud user interface.
*DashboardApi* | [**apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet**](docs/DashboardApi.md#apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet) | **GET** /api/v1.0/dashboard/terminal/annualYearCheck/{organizationId} | Returns an overview of all annual year checks for all active terminals (archived terminals will not be returned).
*DashboardApi* | [**apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet**](docs/DashboardApi.md#apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet) | **GET** /api/v1.0/dashboard/terminal/annualYearCheck/{organizationId}/{year} | Returns a detail list of terminals belonging to the requested organization and containing the requested annual year check.
*DashboardApi* | [**apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet**](docs/DashboardApi.md#apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet) | **GET** /api/v1.0/dashboard/terminal/clientVersion/{organizationId}/{clientVersion} | Returns a detail list of terminals belonging to the requested organization and having requested client version.
*DashboardApi* | [**apiV10DashboardTerminalClientVersionOrganizationIdGet**](docs/DashboardApi.md#apiV10DashboardTerminalClientVersionOrganizationIdGet) | **GET** /api/v1.0/dashboard/terminal/clientVersion/{organizationId} | Returns an overview of all terminals and their versions.
*DocumentApi* | [**apiV10DocumentsDocumentOrganizationIdPost**](docs/DocumentApi.md#apiV10DocumentsDocumentOrganizationIdPost) | **POST** /api/v1.0/documents/document/{organizationId} | Stores a document / fiscal response tuple as RetailForce.Fiscalisation.Model.Helper.CosmosDocument.
*ImplementationApi* | [**apiV10ImplementationTerminalIdAuditLogGet**](docs/ImplementationApi.md#apiV10ImplementationTerminalIdAuditLogGet) | **GET** /api/v1.0/implementation/{terminalId}/auditLog | Returns audit log records from storage.
*ImplementationApi* | [**apiV10ImplementationTerminalIdRestoreclientdataGet**](docs/ImplementationApi.md#apiV10ImplementationTerminalIdRestoreclientdataGet) | **GET** /api/v1.0/implementation/{terminalId}/restoreclientdata | Exports current client data from cloud archive.
*ImplementationControllerAustriaApi* | [**apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet**](docs/ImplementationControllerAustriaApi.md#apiV10ImplementationAtOrganizationOrganizationIdMissingAnnualReceiptsGet) | **GET** /api/v1.0/implementation/at/organization/{organizationId}/missingAnnualReceipts | Returns all missing annual year receipts (annual year receipt check).
*ImplementationControllerAustriaApi* | [**apiV10ImplementationAtSignatureDeviceEntityIdGet**](docs/ImplementationControllerAustriaApi.md#apiV10ImplementationAtSignatureDeviceEntityIdGet) | **GET** /api/v1.0/implementation/at/signatureDevice/{entityId} | Returns the signature devices for the given entity.
*ImplementationControllerAustriaApi* | [**apiV10ImplementationAtTerminalIdAsitCryptoContainerGet**](docs/ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdAsitCryptoContainerGet) | **GET** /api/v1.0/implementation/at/{terminalId}/asitCryptoContainer | Returns the asit crypto container for asit check (as json string).
*ImplementationControllerAustriaApi* | [**apiV10ImplementationAtTerminalIdDep131Get**](docs/ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdDep131Get) | **GET** /api/v1.0/implementation/at/{terminalId}/dep131 | Exports all data requested for dep131 protocol out of stored data.
*ImplementationControllerAustriaApi* | [**apiV10ImplementationAtTerminalIdDepGet**](docs/ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdDepGet) | **GET** /api/v1.0/implementation/at/{terminalId}/dep | Exports austrian dep from cloud archive.
*ImplementationControllerAustriaApi* | [**apiV10ImplementationAtTerminalIdFonGet**](docs/ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdFonGet) | **GET** /api/v1.0/implementation/at/{terminalId}/fon | Returns the pages fon connection log for the requested client.
*ImplementationControllerAustriaApi* | [**apiV10ImplementationAtTerminalIdYearReceiptPost**](docs/ImplementationControllerAustriaApi.md#apiV10ImplementationAtTerminalIdYearReceiptPost) | **POST** /api/v1.0/implementation/at/{terminalId}/yearReceipt | Creates an automatic year receipt and validates this at fon.
*ImplementationControllerDenmarkApi* | [**apiV10ImplementationDkSaftValidatePost**](docs/ImplementationControllerDenmarkApi.md#apiV10ImplementationDkSaftValidatePost) | **POST** /api/v1.0/implementation/dk/saft/validate | Validates given zip file audit file (denmark saf-t) content.
*ImplementationControllerDenmarkApi* | [**apiV10ImplementationDkTerminalIdSaftGet**](docs/ImplementationControllerDenmarkApi.md#apiV10ImplementationDkTerminalIdSaftGet) | **GET** /api/v1.0/implementation/dk/{terminalId}/saft | Exports denmark saf-t format for the given terminal.
*ImplementationControllerFranceApi* | [**apiV10ImplementationFrTerminalIdStoreArchivePost**](docs/ImplementationControllerFranceApi.md#apiV10ImplementationFrTerminalIdStoreArchivePost) | **POST** /api/v1.0/implementation/fr/{terminalId}/storeArchive | Stores the french archive to the french archive store.
*ImplementationControllerFranceApi* | [**apiV10ImplementationFrTerminalIdTaxArchiveGet**](docs/ImplementationControllerFranceApi.md#apiV10ImplementationFrTerminalIdTaxArchiveGet) | **GET** /api/v1.0/implementation/fr/{terminalId}/taxArchive | Exports one or more french fiscal archives according to date parameter.
*ImplementationControllerFranceApi* | [**apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost**](docs/ImplementationControllerFranceApi.md#apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost) | **POST** /api/v1.0/implementation/fr/{terminalId}/taxArchive/verify | Method to verify the french archive.
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeDistributorIdTseInformation2Post**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeDistributorIdTseInformation2Post) | **POST** /api/v1.0/implementation/de/{distributorId}/tse/information2 | Store the given tse information
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeDistributorIdTseInformationPost**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeDistributorIdTseInformationPost) | **POST** /api/v1.0/implementation/de/{distributorId}/tse/information | Store the given tse information
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTerminalIdTarGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTerminalIdTarGet) | **GET** /api/v1.0/implementation/de/{terminalId}/tar | Exports germany tar data (tse) from cloud archive.
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTerminalIdTaxonomyGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTerminalIdTaxonomyGet) | **GET** /api/v1.0/implementation/de/{terminalId}/taxonomy | Exports germany taxonomy data from cloud archive.
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTerminalIdTseProvisioningPatch**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTerminalIdTseProvisioningPatch) | **PATCH** /api/v1.0/implementation/de/{terminalId}/tse/provisioning | Starts tse provisioning for the given terminal.
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsGet) | **GET** /api/v1.0/implementation/de/tse/announcements | Get Tse Announcements
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsPost**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsPost) | **POST** /api/v1.0/implementation/de/tse/announcements | Create TseAnnouncement  starts a new tse announcement progress. An email per store will be sent to the user, containing the announcemnt data and a verification link.
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsStatusGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsStatusGet) | **GET** /api/v1.0/implementation/de/tse/announcements/status | Get TSE Announcement status:  if there are tse changes or an open tse announcement process (changes in announcemnt structure store,terminal,tseInformation) which need to be reported
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdCancelPost) | **POST** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/cancel | Cancel TseAnnouncement
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdDataPdfGet) | **GET** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/data/pdf | Returns the tse announcement data as pdf
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdGet) | **GET** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId} | Get TseAnnouncement
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationEmailPut) | **PUT** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/verification/email | Resend Verification Email
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsTseAnnouncementIdVerificationPost) | **POST** /api/v1.0/implementation/de/tse/announcements/{tseAnnouncementId}/verification | 
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseAnnouncementsUnannouncedGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseAnnouncementsUnannouncedGet) | **GET** /api/v1.0/implementation/de/tse/announcements/unannounced | Get all unannounced TseAnnouncements  This include all TseAnnouncements which has never been announced or include new changes, but not tseAnnouncements which are open (verificationPending or inProgress)
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseFinderDownloadGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseFinderDownloadGet) | **GET** /api/v1.0/implementation/de/tse/finder/download | Download TSE Finder
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseInformationExportGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseInformationExportGet) | **GET** /api/v1.0/implementation/de/tse/information/export | Export TseInformations as CSV
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseInformationGet**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseInformationGet) | **GET** /api/v1.0/implementation/de/tse/information | Get tse informations for the given distributor
*ImplementationControllerGermanyApi* | [**apiV10ImplementationDeTseInformationTseSerialHexAssignPut**](docs/ImplementationControllerGermanyApi.md#apiV10ImplementationDeTseInformationTseSerialHexAssignPut) | **PUT** /api/v1.0/implementation/de/tse/information/{tseSerialHex}/assign | Assign OrganisationId and TerminalId to Tse
*InformationApi* | [**apiV10InformationDocumenttypesSimpleGet**](docs/InformationApi.md#apiV10InformationDocumenttypesSimpleGet) | **GET** /api/v1.0/information/documenttypes/simple | Returns the available document types for fiscalisation and/or digital receipt.
*InformationApi* | [**apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet**](docs/InformationApi.md#apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet) | **GET** /api/v1.0/information/fiscalCountryProperties/{fiscalCountry}/fiscalregions | Returns the fiscal regions for the given fiscal country.
*InformationApi* | [**apiV10InformationFiscalCountryPropertiesFiscalCountryGet**](docs/InformationApi.md#apiV10InformationFiscalCountryPropertiesFiscalCountryGet) | **GET** /api/v1.0/information/fiscalCountryProperties/{fiscalCountry} | Returns fiscal country properties for the given country.
*InformationApi* | [**apiV10InformationFiscalCountryPropertiesGet**](docs/InformationApi.md#apiV10InformationFiscalCountryPropertiesGet) | **GET** /api/v1.0/information/fiscalCountryProperties | Returns all fiscal countries properties for all available fiscal countries.
*InformationApi* | [**apiV10InformationHelpEnumTypeNameEnumValueGet**](docs/InformationApi.md#apiV10InformationHelpEnumTypeNameEnumValueGet) | **GET** /api/v1.0/information/help/enum/{typeName}/{enumValue} | Returns type help information for the requested enum value.
*InformationApi* | [**apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet**](docs/InformationApi.md#apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet) | **GET** /api/v1.0/information/help/fiscalCountryProperties/{fiscalCountry}/{documentType} | Returns the mapping of business transaction types for the given document type and fiscal country.
*InformationApi* | [**apiV10InformationHelpTypeNameGet**](docs/InformationApi.md#apiV10InformationHelpTypeNameGet) | **GET** /api/v1.0/information/help/{typeName} | Returns help information for the given type.
*InformationApi* | [**apiV10InformationHelpTypeNamePropertyNameGet**](docs/InformationApi.md#apiV10InformationHelpTypeNamePropertyNameGet) | **GET** /api/v1.0/information/help/{typeName}/{propertyName} | Returns the help information for the given type and property.
*InformationApi* | [**apiV10InformationMessageTerminalIdPost**](docs/InformationApi.md#apiV10InformationMessageTerminalIdPost) | **POST** /api/v1.0/information/message/{terminalId} | Send a message to the contact of the given terminal.
*InformationApi* | [**apiV10InformationReleasesGet**](docs/InformationApi.md#apiV10InformationReleasesGet) | **GET** /api/v1.0/information/releases | Returns releases stored in the system.
*InformationApi* | [**apiV10InformationTerminalIdVersionGet**](docs/InformationApi.md#apiV10InformationTerminalIdVersionGet) | **GET** /api/v1.0/information/{terminalId}/version | Returns the actual version of the terminal (used sw version of client).
*InformationApi* | [**apiV10InformationTerminalIdVersionPut**](docs/InformationApi.md#apiV10InformationTerminalIdVersionPut) | **PUT** /api/v1.0/information/{terminalId}/version | Sets the actual version of the terminal in the cloud.
*InformationApi* | [**apiV10InformationVersionGet**](docs/InformationApi.md#apiV10InformationVersionGet) | **GET** /api/v1.0/information/version | Returns the actual version (SW) of the software.
*LicencingApi* | [**apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete**](docs/LicencingApi.md#apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete) | **DELETE** /api/v1.0/licensing/license/accessLicense/{accessLicenseId} | 
*LicencingApi* | [**apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet**](docs/LicencingApi.md#apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet) | **GET** /api/v1.0/licensing/license/accessLicense/{accessLicenseId} | 
*LicencingApi* | [**apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut**](docs/LicencingApi.md#apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut) | **PUT** /api/v1.0/licensing/license/accessLicense/{accessLicenseId} | 
*LicencingApi* | [**apiV10LicensingLicenseAccessLicenseGet**](docs/LicencingApi.md#apiV10LicensingLicenseAccessLicenseGet) | **GET** /api/v1.0/licensing/license/accessLicense | 
*LicencingApi* | [**apiV10LicensingLicenseAccessLicensePost**](docs/LicencingApi.md#apiV10LicensingLicenseAccessLicensePost) | **POST** /api/v1.0/licensing/license/accessLicense | 
*LicencingApi* | [**apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete**](docs/LicencingApi.md#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete) | **DELETE** /api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract | 
*LicencingApi* | [**apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet**](docs/LicencingApi.md#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet) | **GET** /api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract | 
*LicencingApi* | [**apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost**](docs/LicencingApi.md#apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost) | **POST** /api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract | 
*LicencingApi* | [**apiV10LicensingLicenseAdminCreateallcontainersPatch**](docs/LicencingApi.md#apiV10LicensingLicenseAdminCreateallcontainersPatch) | **PATCH** /api/v1.0/licensing/license/admin/createallcontainers | 
*LicencingApi* | [**apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete**](docs/LicencingApi.md#apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete) | **DELETE** /api/v1.0/licensing/license/allocation/{licenseId}/{accessLicenseId} | 
*LicencingApi* | [**apiV10LicensingLicenseAllocationLicenseIdGet**](docs/LicencingApi.md#apiV10LicensingLicenseAllocationLicenseIdGet) | **GET** /api/v1.0/licensing/license/allocation/{licenseId} | 
*LicencingApi* | [**apiV10LicensingLicenseAllocationLicenseIdPost**](docs/LicencingApi.md#apiV10LicensingLicenseAllocationLicenseIdPost) | **POST** /api/v1.0/licensing/license/allocation/{licenseId} | 
*LicencingApi* | [**apiV10LicensingLicenseConfigurationConfigurationIdGet**](docs/LicencingApi.md#apiV10LicensingLicenseConfigurationConfigurationIdGet) | **GET** /api/v1.0/licensing/license/configuration/{configurationId} | Returns the possible licenses for the requested organisation.
*LicencingApi* | [**apiV10LicensingLicenseConfigurationConfigurationIdPut**](docs/LicencingApi.md#apiV10LicensingLicenseConfigurationConfigurationIdPut) | **PUT** /api/v1.0/licensing/license/configuration/{configurationId} | Updates licenses to the given configuration.
*LicencingApi* | [**apiV10LicensingLicenseConfigurationConfigurationIdUsageGet**](docs/LicencingApi.md#apiV10LicensingLicenseConfigurationConfigurationIdUsageGet) | **GET** /api/v1.0/licensing/license/configuration/{configurationId}/usage | Returns the used licenses by this configuration.
*LicencingApi* | [**apiV10LicensingLicenseConfigurationGroupsGet**](docs/LicencingApi.md#apiV10LicensingLicenseConfigurationGroupsGet) | **GET** /api/v1.0/licensing/license/configuration/groups | 
*LicencingApi* | [**apiV10LicensingLicenseConfigurationUnitsGet**](docs/LicencingApi.md#apiV10LicensingLicenseConfigurationUnitsGet) | **GET** /api/v1.0/licensing/license/configuration/units | Returns all available license units.
*LicencingApi* | [**apiV10LicensingLicenseEffectiveEntityIdGet**](docs/LicencingApi.md#apiV10LicensingLicenseEffectiveEntityIdGet) | **GET** /api/v1.0/licensing/license/effective/{entityId} | Returns the effective licenses for the given entity.
*LicencingApi* | [**apiV10LicensingLicenseGet**](docs/LicencingApi.md#apiV10LicensingLicenseGet) | **GET** /api/v1.0/licensing/license | 
*LicencingApi* | [**apiV10LicensingLicenseLicenseIdDelete**](docs/LicencingApi.md#apiV10LicensingLicenseLicenseIdDelete) | **DELETE** /api/v1.0/licensing/license/{licenseId} | 
*LicencingApi* | [**apiV10LicensingLicenseLicenseIdGet**](docs/LicencingApi.md#apiV10LicensingLicenseLicenseIdGet) | **GET** /api/v1.0/licensing/license/{licenseId} | 
*LicencingApi* | [**apiV10LicensingLicenseLicenseIdPut**](docs/LicencingApi.md#apiV10LicensingLicenseLicenseIdPut) | **PUT** /api/v1.0/licensing/license/{licenseId} | 
*LicencingApi* | [**apiV10LicensingLicensePost**](docs/LicencingApi.md#apiV10LicensingLicensePost) | **POST** /api/v1.0/licensing/license | 
*LicencingApi* | [**apiV10LicensingTokenAccessCounterGet**](docs/LicencingApi.md#apiV10LicensingTokenAccessCounterGet) | **GET** /api/v1.0/licensing/tokenAccessCounter | Returns a license token (jwt) to use for requests for azure stateless functions.
*LicencingApi* | [**apiV10LicensingTokenGet**](docs/LicencingApi.md#apiV10LicensingTokenGet) | **GET** /api/v1.0/licensing/token | Returns a license token (jwt) to use for requests for azure stateless functions.
*LicencingApi* | [**apiV10LicensingTokenTerminalIdGet**](docs/LicencingApi.md#apiV10LicensingTokenTerminalIdGet) | **GET** /api/v1.0/licensing/token/{terminalId} | Returns the license token (jwt) including all licenses for the requested terminal.
*LicencingApi* | [**apiV10LicensingTokenValidateGet**](docs/LicencingApi.md#apiV10LicensingTokenValidateGet) | **GET** /api/v1.0/licensing/token/validate | Validates the given license token and returns a RetailForce.Cloud.Functions.Entities.JwtLicenseClaim object containing the license of the token.
*MasterDataApi* | [**apiV10MasterdataDistributorsDistributorIdSupportHoursGet**](docs/MasterDataApi.md#apiV10MasterdataDistributorsDistributorIdSupportHoursGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/support-hours | Get open support hours for distributor
*MasterDataApi* | [**apiV10MasterdataDistributorsDistributorIdTimelogGet**](docs/MasterDataApi.md#apiV10MasterdataDistributorsDistributorIdTimelogGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/timelog | Get onboarding or customer care timelog for distributor
*MasterDataApi* | [**apiV10MasterdataEntityEntityIdHasOrganizationGet**](docs/MasterDataApi.md#apiV10MasterdataEntityEntityIdHasOrganizationGet) | **GET** /api/v1.0/masterdata/entity/{entityId}/hasOrganization | Returns true when entity has allocated organizations.
*MasterDataApi* | [**apiV10MasterdataImportGet**](docs/MasterDataApi.md#apiV10MasterdataImportGet) | **GET** /api/v1.0/masterdata/import | Load imports of Organizations/Companies/Terminals imports with pagination for the authenticated user.
*MasterDataApi* | [**apiV10MasterdataImportImportIdGet**](docs/MasterDataApi.md#apiV10MasterdataImportImportIdGet) | **GET** /api/v1.0/masterdata/import/{importId} | Returns the detail information of the requested import log.
*MasterDataApi* | [**apiV10MasterdataImportPost**](docs/MasterDataApi.md#apiV10MasterdataImportPost) | **POST** /api/v1.0/masterdata/import | Import Organizations/Companies/Terminals as CSV
*MasterDataApi* | [**apiV10MasterdataParameterEntityTypeEntityIdGet**](docs/MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdGet) | **GET** /api/v1.0/masterdata/parameter/{entityType}/{entityId} | Returns all stored parameters for the given entity.
*MasterDataApi* | [**apiV10MasterdataParameterEntityTypeEntityIdInfoGet**](docs/MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdInfoGet) | **GET** /api/v1.0/masterdata/parameter/{entityType}/{entityId}/info | Get parameter infos (available parameters) for the given entity.
*MasterDataApi* | [**apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete**](docs/MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete) | **DELETE** /api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid} | Deletes an existing entity parameter.
*MasterDataApi* | [**apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet**](docs/MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet) | **GET** /api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid} | Returns requested stored parameter for the given entity and parameterId.
*MasterDataApi* | [**apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut**](docs/MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut) | **PUT** /api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid} | Updates an existing entity parameter.
*MasterDataApi* | [**apiV10MasterdataParameterEntityTypeEntityIdPost**](docs/MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdPost) | **POST** /api/v1.0/masterdata/parameter/{entityType}/{entityId} | Creates a new entity parameter.
*MasterDataApi* | [**apiV10MasterdataVatCheckSimpleGet**](docs/MasterDataApi.md#apiV10MasterdataVatCheckSimpleGet) | **GET** /api/v1.0/masterdata/vatCheck/simple | Returns whether the given vatNumber is ok or not.
*MasterDataCompaniesApi* | [**apiV10MasterdataCompaniesCompanyIdDelete**](docs/MasterDataCompaniesApi.md#apiV10MasterdataCompaniesCompanyIdDelete) | **DELETE** /api/v1.0/masterdata/companies/{companyId} | Deletes a company from the cloud store.
*MasterDataCompaniesApi* | [**apiV10MasterdataCompaniesCompanyIdGet**](docs/MasterDataCompaniesApi.md#apiV10MasterdataCompaniesCompanyIdGet) | **GET** /api/v1.0/masterdata/companies/{companyId} | Returns the requested company by id.
*MasterDataCompaniesApi* | [**apiV10MasterdataCompaniesCompanyIdPut**](docs/MasterDataCompaniesApi.md#apiV10MasterdataCompaniesCompanyIdPut) | **PUT** /api/v1.0/masterdata/companies/{companyId} | Updates a company in the cloud store.
*MasterDataCompaniesApi* | [**apiV10MasterdataCompaniesCompanyIdVersionsGet**](docs/MasterDataCompaniesApi.md#apiV10MasterdataCompaniesCompanyIdVersionsGet) | **GET** /api/v1.0/masterdata/companies/{companyId}/versions | Get company versions
*MasterDataCompaniesApi* | [**apiV10MasterdataCompaniesGet**](docs/MasterDataCompaniesApi.md#apiV10MasterdataCompaniesGet) | **GET** /api/v1.0/masterdata/companies | Returns all companies for the requested organisation for the authenticated user.
*MasterDataCompaniesApi* | [**apiV10MasterdataCompaniesIdGet**](docs/MasterDataCompaniesApi.md#apiV10MasterdataCompaniesIdGet) | **GET** /api/v1.0/masterdata/companies/id | Returns the company id (if applicable) of the requested store.
*MasterDataCompaniesApi* | [**apiV10MasterdataCompaniesPost**](docs/MasterDataCompaniesApi.md#apiV10MasterdataCompaniesPost) | **POST** /api/v1.0/masterdata/companies | Creates a new company in the cloud store.
*MasterDataCompaniesApi* | [**apiV10MasterdataCompaniesSimpleGet**](docs/MasterDataCompaniesApi.md#apiV10MasterdataCompaniesSimpleGet) | **GET** /api/v1.0/masterdata/companies/simple | Returns all companies as a RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested organisation for the authenticated user.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdContractContractIdDelete**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractContractIdDelete) | **DELETE** /api/v1.0/masterdata/distributors/{distributorId}/contract/{contractId} | Deletes a contract.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdContractContractIdPut**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractContractIdPut) | **PUT** /api/v1.0/masterdata/distributors/{distributorId}/contract/{contractId} | Updates the given contract of the distributor.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractDistributorContractIdOnboardingFinishPut) | **PUT** /api/v1.0/masterdata/distributors/{distributorId}/contract/{distributorContractId}/onboarding/finish | Finishes onboarding for distributor
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdContractPost**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractPost) | **POST** /api/v1.0/masterdata/distributors/{distributorId}/contract | Creates a new contract for a distributor.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdContractsContractIdGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractsContractIdGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/contracts/{contractId} | Returns the requested contract.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdContractsGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdContractsGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/contracts | Returns all contracts for a distributor.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdDelete**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdDelete) | **DELETE** /api/v1.0/masterdata/distributors/{distributorId} | Deletes an distributor from cloud store.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId} | Returns the distributor requested by the given id.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdLicenseGroupsGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/licenseGroups | Returns the available license groups.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdOnboardingFinishPut) | **PUT** /api/v1.0/masterdata/distributors/{distributorId}/onboarding/finish | Finishes onboarding for distributor
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdPut**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdPut) | **PUT** /api/v1.0/masterdata/distributors/{distributorId} | Updates an distributor in the cloud store.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsDistributorIdSimpleGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsDistributorIdSimpleGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/simple | Returns a simple distributorRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsGet) | **GET** /api/v1.0/masterdata/distributors | Returns all distributors for the authenticated user.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsHierarchyAllGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsHierarchyAllGet) | **GET** /api/v1.0/masterdata/distributors/hierarchy/all | Returns all distributors based on the parent distributor (only retailforce users)
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsHierarchyBreadcrumbGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsHierarchyBreadcrumbGet) | **GET** /api/v1.0/masterdata/distributors/hierarchy/breadcrumb | Returns the hierarchy of the actual distributor as a breadcrumb object for navigation.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsHierarchyGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsHierarchyGet) | **GET** /api/v1.0/masterdata/distributors/hierarchy | Returns distributor hierarchy.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsLevelsGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsLevelsGet) | **GET** /api/v1.0/masterdata/distributors/levels | Returns the available distributor levels.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsPost**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsPost) | **POST** /api/v1.0/masterdata/distributors | Creates a new distributor in the cloud store.
*MasterDataDistributorsApi* | [**apiV10MasterdataDistributorsSimpleGet**](docs/MasterDataDistributorsApi.md#apiV10MasterdataDistributorsSimpleGet) | **GET** /api/v1.0/masterdata/distributors/simple | Returns all distributors for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
*MasterDataOrganisationsApi* | [**apiV10MasterdataOrganisationsGet**](docs/MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsGet) | **GET** /api/v1.0/masterdata/organisations | Returns all organisations for the authenticated user.
*MasterDataOrganisationsApi* | [**apiV10MasterdataOrganisationsOrganisationIdDelete**](docs/MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganisationIdDelete) | **DELETE** /api/v1.0/masterdata/organisations/{organisationId} | Deletes an organisation from cloud store.
*MasterDataOrganisationsApi* | [**apiV10MasterdataOrganisationsOrganisationIdGet**](docs/MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganisationIdGet) | **GET** /api/v1.0/masterdata/organisations/{organisationId} | Returns the organisation requested by the given id.
*MasterDataOrganisationsApi* | [**apiV10MasterdataOrganisationsOrganisationIdPut**](docs/MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganisationIdPut) | **PUT** /api/v1.0/masterdata/organisations/{organisationId} | Updates an organisation in the cloud store.
*MasterDataOrganisationsApi* | [**apiV10MasterdataOrganisationsOrganisationIdVersionsGet**](docs/MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganisationIdVersionsGet) | **GET** /api/v1.0/masterdata/organisations/{organisationId}/versions | Get organisation versions
*MasterDataOrganisationsApi* | [**apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet**](docs/MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet) | **GET** /api/v1.0/masterdata/organisations/{organizationId}/documentTypes | Returns all custom document types for the requested organization.
*MasterDataOrganisationsApi* | [**apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut**](docs/MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut) | **PUT** /api/v1.0/masterdata/organisations/{organizationId}/documentTypes | Updates the list of custom document types for the given organization.
*MasterDataOrganisationsApi* | [**apiV10MasterdataOrganisationsPost**](docs/MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsPost) | **POST** /api/v1.0/masterdata/organisations | Creates a new organization in the cloud store.
*MasterDataStoresApi* | [**apiV10MasterdataStoresGet**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresGet) | **GET** /api/v1.0/masterdata/stores | Returns all stores for the given organisation/company for the authenticated user.
*MasterDataStoresApi* | [**apiV10MasterdataStoresIdGet**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresIdGet) | **GET** /api/v1.0/masterdata/stores/id | Returns the store id of the requested store.
*MasterDataStoresApi* | [**apiV10MasterdataStoresPost**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresPost) | **POST** /api/v1.0/masterdata/stores | Creates a store in the cloud store.
*MasterDataStoresApi* | [**apiV10MasterdataStoresSimpleGet**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresSimpleGet) | **GET** /api/v1.0/masterdata/stores/simple | Returns all stores as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the given organisation/company for the authenticated user.
*MasterDataStoresApi* | [**apiV10MasterdataStoresStoreIdDelete**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdDelete) | **DELETE** /api/v1.0/masterdata/stores/{storeId} | Deletes a store from the cloud store.
*MasterDataStoresApi* | [**apiV10MasterdataStoresStoreIdGet**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdGet) | **GET** /api/v1.0/masterdata/stores/{storeId} | Returns the requested store for the authenticated users.
*MasterDataStoresApi* | [**apiV10MasterdataStoresStoreIdHidePost**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdHidePost) | **POST** /api/v1.0/masterdata/stores/{storeId}/hide | Hides the store from the standard list.
*MasterDataStoresApi* | [**apiV10MasterdataStoresStoreIdPut**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdPut) | **PUT** /api/v1.0/masterdata/stores/{storeId} | Updates a store in the cloud store.
*MasterDataStoresApi* | [**apiV10MasterdataStoresStoreIdUnHidePost**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdUnHidePost) | **POST** /api/v1.0/masterdata/stores/{storeId}/unHide | Un-hides a previously hidden store to the standard list.
*MasterDataStoresApi* | [**apiV10MasterdataStoresStoreIdVersionsGet**](docs/MasterDataStoresApi.md#apiV10MasterdataStoresStoreIdVersionsGet) | **GET** /api/v1.0/masterdata/stores/{storeId}/versions | Get store versions
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersContractsTypeSimpleGet**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersContractsTypeSimpleGet) | **GET** /api/v1.0/masterdata/suppliers/contracts/type/simple | Returns a list of RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 objects representing all available supplier contract types.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersGet**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersGet) | **GET** /api/v1.0/masterdata/suppliers | Returns all Suppliers for the authenticated user.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersPost**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersPost) | **POST** /api/v1.0/masterdata/suppliers | Creates a new supplier in the cloud store.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSimpleGet**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSimpleGet) | **GET** /api/v1.0/masterdata/suppliers/simple | Returns all Suppliers for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdContractContractIdDelete**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractContractIdDelete) | **DELETE** /api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId} | Deletes a contract.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete) | **DELETE** /api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId}/pdf | Removes a contract pdf from an existing contract.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost) | **POST** /api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId}/pdf | Adds a contract pdf file to an existing contract.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdContractContractIdPut**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractContractIdPut) | **PUT** /api/v1.0/masterdata/suppliers/{supplierId}/contract/{contractId} | Updates the given contract of the supplier.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdContractPost**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractPost) | **POST** /api/v1.0/masterdata/suppliers/{supplierId}/contract | Creates a new contract for a supplier.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdContractsContractIdGet**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractsContractIdGet) | **GET** /api/v1.0/masterdata/suppliers/{supplierId}/contracts/{contractId} | Returns the requested contract.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdContractsGet**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdContractsGet) | **GET** /api/v1.0/masterdata/suppliers/{supplierId}/contracts | Returns all contracts for a supplier.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdDelete**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdDelete) | **DELETE** /api/v1.0/masterdata/suppliers/{supplierId} | Deletes an supplier from cloud store.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdGet**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdGet) | **GET** /api/v1.0/masterdata/suppliers/{supplierId} | Returns the supplier requested by the given id.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdPut**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdPut) | **PUT** /api/v1.0/masterdata/suppliers/{supplierId} | Updates an supplier in the cloud store.
*MasterDataSuppliersApi* | [**apiV10MasterdataSuppliersSupplierIdSimpleGet**](docs/MasterDataSuppliersApi.md#apiV10MasterdataSuppliersSupplierIdSimpleGet) | **GET** /api/v1.0/masterdata/suppliers/{supplierId}/simple | Returns a simple supplierRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsGet**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsGet) | **GET** /api/v1.0/masterdata/terminals | Returns all terminals for the requested store for the authenticated user.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsPost**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsPost) | **POST** /api/v1.0/masterdata/terminals | Creates a terminal in the cloud store.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsSimpleGet**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsSimpleGet) | **GET** /api/v1.0/masterdata/terminals/simple | Returns all terminals as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested store for the authenticated user.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdArchivePost**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdArchivePost) | **POST** /api/v1.0/masterdata/terminals/{terminalId}/archive | Deactivates and archives the given terminal (cannot be undone).
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdDeactivatePut**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdDeactivatePut) | **PUT** /api/v1.0/masterdata/terminals/{terminalId}/deactivate | Deactivates a terminal temporarly (season functionality).
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdDecommissionDatePost**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdDecommissionDatePost) | **POST** /api/v1.0/masterdata/terminals/{terminalId}/decommissionDate | Set date of decommissioning for selected terminal in the cloud database.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdDelete**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdDelete) | **DELETE** /api/v1.0/masterdata/terminals/{terminalId} | Deletes a terminal from the cloud store.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdFiscaldataDelete**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdFiscaldataDelete) | **DELETE** /api/v1.0/masterdata/terminals/{terminalId}/fiscaldata | Deletes the cloud storage data of the test terminal. If called on a productive terminal exception / error will be raised.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdGet**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId} | Returns the requested terminal for the authenticated user.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdGet_0**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdGet_0) | **GET** /api/v1.0/masterdata/terminals/terminal/id | Returns the terminal id for the requested parameter.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost) | **POST** /api/v1.0/masterdata/terminals/{terminalId}/globalShortId | Generates a new or returns the already existing global short id for the terminal.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdHead**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdHead) | **HEAD** /api/v1.0/masterdata/terminals/{terminalId} | Test if access to terminal is allowed.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdInsightsGet**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdInsightsGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId}/insights | Returns terminal insights
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId}/iotAccessKey | Generates a new or returns the already existing encrypted iot access key for the terminal.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdPut**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdPut) | **PUT** /api/v1.0/masterdata/terminals/{terminalId} | Updates the terminal in the cloud store.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdSupportPackagesGet**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdSupportPackagesGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId}/supportPackages | Returns the last 15 support packages if available.
*MasterDataTerminalsApi* | [**apiV10MasterdataTerminalsTerminalIdVersionsGet**](docs/MasterDataTerminalsApi.md#apiV10MasterdataTerminalsTerminalIdVersionsGet) | **GET** /api/v1.0/masterdata/terminals/{terminalId}/versions | Get terminal versions
*MasterDatsaOrganisationsApi* | [**apiV10MasterdataOrganisationsSimpleGet**](docs/MasterDatsaOrganisationsApi.md#apiV10MasterdataOrganisationsSimpleGet) | **GET** /api/v1.0/masterdata/organisations/simple | Returns all organisations for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
*MoveToCloudApi* | [**apiV10MoveToCloudTerminalIdFinalizePut**](docs/MoveToCloudApi.md#apiV10MoveToCloudTerminalIdFinalizePut) | **PUT** /api/v1.0/moveToCloud/{terminalId}/finalize | Finalize the move from local to cloud for this terminal.
*MoveToCloudApi* | [**apiV10MoveToCloudTerminalIdStartPost**](docs/MoveToCloudApi.md#apiV10MoveToCloudTerminalIdStartPost) | **POST** /api/v1.0/moveToCloud/{terminalId}/start | Starts the move from local to cloud for this terminal.
*MoveToCloudApi* | [**apiV10MoveToCloudTerminalIdStateGet**](docs/MoveToCloudApi.md#apiV10MoveToCloudTerminalIdStateGet) | **GET** /api/v1.0/moveToCloud/{terminalId}/state | Get the state of the move 2 cloud task.
*NotificationApi* | [**apiV10NotificationsGet**](docs/NotificationApi.md#apiV10NotificationsGet) | **GET** /api/v1.0/notifications | Get all notifications for the authenticated user
*NotificationApi* | [**apiV10NotificationsInfoGet**](docs/NotificationApi.md#apiV10NotificationsInfoGet) | **GET** /api/v1.0/notifications/info | GetNotificationsInfo
*NotificationApi* | [**apiV10NotificationsNotificationIdDelete**](docs/NotificationApi.md#apiV10NotificationsNotificationIdDelete) | **DELETE** /api/v1.0/notifications/{notificationId} | Delete Notification
*NotificationApi* | [**apiV10NotificationsNotificationIdReadPut**](docs/NotificationApi.md#apiV10NotificationsNotificationIdReadPut) | **PUT** /api/v1.0/notifications/{notificationId}/read | Mark notification as read
*ReceiptsApi* | [**apiV10ReceiptsSearchOrganizationIdGet**](docs/ReceiptsApi.md#apiV10ReceiptsSearchOrganizationIdGet) | **GET** /api/v1.0/receipts/search/{organizationId} | Method to search and display receipts from digital receipt store (digital receipt license necessary).
*ReceiptsApi* | [**apiV10ReceiptsSearchOrganizationIdPost**](docs/ReceiptsApi.md#apiV10ReceiptsSearchOrganizationIdPost) | **POST** /api/v1.0/receipts/search/{organizationId} | Method to continue search by continuationToken.
*ReceiptsApi* | [**apiV10ReceiptsSearchOrganizationIdProcessIdGet**](docs/ReceiptsApi.md#apiV10ReceiptsSearchOrganizationIdProcessIdGet) | **GET** /api/v1.0/receipts/search/{organizationId}/{processId} | Method to search connected receipts (connected with processId, for instance to search for cancelled receipts and cancelling receipt).
*SearchApi* | [**apiV10SearchGet**](docs/SearchApi.md#apiV10SearchGet) | **GET** /api/v1.0/search | Retail force search
*SecurityApi* | [**apiV10SecurityAccessEntityIdDelete**](docs/SecurityApi.md#apiV10SecurityAccessEntityIdDelete) | **DELETE** /api/v1.0/security/access/{entityId} | Removes an principal access from an entity.
*SecurityApi* | [**apiV10SecurityAccessEntityIdGet**](docs/SecurityApi.md#apiV10SecurityAccessEntityIdGet) | **GET** /api/v1.0/security/access/{entityId} | Returns a list of principals which have access to the given entity. Entity must be of type organization or distributor.
*SecurityApi* | [**apiV10SecurityApikeyEntityIdGet**](docs/SecurityApi.md#apiV10SecurityApikeyEntityIdGet) | **GET** /api/v1.0/security/apikey/{entityId} | Returns all api keys for the given entity (organisation or distributor). The given entity must exist (organisation or distributor).
*SecurityApi* | [**apiV10SecurityApikeyEntityIdPost**](docs/SecurityApi.md#apiV10SecurityApikeyEntityIdPost) | **POST** /api/v1.0/security/apikey/{entityId} | Creates an api key for an organisation or a distributor. The given entity must exist (organisation or distributor).
*SecurityApi* | [**apiV10SecurityAssignEntityTypeEntityIdPut**](docs/SecurityApi.md#apiV10SecurityAssignEntityTypeEntityIdPut) | **PUT** /api/v1.0/security/assign/{entityType}/{entityId} | Self assign the current user to an entity, without the whole invitation process  this is only allowed for retail force users
*SecurityApi* | [**apiV10SecurityInvitationEntityTypeEntityIdPost**](docs/SecurityApi.md#apiV10SecurityInvitationEntityTypeEntityIdPost) | **POST** /api/v1.0/security/invitation/{entityType}/{entityId} | Creates a new invitation in the backend.
*SecurityApi* | [**apiV10SecurityInvitationGet**](docs/SecurityApi.md#apiV10SecurityInvitationGet) | **GET** /api/v1.0/security/invitation | Returns all open invitation which where sent for current organisation/distributor where authenticated principal has access.
*SecurityApi* | [**apiV10SecurityInvitationInvitationIdAcceptPut**](docs/SecurityApi.md#apiV10SecurityInvitationInvitationIdAcceptPut) | **PUT** /api/v1.0/security/invitation/{invitationId}/accept | Accepts an invitation.
*SecurityApi* | [**apiV10SecurityInvitationInvitationIdDeclinePut**](docs/SecurityApi.md#apiV10SecurityInvitationInvitationIdDeclinePut) | **PUT** /api/v1.0/security/invitation/{invitationId}/decline | Declines an invitation.
*SecurityApi* | [**apiV10SecurityInvitationInvitationIdDelete**](docs/SecurityApi.md#apiV10SecurityInvitationInvitationIdDelete) | **DELETE** /api/v1.0/security/invitation/{invitationId} | Deletes an existing invitation. If invitation does not exists, nothing happens.
*SecurityApi* | [**apiV10SecurityInvitationInvitationIdInfoGet**](docs/SecurityApi.md#apiV10SecurityInvitationInvitationIdInfoGet) | **GET** /api/v1.0/security/invitation/{invitationId}/info | Returns an information if the invitation exists, the email of the invited user and if the user already exists in the backend.
*SecurityApi* | [**apiV10SecurityInvitationInvitationIdPut**](docs/SecurityApi.md#apiV10SecurityInvitationInvitationIdPut) | **PUT** /api/v1.0/security/invitation/{invitationId} | Resends an existing invitation.
*SecurityApi* | [**apiV10SecurityInvitationUserGet**](docs/SecurityApi.md#apiV10SecurityInvitationUserGet) | **GET** /api/v1.0/security/invitation/user | Returns all open invitations for the authenticated principal.
*SecurityApi* | [**apiV10SecurityPasswordResetLinkPost**](docs/SecurityApi.md#apiV10SecurityPasswordResetLinkPost) | **POST** /api/v1.0/security/password/reset/link | Send / Resend password reset email
*SecurityApi* | [**apiV10SecurityPasswordResetTokenHead**](docs/SecurityApi.md#apiV10SecurityPasswordResetTokenHead) | **HEAD** /api/v1.0/security/password/reset/{token} | Send / Resend password reset email
*SecurityApi* | [**apiV10SecurityPasswordResetTokenPost**](docs/SecurityApi.md#apiV10SecurityPasswordResetTokenPost) | **POST** /api/v1.0/security/password/reset/{token} | Reset the password based on a reset password token
*SecurityApi* | [**apiV10SecurityPermissionsDisplayGet**](docs/SecurityApi.md#apiV10SecurityPermissionsDisplayGet) | **GET** /api/v1.0/security/permissions/display | Get display permissions
*SecurityApi* | [**apiV10SecurityPermissionsGet**](docs/SecurityApi.md#apiV10SecurityPermissionsGet) | **GET** /api/v1.0/security/permissions | Get entity permissions for requested type.
*SecurityApi* | [**apiV10SecurityPrincipalGet**](docs/SecurityApi.md#apiV10SecurityPrincipalGet) | **GET** /api/v1.0/security/principal | Returns the user information about the actual logged on user.
*SecurityApi* | [**apiV10SecurityPrincipalLockPrincipalIdPut**](docs/SecurityApi.md#apiV10SecurityPrincipalLockPrincipalIdPut) | **PUT** /api/v1.0/security/principal/lock/{principalId} | Possiblity to lock a principal.
*SecurityApi* | [**apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet**](docs/SecurityApi.md#apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet) | **GET** /api/v1.0/security/principal/organisation/{organisationId}/simple | Returns all principals which are available for this organization.
*SecurityApi* | [**apiV10SecurityPrincipalPictureGet**](docs/SecurityApi.md#apiV10SecurityPrincipalPictureGet) | **GET** /api/v1.0/security/principal/picture | Loads the user profile image as url or dataUrl (can be optimized to return multiple sizes at once with retina, but then it would be better to use a CDN than generating all of them)
*SecurityApi* | [**apiV10SecurityPrincipalPicturePost**](docs/SecurityApi.md#apiV10SecurityPrincipalPicturePost) | **POST** /api/v1.0/security/principal/picture | Return profile picture url or data url
*SecurityApi* | [**apiV10SecurityPrincipalPost**](docs/SecurityApi.md#apiV10SecurityPrincipalPost) | **POST** /api/v1.0/security/principal | Creates the principal with the given registration model and does authentication (refresh token cookie).
*SecurityApi* | [**apiV10SecurityPrincipalPut**](docs/SecurityApi.md#apiV10SecurityPrincipalPut) | **PUT** /api/v1.0/security/principal | Updates principal master data in the data store.
*SecurityApi* | [**apiV10SecurityPrincipalUnlockPrincipalIdPut**](docs/SecurityApi.md#apiV10SecurityPrincipalUnlockPrincipalIdPut) | **PUT** /api/v1.0/security/principal/unlock/{principalId} | Unlocks the given principal.
*SecurityApi* | [**apiV10SecuritySecretPost**](docs/SecurityApi.md#apiV10SecuritySecretPost) | **POST** /api/v1.0/security/secret | Changes the secret for the given authentication key.
*StaticApi* | [**apiV10ConfigurationBase64Post**](docs/StaticApi.md#apiV10ConfigurationBase64Post) | **POST** /api/v1.0/configuration/base64 | Returns the file containing the content from the base64 string.
*StaticApi* | [**apiV10ConfigurationCountriesGet**](docs/StaticApi.md#apiV10ConfigurationCountriesGet) | **GET** /api/v1.0/configuration/countries | Get Countries
*StaticApi* | [**apiV10ConfigurationCultureInfosGet**](docs/StaticApi.md#apiV10ConfigurationCultureInfosGet) | **GET** /api/v1.0/configuration/cultureInfos | Returns the available culture infos.
*SupportTicketApi* | [**apiV10SupportTicketChargePut**](docs/SupportTicketApi.md#apiV10SupportTicketChargePut) | **PUT** /api/v1.0/support-ticket/charge | Charge tickets
*SupportTicketApi* | [**apiV10SupportTicketGet**](docs/SupportTicketApi.md#apiV10SupportTicketGet) | **GET** /api/v1.0/support-ticket | Get SupportTickets
*SupportTicketApi* | [**apiV10SupportTicketOnboardingGet**](docs/SupportTicketApi.md#apiV10SupportTicketOnboardingGet) | **GET** /api/v1.0/support-ticket/onboarding | Returns a list of onboarding tickets to move support ticket to onboarding.
*SupportTicketApi* | [**apiV10SupportTicketOrderGet**](docs/SupportTicketApi.md#apiV10SupportTicketOrderGet) | **GET** /api/v1.0/support-ticket/order | Returns possible orders to map support tickets.
*SupportTicketApi* | [**apiV10SupportTicketSupportTicketNumberCustomerPut**](docs/SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberCustomerPut) | **PUT** /api/v1.0/support-ticket/{supportTicketNumber}/customer | AssignCustomer
*SupportTicketApi* | [**apiV10SupportTicketSupportTicketNumberOnboardingPost**](docs/SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberOnboardingPost) | **POST** /api/v1.0/support-ticket/{supportTicketNumber}/onboarding | Map a support ticket to onboarding of a customer.
*SupportTicketApi* | [**apiV10SupportTicketSupportTicketNumberOrderPost**](docs/SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberOrderPost) | **POST** /api/v1.0/support-ticket/{supportTicketNumber}/order | Map a support ticket to order of a customer.
*SupportTicketApi* | [**apiV10SupportTicketSupportTicketNumberStatusPut**](docs/SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberStatusPut) | **PUT** /api/v1.0/support-ticket/{supportTicketNumber}/status | UpdateChargeable status of ticket
*SupportTicketApi* | [**apiV10SupportTicketSupportTicketNumberTimelogGet**](docs/SupportTicketApi.md#apiV10SupportTicketSupportTicketNumberTimelogGet) | **GET** /api/v1.0/support-ticket/{supportTicketNumber}/timelog | Get Timelog for a support ticket
*ThemingApi* | [**apiV10ThemingThemeCssGet**](docs/ThemingApi.md#apiV10ThemingThemeCssGet) | **GET** /api/v1.0/theming/theme.css | Get Countries


## Documentation for Models

 - [AccessLicense](docs/AccessLicense.md)
 - [AccessLicenseAllocation](docs/AccessLicenseAllocation.md)
 - [AccessLicenseConfigurationInfo](docs/AccessLicenseConfigurationInfo.md)
 - [AccessLicenseConfigurationParameter](docs/AccessLicenseConfigurationParameter.md)
 - [AccessLicenseContract](docs/AccessLicenseContract.md)
 - [AccessLicensePageResultModel](docs/AccessLicensePageResultModel.md)
 - [Address](docs/Address.md)
 - [ApiKey](docs/ApiKey.md)
 - [AuditLogEntry](docs/AuditLogEntry.md)
 - [AuthenticationType](docs/AuthenticationType.md)
 - [AutomaticVatCalculation](docs/AutomaticVatCalculation.md)
 - [BackupData](docs/BackupData.md)
 - [BackupDataPageResultModel](docs/BackupDataPageResultModel.md)
 - [BarcodeType](docs/BarcodeType.md)
 - [BillingAccessLicenseCount](docs/BillingAccessLicenseCount.md)
 - [BillingDistributorLicenseCount](docs/BillingDistributorLicenseCount.md)
 - [BillingDistributorLicenseCountBillingLicenseOverview](docs/BillingDistributorLicenseCountBillingLicenseOverview.md)
 - [BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel](docs/BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel.md)
 - [BillingLicenseCount](docs/BillingLicenseCount.md)
 - [BillingLicenseCountBillingLicenseOverview](docs/BillingLicenseCountBillingLicenseOverview.md)
 - [BillingLicenseCountBillingLicenseOverviewPageResultModel](docs/BillingLicenseCountBillingLicenseOverviewPageResultModel.md)
 - [BillingLicenseDetail](docs/BillingLicenseDetail.md)
 - [BillingLicenseDetailPageResultModel](docs/BillingLicenseDetailPageResultModel.md)
 - [BoolResponse](docs/BoolResponse.md)
 - [BusinessSector](docs/BusinessSector.md)
 - [BusinessTransactionType](docs/BusinessTransactionType.md)
 - [BusinessTransactionTypeDocumentTypeMapping](docs/BusinessTransactionTypeDocumentTypeMapping.md)
 - [CashRegister](docs/CashRegister.md)
 - [CashRegisterDropoutReason](docs/CashRegisterDropoutReason.md)
 - [Certificate](docs/Certificate.md)
 - [CertificateFormat](docs/CertificateFormat.md)
 - [CertificateModel](docs/CertificateModel.md)
 - [ClearingRun](docs/ClearingRun.md)
 - [ClientConfigurationGermany](docs/ClientConfigurationGermany.md)
 - [CloudParameter](docs/CloudParameter.md)
 - [Company](docs/Company.md)
 - [CompanyIdentification](docs/CompanyIdentification.md)
 - [CompanyModel](docs/CompanyModel.md)
 - [CompanyModelPageResultModel](docs/CompanyModelPageResultModel.md)
 - [ConfigLicenseModel](docs/ConfigLicenseModel.md)
 - [ConfigurationParameter](docs/ConfigurationParameter.md)
 - [ControlUnitDriver](docs/ControlUnitDriver.md)
 - [ControlUnitDriverInfo](docs/ControlUnitDriverInfo.md)
 - [CosmosDocument](docs/CosmosDocument.md)
 - [DashboardTerminalDetail](docs/DashboardTerminalDetail.md)
 - [DashboardTerminalDetailPageResultModel](docs/DashboardTerminalDetailPageResultModel.md)
 - [DashboardTerminalOverview](docs/DashboardTerminalOverview.md)
 - [DashboardTerminalOverviewPageResultModel](docs/DashboardTerminalOverviewPageResultModel.md)
 - [DigitalReceipt](docs/DigitalReceipt.md)
 - [Distributor](docs/Distributor.md)
 - [DistributorContract](docs/DistributorContract.md)
 - [DistributorContractModel](docs/DistributorContractModel.md)
 - [DistributorContractModelPageResultModel](docs/DistributorContractModelPageResultModel.md)
 - [DistributorModel](docs/DistributorModel.md)
 - [DistributorModelPageResultModel](docs/DistributorModelPageResultModel.md)
 - [Document](docs/Document.md)
 - [DocumentCoupon](docs/DocumentCoupon.md)
 - [DocumentCouponTextLine](docs/DocumentCouponTextLine.md)
 - [DocumentIssueType](docs/DocumentIssueType.md)
 - [DocumentPayment](docs/DocumentPayment.md)
 - [DocumentPaymentCardData](docs/DocumentPaymentCardData.md)
 - [DocumentPositionBase](docs/DocumentPositionBase.md)
 - [DocumentPositionReference](docs/DocumentPositionReference.md)
 - [DocumentPositionType](docs/DocumentPositionType.md)
 - [DocumentReference](docs/DocumentReference.md)
 - [DocumentTaxPosition](docs/DocumentTaxPosition.md)
 - [DocumentType](docs/DocumentType.md)
 - [DownloadLink](docs/DownloadLink.md)
 - [DownloadLinkPageResultModel](docs/DownloadLinkPageResultModel.md)
 - [EntityParameterInfo](docs/EntityParameterInfo.md)
 - [EntityPermissions](docs/EntityPermissions.md)
 - [EntitySecurity](docs/EntitySecurity.md)
 - [EntitySecurityPageResultModel](docs/EntitySecurityPageResultModel.md)
 - [EntityTypes](docs/EntityTypes.md)
 - [ErrorLevel](docs/ErrorLevel.md)
 - [FiscalClient](docs/FiscalClient.md)
 - [FiscalClientConfiguration](docs/FiscalClientConfiguration.md)
 - [FiscalClientConfigurationModel](docs/FiscalClientConfigurationModel.md)
 - [FiscalCountry](docs/FiscalCountry.md)
 - [FiscalModuleEnvironment](docs/FiscalModuleEnvironment.md)
 - [FiscalResponse](docs/FiscalResponse.md)
 - [FiscalisationType](docs/FiscalisationType.md)
 - [FonConnectionLogMessage](docs/FonConnectionLogMessage.md)
 - [FonConnectionLogMessagePageResultModel](docs/FonConnectionLogMessagePageResultModel.md)
 - [FonCredentials](docs/FonCredentials.md)
 - [GuidBreadCrumb](docs/GuidBreadCrumb.md)
 - [GuidEntityVersion](docs/GuidEntityVersion.md)
 - [GuidEntityVersionPageResultModel](docs/GuidEntityVersionPageResultModel.md)
 - [GuidExtendedSimpleCountryObject](docs/GuidExtendedSimpleCountryObject.md)
 - [GuidExtendedSimpleCountryObjectPageResultModel](docs/GuidExtendedSimpleCountryObjectPageResultModel.md)
 - [GuidHierarchicalSimpleObject](docs/GuidHierarchicalSimpleObject.md)
 - [GuidHierarchicalSimpleObjectPageResultModel](docs/GuidHierarchicalSimpleObjectPageResultModel.md)
 - [GuidSimpleObject](docs/GuidSimpleObject.md)
 - [GuidSimpleObjectPageResultModel](docs/GuidSimpleObjectPageResultModel.md)
 - [HelpInformation](docs/HelpInformation.md)
 - [IFiscalCountryProperties](docs/IFiscalCountryProperties.md)
 - [IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport](docs/IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport.md)
 - [IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt](docs/IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md)
 - [IFiscalCountryPropertiesPageResultModel](docs/IFiscalCountryPropertiesPageResultModel.md)
 - [IFiscalImplementationConfiguration](docs/IFiscalImplementationConfiguration.md)
 - [INotification](docs/INotification.md)
 - [IdentificationType](docs/IdentificationType.md)
 - [ImportModel](docs/ImportModel.md)
 - [ImportModelPageResultModel](docs/ImportModelPageResultModel.md)
 - [Int32SimpleObject](docs/Int32SimpleObject.md)
 - [Int32SimpleObjectPageResultModel](docs/Int32SimpleObjectPageResultModel.md)
 - [InvitationInfoModel](docs/InvitationInfoModel.md)
 - [InvitationModel](docs/InvitationModel.md)
 - [JwtLicenseClaim](docs/JwtLicenseClaim.md)
 - [LegalForm](docs/LegalForm.md)
 - [License](docs/License.md)
 - [LicenseDetailUsage](docs/LicenseDetailUsage.md)
 - [LicenseModel](docs/LicenseModel.md)
 - [LicenseModelPageResultModel](docs/LicenseModelPageResultModel.md)
 - [LicenseOption](docs/LicenseOption.md)
 - [LogEntryType](docs/LogEntryType.md)
 - [MessageApiModel](docs/MessageApiModel.md)
 - [NotificationResult](docs/NotificationResult.md)
 - [NotificationType](docs/NotificationType.md)
 - [NotificationsInfo](docs/NotificationsInfo.md)
 - [OfflineConfiguration](docs/OfflineConfiguration.md)
 - [OnboardingFinishData](docs/OnboardingFinishData.md)
 - [OnboardingFinishDistributorData](docs/OnboardingFinishDistributorData.md)
 - [Organisation](docs/Organisation.md)
 - [OrganisationModel](docs/OrganisationModel.md)
 - [OrganisationModelPageResultModel](docs/OrganisationModelPageResultModel.md)
 - [OutstandingPayment](docs/OutstandingPayment.md)
 - [ParameterInfo](docs/ParameterInfo.md)
 - [Partner](docs/Partner.md)
 - [PartnerIdentificationType](docs/PartnerIdentificationType.md)
 - [PartnerType](docs/PartnerType.md)
 - [PayOutType](docs/PayOutType.md)
 - [PaymentTerms](docs/PaymentTerms.md)
 - [PaymentType](docs/PaymentType.md)
 - [PlatformType](docs/PlatformType.md)
 - [Principal](docs/Principal.md)
 - [PrinterDriverInfo](docs/PrinterDriverInfo.md)
 - [PrinterImageFile](docs/PrinterImageFile.md)
 - [PrinterModel](docs/PrinterModel.md)
 - [ProfilePictureModel](docs/ProfilePictureModel.md)
 - [ReceiptData](docs/ReceiptData.md)
 - [ReceiptDataScrollResultModel](docs/ReceiptDataScrollResultModel.md)
 - [ReceiptDomain](docs/ReceiptDomain.md)
 - [ReferenceType](docs/ReferenceType.md)
 - [RegistrationModel](docs/RegistrationModel.md)
 - [Release](docs/Release.md)
 - [ResultResponse](docs/ResultResponse.md)
 - [ReturnAndSaleBehavior](docs/ReturnAndSaleBehavior.md)
 - [ReturnReasonType](docs/ReturnReasonType.md)
 - [ReturnReferenceBehavior](docs/ReturnReferenceBehavior.md)
 - [Salutation](docs/Salutation.md)
 - [SearchResultModel](docs/SearchResultModel.md)
 - [SearchResultModelPageResultModel](docs/SearchResultModelPageResultModel.md)
 - [SecurityCertificateDropoutReason](docs/SecurityCertificateDropoutReason.md)
 - [SecurityCertificateIssuer](docs/SecurityCertificateIssuer.md)
 - [SecurityCertificateType](docs/SecurityCertificateType.md)
 - [Session](docs/Session.md)
 - [SignDeviceDriver](docs/SignDeviceDriver.md)
 - [SignDeviceDriverInfo](docs/SignDeviceDriverInfo.md)
 - [SignatureDevice](docs/SignatureDevice.md)
 - [Software](docs/Software.md)
 - [Store](docs/Store.md)
 - [StoreModel](docs/StoreModel.md)
 - [StoreModelPageResultModel](docs/StoreModelPageResultModel.md)
 - [StringSimpleObject](docs/StringSimpleObject.md)
 - [StringSimpleObjectPageResultModel](docs/StringSimpleObjectPageResultModel.md)
 - [Supplier](docs/Supplier.md)
 - [SupplierContract](docs/SupplierContract.md)
 - [SupplierContractModel](docs/SupplierContractModel.md)
 - [SupplierContractModelPageResultModel](docs/SupplierContractModelPageResultModel.md)
 - [SupplierModel](docs/SupplierModel.md)
 - [SupplierModelPageResultModel](docs/SupplierModelPageResultModel.md)
 - [SupportTicketModel](docs/SupportTicketModel.md)
 - [SupportTicketModelPageResultModel](docs/SupportTicketModelPageResultModel.md)
 - [SupportTicketSimple](docs/SupportTicketSimple.md)
 - [SupportTicketStatus](docs/SupportTicketStatus.md)
 - [TaxonomyCloudStoreConfiguration](docs/TaxonomyCloudStoreConfiguration.md)
 - [TaxonomyFileStoreConfiguration](docs/TaxonomyFileStoreConfiguration.md)
 - [Terminal](docs/Terminal.md)
 - [TerminalContactType](docs/TerminalContactType.md)
 - [TerminalInsightModel](docs/TerminalInsightModel.md)
 - [TerminalModel](docs/TerminalModel.md)
 - [TerminalModelPageResultModel](docs/TerminalModelPageResultModel.md)
 - [TerminalType](docs/TerminalType.md)
 - [TimelogOverviewModel](docs/TimelogOverviewModel.md)
 - [TseAnnouncementCreateResult](docs/TseAnnouncementCreateResult.md)
 - [TseAnnouncementOverview](docs/TseAnnouncementOverview.md)
 - [TseAnnouncementOverviewPageResultModel](docs/TseAnnouncementOverviewPageResultModel.md)
 - [TseAnnouncementProgress](docs/TseAnnouncementProgress.md)
 - [TseAnnouncementStatus](docs/TseAnnouncementStatus.md)
 - [TseAnnouncementStatusInfo](docs/TseAnnouncementStatusInfo.md)
 - [TseConfiguration](docs/TseConfiguration.md)
 - [TseDriver](docs/TseDriver.md)
 - [TseDriverInfo](docs/TseDriverInfo.md)
 - [TseInformationOverview](docs/TseInformationOverview.md)
 - [TseInformationOverviewPageResultModel](docs/TseInformationOverviewPageResultModel.md)
 - [TseType](docs/TseType.md)
 - [User](docs/User.md)
 - [ValidationError](docs/ValidationError.md)
 - [Vat](docs/Vat.md)


<a id="documentation-for-authorization"></a>
## Documentation for Authorization


Authentication schemes defined for the API:
<a id="Bearer"></a>
### Bearer

- **Type**: API key
- **API key parameter name**: Authorization
- **Location**: HTTP header


## Recommendation

It's recommended to create an instance of `ApiClient` per thread in a multithreaded environment to avoid any potential issues.

## Author



