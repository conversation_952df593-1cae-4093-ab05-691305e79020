

# LogEntryType

## Enum


* `DOCUMENTREPRINTOTHER` (value: `"documentReprintOther"`)

* `DOCUMENTSUSPEND` (value: `"documentSuspend"`)

* `DOCUMENTRESUME` (value: `"documentResume"`)

* `DOCUMENTTRAININGMODEON` (value: `"documentTrainingModeOn"`)

* `DOCUMENTTRAININGMODEOFF` (value: `"documentTrainingModeOff"`)

* `DOCUMENTTYPESALES` (value: `"documentTypeSales"`)

* `DOCUMENTTYPERETURN` (value: `"documentTypeReturn"`)

* `DOCUMENTTYPEPROFORMA` (value: `"documentTypeProforma"`)

* `DOCUMENTTYPEDELIVERY` (value: `"documentTypeDelivery"`)

* `DOCUMENTTYPETRAINING` (value: `"documentTypeTraining"`)

* `DOCUMENTTYPEWITHDRAWAL` (value: `"documentTypeWithDrawal"`)

* `DOCUMENTTYPEPAYOUT` (value: `"documentTypePayOut"`)

* `DOCUMENTTYPEPAYIN` (value: `"documentTypePayIn"`)

* `DOCUMENTTYPEOPENINGBALANCE` (value: `"documentTypeOpeningBalance"`)

* `DOCUMENTTYPENULLRECEIPT` (value: `"documentTypeNullReceipt"`)

* `DOCUMENTTYPEINVOICE` (value: `"documentTypeInvoice"`)

* `DOCUMENTTYPEOTHER` (value: `"documentTypeOther"`)

* `DOCUMENTTYPEVOID` (value: `"documentTypeVoid"`)

* `DOCUMENTTYPELONGTERMORDER` (value: `"documentTypeLongTermOrder"`)

* `DOCUMENTTYPEPAYMENTCONFIRMATION` (value: `"documentTypePaymentConfirmation"`)

* `DOCUMENTTYPECUSTOMERORDER` (value: `"documentTypeCustomerOrder"`)

* `DOCUMENTTYPEINVENTORY` (value: `"documentTypeInventory"`)

* `DOCUMENTTYPEPURCHASE` (value: `"documentTypePurchase"`)

* `DOCUMENTREPRINTLONGTERMORDER` (value: `"documentReprintLongTermOrder"`)

* `DOCUMENTREPRINTPAYMENTCONFIRMATION` (value: `"documentReprintPaymentConfirmation"`)

* `DOCUMENTABANDONLONGTERMORDER` (value: `"documentAbandonLongTermOrder"`)

* `DOCUMENTTRACEUNPRINTEDLONGTERMORDER` (value: `"documentTraceUnprintedLongTermOrder"`)

* `DOCUMENTCANCELLINE` (value: `"documentCancelLine"`)

* `DOCUMENTVOIDLINE` (value: `"documentVoidLine"`)

* `DOCUMENTUPDATEPAYMENT` (value: `"documentUpdatePayment"`)

* `DOCUMENTUPDATELONGTERMORDER` (value: `"documentUpdateLongTermOrder"`)

* `DOCUMENTUPDATELINE` (value: `"documentUpdateLine"`)

* `DOCUMENTUPDATETABLE` (value: `"documentUpdateTable"`)

* `DRAWEROPEN` (value: `"drawerOpen"`)

* `DRAWERCLOSE` (value: `"drawerClose"`)

* `PRINTERUNAVAILABLE` (value: `"printerUnavailable"`)

* `PRINTERAVAILABLE` (value: `"printerAvailable"`)

* `USERLOGIN` (value: `"userLogin"`)

* `USERLOGOUT` (value: `"userLogout"`)

* `USERRIGHTSCHANGE` (value: `"userRightsChange"`)

* `APPLICATIONSTART` (value: `"applicationStart"`)

* `APPLICATIONSHUTDOWN` (value: `"applicationShutDown"`)

* `APPLICATIONUPDATE` (value: `"applicationUpdate"`)

* `APPLICATIONUPDATEMINOR` (value: `"applicationUpdateMinor"`)

* `APPLICATIONFISCALMIDDLEWAREUPDATE` (value: `"applicationFiscalMiddlewareUpdate"`)

* `APPLICATIONEMERGENCYMODEON` (value: `"applicationEmergencyModeOn"`)

* `APPLICATIONEMERGENCYMODEOFF` (value: `"applicationEmergencyModeOff"`)

* `APPLICATIONINITIALIZE` (value: `"applicationInitialize"`)

* `APPLICATIONADDWORKSTATION` (value: `"applicationAddWorkstation"`)

* `APPLICATIONDELETEWORKSTATION` (value: `"applicationDeleteWorkstation"`)

* `MASTERDATAITEMPRICECHANGE` (value: `"masterDataItemPriceChange"`)

* `MASTERDATAITEMPRICELOOKUP` (value: `"masterDataItemPriceLookup"`)

* `MASTERDATAITEMUPDATECOMPANY` (value: `"masterDataItemUpdateCompany"`)

* `TRANSFEROWNERSHIP` (value: `"transferOwnership"`)

* `DATAINTEGRITYBACKUPDATABASE` (value: `"dataIntegrityBackupDatabase"`)

* `DATAINTEGRITYRESTOREDATABASE` (value: `"dataIntegrityRestoreDatabase"`)

* `DATAINTEGRITYPURGEDATABASE` (value: `"dataIntegrityPurgeDatabase"`)

* `DATAINTEGRITYPURGEEVENTLOG` (value: `"dataIntegrityPurgeEventLog"`)

* `DATAINTEGRITYDATARECOVERY` (value: `"dataIntegrityDataRecovery"`)

* `DATAINTEGRITYFORWARDDATA` (value: `"dataIntegrityForwardData"`)

* `DATAINTEGRITYCONSOLIDATIONCOMPLETE` (value: `"dataIntegrityConsolidationComplete"`)

* `DATAINTEGRITYFAILURE` (value: `"dataIntegrityFailure"`)

* `DATASEQUENCECHANGE` (value: `"dataSequenceChange"`)

* `DATASEQUENCEFAILURE` (value: `"dataSequenceFailure"`)

* `DATAIMPORTEXTERNAL` (value: `"dataImportExternal"`)

* `DATAEXPORTACCOUNTING` (value: `"dataExportAccounting"`)

* `DATAEXPORTGENERIC` (value: `"dataExportGeneric"`)

* `FISCALPERIODCLOSINGDAY` (value: `"fiscalPeriodClosingDay"`)

* `FISCALPERIODCLOSINGMONTH` (value: `"fiscalPeriodClosingMonth"`)

* `FISCALPERIODCLOSINGYEAR` (value: `"fiscalPeriodClosingYear"`)

* `FISCALREPORTCLOSINGDAY` (value: `"fiscalReportClosingDay"`)

* `FISCALARCHIVEYEAR` (value: `"fiscalArchiveYear"`)

* `FISCALARCHIVEINTERMEDIATE` (value: `"fiscalArchiveIntermediate"`)

* `FISCALJOURNALEXPORT` (value: `"fiscalJournalExport"`)

* `FISCALSIGNATUREUPDATEKEY` (value: `"fiscalSignatureUpdateKey"`)

* `FISCALAUDIT` (value: `"fiscalAudit"`)

* `FISCALAUDITFILETRANSFER` (value: `"fiscalAuditFileTransfer"`)

* `OTHER` (value: `"other"`)



