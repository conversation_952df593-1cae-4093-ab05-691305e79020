/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.AccessLicense;
import com.untill.retailforce.model.AccessLicenseAllocation;
import com.untill.retailforce.model.AccessLicenseContract;
import com.untill.retailforce.model.AccessLicensePageResultModel;
import com.untill.retailforce.model.BillingLicenseCount;
import com.untill.retailforce.model.ConfigLicenseModel;
import com.untill.retailforce.model.JwtLicenseClaim;
import com.untill.retailforce.model.License;
import com.untill.retailforce.model.LicenseModel;
import com.untill.retailforce.model.LicenseModelPageResultModel;
import com.untill.retailforce.model.StringSimpleObject;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for LicencingApi
 */
@Disabled
public class LicencingApiTest {

    private final LicencingApi api = new LicencingApi();

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteTest() throws ApiException {
        String accessLicenseId = null;
        api.apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete(accessLicenseId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetTest() throws ApiException {
        String accessLicenseId = null;
        AccessLicense response = api.apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet(accessLicenseId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutTest() throws ApiException {
        String accessLicenseId = null;
        AccessLicense accessLicense = null;
        AccessLicense response = api.apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut(accessLicenseId, accessLicense);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAccessLicenseGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        AccessLicensePageResultModel response = api.apiV10LicensingLicenseAccessLicenseGet(pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAccessLicensePostTest() throws ApiException {
        AccessLicense accessLicense = null;
        AccessLicense response = api.apiV10LicensingLicenseAccessLicensePost(accessLicense);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteTest() throws ApiException {
        String accessLicenseId = null;
        UUID supplierId = null;
        UUID contractId = null;
        List<AccessLicenseContract> response = api.apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete(accessLicenseId, supplierId, contractId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetTest() throws ApiException {
        String accessLicenseId = null;
        List<AccessLicenseContract> response = api.apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet(accessLicenseId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostTest() throws ApiException {
        String accessLicenseId = null;
        UUID supplierId = null;
        UUID contractId = null;
        List<AccessLicenseContract> response = api.apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost(accessLicenseId, supplierId, contractId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAdminCreateallcontainersPatchTest() throws ApiException {
        api.apiV10LicensingLicenseAdminCreateallcontainersPatch();
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteTest() throws ApiException {
        String licenseId = null;
        String accessLicenseId = null;
        List<AccessLicenseAllocation> response = api.apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete(licenseId, accessLicenseId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAllocationLicenseIdGetTest() throws ApiException {
        String licenseId = null;
        List<AccessLicenseAllocation> response = api.apiV10LicensingLicenseAllocationLicenseIdGet(licenseId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseAllocationLicenseIdPostTest() throws ApiException {
        String licenseId = null;
        AccessLicenseAllocation accessLicenseAllocation = null;
        List<AccessLicenseAllocation> response = api.apiV10LicensingLicenseAllocationLicenseIdPost(licenseId, accessLicenseAllocation);
        // TODO: test validations
    }

    /**
     * Returns the possible licenses for the requested organisation.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseConfigurationConfigurationIdGetTest() throws ApiException {
        UUID configurationId = null;
        List<ConfigLicenseModel> response = api.apiV10LicensingLicenseConfigurationConfigurationIdGet(configurationId);
        // TODO: test validations
    }

    /**
     * Updates licenses to the given configuration.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseConfigurationConfigurationIdPutTest() throws ApiException {
        UUID configurationId = null;
        List<LicenseModel> licenseModel = null;
        List<LicenseModel> response = api.apiV10LicensingLicenseConfigurationConfigurationIdPut(configurationId, licenseModel);
        // TODO: test validations
    }

    /**
     * Returns the used licenses by this configuration.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseConfigurationConfigurationIdUsageGetTest() throws ApiException {
        UUID configurationId = null;
        List<BillingLicenseCount> response = api.apiV10LicensingLicenseConfigurationConfigurationIdUsageGet(configurationId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseConfigurationGroupsGetTest() throws ApiException {
        List<StringSimpleObject> response = api.apiV10LicensingLicenseConfigurationGroupsGet();
        // TODO: test validations
    }

    /**
     * Returns all available license units.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseConfigurationUnitsGetTest() throws ApiException {
        List<StringSimpleObject> response = api.apiV10LicensingLicenseConfigurationUnitsGet();
        // TODO: test validations
    }

    /**
     * Returns the effective licenses for the given entity.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseEffectiveEntityIdGetTest() throws ApiException {
        UUID entityId = null;
        List<LicenseModel> response = api.apiV10LicensingLicenseEffectiveEntityIdGet(entityId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        LicenseModelPageResultModel response = api.apiV10LicensingLicenseGet(pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseLicenseIdDeleteTest() throws ApiException {
        String licenseId = null;
        api.apiV10LicensingLicenseLicenseIdDelete(licenseId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseLicenseIdGetTest() throws ApiException {
        String licenseId = null;
        License response = api.apiV10LicensingLicenseLicenseIdGet(licenseId);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicenseLicenseIdPutTest() throws ApiException {
        String licenseId = null;
        License license = null;
        License response = api.apiV10LicensingLicenseLicenseIdPut(licenseId, license);
        // TODO: test validations
    }

    /**
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingLicensePostTest() throws ApiException {
        License license = null;
        License response = api.apiV10LicensingLicensePost(license);
        // TODO: test validations
    }

    /**
     * Returns a license token (jwt) to use for requests for azure stateless functions.
     *
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingTokenAccessCounterGetTest() throws ApiException {
        UUID uniqueClientId = null;
        String accesslicenseId = null;
        String response = api.apiV10LicensingTokenAccessCounterGet(uniqueClientId, accesslicenseId);
        // TODO: test validations
    }

    /**
     * Returns a license token (jwt) to use for requests for azure stateless functions.
     *
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingTokenGetTest() throws ApiException {
        UUID uniqueClientId = null;
        String accesslicenseId = null;
        String response = api.apiV10LicensingTokenGet(uniqueClientId, accesslicenseId);
        // TODO: test validations
    }

    /**
     * Returns the license token (jwt) including all licenses for the requested terminal.
     *
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingTokenTerminalIdGetTest() throws ApiException {
        UUID terminalId = null;
        String clientVersion = null;
        String response = api.apiV10LicensingTokenTerminalIdGet(terminalId, clientVersion);
        // TODO: test validations
    }

    /**
     * Validates the given license token and returns a RetailForce.Cloud.Functions.Entities.JwtLicenseClaim object containing the license of the token.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10LicensingTokenValidateGetTest() throws ApiException {
        UUID uniqueClientId = null;
        String licenseToken = null;
        JwtLicenseClaim response = api.apiV10LicensingTokenValidateGet(uniqueClientId, licenseToken);
        // TODO: test validations
    }

}
