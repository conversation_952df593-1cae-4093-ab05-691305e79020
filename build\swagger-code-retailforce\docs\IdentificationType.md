

# IdentificationType

## Enum


* `_0_VATNUMBER` (value: `"[0] = VatNumber"`)

* `_1_TAXNUMBER` (value: `"[1] = TaxNumber"`)

* `_2_GLNNUMBER` (value: `"[2] = GlnNumber"`)

* `_3_BUSINESSIDENTIFICATIONNUMBER` (value: `"[3] = BusinessIdentificationNumber"`)

* `_4_STATISTICAL_CLASSIFICATION` (value: `"[4] = Statistical classification"`)

* `_5_COMMERCIALREGISTERNUMBER` (value: `"[5] = CommercialRegisterNumber"`)

* `_6_COMMERCIALREGISTERNUMBEROFFICE` (value: `"[6] = CommercialRegisterNumberOffice"`)

* `_7_TRADEREGISTERNUMBER` (value: `"[7] = TradeRegisterNumber"`)

* `_8_TAXOFFICENUMBER` (value: `"[8] = TaxOfficeNumber"`)



