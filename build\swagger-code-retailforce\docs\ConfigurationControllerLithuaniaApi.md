# ConfigurationControllerLithuaniaApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10ConfigurationLtCertificatesDistributorIdPost**](ConfigurationControllerLithuaniaApi.md#apiV10ConfigurationLtCertificatesDistributorIdPost) | **POST** /api/v1.0/configuration/lt/certificates/{distributorId} |  |
| [**apiV10ConfigurationLtCertificatesTerminalIdSecurityGet**](ConfigurationControllerLithuaniaApi.md#apiV10ConfigurationLtCertificatesTerminalIdSecurityGet) | **GET** /api/v1.0/configuration/lt/certificates/{terminalId}/security |  |
| [**apiV10ConfigurationLtCertificatesTerminalIdTransportGet**](ConfigurationControllerLithuaniaApi.md#apiV10ConfigurationLtCertificatesTerminalIdTransportGet) | **GET** /api/v1.0/configuration/lt/certificates/{terminalId}/transport |  |


<a id="apiV10ConfigurationLtCertificatesDistributorIdPost"></a>
# **apiV10ConfigurationLtCertificatesDistributorIdPost**
> apiV10ConfigurationLtCertificatesDistributorIdPost(distributorId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerLithuaniaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerLithuaniaApi apiInstance = new ConfigurationControllerLithuaniaApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | 
    try {
      apiInstance.apiV10ConfigurationLtCertificatesDistributorIdPost(distributorId);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerLithuaniaApi#apiV10ConfigurationLtCertificatesDistributorIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**|  | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationLtCertificatesTerminalIdSecurityGet"></a>
# **apiV10ConfigurationLtCertificatesTerminalIdSecurityGet**
> Certificate apiV10ConfigurationLtCertificatesTerminalIdSecurityGet(terminalId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerLithuaniaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerLithuaniaApi apiInstance = new ConfigurationControllerLithuaniaApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | 
    try {
      Certificate result = apiInstance.apiV10ConfigurationLtCertificatesTerminalIdSecurityGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerLithuaniaApi#apiV10ConfigurationLtCertificatesTerminalIdSecurityGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**|  | |

### Return type

[**Certificate**](Certificate.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10ConfigurationLtCertificatesTerminalIdTransportGet"></a>
# **apiV10ConfigurationLtCertificatesTerminalIdTransportGet**
> Certificate apiV10ConfigurationLtCertificatesTerminalIdTransportGet(terminalId)



### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.ConfigurationControllerLithuaniaApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    ConfigurationControllerLithuaniaApi apiInstance = new ConfigurationControllerLithuaniaApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | 
    try {
      Certificate result = apiInstance.apiV10ConfigurationLtCertificatesTerminalIdTransportGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling ConfigurationControllerLithuaniaApi#apiV10ConfigurationLtCertificatesTerminalIdTransportGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**|  | |

### Return type

[**Certificate**](Certificate.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

