

# RegistrationModel

Data to create a new principal with security access to the given entity.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**name1** | **String** | The name1 (firstname) of the new principal. |  [optional] |
|**name2** | **String** | The name2 (lastname) of the new principal. |  [optional] |
|**cultureInfo** | **String** | The language of the new principal. |  [optional] |
|**email** | **String** | The email address of the new principal. |  [optional] |
|**type** | **AuthenticationType** |  |  [optional] |
|**secret** | **String** | The secret of the new logon. |  [optional] |
|**invitationId** | **UUID** | The id of the invitation. |  [optional] |



