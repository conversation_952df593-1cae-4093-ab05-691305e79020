/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.DownloadLink;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.SupportTicketModel;
import com.untill.retailforce.model.SupportTicketModelPageResultModel;
import com.untill.retailforce.model.SupportTicketSimple;
import com.untill.retailforce.model.SupportTicketStatus;
import com.untill.retailforce.model.TimelogOverviewModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for SupportTicketApi
 */
@Disabled
public class SupportTicketApiTest {

    private final SupportTicketApi api = new SupportTicketApi();

    /**
     * Charge tickets
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketChargePutTest() throws ApiException {
        DownloadLink response = api.apiV10SupportTicketChargePut();
        // TODO: test validations
    }

    /**
     * Get SupportTickets
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        SupportTicketStatus statusFilter = null;
        String searchString = null;
        SupportTicketModelPageResultModel response = api.apiV10SupportTicketGet(pageOffset, pageSize, statusFilter, searchString);
        // TODO: test validations
    }

    /**
     * Returns a list of onboarding tickets to move support ticket to onboarding.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketOnboardingGetTest() throws ApiException {
        List<SupportTicketSimple> response = api.apiV10SupportTicketOnboardingGet();
        // TODO: test validations
    }

    /**
     * Returns possible orders to map support tickets.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketOrderGetTest() throws ApiException {
        String supportTicketNumber = null;
        List<GuidSimpleObject> response = api.apiV10SupportTicketOrderGet(supportTicketNumber);
        // TODO: test validations
    }

    /**
     * AssignCustomer
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketSupportTicketNumberCustomerPutTest() throws ApiException {
        String supportTicketNumber = null;
        UUID customerId = null;
        SupportTicketModel response = api.apiV10SupportTicketSupportTicketNumberCustomerPut(supportTicketNumber, customerId);
        // TODO: test validations
    }

    /**
     * Map a support ticket to onboarding of a customer.
     *
     * Only hours in time tracking were moved to onboarding which are not already charged.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketSupportTicketNumberOnboardingPostTest() throws ApiException {
        String supportTicketNumber = null;
        UUID onboardingTicketNumber = null;
        SupportTicketModel response = api.apiV10SupportTicketSupportTicketNumberOnboardingPost(supportTicketNumber, onboardingTicketNumber);
        // TODO: test validations
    }

    /**
     * Map a support ticket to order of a customer.
     *
     * Only hours in time tracking were moved to order which are not already charged.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketSupportTicketNumberOrderPostTest() throws ApiException {
        String supportTicketNumber = null;
        UUID orderTicketNumber = null;
        SupportTicketModel response = api.apiV10SupportTicketSupportTicketNumberOrderPost(supportTicketNumber, orderTicketNumber);
        // TODO: test validations
    }

    /**
     * UpdateChargeable status of ticket
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketSupportTicketNumberStatusPutTest() throws ApiException {
        String supportTicketNumber = null;
        Boolean chargeable = null;
        SupportTicketModel response = api.apiV10SupportTicketSupportTicketNumberStatusPut(supportTicketNumber, chargeable);
        // TODO: test validations
    }

    /**
     * Get Timelog for a support ticket
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10SupportTicketSupportTicketNumberTimelogGetTest() throws ApiException {
        String supportTicketNumber = null;
        List<TimelogOverviewModel> response = api.apiV10SupportTicketSupportTicketNumberTimelogGet(supportTicketNumber);
        // TODO: test validations
    }

}
