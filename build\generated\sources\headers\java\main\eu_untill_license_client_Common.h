/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class eu_untill_license_client_Common */

#ifndef _Included_eu_untill_license_client_Common
#define _Included_eu_untill_license_client_Common
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     eu_untill_license_client_Common
 * Method:    export
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_eu_untill_license_client_Common_export
  (JNIEnv *, jclass, jstring, jstring, jstring);

#ifdef __cplusplus
}
#endif
#endif
