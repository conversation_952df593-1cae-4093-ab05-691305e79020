/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.DashboardTerminalDetailPageResultModel;
import com.untill.retailforce.model.DashboardTerminalOverviewPageResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for DashboardApi
 */
@Disabled
public class DashboardApiTest {

    private final DashboardApi api = new DashboardApi();

    /**
     * Returns an overview of all annual year checks for all active terminals (archived terminals will not be returned).
     *
     * If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10DashboardTerminalAnnualYearCheckOrganizationIdGetTest() throws ApiException {
        UUID organizationId = null;
        Boolean includeTest = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        DashboardTerminalOverviewPageResultModel response = api.apiV10DashboardTerminalAnnualYearCheckOrganizationIdGet(organizationId, includeTest, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Returns a detail list of terminals belonging to the requested organization and containing the requested annual year check.
     *
     * If no annual year check was done so far in the retailforce system \&quot;MISS\&quot; is returned instead of year value.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGetTest() throws ApiException {
        UUID organizationId = null;
        String year = null;
        Boolean includeTest = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        DashboardTerminalDetailPageResultModel response = api.apiV10DashboardTerminalAnnualYearCheckOrganizationIdYearGet(organizationId, year, includeTest, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Returns a detail list of terminals belonging to the requested organization and having requested client version.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGetTest() throws ApiException {
        UUID organizationId = null;
        String clientVersion = null;
        Boolean includeTest = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        DashboardTerminalDetailPageResultModel response = api.apiV10DashboardTerminalClientVersionOrganizationIdClientVersionGet(organizationId, clientVersion, includeTest, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Returns an overview of all terminals and their versions.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10DashboardTerminalClientVersionOrganizationIdGetTest() throws ApiException {
        UUID organizationId = null;
        Boolean includeTest = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        DashboardTerminalOverviewPageResultModel response = api.apiV10DashboardTerminalClientVersionOrganizationIdGet(organizationId, includeTest, pageOffset, pageSize);
        // TODO: test validations
    }

}
