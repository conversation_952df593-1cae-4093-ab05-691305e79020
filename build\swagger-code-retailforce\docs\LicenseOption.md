

# LicenseOption

Represents an option for a license.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**accessLicenseId** | **String** | The access license id (an option is always an access license) |  [optional] |
|**caption** | **String** | The caption of the access license id. |  [optional] |
|**description** | **String** | The description of the license id. |  [optional] |
|**unit** | **String** | The unit of the license. |  [optional] |
|**defaultValue** | **Boolean** | The default value of this option, when the owning license is selected. |  [optional] |
|**selectable** | **Boolean** | True if this option can be selected or not. |  [optional] |



