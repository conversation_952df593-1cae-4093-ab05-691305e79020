/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.SecurityCertificateType;
import java.io.IOException;
import java.time.OffsetDateTime;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Represents a signature device for an organization / company in Austria.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SignatureDevice {
  public static final String SERIALIZED_NAME_TYPE = "type";
  @SerializedName(SERIALIZED_NAME_TYPE)
  private SecurityCertificateType type;

  public static final String SERIALIZED_NAME_CERTIFICATE_SERIAL = "certificateSerial";
  @SerializedName(SERIALIZED_NAME_CERTIFICATE_SERIAL)
  private String certificateSerial;

  public static final String SERIALIZED_NAME_CERTIFICATE_VALID_TO = "certificateValidTo";
  @SerializedName(SERIALIZED_NAME_CERTIFICATE_VALID_TO)
  private OffsetDateTime certificateValidTo;

  public static final String SERIALIZED_NAME_IS_DEACTIVATED = "isDeactivated";
  @SerializedName(SERIALIZED_NAME_IS_DEACTIVATED)
  private Boolean isDeactivated;

  public static final String SERIALIZED_NAME_IS_DECOMMISSIONED = "isDecommissioned";
  @SerializedName(SERIALIZED_NAME_IS_DECOMMISSIONED)
  private Boolean isDecommissioned;

  public static final String SERIALIZED_NAME_LAST_FON_CHECK = "lastFonCheck";
  @SerializedName(SERIALIZED_NAME_LAST_FON_CHECK)
  private OffsetDateTime lastFonCheck;

  public static final String SERIALIZED_NAME_FIN_ON_ANNOUNCED = "finOnAnnounced";
  @SerializedName(SERIALIZED_NAME_FIN_ON_ANNOUNCED)
  private OffsetDateTime finOnAnnounced;

  public SignatureDevice() {
  }

  public SignatureDevice type(SecurityCertificateType type) {
    
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @javax.annotation.Nullable
  public SecurityCertificateType getType() {
    return type;
  }


  public void setType(SecurityCertificateType type) {
    this.type = type;
  }


  public SignatureDevice certificateSerial(String certificateSerial) {
    
    this.certificateSerial = certificateSerial;
    return this;
  }

   /**
   * The serial number of the certificate (hex notation)
   * @return certificateSerial
  **/
  @javax.annotation.Nullable
  public String getCertificateSerial() {
    return certificateSerial;
  }


  public void setCertificateSerial(String certificateSerial) {
    this.certificateSerial = certificateSerial;
  }


  public SignatureDevice certificateValidTo(OffsetDateTime certificateValidTo) {
    
    this.certificateValidTo = certificateValidTo;
    return this;
  }

   /**
   * Date/time of the end of the certificate serial (if available)
   * @return certificateValidTo
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getCertificateValidTo() {
    return certificateValidTo;
  }


  public void setCertificateValidTo(OffsetDateTime certificateValidTo) {
    this.certificateValidTo = certificateValidTo;
  }


  public SignatureDevice isDeactivated(Boolean isDeactivated) {
    
    this.isDeactivated = isDeactivated;
    return this;
  }

   /**
   * True if the signature device was deactivated (not decommissioned) (temporarily) (Ausfall)
   * @return isDeactivated
  **/
  @javax.annotation.Nullable
  public Boolean getIsDeactivated() {
    return isDeactivated;
  }


  public void setIsDeactivated(Boolean isDeactivated) {
    this.isDeactivated = isDeactivated;
  }


  public SignatureDevice isDecommissioned(Boolean isDecommissioned) {
    
    this.isDecommissioned = isDecommissioned;
    return this;
  }

   /**
   * True if the signature device was decommissioned (Abmeldung, weil gestohlen, etc.).
   * @return isDecommissioned
  **/
  @javax.annotation.Nullable
  public Boolean getIsDecommissioned() {
    return isDecommissioned;
  }


  public void setIsDecommissioned(Boolean isDecommissioned) {
    this.isDecommissioned = isDecommissioned;
  }


  public SignatureDevice lastFonCheck(OffsetDateTime lastFonCheck) {
    
    this.lastFonCheck = lastFonCheck;
    return this;
  }

   /**
   * Date/time of the last check with fon.
   * @return lastFonCheck
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getLastFonCheck() {
    return lastFonCheck;
  }


  public void setLastFonCheck(OffsetDateTime lastFonCheck) {
    this.lastFonCheck = lastFonCheck;
  }


  public SignatureDevice finOnAnnounced(OffsetDateTime finOnAnnounced) {
    
    this.finOnAnnounced = finOnAnnounced;
    return this;
  }

   /**
   * Date/time when the signature device was announced to fon.
   * @return finOnAnnounced
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getFinOnAnnounced() {
    return finOnAnnounced;
  }


  public void setFinOnAnnounced(OffsetDateTime finOnAnnounced) {
    this.finOnAnnounced = finOnAnnounced;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SignatureDevice signatureDevice = (SignatureDevice) o;
    return Objects.equals(this.type, signatureDevice.type) &&
        Objects.equals(this.certificateSerial, signatureDevice.certificateSerial) &&
        Objects.equals(this.certificateValidTo, signatureDevice.certificateValidTo) &&
        Objects.equals(this.isDeactivated, signatureDevice.isDeactivated) &&
        Objects.equals(this.isDecommissioned, signatureDevice.isDecommissioned) &&
        Objects.equals(this.lastFonCheck, signatureDevice.lastFonCheck) &&
        Objects.equals(this.finOnAnnounced, signatureDevice.finOnAnnounced);
  }

  @Override
  public int hashCode() {
    return Objects.hash(type, certificateSerial, certificateValidTo, isDeactivated, isDecommissioned, lastFonCheck, finOnAnnounced);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SignatureDevice {\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    certificateSerial: ").append(toIndentedString(certificateSerial)).append("\n");
    sb.append("    certificateValidTo: ").append(toIndentedString(certificateValidTo)).append("\n");
    sb.append("    isDeactivated: ").append(toIndentedString(isDeactivated)).append("\n");
    sb.append("    isDecommissioned: ").append(toIndentedString(isDecommissioned)).append("\n");
    sb.append("    lastFonCheck: ").append(toIndentedString(lastFonCheck)).append("\n");
    sb.append("    finOnAnnounced: ").append(toIndentedString(finOnAnnounced)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("type");
    openapiFields.add("certificateSerial");
    openapiFields.add("certificateValidTo");
    openapiFields.add("isDeactivated");
    openapiFields.add("isDecommissioned");
    openapiFields.add("lastFonCheck");
    openapiFields.add("finOnAnnounced");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to SignatureDevice
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!SignatureDevice.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in SignatureDevice is not found in the empty JSON string", SignatureDevice.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!SignatureDevice.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `SignatureDevice` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("certificateSerial") != null && !jsonObj.get("certificateSerial").isJsonNull()) && !jsonObj.get("certificateSerial").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `certificateSerial` to be a primitive type in the JSON string but got `%s`", jsonObj.get("certificateSerial").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!SignatureDevice.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'SignatureDevice' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<SignatureDevice> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(SignatureDevice.class));

       return (TypeAdapter<T>) new TypeAdapter<SignatureDevice>() {
           @Override
           public void write(JsonWriter out, SignatureDevice value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public SignatureDevice read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of SignatureDevice given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of SignatureDevice
  * @throws IOException if the JSON string is invalid with respect to SignatureDevice
  */
  public static SignatureDevice fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, SignatureDevice.class);
  }

 /**
  * Convert an instance of SignatureDevice to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

