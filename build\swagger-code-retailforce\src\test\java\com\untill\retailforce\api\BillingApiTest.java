/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel;
import com.untill.retailforce.model.BillingLicenseCountBillingLicenseOverviewPageResultModel;
import com.untill.retailforce.model.BillingLicenseDetailPageResultModel;
import com.untill.retailforce.model.ClearingRun;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.OutstandingPayment;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for BillingApi
 */
@Disabled
public class BillingApiTest {

    private final BillingApi api = new BillingApi();

    /**
     * Returns clearing license overview for distributor.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingClearingClearingIdGetTest() throws ApiException {
        UUID clearingId = null;
        UUID distributorId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel response = api.apiV10BillingClearingClearingIdGet(clearingId, distributorId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * Returns clearing license overview for all organizations where the actual users is authorized.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingClearingClearingIdOrganizationGetTest() throws ApiException {
        UUID clearingId = null;
        UUID distributorId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingLicenseCountBillingLicenseOverviewPageResultModel response = api.apiV10BillingClearingClearingIdOrganizationGet(clearingId, distributorId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * Returns clearing license details for the requested organization.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingClearingClearingIdOrganizationIdDetailGetTest() throws ApiException {
        UUID clearingId = null;
        UUID organizationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingLicenseDetailPageResultModel response = api.apiV10BillingClearingClearingIdOrganizationIdDetailGet(clearingId, organizationId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * returns the clearing detail usage for an organisation
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetTest() throws ApiException {
        UUID clearingId = null;
        UUID organizationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingLicenseDetailPageResultModel response = api.apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet(clearingId, organizationId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * Returns clearing license overview usage for all organizations where the actual users is authorized.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingClearingClearingIdOrganizationUsageGetTest() throws ApiException {
        UUID clearingId = null;
        UUID distributorId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingLicenseCountBillingLicenseOverviewPageResultModel response = api.apiV10BillingClearingClearingIdOrganizationUsageGet(clearingId, distributorId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * Returns the billin license overview usage for the requested distributor
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingClearingClearingIdUsageGetTest() throws ApiException {
        UUID clearingId = null;
        UUID distributorId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel response = api.apiV10BillingClearingClearingIdUsageGet(clearingId, distributorId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * Returns all available clearing runs.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingClearingGetTest() throws ApiException {
        List<ClearingRun> response = api.apiV10BillingClearingGet();
        // TODO: test validations
    }

    /**
     * Creates a new clearing run.  Clearing run will be created in background
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingClearingPutTest() throws ApiException {
        String caption = null;
        OffsetDateTime clearingRunDateTime = null;
        api.apiV10BillingClearingPut(caption, clearingRunDateTime);
        // TODO: test validations
    }

    /**
     * Returns billing license overview for distributor. [preview]
     *
     * This controller is in preview state and subject to change in future.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingOverviewGetTest() throws ApiException {
        UUID distributorId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel response = api.apiV10BillingOverviewGet(distributorId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * Returns billing license overview for all organizations where the actual users is authorized. [preview]
     *
     * This controller is in preview state and subject to change in future.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingOverviewOrganizationGetTest() throws ApiException {
        UUID distributorId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingLicenseCountBillingLicenseOverviewPageResultModel response = api.apiV10BillingOverviewOrganizationGet(distributorId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * Returns license details for the requested organization. [preview]
     *
     * This controller is in preview state and subject to change in future.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingOverviewOrganizationIdDetailGetTest() throws ApiException {
        UUID organizationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchText = null;
        BillingLicenseDetailPageResultModel response = api.apiV10BillingOverviewOrganizationIdDetailGet(organizationId, pageOffset, pageSize, searchText);
        // TODO: test validations
    }

    /**
     * Log the acknowledgement of the outstanding payment message
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10BillingPaymentOutstandingGetTest() throws ApiException {
        List<OutstandingPayment> response = api.apiV10BillingPaymentOutstandingGet();
        // TODO: test validations
    }

}
