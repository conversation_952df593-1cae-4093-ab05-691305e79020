/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.AccessLicense;
import com.untill.retailforce.model.AccessLicenseAllocation;
import com.untill.retailforce.model.AccessLicenseContract;
import com.untill.retailforce.model.AccessLicensePageResultModel;
import com.untill.retailforce.model.BillingLicenseCount;
import com.untill.retailforce.model.ConfigLicenseModel;
import com.untill.retailforce.model.JwtLicenseClaim;
import com.untill.retailforce.model.License;
import com.untill.retailforce.model.LicenseModel;
import com.untill.retailforce.model.LicenseModelPageResultModel;
import com.untill.retailforce.model.StringSimpleObject;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class LicencingApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public LicencingApi() {
        this(Configuration.getDefaultApiClient());
    }

    public LicencingApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete
     * @param accessLicenseId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteCall(String accessLicenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/accessLicense/{accessLicenseId}"
            .replace("{" + "accessLicenseId" + "}", localVarApiClient.escapeString(accessLicenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteValidateBeforeCall(String accessLicenseId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'accessLicenseId' is set
        if (accessLicenseId == null) {
            throw new ApiException("Missing the required parameter 'accessLicenseId' when calling apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete(Async)");
        }

        return apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteCall(accessLicenseId, _callback);

    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10LicensingLicenseAccessLicenseAccessLicenseIdDelete(String accessLicenseId) throws ApiException {
        apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteWithHttpInfo(accessLicenseId);
    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteWithHttpInfo(String accessLicenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteValidateBeforeCall(accessLicenseId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     *  (asynchronously)
     * 
     * @param accessLicenseId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteAsync(String accessLicenseId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicenseAccessLicenseIdDeleteValidateBeforeCall(accessLicenseId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet
     * @param accessLicenseId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetCall(String accessLicenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/accessLicense/{accessLicenseId}"
            .replace("{" + "accessLicenseId" + "}", localVarApiClient.escapeString(accessLicenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetValidateBeforeCall(String accessLicenseId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'accessLicenseId' is set
        if (accessLicenseId == null) {
            throw new ApiException("Missing the required parameter 'accessLicenseId' when calling apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet(Async)");
        }

        return apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetCall(accessLicenseId, _callback);

    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @return AccessLicense
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public AccessLicense apiV10LicensingLicenseAccessLicenseAccessLicenseIdGet(String accessLicenseId) throws ApiException {
        ApiResponse<AccessLicense> localVarResp = apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetWithHttpInfo(accessLicenseId);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @return ApiResponse&lt;AccessLicense&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<AccessLicense> apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetWithHttpInfo(String accessLicenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetValidateBeforeCall(accessLicenseId, null);
        Type localVarReturnType = new TypeToken<AccessLicense>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param accessLicenseId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetAsync(String accessLicenseId, final ApiCallback<AccessLicense> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicenseAccessLicenseIdGetValidateBeforeCall(accessLicenseId, _callback);
        Type localVarReturnType = new TypeToken<AccessLicense>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut
     * @param accessLicenseId  (required)
     * @param accessLicense  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutCall(String accessLicenseId, AccessLicense accessLicense, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = accessLicense;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/accessLicense/{accessLicenseId}"
            .replace("{" + "accessLicenseId" + "}", localVarApiClient.escapeString(accessLicenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutValidateBeforeCall(String accessLicenseId, AccessLicense accessLicense, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'accessLicenseId' is set
        if (accessLicenseId == null) {
            throw new ApiException("Missing the required parameter 'accessLicenseId' when calling apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut(Async)");
        }

        return apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutCall(accessLicenseId, accessLicense, _callback);

    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @param accessLicense  (optional)
     * @return AccessLicense
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public AccessLicense apiV10LicensingLicenseAccessLicenseAccessLicenseIdPut(String accessLicenseId, AccessLicense accessLicense) throws ApiException {
        ApiResponse<AccessLicense> localVarResp = apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutWithHttpInfo(accessLicenseId, accessLicense);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @param accessLicense  (optional)
     * @return ApiResponse&lt;AccessLicense&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<AccessLicense> apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutWithHttpInfo(String accessLicenseId, AccessLicense accessLicense) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutValidateBeforeCall(accessLicenseId, accessLicense, null);
        Type localVarReturnType = new TypeToken<AccessLicense>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param accessLicenseId  (required)
     * @param accessLicense  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutAsync(String accessLicenseId, AccessLicense accessLicense, final ApiCallback<AccessLicense> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicenseAccessLicenseIdPutValidateBeforeCall(accessLicenseId, accessLicense, _callback);
        Type localVarReturnType = new TypeToken<AccessLicense>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAccessLicenseGet
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param searchString  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicenseGetCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/accessLicense";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAccessLicenseGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10LicensingLicenseAccessLicenseGetCall(pageOffset, pageSize, searchString, _callback);

    }

    /**
     * 
     * 
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param searchString  (optional)
     * @return AccessLicensePageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public AccessLicensePageResultModel apiV10LicensingLicenseAccessLicenseGet(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<AccessLicensePageResultModel> localVarResp = apiV10LicensingLicenseAccessLicenseGetWithHttpInfo(pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param searchString  (optional)
     * @return ApiResponse&lt;AccessLicensePageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<AccessLicensePageResultModel> apiV10LicensingLicenseAccessLicenseGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicenseGetValidateBeforeCall(pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<AccessLicensePageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param searchString  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicenseGetAsync(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<AccessLicensePageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicenseGetValidateBeforeCall(pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<AccessLicensePageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAccessLicensePost
     * @param accessLicense  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicensePostCall(AccessLicense accessLicense, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = accessLicense;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/accessLicense";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAccessLicensePostValidateBeforeCall(AccessLicense accessLicense, final ApiCallback _callback) throws ApiException {
        return apiV10LicensingLicenseAccessLicensePostCall(accessLicense, _callback);

    }

    /**
     * 
     * 
     * @param accessLicense  (optional)
     * @return AccessLicense
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public AccessLicense apiV10LicensingLicenseAccessLicensePost(AccessLicense accessLicense) throws ApiException {
        ApiResponse<AccessLicense> localVarResp = apiV10LicensingLicenseAccessLicensePostWithHttpInfo(accessLicense);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param accessLicense  (optional)
     * @return ApiResponse&lt;AccessLicense&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<AccessLicense> apiV10LicensingLicenseAccessLicensePostWithHttpInfo(AccessLicense accessLicense) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicensePostValidateBeforeCall(accessLicense, null);
        Type localVarReturnType = new TypeToken<AccessLicense>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param accessLicense  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccessLicensePostAsync(AccessLicense accessLicense, final ApiCallback<AccessLicense> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAccessLicensePostValidateBeforeCall(accessLicense, _callback);
        Type localVarReturnType = new TypeToken<AccessLicense>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete
     * @param accessLicenseId  (required)
     * @param supplierId  (optional)
     * @param contractId  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteCall(String accessLicenseId, UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract"
            .replace("{" + "accessLicenseId" + "}", localVarApiClient.escapeString(accessLicenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (supplierId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("supplierId", supplierId));
        }

        if (contractId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("contractId", contractId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteValidateBeforeCall(String accessLicenseId, UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'accessLicenseId' is set
        if (accessLicenseId == null) {
            throw new ApiException("Missing the required parameter 'accessLicenseId' when calling apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete(Async)");
        }

        return apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteCall(accessLicenseId, supplierId, contractId, _callback);

    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @param supplierId  (optional)
     * @param contractId  (optional)
     * @return List&lt;AccessLicenseContract&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<AccessLicenseContract> apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDelete(String accessLicenseId, UUID supplierId, UUID contractId) throws ApiException {
        ApiResponse<List<AccessLicenseContract>> localVarResp = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteWithHttpInfo(accessLicenseId, supplierId, contractId);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @param supplierId  (optional)
     * @param contractId  (optional)
     * @return ApiResponse&lt;List&lt;AccessLicenseContract&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<AccessLicenseContract>> apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteWithHttpInfo(String accessLicenseId, UUID supplierId, UUID contractId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteValidateBeforeCall(accessLicenseId, supplierId, contractId, null);
        Type localVarReturnType = new TypeToken<List<AccessLicenseContract>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param accessLicenseId  (required)
     * @param supplierId  (optional)
     * @param contractId  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteAsync(String accessLicenseId, UUID supplierId, UUID contractId, final ApiCallback<List<AccessLicenseContract>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractDeleteValidateBeforeCall(accessLicenseId, supplierId, contractId, _callback);
        Type localVarReturnType = new TypeToken<List<AccessLicenseContract>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet
     * @param accessLicenseId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetCall(String accessLicenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract"
            .replace("{" + "accessLicenseId" + "}", localVarApiClient.escapeString(accessLicenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetValidateBeforeCall(String accessLicenseId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'accessLicenseId' is set
        if (accessLicenseId == null) {
            throw new ApiException("Missing the required parameter 'accessLicenseId' when calling apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet(Async)");
        }

        return apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetCall(accessLicenseId, _callback);

    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @return List&lt;AccessLicenseContract&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<AccessLicenseContract> apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGet(String accessLicenseId) throws ApiException {
        ApiResponse<List<AccessLicenseContract>> localVarResp = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetWithHttpInfo(accessLicenseId);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @return ApiResponse&lt;List&lt;AccessLicenseContract&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<AccessLicenseContract>> apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetWithHttpInfo(String accessLicenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetValidateBeforeCall(accessLicenseId, null);
        Type localVarReturnType = new TypeToken<List<AccessLicenseContract>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param accessLicenseId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetAsync(String accessLicenseId, final ApiCallback<List<AccessLicenseContract>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractGetValidateBeforeCall(accessLicenseId, _callback);
        Type localVarReturnType = new TypeToken<List<AccessLicenseContract>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost
     * @param accessLicenseId  (required)
     * @param supplierId  (optional)
     * @param contractId  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostCall(String accessLicenseId, UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/accesslicense/{accessLicenseId}/contract"
            .replace("{" + "accessLicenseId" + "}", localVarApiClient.escapeString(accessLicenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (supplierId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("supplierId", supplierId));
        }

        if (contractId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("contractId", contractId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostValidateBeforeCall(String accessLicenseId, UUID supplierId, UUID contractId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'accessLicenseId' is set
        if (accessLicenseId == null) {
            throw new ApiException("Missing the required parameter 'accessLicenseId' when calling apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost(Async)");
        }

        return apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostCall(accessLicenseId, supplierId, contractId, _callback);

    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @param supplierId  (optional)
     * @param contractId  (optional)
     * @return List&lt;AccessLicenseContract&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<AccessLicenseContract> apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPost(String accessLicenseId, UUID supplierId, UUID contractId) throws ApiException {
        ApiResponse<List<AccessLicenseContract>> localVarResp = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostWithHttpInfo(accessLicenseId, supplierId, contractId);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param accessLicenseId  (required)
     * @param supplierId  (optional)
     * @param contractId  (optional)
     * @return ApiResponse&lt;List&lt;AccessLicenseContract&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<AccessLicenseContract>> apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostWithHttpInfo(String accessLicenseId, UUID supplierId, UUID contractId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostValidateBeforeCall(accessLicenseId, supplierId, contractId, null);
        Type localVarReturnType = new TypeToken<List<AccessLicenseContract>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param accessLicenseId  (required)
     * @param supplierId  (optional)
     * @param contractId  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostAsync(String accessLicenseId, UUID supplierId, UUID contractId, final ApiCallback<List<AccessLicenseContract>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAccesslicenseAccessLicenseIdContractPostValidateBeforeCall(accessLicenseId, supplierId, contractId, _callback);
        Type localVarReturnType = new TypeToken<List<AccessLicenseContract>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAdminCreateallcontainersPatch
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     * @deprecated
     */
    @Deprecated
    public okhttp3.Call apiV10LicensingLicenseAdminCreateallcontainersPatchCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/admin/createallcontainers";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PATCH", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @Deprecated
    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAdminCreateallcontainersPatchValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10LicensingLicenseAdminCreateallcontainersPatchCall(_callback);

    }

    /**
     * 
     * 
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     * @deprecated
     */
    @Deprecated
    public void apiV10LicensingLicenseAdminCreateallcontainersPatch() throws ApiException {
        apiV10LicensingLicenseAdminCreateallcontainersPatchWithHttpInfo();
    }

    /**
     * 
     * 
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     * @deprecated
     */
    @Deprecated
    public ApiResponse<Void> apiV10LicensingLicenseAdminCreateallcontainersPatchWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAdminCreateallcontainersPatchValidateBeforeCall(null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     *  (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     * @deprecated
     */
    @Deprecated
    public okhttp3.Call apiV10LicensingLicenseAdminCreateallcontainersPatchAsync(final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAdminCreateallcontainersPatchValidateBeforeCall(_callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete
     * @param licenseId  (required)
     * @param accessLicenseId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteCall(String licenseId, String accessLicenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/allocation/{licenseId}/{accessLicenseId}"
            .replace("{" + "licenseId" + "}", localVarApiClient.escapeString(licenseId.toString()))
            .replace("{" + "accessLicenseId" + "}", localVarApiClient.escapeString(accessLicenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteValidateBeforeCall(String licenseId, String accessLicenseId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'licenseId' is set
        if (licenseId == null) {
            throw new ApiException("Missing the required parameter 'licenseId' when calling apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete(Async)");
        }

        // verify the required parameter 'accessLicenseId' is set
        if (accessLicenseId == null) {
            throw new ApiException("Missing the required parameter 'accessLicenseId' when calling apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete(Async)");
        }

        return apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteCall(licenseId, accessLicenseId, _callback);

    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @param accessLicenseId  (required)
     * @return List&lt;AccessLicenseAllocation&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<AccessLicenseAllocation> apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDelete(String licenseId, String accessLicenseId) throws ApiException {
        ApiResponse<List<AccessLicenseAllocation>> localVarResp = apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteWithHttpInfo(licenseId, accessLicenseId);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @param accessLicenseId  (required)
     * @return ApiResponse&lt;List&lt;AccessLicenseAllocation&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<AccessLicenseAllocation>> apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteWithHttpInfo(String licenseId, String accessLicenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteValidateBeforeCall(licenseId, accessLicenseId, null);
        Type localVarReturnType = new TypeToken<List<AccessLicenseAllocation>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param licenseId  (required)
     * @param accessLicenseId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteAsync(String licenseId, String accessLicenseId, final ApiCallback<List<AccessLicenseAllocation>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAllocationLicenseIdAccessLicenseIdDeleteValidateBeforeCall(licenseId, accessLicenseId, _callback);
        Type localVarReturnType = new TypeToken<List<AccessLicenseAllocation>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAllocationLicenseIdGet
     * @param licenseId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdGetCall(String licenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/allocation/{licenseId}"
            .replace("{" + "licenseId" + "}", localVarApiClient.escapeString(licenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdGetValidateBeforeCall(String licenseId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'licenseId' is set
        if (licenseId == null) {
            throw new ApiException("Missing the required parameter 'licenseId' when calling apiV10LicensingLicenseAllocationLicenseIdGet(Async)");
        }

        return apiV10LicensingLicenseAllocationLicenseIdGetCall(licenseId, _callback);

    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @return List&lt;AccessLicenseAllocation&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<AccessLicenseAllocation> apiV10LicensingLicenseAllocationLicenseIdGet(String licenseId) throws ApiException {
        ApiResponse<List<AccessLicenseAllocation>> localVarResp = apiV10LicensingLicenseAllocationLicenseIdGetWithHttpInfo(licenseId);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @return ApiResponse&lt;List&lt;AccessLicenseAllocation&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<AccessLicenseAllocation>> apiV10LicensingLicenseAllocationLicenseIdGetWithHttpInfo(String licenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAllocationLicenseIdGetValidateBeforeCall(licenseId, null);
        Type localVarReturnType = new TypeToken<List<AccessLicenseAllocation>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param licenseId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdGetAsync(String licenseId, final ApiCallback<List<AccessLicenseAllocation>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAllocationLicenseIdGetValidateBeforeCall(licenseId, _callback);
        Type localVarReturnType = new TypeToken<List<AccessLicenseAllocation>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseAllocationLicenseIdPost
     * @param licenseId  (required)
     * @param accessLicenseAllocation  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdPostCall(String licenseId, AccessLicenseAllocation accessLicenseAllocation, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = accessLicenseAllocation;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/allocation/{licenseId}"
            .replace("{" + "licenseId" + "}", localVarApiClient.escapeString(licenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdPostValidateBeforeCall(String licenseId, AccessLicenseAllocation accessLicenseAllocation, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'licenseId' is set
        if (licenseId == null) {
            throw new ApiException("Missing the required parameter 'licenseId' when calling apiV10LicensingLicenseAllocationLicenseIdPost(Async)");
        }

        return apiV10LicensingLicenseAllocationLicenseIdPostCall(licenseId, accessLicenseAllocation, _callback);

    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @param accessLicenseAllocation  (optional)
     * @return List&lt;AccessLicenseAllocation&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<AccessLicenseAllocation> apiV10LicensingLicenseAllocationLicenseIdPost(String licenseId, AccessLicenseAllocation accessLicenseAllocation) throws ApiException {
        ApiResponse<List<AccessLicenseAllocation>> localVarResp = apiV10LicensingLicenseAllocationLicenseIdPostWithHttpInfo(licenseId, accessLicenseAllocation);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @param accessLicenseAllocation  (optional)
     * @return ApiResponse&lt;List&lt;AccessLicenseAllocation&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<AccessLicenseAllocation>> apiV10LicensingLicenseAllocationLicenseIdPostWithHttpInfo(String licenseId, AccessLicenseAllocation accessLicenseAllocation) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseAllocationLicenseIdPostValidateBeforeCall(licenseId, accessLicenseAllocation, null);
        Type localVarReturnType = new TypeToken<List<AccessLicenseAllocation>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param licenseId  (required)
     * @param accessLicenseAllocation  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseAllocationLicenseIdPostAsync(String licenseId, AccessLicenseAllocation accessLicenseAllocation, final ApiCallback<List<AccessLicenseAllocation>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseAllocationLicenseIdPostValidateBeforeCall(licenseId, accessLicenseAllocation, _callback);
        Type localVarReturnType = new TypeToken<List<AccessLicenseAllocation>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseConfigurationConfigurationIdGet
     * @param configurationId The id of the configuration where the possible licenses are requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdGetCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/configuration/{configurationId}"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdGetValidateBeforeCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10LicensingLicenseConfigurationConfigurationIdGet(Async)");
        }

        return apiV10LicensingLicenseConfigurationConfigurationIdGetCall(configurationId, _callback);

    }

    /**
     * Returns the possible licenses for the requested organisation.
     * 
     * @param configurationId The id of the configuration where the possible licenses are requested. (required)
     * @return List&lt;ConfigLicenseModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<ConfigLicenseModel> apiV10LicensingLicenseConfigurationConfigurationIdGet(UUID configurationId) throws ApiException {
        ApiResponse<List<ConfigLicenseModel>> localVarResp = apiV10LicensingLicenseConfigurationConfigurationIdGetWithHttpInfo(configurationId);
        return localVarResp.getData();
    }

    /**
     * Returns the possible licenses for the requested organisation.
     * 
     * @param configurationId The id of the configuration where the possible licenses are requested. (required)
     * @return ApiResponse&lt;List&lt;ConfigLicenseModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ConfigLicenseModel>> apiV10LicensingLicenseConfigurationConfigurationIdGetWithHttpInfo(UUID configurationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationConfigurationIdGetValidateBeforeCall(configurationId, null);
        Type localVarReturnType = new TypeToken<List<ConfigLicenseModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the possible licenses for the requested organisation. (asynchronously)
     * 
     * @param configurationId The id of the configuration where the possible licenses are requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdGetAsync(UUID configurationId, final ApiCallback<List<ConfigLicenseModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationConfigurationIdGetValidateBeforeCall(configurationId, _callback);
        Type localVarReturnType = new TypeToken<List<ConfigLicenseModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseConfigurationConfigurationIdPut
     * @param configurationId The id of the configuration to add the licenses. (required)
     * @param licenseModel A list of licenseIds to add / update. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdPutCall(UUID configurationId, List<LicenseModel> licenseModel, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = licenseModel;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/configuration/{configurationId}"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdPutValidateBeforeCall(UUID configurationId, List<LicenseModel> licenseModel, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10LicensingLicenseConfigurationConfigurationIdPut(Async)");
        }

        return apiV10LicensingLicenseConfigurationConfigurationIdPutCall(configurationId, licenseModel, _callback);

    }

    /**
     * Updates licenses to the given configuration.
     * 
     * @param configurationId The id of the configuration to add the licenses. (required)
     * @param licenseModel A list of licenseIds to add / update. (optional)
     * @return List&lt;LicenseModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<LicenseModel> apiV10LicensingLicenseConfigurationConfigurationIdPut(UUID configurationId, List<LicenseModel> licenseModel) throws ApiException {
        ApiResponse<List<LicenseModel>> localVarResp = apiV10LicensingLicenseConfigurationConfigurationIdPutWithHttpInfo(configurationId, licenseModel);
        return localVarResp.getData();
    }

    /**
     * Updates licenses to the given configuration.
     * 
     * @param configurationId The id of the configuration to add the licenses. (required)
     * @param licenseModel A list of licenseIds to add / update. (optional)
     * @return ApiResponse&lt;List&lt;LicenseModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<LicenseModel>> apiV10LicensingLicenseConfigurationConfigurationIdPutWithHttpInfo(UUID configurationId, List<LicenseModel> licenseModel) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationConfigurationIdPutValidateBeforeCall(configurationId, licenseModel, null);
        Type localVarReturnType = new TypeToken<List<LicenseModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates licenses to the given configuration. (asynchronously)
     * 
     * @param configurationId The id of the configuration to add the licenses. (required)
     * @param licenseModel A list of licenseIds to add / update. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdPutAsync(UUID configurationId, List<LicenseModel> licenseModel, final ApiCallback<List<LicenseModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationConfigurationIdPutValidateBeforeCall(configurationId, licenseModel, _callback);
        Type localVarReturnType = new TypeToken<List<LicenseModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseConfigurationConfigurationIdUsageGet
     * @param configurationId The id of teh configuration where the used licenses are requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdUsageGetCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/configuration/{configurationId}/usage"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdUsageGetValidateBeforeCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10LicensingLicenseConfigurationConfigurationIdUsageGet(Async)");
        }

        return apiV10LicensingLicenseConfigurationConfigurationIdUsageGetCall(configurationId, _callback);

    }

    /**
     * Returns the used licenses by this configuration.
     * 
     * @param configurationId The id of teh configuration where the used licenses are requested. (required)
     * @return List&lt;BillingLicenseCount&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<BillingLicenseCount> apiV10LicensingLicenseConfigurationConfigurationIdUsageGet(UUID configurationId) throws ApiException {
        ApiResponse<List<BillingLicenseCount>> localVarResp = apiV10LicensingLicenseConfigurationConfigurationIdUsageGetWithHttpInfo(configurationId);
        return localVarResp.getData();
    }

    /**
     * Returns the used licenses by this configuration.
     * 
     * @param configurationId The id of teh configuration where the used licenses are requested. (required)
     * @return ApiResponse&lt;List&lt;BillingLicenseCount&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<BillingLicenseCount>> apiV10LicensingLicenseConfigurationConfigurationIdUsageGetWithHttpInfo(UUID configurationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationConfigurationIdUsageGetValidateBeforeCall(configurationId, null);
        Type localVarReturnType = new TypeToken<List<BillingLicenseCount>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the used licenses by this configuration. (asynchronously)
     * 
     * @param configurationId The id of teh configuration where the used licenses are requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationConfigurationIdUsageGetAsync(UUID configurationId, final ApiCallback<List<BillingLicenseCount>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationConfigurationIdUsageGetValidateBeforeCall(configurationId, _callback);
        Type localVarReturnType = new TypeToken<List<BillingLicenseCount>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseConfigurationGroupsGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationGroupsGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/configuration/groups";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseConfigurationGroupsGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10LicensingLicenseConfigurationGroupsGetCall(_callback);

    }

    /**
     * 
     * 
     * @return List&lt;StringSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<StringSimpleObject> apiV10LicensingLicenseConfigurationGroupsGet() throws ApiException {
        ApiResponse<List<StringSimpleObject>> localVarResp = apiV10LicensingLicenseConfigurationGroupsGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @return ApiResponse&lt;List&lt;StringSimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<StringSimpleObject>> apiV10LicensingLicenseConfigurationGroupsGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationGroupsGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<StringSimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationGroupsGetAsync(final ApiCallback<List<StringSimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationGroupsGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<StringSimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseConfigurationUnitsGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationUnitsGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/configuration/units";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseConfigurationUnitsGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10LicensingLicenseConfigurationUnitsGetCall(_callback);

    }

    /**
     * Returns all available license units.
     * 
     * @return List&lt;StringSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<StringSimpleObject> apiV10LicensingLicenseConfigurationUnitsGet() throws ApiException {
        ApiResponse<List<StringSimpleObject>> localVarResp = apiV10LicensingLicenseConfigurationUnitsGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns all available license units.
     * 
     * @return ApiResponse&lt;List&lt;StringSimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<StringSimpleObject>> apiV10LicensingLicenseConfigurationUnitsGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationUnitsGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<StringSimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all available license units. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseConfigurationUnitsGetAsync(final ApiCallback<List<StringSimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseConfigurationUnitsGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<StringSimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseEffectiveEntityIdGet
     * @param entityId The entity where the licenses are requested. Can be of type Configuration, Organisation, Company, Store or Terminal. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseEffectiveEntityIdGetCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/effective/{entityId}"
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseEffectiveEntityIdGetValidateBeforeCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10LicensingLicenseEffectiveEntityIdGet(Async)");
        }

        return apiV10LicensingLicenseEffectiveEntityIdGetCall(entityId, _callback);

    }

    /**
     * Returns the effective licenses for the given entity.
     * 
     * @param entityId The entity where the licenses are requested. Can be of type Configuration, Organisation, Company, Store or Terminal. (required)
     * @return List&lt;LicenseModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<LicenseModel> apiV10LicensingLicenseEffectiveEntityIdGet(UUID entityId) throws ApiException {
        ApiResponse<List<LicenseModel>> localVarResp = apiV10LicensingLicenseEffectiveEntityIdGetWithHttpInfo(entityId);
        return localVarResp.getData();
    }

    /**
     * Returns the effective licenses for the given entity.
     * 
     * @param entityId The entity where the licenses are requested. Can be of type Configuration, Organisation, Company, Store or Terminal. (required)
     * @return ApiResponse&lt;List&lt;LicenseModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<LicenseModel>> apiV10LicensingLicenseEffectiveEntityIdGetWithHttpInfo(UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseEffectiveEntityIdGetValidateBeforeCall(entityId, null);
        Type localVarReturnType = new TypeToken<List<LicenseModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the effective licenses for the given entity. (asynchronously)
     * 
     * @param entityId The entity where the licenses are requested. Can be of type Configuration, Organisation, Company, Store or Terminal. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseEffectiveEntityIdGetAsync(UUID entityId, final ApiCallback<List<LicenseModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseEffectiveEntityIdGetValidateBeforeCall(entityId, _callback);
        Type localVarReturnType = new TypeToken<List<LicenseModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseGet
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param searchString  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseGetCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseGetValidateBeforeCall(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10LicensingLicenseGetCall(pageOffset, pageSize, searchString, _callback);

    }

    /**
     * 
     * 
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param searchString  (optional)
     * @return LicenseModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public LicenseModelPageResultModel apiV10LicensingLicenseGet(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<LicenseModelPageResultModel> localVarResp = apiV10LicensingLicenseGetWithHttpInfo(pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param searchString  (optional)
     * @return ApiResponse&lt;LicenseModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<LicenseModelPageResultModel> apiV10LicensingLicenseGetWithHttpInfo(Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseGetValidateBeforeCall(pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<LicenseModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param searchString  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseGetAsync(Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<LicenseModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseGetValidateBeforeCall(pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<LicenseModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseLicenseIdDelete
     * @param licenseId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseLicenseIdDeleteCall(String licenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/{licenseId}"
            .replace("{" + "licenseId" + "}", localVarApiClient.escapeString(licenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseLicenseIdDeleteValidateBeforeCall(String licenseId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'licenseId' is set
        if (licenseId == null) {
            throw new ApiException("Missing the required parameter 'licenseId' when calling apiV10LicensingLicenseLicenseIdDelete(Async)");
        }

        return apiV10LicensingLicenseLicenseIdDeleteCall(licenseId, _callback);

    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10LicensingLicenseLicenseIdDelete(String licenseId) throws ApiException {
        apiV10LicensingLicenseLicenseIdDeleteWithHttpInfo(licenseId);
    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10LicensingLicenseLicenseIdDeleteWithHttpInfo(String licenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseLicenseIdDeleteValidateBeforeCall(licenseId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     *  (asynchronously)
     * 
     * @param licenseId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseLicenseIdDeleteAsync(String licenseId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseLicenseIdDeleteValidateBeforeCall(licenseId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseLicenseIdGet
     * @param licenseId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseLicenseIdGetCall(String licenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/{licenseId}"
            .replace("{" + "licenseId" + "}", localVarApiClient.escapeString(licenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseLicenseIdGetValidateBeforeCall(String licenseId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'licenseId' is set
        if (licenseId == null) {
            throw new ApiException("Missing the required parameter 'licenseId' when calling apiV10LicensingLicenseLicenseIdGet(Async)");
        }

        return apiV10LicensingLicenseLicenseIdGetCall(licenseId, _callback);

    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @return License
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public License apiV10LicensingLicenseLicenseIdGet(String licenseId) throws ApiException {
        ApiResponse<License> localVarResp = apiV10LicensingLicenseLicenseIdGetWithHttpInfo(licenseId);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @return ApiResponse&lt;License&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<License> apiV10LicensingLicenseLicenseIdGetWithHttpInfo(String licenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseLicenseIdGetValidateBeforeCall(licenseId, null);
        Type localVarReturnType = new TypeToken<License>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param licenseId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseLicenseIdGetAsync(String licenseId, final ApiCallback<License> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseLicenseIdGetValidateBeforeCall(licenseId, _callback);
        Type localVarReturnType = new TypeToken<License>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicenseLicenseIdPut
     * @param licenseId  (required)
     * @param license  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseLicenseIdPutCall(String licenseId, License license, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = license;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license/{licenseId}"
            .replace("{" + "licenseId" + "}", localVarApiClient.escapeString(licenseId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicenseLicenseIdPutValidateBeforeCall(String licenseId, License license, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'licenseId' is set
        if (licenseId == null) {
            throw new ApiException("Missing the required parameter 'licenseId' when calling apiV10LicensingLicenseLicenseIdPut(Async)");
        }

        return apiV10LicensingLicenseLicenseIdPutCall(licenseId, license, _callback);

    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @param license  (optional)
     * @return License
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public License apiV10LicensingLicenseLicenseIdPut(String licenseId, License license) throws ApiException {
        ApiResponse<License> localVarResp = apiV10LicensingLicenseLicenseIdPutWithHttpInfo(licenseId, license);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param licenseId  (required)
     * @param license  (optional)
     * @return ApiResponse&lt;License&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<License> apiV10LicensingLicenseLicenseIdPutWithHttpInfo(String licenseId, License license) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicenseLicenseIdPutValidateBeforeCall(licenseId, license, null);
        Type localVarReturnType = new TypeToken<License>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param licenseId  (required)
     * @param license  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicenseLicenseIdPutAsync(String licenseId, License license, final ApiCallback<License> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicenseLicenseIdPutValidateBeforeCall(licenseId, license, _callback);
        Type localVarReturnType = new TypeToken<License>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingLicensePost
     * @param license  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicensePostCall(License license, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = license;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/license";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingLicensePostValidateBeforeCall(License license, final ApiCallback _callback) throws ApiException {
        return apiV10LicensingLicensePostCall(license, _callback);

    }

    /**
     * 
     * 
     * @param license  (optional)
     * @return License
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public License apiV10LicensingLicensePost(License license) throws ApiException {
        ApiResponse<License> localVarResp = apiV10LicensingLicensePostWithHttpInfo(license);
        return localVarResp.getData();
    }

    /**
     * 
     * 
     * @param license  (optional)
     * @return ApiResponse&lt;License&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<License> apiV10LicensingLicensePostWithHttpInfo(License license) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingLicensePostValidateBeforeCall(license, null);
        Type localVarReturnType = new TypeToken<License>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     *  (asynchronously)
     * 
     * @param license  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingLicensePostAsync(License license, final ApiCallback<License> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingLicensePostValidateBeforeCall(license, _callback);
        Type localVarReturnType = new TypeToken<License>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingTokenAccessCounterGet
     * @param uniqueClientId The clientId for which the license token is requested. (optional)
     * @param accesslicenseId The requested access license (technical license). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingTokenAccessCounterGetCall(UUID uniqueClientId, String accesslicenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/tokenAccessCounter";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (uniqueClientId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("uniqueClientId", uniqueClientId));
        }

        if (accesslicenseId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("accesslicenseId", accesslicenseId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingTokenAccessCounterGetValidateBeforeCall(UUID uniqueClientId, String accesslicenseId, final ApiCallback _callback) throws ApiException {
        return apiV10LicensingTokenAccessCounterGetCall(uniqueClientId, accesslicenseId, _callback);

    }

    /**
     * Returns a license token (jwt) to use for requests for azure stateless functions.
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param uniqueClientId The clientId for which the license token is requested. (optional)
     * @param accesslicenseId The requested access license (technical license). (optional)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10LicensingTokenAccessCounterGet(UUID uniqueClientId, String accesslicenseId) throws ApiException {
        ApiResponse<String> localVarResp = apiV10LicensingTokenAccessCounterGetWithHttpInfo(uniqueClientId, accesslicenseId);
        return localVarResp.getData();
    }

    /**
     * Returns a license token (jwt) to use for requests for azure stateless functions.
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param uniqueClientId The clientId for which the license token is requested. (optional)
     * @param accesslicenseId The requested access license (technical license). (optional)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10LicensingTokenAccessCounterGetWithHttpInfo(UUID uniqueClientId, String accesslicenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingTokenAccessCounterGetValidateBeforeCall(uniqueClientId, accesslicenseId, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a license token (jwt) to use for requests for azure stateless functions. (asynchronously)
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param uniqueClientId The clientId for which the license token is requested. (optional)
     * @param accesslicenseId The requested access license (technical license). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingTokenAccessCounterGetAsync(UUID uniqueClientId, String accesslicenseId, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingTokenAccessCounterGetValidateBeforeCall(uniqueClientId, accesslicenseId, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingTokenGet
     * @param uniqueClientId The clientId for which the license token is requested. (optional)
     * @param accesslicenseId The requested access license (technical license). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingTokenGetCall(UUID uniqueClientId, String accesslicenseId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/token";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (uniqueClientId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("uniqueClientId", uniqueClientId));
        }

        if (accesslicenseId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("accesslicenseId", accesslicenseId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingTokenGetValidateBeforeCall(UUID uniqueClientId, String accesslicenseId, final ApiCallback _callback) throws ApiException {
        return apiV10LicensingTokenGetCall(uniqueClientId, accesslicenseId, _callback);

    }

    /**
     * Returns a license token (jwt) to use for requests for azure stateless functions.
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param uniqueClientId The clientId for which the license token is requested. (optional)
     * @param accesslicenseId The requested access license (technical license). (optional)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10LicensingTokenGet(UUID uniqueClientId, String accesslicenseId) throws ApiException {
        ApiResponse<String> localVarResp = apiV10LicensingTokenGetWithHttpInfo(uniqueClientId, accesslicenseId);
        return localVarResp.getData();
    }

    /**
     * Returns a license token (jwt) to use for requests for azure stateless functions.
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param uniqueClientId The clientId for which the license token is requested. (optional)
     * @param accesslicenseId The requested access license (technical license). (optional)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10LicensingTokenGetWithHttpInfo(UUID uniqueClientId, String accesslicenseId) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingTokenGetValidateBeforeCall(uniqueClientId, accesslicenseId, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a license token (jwt) to use for requests for azure stateless functions. (asynchronously)
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param uniqueClientId The clientId for which the license token is requested. (optional)
     * @param accesslicenseId The requested access license (technical license). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingTokenGetAsync(UUID uniqueClientId, String accesslicenseId, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingTokenGetValidateBeforeCall(uniqueClientId, accesslicenseId, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingTokenTerminalIdGet
     * @param terminalId The terminal id for which terminal the license is requested. (required)
     * @param clientVersion The software version of the client. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingTokenTerminalIdGetCall(UUID terminalId, String clientVersion, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/token/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (clientVersion != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("clientVersion", clientVersion));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingTokenTerminalIdGetValidateBeforeCall(UUID terminalId, String clientVersion, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10LicensingTokenTerminalIdGet(Async)");
        }

        return apiV10LicensingTokenTerminalIdGetCall(terminalId, clientVersion, _callback);

    }

    /**
     * Returns the license token (jwt) including all licenses for the requested terminal.
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param terminalId The terminal id for which terminal the license is requested. (required)
     * @param clientVersion The software version of the client. (optional)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10LicensingTokenTerminalIdGet(UUID terminalId, String clientVersion) throws ApiException {
        ApiResponse<String> localVarResp = apiV10LicensingTokenTerminalIdGetWithHttpInfo(terminalId, clientVersion);
        return localVarResp.getData();
    }

    /**
     * Returns the license token (jwt) including all licenses for the requested terminal.
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param terminalId The terminal id for which terminal the license is requested. (required)
     * @param clientVersion The software version of the client. (optional)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10LicensingTokenTerminalIdGetWithHttpInfo(UUID terminalId, String clientVersion) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingTokenTerminalIdGetValidateBeforeCall(terminalId, clientVersion, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the license token (jwt) including all licenses for the requested terminal. (asynchronously)
     * The token is issued for 30 days. It is not allowed to query the token continuously, the query is limited to 10 queries per client per day.
     * @param terminalId The terminal id for which terminal the license is requested. (required)
     * @param clientVersion The software version of the client. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingTokenTerminalIdGetAsync(UUID terminalId, String clientVersion, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingTokenTerminalIdGetValidateBeforeCall(terminalId, clientVersion, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10LicensingTokenValidateGet
     * @param uniqueClientId The clientid of requested license token. (optional)
     * @param licenseToken The license token to validate and read. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingTokenValidateGetCall(UUID uniqueClientId, String licenseToken, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/licensing/token/validate";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (uniqueClientId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("uniqueClientId", uniqueClientId));
        }

        if (licenseToken != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("licenseToken", licenseToken));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10LicensingTokenValidateGetValidateBeforeCall(UUID uniqueClientId, String licenseToken, final ApiCallback _callback) throws ApiException {
        return apiV10LicensingTokenValidateGetCall(uniqueClientId, licenseToken, _callback);

    }

    /**
     * Validates the given license token and returns a RetailForce.Cloud.Functions.Entities.JwtLicenseClaim object containing the license of the token.
     * 
     * @param uniqueClientId The clientid of requested license token. (optional)
     * @param licenseToken The license token to validate and read. (optional)
     * @return JwtLicenseClaim
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public JwtLicenseClaim apiV10LicensingTokenValidateGet(UUID uniqueClientId, String licenseToken) throws ApiException {
        ApiResponse<JwtLicenseClaim> localVarResp = apiV10LicensingTokenValidateGetWithHttpInfo(uniqueClientId, licenseToken);
        return localVarResp.getData();
    }

    /**
     * Validates the given license token and returns a RetailForce.Cloud.Functions.Entities.JwtLicenseClaim object containing the license of the token.
     * 
     * @param uniqueClientId The clientid of requested license token. (optional)
     * @param licenseToken The license token to validate and read. (optional)
     * @return ApiResponse&lt;JwtLicenseClaim&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<JwtLicenseClaim> apiV10LicensingTokenValidateGetWithHttpInfo(UUID uniqueClientId, String licenseToken) throws ApiException {
        okhttp3.Call localVarCall = apiV10LicensingTokenValidateGetValidateBeforeCall(uniqueClientId, licenseToken, null);
        Type localVarReturnType = new TypeToken<JwtLicenseClaim>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Validates the given license token and returns a RetailForce.Cloud.Functions.Entities.JwtLicenseClaim object containing the license of the token. (asynchronously)
     * 
     * @param uniqueClientId The clientid of requested license token. (optional)
     * @param licenseToken The license token to validate and read. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10LicensingTokenValidateGetAsync(UUID uniqueClientId, String licenseToken, final ApiCallback<JwtLicenseClaim> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10LicensingTokenValidateGetValidateBeforeCall(uniqueClientId, licenseToken, _callback);
        Type localVarReturnType = new TypeToken<JwtLicenseClaim>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
