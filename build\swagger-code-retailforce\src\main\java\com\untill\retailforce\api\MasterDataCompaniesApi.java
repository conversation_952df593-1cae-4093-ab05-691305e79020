/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.Company;
import com.untill.retailforce.model.CompanyModel;
import com.untill.retailforce.model.CompanyModelPageResultModel;
import com.untill.retailforce.model.GuidEntityVersionPageResultModel;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class MasterDataCompaniesApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public MasterDataCompaniesApi() {
        this(Configuration.getDefaultApiClient());
    }

    public MasterDataCompaniesApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10MasterdataCompaniesCompanyIdDelete
     * @param companyId The id of the company to delete. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesCompanyIdDeleteCall(UUID companyId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/companies/{companyId}"
            .replace("{" + "companyId" + "}", localVarApiClient.escapeString(companyId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataCompaniesCompanyIdDeleteValidateBeforeCall(UUID companyId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'companyId' is set
        if (companyId == null) {
            throw new ApiException("Missing the required parameter 'companyId' when calling apiV10MasterdataCompaniesCompanyIdDelete(Async)");
        }

        return apiV10MasterdataCompaniesCompanyIdDeleteCall(companyId, _callback);

    }

    /**
     * Deletes a company from the cloud store.
     * 
     * @param companyId The id of the company to delete. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataCompaniesCompanyIdDelete(UUID companyId) throws ApiException {
        apiV10MasterdataCompaniesCompanyIdDeleteWithHttpInfo(companyId);
    }

    /**
     * Deletes a company from the cloud store.
     * 
     * @param companyId The id of the company to delete. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataCompaniesCompanyIdDeleteWithHttpInfo(UUID companyId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataCompaniesCompanyIdDeleteValidateBeforeCall(companyId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes a company from the cloud store. (asynchronously)
     * 
     * @param companyId The id of the company to delete. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesCompanyIdDeleteAsync(UUID companyId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataCompaniesCompanyIdDeleteValidateBeforeCall(companyId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataCompaniesCompanyIdGet
     * @param companyId The if of the requested company. (required)
     * @param entityVersion load specific version (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesCompanyIdGetCall(UUID companyId, String entityVersion, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/companies/{companyId}"
            .replace("{" + "companyId" + "}", localVarApiClient.escapeString(companyId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (entityVersion != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityVersion", entityVersion));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataCompaniesCompanyIdGetValidateBeforeCall(UUID companyId, String entityVersion, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'companyId' is set
        if (companyId == null) {
            throw new ApiException("Missing the required parameter 'companyId' when calling apiV10MasterdataCompaniesCompanyIdGet(Async)");
        }

        return apiV10MasterdataCompaniesCompanyIdGetCall(companyId, entityVersion, _callback);

    }

    /**
     * Returns the requested company by id.
     * 
     * @param companyId The if of the requested company. (required)
     * @param entityVersion load specific version (optional)
     * @return CompanyModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CompanyModel apiV10MasterdataCompaniesCompanyIdGet(UUID companyId, String entityVersion) throws ApiException {
        ApiResponse<CompanyModel> localVarResp = apiV10MasterdataCompaniesCompanyIdGetWithHttpInfo(companyId, entityVersion);
        return localVarResp.getData();
    }

    /**
     * Returns the requested company by id.
     * 
     * @param companyId The if of the requested company. (required)
     * @param entityVersion load specific version (optional)
     * @return ApiResponse&lt;CompanyModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CompanyModel> apiV10MasterdataCompaniesCompanyIdGetWithHttpInfo(UUID companyId, String entityVersion) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataCompaniesCompanyIdGetValidateBeforeCall(companyId, entityVersion, null);
        Type localVarReturnType = new TypeToken<CompanyModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the requested company by id. (asynchronously)
     * 
     * @param companyId The if of the requested company. (required)
     * @param entityVersion load specific version (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesCompanyIdGetAsync(UUID companyId, String entityVersion, final ApiCallback<CompanyModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataCompaniesCompanyIdGetValidateBeforeCall(companyId, entityVersion, _callback);
        Type localVarReturnType = new TypeToken<CompanyModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataCompaniesCompanyIdPut
     * @param companyId The id of the company to update. (required)
     * @param company The company data. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesCompanyIdPutCall(UUID companyId, Company company, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = company;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/companies/{companyId}"
            .replace("{" + "companyId" + "}", localVarApiClient.escapeString(companyId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataCompaniesCompanyIdPutValidateBeforeCall(UUID companyId, Company company, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'companyId' is set
        if (companyId == null) {
            throw new ApiException("Missing the required parameter 'companyId' when calling apiV10MasterdataCompaniesCompanyIdPut(Async)");
        }

        return apiV10MasterdataCompaniesCompanyIdPutCall(companyId, company, _callback);

    }

    /**
     * Updates a company in the cloud store.
     * 
     * @param companyId The id of the company to update. (required)
     * @param company The company data. (optional)
     * @return CompanyModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CompanyModel apiV10MasterdataCompaniesCompanyIdPut(UUID companyId, Company company) throws ApiException {
        ApiResponse<CompanyModel> localVarResp = apiV10MasterdataCompaniesCompanyIdPutWithHttpInfo(companyId, company);
        return localVarResp.getData();
    }

    /**
     * Updates a company in the cloud store.
     * 
     * @param companyId The id of the company to update. (required)
     * @param company The company data. (optional)
     * @return ApiResponse&lt;CompanyModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CompanyModel> apiV10MasterdataCompaniesCompanyIdPutWithHttpInfo(UUID companyId, Company company) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataCompaniesCompanyIdPutValidateBeforeCall(companyId, company, null);
        Type localVarReturnType = new TypeToken<CompanyModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates a company in the cloud store. (asynchronously)
     * 
     * @param companyId The id of the company to update. (required)
     * @param company The company data. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesCompanyIdPutAsync(UUID companyId, Company company, final ApiCallback<CompanyModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataCompaniesCompanyIdPutValidateBeforeCall(companyId, company, _callback);
        Type localVarReturnType = new TypeToken<CompanyModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataCompaniesCompanyIdVersionsGet
     * @param companyId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesCompanyIdVersionsGetCall(UUID companyId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/companies/{companyId}/versions"
            .replace("{" + "companyId" + "}", localVarApiClient.escapeString(companyId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataCompaniesCompanyIdVersionsGetValidateBeforeCall(UUID companyId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'companyId' is set
        if (companyId == null) {
            throw new ApiException("Missing the required parameter 'companyId' when calling apiV10MasterdataCompaniesCompanyIdVersionsGet(Async)");
        }

        return apiV10MasterdataCompaniesCompanyIdVersionsGetCall(companyId, pageOffset, pageSize, _callback);

    }

    /**
     * Get company versions
     * 
     * @param companyId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return GuidEntityVersionPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidEntityVersionPageResultModel apiV10MasterdataCompaniesCompanyIdVersionsGet(UUID companyId, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<GuidEntityVersionPageResultModel> localVarResp = apiV10MasterdataCompaniesCompanyIdVersionsGetWithHttpInfo(companyId, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Get company versions
     * 
     * @param companyId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return ApiResponse&lt;GuidEntityVersionPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidEntityVersionPageResultModel> apiV10MasterdataCompaniesCompanyIdVersionsGetWithHttpInfo(UUID companyId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataCompaniesCompanyIdVersionsGetValidateBeforeCall(companyId, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<GuidEntityVersionPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get company versions (asynchronously)
     * 
     * @param companyId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesCompanyIdVersionsGetAsync(UUID companyId, Integer pageOffset, Integer pageSize, final ApiCallback<GuidEntityVersionPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataCompaniesCompanyIdVersionsGetValidateBeforeCall(companyId, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<GuidEntityVersionPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataCompaniesGet
     * @param organisationId The id of the organisation for which the companies are requested. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesGetCall(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/companies";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataCompaniesGetValidateBeforeCall(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataCompaniesGetCall(organisationId, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all companies for the requested organisation for the authenticated user.
     * 
     * @param organisationId The id of the organisation for which the companies are requested. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return CompanyModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CompanyModelPageResultModel apiV10MasterdataCompaniesGet(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<CompanyModelPageResultModel> localVarResp = apiV10MasterdataCompaniesGetWithHttpInfo(organisationId, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all companies for the requested organisation for the authenticated user.
     * 
     * @param organisationId The id of the organisation for which the companies are requested. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;CompanyModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CompanyModelPageResultModel> apiV10MasterdataCompaniesGetWithHttpInfo(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataCompaniesGetValidateBeforeCall(organisationId, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<CompanyModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all companies for the requested organisation for the authenticated user. (asynchronously)
     * 
     * @param organisationId The id of the organisation for which the companies are requested. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesGetAsync(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<CompanyModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataCompaniesGetValidateBeforeCall(organisationId, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<CompanyModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataCompaniesIdGet
     * @param organizationId The id of the organization of the requested store. (optional)
     * @param storeNumber The store number of the requested store. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesIdGetCall(UUID organizationId, String storeNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/companies/id";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organizationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organizationId", organizationId));
        }

        if (storeNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeNumber", storeNumber));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataCompaniesIdGetValidateBeforeCall(UUID organizationId, String storeNumber, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataCompaniesIdGetCall(organizationId, storeNumber, _callback);

    }

    /**
     * Returns the company id (if applicable) of the requested store.
     * Not every store belongs to a company, therefore it possible that no companyid is returned.
     * @param organizationId The id of the organization of the requested store. (optional)
     * @param storeNumber The store number of the requested store. (optional)
     * @return UUID
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public UUID apiV10MasterdataCompaniesIdGet(UUID organizationId, String storeNumber) throws ApiException {
        ApiResponse<UUID> localVarResp = apiV10MasterdataCompaniesIdGetWithHttpInfo(organizationId, storeNumber);
        return localVarResp.getData();
    }

    /**
     * Returns the company id (if applicable) of the requested store.
     * Not every store belongs to a company, therefore it possible that no companyid is returned.
     * @param organizationId The id of the organization of the requested store. (optional)
     * @param storeNumber The store number of the requested store. (optional)
     * @return ApiResponse&lt;UUID&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<UUID> apiV10MasterdataCompaniesIdGetWithHttpInfo(UUID organizationId, String storeNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataCompaniesIdGetValidateBeforeCall(organizationId, storeNumber, null);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the company id (if applicable) of the requested store. (asynchronously)
     * Not every store belongs to a company, therefore it possible that no companyid is returned.
     * @param organizationId The id of the organization of the requested store. (optional)
     * @param storeNumber The store number of the requested store. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesIdGetAsync(UUID organizationId, String storeNumber, final ApiCallback<UUID> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataCompaniesIdGetValidateBeforeCall(organizationId, storeNumber, _callback);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataCompaniesPost
     * @param company The new company to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesPostCall(Company company, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = company;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/companies";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataCompaniesPostValidateBeforeCall(Company company, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataCompaniesPostCall(company, _callback);

    }

    /**
     * Creates a new company in the cloud store.
     * If RetailForce.Cloud.Model.Company.CompanyId set to System.Guid.Empty, then the company id will be generated by the service.
     * @param company The new company to create. (optional)
     * @return CompanyModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CompanyModel apiV10MasterdataCompaniesPost(Company company) throws ApiException {
        ApiResponse<CompanyModel> localVarResp = apiV10MasterdataCompaniesPostWithHttpInfo(company);
        return localVarResp.getData();
    }

    /**
     * Creates a new company in the cloud store.
     * If RetailForce.Cloud.Model.Company.CompanyId set to System.Guid.Empty, then the company id will be generated by the service.
     * @param company The new company to create. (optional)
     * @return ApiResponse&lt;CompanyModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CompanyModel> apiV10MasterdataCompaniesPostWithHttpInfo(Company company) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataCompaniesPostValidateBeforeCall(company, null);
        Type localVarReturnType = new TypeToken<CompanyModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a new company in the cloud store. (asynchronously)
     * If RetailForce.Cloud.Model.Company.CompanyId set to System.Guid.Empty, then the company id will be generated by the service.
     * @param company The new company to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesPostAsync(Company company, final ApiCallback<CompanyModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataCompaniesPostValidateBeforeCall(company, _callback);
        Type localVarReturnType = new TypeToken<CompanyModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataCompaniesSimpleGet
     * @param organisationId The id of the organisation for which the companies are requested. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesSimpleGetCall(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/companies/simple";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataCompaniesSimpleGetValidateBeforeCall(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataCompaniesSimpleGetCall(organisationId, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all companies as a RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested organisation for the authenticated user.
     * 
     * @param organisationId The id of the organisation for which the companies are requested. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return GuidSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObjectPageResultModel apiV10MasterdataCompaniesSimpleGet(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<GuidSimpleObjectPageResultModel> localVarResp = apiV10MasterdataCompaniesSimpleGetWithHttpInfo(organisationId, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all companies as a RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested organisation for the authenticated user.
     * 
     * @param organisationId The id of the organisation for which the companies are requested. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;GuidSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObjectPageResultModel> apiV10MasterdataCompaniesSimpleGetWithHttpInfo(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataCompaniesSimpleGetValidateBeforeCall(organisationId, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all companies as a RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested organisation for the authenticated user. (asynchronously)
     * 
     * @param organisationId The id of the organisation for which the companies are requested. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataCompaniesSimpleGetAsync(UUID organisationId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<GuidSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataCompaniesSimpleGetValidateBeforeCall(organisationId, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
