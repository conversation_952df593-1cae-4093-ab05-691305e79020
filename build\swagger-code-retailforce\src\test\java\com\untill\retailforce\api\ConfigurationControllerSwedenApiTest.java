/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.ControlUnitDriverInfo;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ConfigurationControllerSwedenApi
 */
@Disabled
public class ConfigurationControllerSwedenApiTest {

    private final ConfigurationControllerSwedenApi api = new ConfigurationControllerSwedenApi();

    /**
     * Returns supported smart card drivers for configuration in the cloud user interface.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationSeControlUnitDriverInfoGetTest() throws ApiException {
        List<ControlUnitDriverInfo> response = api.apiV10ConfigurationSeControlUnitDriverInfoGet();
        // TODO: test validations
    }

}
