/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ThemingApi
 */
@Disabled
public class ThemingApiTest {

    private final ThemingApi api = new ThemingApi();

    /**
     * Get Countries
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ThemingThemeCssGetTest() throws ApiException {
        api.apiV10ThemingThemeCssGet();
        // TODO: test validations
    }

}
