# MasterDataApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MasterdataDistributorsDistributorIdSupportHoursGet**](MasterDataApi.md#apiV10MasterdataDistributorsDistributorIdSupportHoursGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/support-hours | Get open support hours for distributor |
| [**apiV10MasterdataDistributorsDistributorIdTimelogGet**](MasterDataApi.md#apiV10MasterdataDistributorsDistributorIdTimelogGet) | **GET** /api/v1.0/masterdata/distributors/{distributorId}/timelog | Get onboarding or customer care timelog for distributor |
| [**apiV10MasterdataEntityEntityIdHasOrganizationGet**](MasterDataApi.md#apiV10MasterdataEntityEntityIdHasOrganizationGet) | **GET** /api/v1.0/masterdata/entity/{entityId}/hasOrganization | Returns true when entity has allocated organizations. |
| [**apiV10MasterdataImportGet**](MasterDataApi.md#apiV10MasterdataImportGet) | **GET** /api/v1.0/masterdata/import | Load imports of Organizations/Companies/Terminals imports with pagination for the authenticated user. |
| [**apiV10MasterdataImportImportIdGet**](MasterDataApi.md#apiV10MasterdataImportImportIdGet) | **GET** /api/v1.0/masterdata/import/{importId} | Returns the detail information of the requested import log. |
| [**apiV10MasterdataImportPost**](MasterDataApi.md#apiV10MasterdataImportPost) | **POST** /api/v1.0/masterdata/import | Import Organizations/Companies/Terminals as CSV |
| [**apiV10MasterdataParameterEntityTypeEntityIdGet**](MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdGet) | **GET** /api/v1.0/masterdata/parameter/{entityType}/{entityId} | Returns all stored parameters for the given entity. |
| [**apiV10MasterdataParameterEntityTypeEntityIdInfoGet**](MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdInfoGet) | **GET** /api/v1.0/masterdata/parameter/{entityType}/{entityId}/info | Get parameter infos (available parameters) for the given entity. |
| [**apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete**](MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete) | **DELETE** /api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid} | Deletes an existing entity parameter. |
| [**apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet**](MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet) | **GET** /api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid} | Returns requested stored parameter for the given entity and parameterId. |
| [**apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut**](MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut) | **PUT** /api/v1.0/masterdata/parameter/{entityType}/{entityId}/{parameterGuid} | Updates an existing entity parameter. |
| [**apiV10MasterdataParameterEntityTypeEntityIdPost**](MasterDataApi.md#apiV10MasterdataParameterEntityTypeEntityIdPost) | **POST** /api/v1.0/masterdata/parameter/{entityType}/{entityId} | Creates a new entity parameter. |
| [**apiV10MasterdataVatCheckSimpleGet**](MasterDataApi.md#apiV10MasterdataVatCheckSimpleGet) | **GET** /api/v1.0/masterdata/vatCheck/simple | Returns whether the given vatNumber is ok or not. |


<a id="apiV10MasterdataDistributorsDistributorIdSupportHoursGet"></a>
# **apiV10MasterdataDistributorsDistributorIdSupportHoursGet**
> List&lt;TimelogOverviewModel&gt; apiV10MasterdataDistributorsDistributorIdSupportHoursGet(distributorId)

Get open support hours for distributor

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | 
    try {
      List<TimelogOverviewModel> result = apiInstance.apiV10MasterdataDistributorsDistributorIdSupportHoursGet(distributorId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataDistributorsDistributorIdSupportHoursGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**|  | |

### Return type

[**List&lt;TimelogOverviewModel&gt;**](TimelogOverviewModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataDistributorsDistributorIdTimelogGet"></a>
# **apiV10MasterdataDistributorsDistributorIdTimelogGet**
> List&lt;TimelogOverviewModel&gt; apiV10MasterdataDistributorsDistributorIdTimelogGet(distributorId)

Get onboarding or customer care timelog for distributor

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    UUID distributorId = UUID.randomUUID(); // UUID | 
    try {
      List<TimelogOverviewModel> result = apiInstance.apiV10MasterdataDistributorsDistributorIdTimelogGet(distributorId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataDistributorsDistributorIdTimelogGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **distributorId** | **UUID**|  | |

### Return type

[**List&lt;TimelogOverviewModel&gt;**](TimelogOverviewModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataEntityEntityIdHasOrganizationGet"></a>
# **apiV10MasterdataEntityEntityIdHasOrganizationGet**
> Boolean apiV10MasterdataEntityEntityIdHasOrganizationGet(entityId)

Returns true when entity has allocated organizations.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity to check (distributor or supplier).
    try {
      Boolean result = apiInstance.apiV10MasterdataEntityEntityIdHasOrganizationGet(entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataEntityEntityIdHasOrganizationGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| The id of the entity to check (distributor or supplier). | |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataImportGet"></a>
# **apiV10MasterdataImportGet**
> ImportModelPageResultModel apiV10MasterdataImportGet(pageOffset, pageSize)

Load imports of Organizations/Companies/Terminals imports with pagination for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    try {
      ImportModelPageResultModel result = apiInstance.apiV10MasterdataImportGet(pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataImportGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |

### Return type

[**ImportModelPageResultModel**](ImportModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataImportImportIdGet"></a>
# **apiV10MasterdataImportImportIdGet**
> ImportModelPageResultModel apiV10MasterdataImportImportIdGet(importId, pageOffset, pageSize)

Returns the detail information of the requested import log.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    UUID importId = UUID.randomUUID(); // UUID | The id of the requested import.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit
    try {
      ImportModelPageResultModel result = apiInstance.apiV10MasterdataImportImportIdGet(importId, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataImportImportIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **importId** | **UUID**| The id of the requested import. | |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit | [optional] |

### Return type

[**ImportModelPageResultModel**](ImportModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataImportPost"></a>
# **apiV10MasterdataImportPost**
> ImportModel apiV10MasterdataImportPost(_file)

Import Organizations/Companies/Terminals as CSV

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    File _file = new File("/path/to/file"); // File | 
    try {
      ImportModel result = apiInstance.apiV10MasterdataImportPost(_file);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataImportPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **_file** | **File**|  | |

### Return type

[**ImportModel**](ImportModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataParameterEntityTypeEntityIdGet"></a>
# **apiV10MasterdataParameterEntityTypeEntityIdGet**
> List&lt;CloudParameter&gt; apiV10MasterdataParameterEntityTypeEntityIdGet(entityType, entityId)

Returns all stored parameters for the given entity.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    String entityType = "entityType_example"; // String | The type of the entity for the request.
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity for the request.
    try {
      List<CloudParameter> result = apiInstance.apiV10MasterdataParameterEntityTypeEntityIdGet(entityType, entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataParameterEntityTypeEntityIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The type of the entity for the request. | |
| **entityId** | **UUID**| The id of the entity for the request. | |

### Return type

[**List&lt;CloudParameter&gt;**](CloudParameter.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataParameterEntityTypeEntityIdInfoGet"></a>
# **apiV10MasterdataParameterEntityTypeEntityIdInfoGet**
> List&lt;ParameterInfo&gt; apiV10MasterdataParameterEntityTypeEntityIdInfoGet(entityType, entityId)

Get parameter infos (available parameters) for the given entity.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    String entityType = "entityType_example"; // String | The type of the entity for the request.
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity for the request.
    try {
      List<ParameterInfo> result = apiInstance.apiV10MasterdataParameterEntityTypeEntityIdInfoGet(entityType, entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataParameterEntityTypeEntityIdInfoGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The type of the entity for the request. | |
| **entityId** | **UUID**| The id of the entity for the request. | |

### Return type

[**List&lt;ParameterInfo&gt;**](ParameterInfo.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete"></a>
# **apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete**
> apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete(entityType, entityId, parameterGuid)

Deletes an existing entity parameter.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    String entityType = "entityType_example"; // String | The type of the entity for the request.
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity for the request.
    UUID parameterGuid = UUID.randomUUID(); // UUID | The guid of the parameter to delete.
    try {
      apiInstance.apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete(entityType, entityId, parameterGuid);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The type of the entity for the request. | |
| **entityId** | **UUID**| The id of the entity for the request. | |
| **parameterGuid** | **UUID**| The guid of the parameter to delete. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet"></a>
# **apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet**
> CloudParameter apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet(entityType, entityId, parameterGuid)

Returns requested stored parameter for the given entity and parameterId.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    String entityType = "entityType_example"; // String | The type of the entity for the request.
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity for the request.
    UUID parameterGuid = UUID.randomUUID(); // UUID | The id of the parameter to load.
    try {
      CloudParameter result = apiInstance.apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet(entityType, entityId, parameterGuid);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The type of the entity for the request. | |
| **entityId** | **UUID**| The id of the entity for the request. | |
| **parameterGuid** | **UUID**| The id of the parameter to load. | |

### Return type

[**CloudParameter**](CloudParameter.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut"></a>
# **apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut**
> CloudParameter apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut(entityType, entityId, parameterGuid, cloudParameter)

Updates an existing entity parameter.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    String entityType = "entityType_example"; // String | The type of the entity for the request.
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity for the request.
    UUID parameterGuid = UUID.randomUUID(); // UUID | The guid of the parameter to change.
    CloudParameter cloudParameter = new CloudParameter(); // CloudParameter | The parameter to update.
    try {
      CloudParameter result = apiInstance.apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut(entityType, entityId, parameterGuid, cloudParameter);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataParameterEntityTypeEntityIdParameterGuidPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The type of the entity for the request. | |
| **entityId** | **UUID**| The id of the entity for the request. | |
| **parameterGuid** | **UUID**| The guid of the parameter to change. | |
| **cloudParameter** | [**CloudParameter**](CloudParameter.md)| The parameter to update. | |

### Return type

[**CloudParameter**](CloudParameter.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataParameterEntityTypeEntityIdPost"></a>
# **apiV10MasterdataParameterEntityTypeEntityIdPost**
> CloudParameter apiV10MasterdataParameterEntityTypeEntityIdPost(entityType, entityId, cloudParameter)

Creates a new entity parameter.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    String entityType = "entityType_example"; // String | The type of the entity for the request.
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity for the request.
    CloudParameter cloudParameter = new CloudParameter(); // CloudParameter | The RetailForce.Cloud.Model.Configuration.CloudParameter to create.
    try {
      CloudParameter result = apiInstance.apiV10MasterdataParameterEntityTypeEntityIdPost(entityType, entityId, cloudParameter);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataParameterEntityTypeEntityIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The type of the entity for the request. | |
| **entityId** | **UUID**| The id of the entity for the request. | |
| **cloudParameter** | [**CloudParameter**](CloudParameter.md)| The RetailForce.Cloud.Model.Configuration.CloudParameter to create. | |

### Return type

[**CloudParameter**](CloudParameter.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataVatCheckSimpleGet"></a>
# **apiV10MasterdataVatCheckSimpleGet**
> BoolResponse apiV10MasterdataVatCheckSimpleGet(vatNumber, entityType)

Returns whether the given vatNumber is ok or not.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataApi apiInstance = new MasterDataApi(defaultClient);
    String vatNumber = "vatNumber_example"; // String | The vatNumber to check.
    EntityTypes entityType = EntityTypes.fromValue("organisation"); // EntityTypes | 
    try {
      BoolResponse result = apiInstance.apiV10MasterdataVatCheckSimpleGet(vatNumber, entityType);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataApi#apiV10MasterdataVatCheckSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **vatNumber** | **String**| The vatNumber to check. | [optional] |
| **entityType** | [**EntityTypes**](.md)|  | [optional] [enum: organisation, distributor, distributorContract, configuration, invitation, import, company, store, terminal, profile, configurationLicense, userAssignment, supportPackage, receipt, billingDistributor, billingOrganisation, billingPricing, certificate, backupdata, entityParameter, releases, license, accessLicense, signatureDevice, upload, supplier, supplierContract, accessLicenseContract, supportTicket, timelog, tseInformation, clearingRun, tseAnnouncement] |

### Return type

[**BoolResponse**](BoolResponse.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

