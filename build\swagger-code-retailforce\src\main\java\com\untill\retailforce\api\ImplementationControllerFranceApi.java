/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import java.io.File;
import java.time.OffsetDateTime;
import java.util.UUID;
import com.untill.retailforce.model.ValidationError;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ImplementationControllerFranceApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ImplementationControllerFranceApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ImplementationControllerFranceApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ImplementationFrTerminalIdStoreArchivePost
     * @param terminalId The terminal id of the terminal which wants to store the french archive. (required)
     * @param archiveFilename The filename of the archive. (optional)
     * @param frenchArchiveFile  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or Frencharchive is null or archiveFilename is null or string.Empty or the unique client id of the fiscal client in the french archive does not correspond to parameter terminalId. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationFrTerminalIdStoreArchivePostCall(UUID terminalId, String archiveFilename, File frenchArchiveFile, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/fr/{terminalId}/storeArchive"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (frenchArchiveFile != null) {
            localVarFormParams.put("frenchArchiveFile", frenchArchiveFile);
        }

        if (archiveFilename != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("archiveFilename", archiveFilename));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationFrTerminalIdStoreArchivePostValidateBeforeCall(UUID terminalId, String archiveFilename, File frenchArchiveFile, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationFrTerminalIdStoreArchivePost(Async)");
        }

        return apiV10ImplementationFrTerminalIdStoreArchivePostCall(terminalId, archiveFilename, frenchArchiveFile, _callback);

    }

    /**
     * Stores the french archive to the french archive store.
     * 
     * @param terminalId The terminal id of the terminal which wants to store the french archive. (required)
     * @param archiveFilename The filename of the archive. (optional)
     * @param frenchArchiveFile  (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or Frencharchive is null or archiveFilename is null or string.Empty or the unique client id of the fiscal client in the french archive does not correspond to parameter terminalId. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationFrTerminalIdStoreArchivePost(UUID terminalId, String archiveFilename, File frenchArchiveFile) throws ApiException {
        apiV10ImplementationFrTerminalIdStoreArchivePostWithHttpInfo(terminalId, archiveFilename, frenchArchiveFile);
    }

    /**
     * Stores the french archive to the french archive store.
     * 
     * @param terminalId The terminal id of the terminal which wants to store the french archive. (required)
     * @param archiveFilename The filename of the archive. (optional)
     * @param frenchArchiveFile  (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or Frencharchive is null or archiveFilename is null or string.Empty or the unique client id of the fiscal client in the french archive does not correspond to parameter terminalId. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationFrTerminalIdStoreArchivePostWithHttpInfo(UUID terminalId, String archiveFilename, File frenchArchiveFile) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationFrTerminalIdStoreArchivePostValidateBeforeCall(terminalId, archiveFilename, frenchArchiveFile, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Stores the french archive to the french archive store. (asynchronously)
     * 
     * @param terminalId The terminal id of the terminal which wants to store the french archive. (required)
     * @param archiveFilename The filename of the archive. (optional)
     * @param frenchArchiveFile  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or Frencharchive is null or archiveFilename is null or string.Empty or the unique client id of the fiscal client in the french archive does not correspond to parameter terminalId. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationFrTerminalIdStoreArchivePostAsync(UUID terminalId, String archiveFilename, File frenchArchiveFile, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationFrTerminalIdStoreArchivePostValidateBeforeCall(terminalId, archiveFilename, frenchArchiveFile, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationFrTerminalIdTaxArchiveGet
     * @param terminalId The terminal id where the archives should be exported. (required)
     * @param fromDate The start date of the request. (required)
     * @param tillDate The end date of the request. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Terminal id was set to Guid.Empty or from date / till date are not in valid range (1.1.2016 - now). </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationFrTerminalIdTaxArchiveGetCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/fr/{terminalId}/taxArchive"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (tillDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tillDate", tillDate));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationFrTerminalIdTaxArchiveGetValidateBeforeCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationFrTerminalIdTaxArchiveGet(Async)");
        }

        // verify the required parameter 'fromDate' is set
        if (fromDate == null) {
            throw new ApiException("Missing the required parameter 'fromDate' when calling apiV10ImplementationFrTerminalIdTaxArchiveGet(Async)");
        }

        // verify the required parameter 'tillDate' is set
        if (tillDate == null) {
            throw new ApiException("Missing the required parameter 'tillDate' when calling apiV10ImplementationFrTerminalIdTaxArchiveGet(Async)");
        }

        return apiV10ImplementationFrTerminalIdTaxArchiveGetCall(terminalId, fromDate, tillDate, _callback);

    }

    /**
     * Exports one or more french fiscal archives according to date parameter.
     * 
     * @param terminalId The terminal id where the archives should be exported. (required)
     * @param fromDate The start date of the request. (required)
     * @param tillDate The end date of the request. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Terminal id was set to Guid.Empty or from date / till date are not in valid range (1.1.2016 - now). </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationFrTerminalIdTaxArchiveGet(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate) throws ApiException {
        apiV10ImplementationFrTerminalIdTaxArchiveGetWithHttpInfo(terminalId, fromDate, tillDate);
    }

    /**
     * Exports one or more french fiscal archives according to date parameter.
     * 
     * @param terminalId The terminal id where the archives should be exported. (required)
     * @param fromDate The start date of the request. (required)
     * @param tillDate The end date of the request. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Terminal id was set to Guid.Empty or from date / till date are not in valid range (1.1.2016 - now). </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationFrTerminalIdTaxArchiveGetWithHttpInfo(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationFrTerminalIdTaxArchiveGetValidateBeforeCall(terminalId, fromDate, tillDate, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Exports one or more french fiscal archives according to date parameter. (asynchronously)
     * 
     * @param terminalId The terminal id where the archives should be exported. (required)
     * @param fromDate The start date of the request. (required)
     * @param tillDate The end date of the request. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Terminal id was set to Guid.Empty or from date / till date are not in valid range (1.1.2016 - now). </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationFrTerminalIdTaxArchiveGetAsync(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationFrTerminalIdTaxArchiveGetValidateBeforeCall(terminalId, fromDate, tillDate, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost
     * @param terminalId The terminal id of the french archive. (required)
     * @param frenchArchiveFile  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or no file was uploaded. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostCall(UUID terminalId, File frenchArchiveFile, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/fr/{terminalId}/taxArchive/verify"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (frenchArchiveFile != null) {
            localVarFormParams.put("frenchArchiveFile", frenchArchiveFile);
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostValidateBeforeCall(UUID terminalId, File frenchArchiveFile, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost(Async)");
        }

        // verify the required parameter 'frenchArchiveFile' is set
        if (frenchArchiveFile == null) {
            throw new ApiException("Missing the required parameter 'frenchArchiveFile' when calling apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost(Async)");
        }

        return apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostCall(terminalId, frenchArchiveFile, _callback);

    }

    /**
     * Method to verify the french archive.
     * 
     * @param terminalId The terminal id of the french archive. (required)
     * @param frenchArchiveFile  (required)
     * @return List&lt;ValidationError&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or no file was uploaded. </td><td>  -  </td></tr>
     </table>
     */
    public List<ValidationError> apiV10ImplementationFrTerminalIdTaxArchiveVerifyPost(UUID terminalId, File frenchArchiveFile) throws ApiException {
        ApiResponse<List<ValidationError>> localVarResp = apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostWithHttpInfo(terminalId, frenchArchiveFile);
        return localVarResp.getData();
    }

    /**
     * Method to verify the french archive.
     * 
     * @param terminalId The terminal id of the french archive. (required)
     * @param frenchArchiveFile  (required)
     * @return ApiResponse&lt;List&lt;ValidationError&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or no file was uploaded. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ValidationError>> apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostWithHttpInfo(UUID terminalId, File frenchArchiveFile) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostValidateBeforeCall(terminalId, frenchArchiveFile, null);
        Type localVarReturnType = new TypeToken<List<ValidationError>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Method to verify the french archive. (asynchronously)
     * 
     * @param terminalId The terminal id of the french archive. (required)
     * @param frenchArchiveFile  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId &#x3D; Guid.Empty or no file was uploaded. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostAsync(UUID terminalId, File frenchArchiveFile, final ApiCallback<List<ValidationError>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationFrTerminalIdTaxArchiveVerifyPostValidateBeforeCall(terminalId, frenchArchiveFile, _callback);
        Type localVarReturnType = new TypeToken<List<ValidationError>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
