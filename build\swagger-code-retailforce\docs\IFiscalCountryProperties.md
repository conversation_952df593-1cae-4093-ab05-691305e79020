

# IFiscalCountryProperties


## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**fiscalCountry** | **FiscalCountry** |  |  [optional] |
|**fiscalLineInterface** | **Boolean** |  |  [optional] [readonly] |
|**supportedLegislation** | **String** |  |  [optional] [readonly] |
|**fiscalisationType** | **FiscalisationType** |  |  [optional] |
|**startDocumentType** | **DocumentType** |  |  [optional] |
|**fiscalDocumentTypes** | **List&lt;DocumentType&gt;** |  |  [optional] [readonly] |
|**supportedPlatformTypes** | **List&lt;PlatformType&gt;** |  |  [optional] [readonly] |
|**supportedDocumentTypes** | **List&lt;DocumentType&gt;** |  |  [optional] [readonly] |
|**notSupportedDocumentTypes** | **List&lt;DocumentType&gt;** |  |  [optional] [readonly] |
|**supportedPaymentTypes** | **List&lt;PaymentType&gt;** |  |  [optional] [readonly] |
|**notSupportedPaymentTypes** | **List&lt;PaymentType&gt;** |  |  [optional] [readonly] |
|**overpaymentNotAllowed** | **List&lt;PaymentType&gt;** |  |  [optional] [readonly] |
|**supportedBusinessTransactionTypes** | **List&lt;BusinessTransactionType&gt;** |  |  [optional] [readonly] |
|**notSupportedBusinessTransactionTypes** | **List&lt;BusinessTransactionType&gt;** |  |  [optional] [readonly] |
|**supportedPayOutTypes** | **List&lt;PayOutType&gt;** |  |  [optional] [readonly] |
|**notSupportedPayOutTypes** | **List&lt;PayOutType&gt;** |  |  [optional] [readonly] |
|**documentTypeBusinessTransactionTypeSupport** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport.md) |  |  [optional] |
|**supportedVatDefinitions** | [**List&lt;Vat&gt;**](Vat.md) |  |  [optional] [readonly] |
|**fiscalModuleCalculatesTaxRates** | **Boolean** |  |  [optional] [readonly] |
|**customerDisplayMandatory** | **Boolean** |  |  [optional] [readonly] |
|**terminalSlaveSupported** | **Boolean** |  |  [optional] [readonly] |
|**auditLogMandatory** | **Boolean** |  |  [optional] [readonly] |
|**mustRecordDocumentReprint** | **Boolean** |  |  [optional] [readonly] |
|**reprintAllowedDomain** | **ReceiptDomain** |  |  [optional] |
|**mustSendClosingPerDay** | **Boolean** |  |  [optional] [readonly] |
|**maximumHourIntervalForClosing** | **Integer** |  |  [optional] [readonly] |
|**returnAndSaleBehavior** | **ReturnAndSaleBehavior** |  |  [optional] |
|**returnReferenceBehavior** | **ReturnReferenceBehavior** |  |  [optional] |
|**returnDomainAllowed** | **ReceiptDomain** |  |  [optional] |
|**printFiscalDocumentNumber** | **Boolean** |  |  [optional] [readonly] |
|**getCloudArchiveAccessLicense** | **String** |  |  [optional] [readonly] |
|**countryModuleVersion** | **String** |  |  [optional] [readonly] |
|**taxFreeVat** | [**Vat**](Vat.md) |  |  [optional] |
|**storeNumberValidCharsRegex** | **String** |  |  [optional] [readonly] |
|**terminalNumberValidCharsRegex** | **String** |  |  [optional] [readonly] |
|**fiscalRegions** | **List&lt;String&gt;** |  |  [optional] [readonly] |
|**surchargesPossible** | **Boolean** |  |  [optional] [readonly] |
|**trainingModeAllowed** | **Boolean** |  |  [optional] [readonly] |
|**amountDecimalPlaces** | **Integer** |  |  [optional] [readonly] |
|**supportedEnvironments** | **List&lt;FiscalModuleEnvironment&gt;** |  |  [optional] [readonly] |
|**entityParameters** | [**List&lt;EntityParameterInfo&gt;**](EntityParameterInfo.md) |  |  [optional] [readonly] |
|**legalForms** | [**List&lt;LegalForm&gt;**](LegalForm.md) |  |  [optional] [readonly] |



