/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.AuditLogEntry;
import java.time.OffsetDateTime;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ImplementationApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ImplementationApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ImplementationApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ImplementationTerminalIdAuditLogGet
     * @param terminalId The id of the terminal where the data is requested. (required)
     * @param fromDate The start date of the requested records. (optional)
     * @param tillDate The end date of the requested records. (optional)
     * @param orderDesc Order is by date ascending, set to true for order by date descending. (optional, default to false)
     * @param pageOffset Page offset parameter for paging. (optional)
     * @param pageSize Page size parameter for paging. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId was set to Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationTerminalIdAuditLogGetCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean orderDesc, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/{terminalId}/auditLog"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (tillDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tillDate", tillDate));
        }

        if (orderDesc != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("orderDesc", orderDesc));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationTerminalIdAuditLogGetValidateBeforeCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean orderDesc, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationTerminalIdAuditLogGet(Async)");
        }

        return apiV10ImplementationTerminalIdAuditLogGetCall(terminalId, fromDate, tillDate, orderDesc, pageOffset, pageSize, _callback);

    }

    /**
     * Returns audit log records from storage.
     * 
     * @param terminalId The id of the terminal where the data is requested. (required)
     * @param fromDate The start date of the requested records. (optional)
     * @param tillDate The end date of the requested records. (optional)
     * @param orderDesc Order is by date ascending, set to true for order by date descending. (optional, default to false)
     * @param pageOffset Page offset parameter for paging. (optional)
     * @param pageSize Page size parameter for paging. (optional)
     * @return List&lt;AuditLogEntry&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId was set to Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public List<AuditLogEntry> apiV10ImplementationTerminalIdAuditLogGet(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean orderDesc, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<List<AuditLogEntry>> localVarResp = apiV10ImplementationTerminalIdAuditLogGetWithHttpInfo(terminalId, fromDate, tillDate, orderDesc, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Returns audit log records from storage.
     * 
     * @param terminalId The id of the terminal where the data is requested. (required)
     * @param fromDate The start date of the requested records. (optional)
     * @param tillDate The end date of the requested records. (optional)
     * @param orderDesc Order is by date ascending, set to true for order by date descending. (optional, default to false)
     * @param pageOffset Page offset parameter for paging. (optional)
     * @param pageSize Page size parameter for paging. (optional)
     * @return ApiResponse&lt;List&lt;AuditLogEntry&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId was set to Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<AuditLogEntry>> apiV10ImplementationTerminalIdAuditLogGetWithHttpInfo(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean orderDesc, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationTerminalIdAuditLogGetValidateBeforeCall(terminalId, fromDate, tillDate, orderDesc, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<List<AuditLogEntry>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns audit log records from storage. (asynchronously)
     * 
     * @param terminalId The id of the terminal where the data is requested. (required)
     * @param fromDate The start date of the requested records. (optional)
     * @param tillDate The end date of the requested records. (optional)
     * @param orderDesc Order is by date ascending, set to true for order by date descending. (optional, default to false)
     * @param pageOffset Page offset parameter for paging. (optional)
     * @param pageSize Page size parameter for paging. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> TerminalId was set to Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationTerminalIdAuditLogGetAsync(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean orderDesc, Integer pageOffset, Integer pageSize, final ApiCallback<List<AuditLogEntry>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationTerminalIdAuditLogGetValidateBeforeCall(terminalId, fromDate, tillDate, orderDesc, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<List<AuditLogEntry>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationTerminalIdRestoreclientdataGet
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationTerminalIdRestoreclientdataGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/{terminalId}/restoreclientdata"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationTerminalIdRestoreclientdataGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationTerminalIdRestoreclientdataGet(Async)");
        }

        return apiV10ImplementationTerminalIdRestoreclientdataGetCall(terminalId, _callback);

    }

    /**
     * Exports current client data from cloud archive.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationTerminalIdRestoreclientdataGet(UUID terminalId) throws ApiException {
        apiV10ImplementationTerminalIdRestoreclientdataGetWithHttpInfo(terminalId);
    }

    /**
     * Exports current client data from cloud archive.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationTerminalIdRestoreclientdataGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationTerminalIdRestoreclientdataGetValidateBeforeCall(terminalId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Exports current client data from cloud archive. (asynchronously)
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationTerminalIdRestoreclientdataGetAsync(UUID terminalId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationTerminalIdRestoreclientdataGetValidateBeforeCall(terminalId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
}
