

# IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport


## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**_0equalReceipt** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_1equalInvoice** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_2equalDeliveryNote** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_10equalPayOut** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_11equalPayIn** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_12equalProformaInvoice** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_13equalCustomerOrder** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_14equalPreliminaryReceipt** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_15equalPaymentReceipt** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_80equalLongTermOrder** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_90equalOpeningBalance** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_98equalCashCheck** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_99equalEndOfDay** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_100equalInventory** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_101equalPurchase** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_1000equalNullReceipt** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_1001equalPrintingReceipt** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |
|**_1100equalMiscellaneousNonFiscal** | [**IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt**](IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.md) |  |  [optional] |



