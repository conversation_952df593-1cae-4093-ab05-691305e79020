

# Terminal

Represents a terminal and it's configuration.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**terminalId** | **UUID** | The id of the terminal. |  [optional] |
|**storeId** | **UUID** | The store of the terminal. |  [optional] |
|**terminalNumber** | **String** | The terminal number. |  [optional] |
|**fiscalCountry** | **FiscalCountry** |  |  [optional] |
|**caption** | **String** | Gets or sets a possible caption for a terminal. |  [optional] |
|**platformType** | **PlatformType** |  |  [optional] |
|**isTest** | **Boolean** | True if it is a test terminal. |  [optional] |
|**archiveDate** | **OffsetDateTime** | The date when the terminal was archived (decommissioned). |  [optional] |
|**clientConfigurationId** | **UUID** | The configuration for this terminal. |  [optional] |
|**newClientConfigurationId** | **UUID** | The new configuration for this terminal valid from RetailForce.Cloud.Model.Terminal.NewClientConfigurationValidFrom. |  [optional] |
|**newClientConfigurationValidFrom** | **OffsetDateTime** | The validity date for the new configuration. |  [optional] |
|**globalShortId** | **String** | Represents a 4 digit (alphanumeric, case-sensitive) value representing a global unique short id for the terminal. |  [optional] |
|**purchaseDate** | **OffsetDateTime** | Purchase date |  [optional] |
|**commissioningDate** | **OffsetDateTime** | Commissioning date |  [optional] |
|**decommissioningDate** | **OffsetDateTime** | Decommissioning date |  [optional] |
|**terminalType** | **TerminalType** |  |  [optional] |
|**updatedByPrincipalId** | **UUID** | Updated by PrincipalId |  [optional] |
|**cashRegisterId** | **String** | The id of the cash register hardware. |  [optional] |
|**cashRegisterBrand** | **String** | The brand of the cash register hardware. |  [optional] |
|**cashRegisterModelname** | **String** | The model name of the cash register hardware. |  [optional] |
|**cashRegisterSoftwareBrand** | **String** | The software of the cash register. |  [optional] |
|**cashRegisterSoftwareVersion** | **String** | The version of the software of the cash register. |  [optional] |
|**cashRegisterSoftwareCompany** | **String** | The company name of the creator of the cash register software. |  [optional] |



