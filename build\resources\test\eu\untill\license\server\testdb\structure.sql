--CREATE SEQUENCE GEN_TABLE_ID;
--CREATE VIEW GENERATE_TABLE_ID (ID) AS SELECT 1*********** + NEXT VALUE FOR GEN_TABLE_ID;
CREATE ALIAS GENERATE_TABLE_ID FOR "eu.untill.license.server.TestDb.generateTableId";
CREATE VIEW GENERATE_TABLE_ID (ID) AS SELECT GENERATE_TABLE_ID();

CREATE ALIAS BIN_AND FOR "eu.untill.license.server.TestDb.binAnd";
CREATE ALIAS BIN_SHL FOR "eu.untill.license.server.TestDb.binShl";
--CREATE ALIAS BIN_AND AS $$ long bin_and(long a, long b) { return a & b; } $$;

CREATE TABLE COUNTRIES (
	ID                  BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID), 
	NAME                VARCHAR(100) NOT NULL,
	KIND                SMALLINT NOT NULL,
	IS_ACTIVE           SMALLINT,
	IS_ACTIVE_MODIFIED  TIMESTAMP,
	IS_ACTIVE_MODIFIER  VARCHAR(30),
	CONSTRAINT COUNTRIES_PK PRIMARY KEY (ID)
);

CREATE TABLE EXTRA_FIELD_VALUES (
	ID  BIGINT NOT NULL,
	CONSTRAINT EXTRA_FIELD_VALUES_PK PRIMARY KEY (ID)
);

CREATE TABLE EXTRA_FIELD_DEFS (
	ID                  BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	NAME                VARCHAR(50),
	DATATYPE            SMALLINT,
	GUID                VARCHAR(50),
	IS_ACTIVE           SMALLINT,
	IS_ACTIVE_MODIFIED  TIMESTAMP,
	IS_ACTIVE_MODIFIER  VARCHAR(30),
	CONSTRAINT EXTRA_FIELD_DEFS_PK PRIMARY KEY (ID)
);

CREATE TABLE EXTRA_FIELD_ITEMS (
	ID                     BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	ID_EXTRA_FIELD_VALUES  BIGINT NOT NULL,
	ID_EXTRA_FIELD_DEFS    BIGINT NOT NULL,
	VAL                    CLOB,
	CONSTRAINT EXTRA_FIELD_ITEMS_PK PRIMARY KEY (ID),
	CONSTRAINT EXTRA_FIELD_ITEMS_FK FOREIGN KEY (ID_EXTRA_FIELD_VALUES) REFERENCES EXTRA_FIELD_VALUES (ID),
	CONSTRAINT EXTRA_FIELD_ITEMS_FK2 FOREIGN KEY (ID_EXTRA_FIELD_DEFS) REFERENCES EXTRA_FIELD_DEFS (ID)
);

CREATE TABLE CLIENTS (
	ID                     BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	NAME                   VARCHAR(100),
	NUMBER                 INTEGER,
--	INFO                   VARCHAR(1024),
	ADDRESS                VARCHAR(1024),
	ID_COUNTRIES           BIGINT,
--	PHONE                  VARCHAR(50),
--	FAX                    VARCHAR(50),
	EMAIL                  VARCHAR(50),
--	WEBSITE                VARCHAR(50),
--	CODE                   VARCHAR(20),
	IS_ACTIVE              SMALLINT,
	ID_EXTRA_FIELD_VALUES  BIGINT,
	IS_ACTIVE_MODIFIED     TIMESTAMP,
	IS_ACTIVE_MODIFIER     VARCHAR(30),
	CONSTRAINT CLIENTS_PK PRIMARY KEY (ID),
	CONSTRAINT CLIENTS_COUNTRIES_FK FOREIGN KEY (ID_COUNTRIES) REFERENCES COUNTRIES (ID),
	CONSTRAINT FK_CLIENTS_EXTRAFIELDS FOREIGN KEY (ID_EXTRA_FIELD_VALUES) REFERENCES EXTRA_FIELD_VALUES (ID)
);

CREATE TABLE CURRENCY (
	ID                  BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	CODE                VARCHAR(8) NOT NULL,
--	NAME                VARCHAR(100) NOT NULL,
	"ROUND"             INTEGER NOT NULL,
	RATE                FLOAT NOT NULL,
	SYMBOL              VARCHAR(50),
	IS_ACTIVE           SMALLINT,
	IS_ACTIVE_MODIFIED  TIMESTAMP,
	IS_ACTIVE_MODIFIER  VARCHAR(30),
	CONSTRAINT CURRENCY_PK PRIMARY KEY (ID)
);

CREATE TABLE BANKS (
	ID                  BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	NAME                VARCHAR(100) NOT NULL,
	DETAILS             CLOB,
	ID_CURRENCY         BIGINT,
	IS_ACTIVE           SMALLINT,
	IS_ACTIVE_MODIFIED  TIMESTAMP,
	IS_ACTIVE_MODIFIER  VARCHAR(30),
	ID_ORDER_LAYOUT     BIGINT,
	CONSTRAINT PK_CURRENCY PRIMARY KEY (ID),
--	TODO: CONSTRAINT BANKS_TICKETS_FK FOREIGN KEY (ID_ORDER_LAYOUT) REFERENCES TICKETS (ID),
	CONSTRAINT FK_CURRENCY FOREIGN KEY (ID_CURRENCY) REFERENCES CURRENCY (ID)
);

CREATE TABLE DEALERS (
	ID                  BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	LOGIN               VARCHAR(50),
	PASS_HASH           VARCHAR(50),
	LAST_LOGON          TIMESTAMP,
	SUPER_DEALER        SMALLINT,
	ID_CLIENTS          BIGINT NOT NULL,
	IS_DEALER_ACTIVE    SMALLINT,
	APOSTILL_DEALER     SMALLINT,
	IS_ACTIVE_MODIFIED  TIMESTAMP,
	IS_ACTIVE_MODIFIER  VARCHAR(30),
	ID_BANKS            BIGINT,
	ONLY_PROLONG        SMALLINT,
	DEMO_DEALER         SMALLINT,                              -- 2019-03-07-1036-kvn-demo_dealer.sql
	ID_SALES_AREA       BIGINT,                                -- 2022-08-12-1900-kvn-large-account.sql
	ID_SALES_AREA_LA    BIGINT,                                -- 2022-08-12-1900-kvn-large-account.sql
	AUTO_PROLONG_DEF    SMALLINT,                              -- 2022-08-19-1900-kvn-auto-prolong-def.sql
	PRICES_PASS         VARCHAR(50),                           -- 2023-07-28-0123-kvn-prices-pass.sql
	CONSTRAINT PK_DEALERS PRIMARY KEY (ID),
	CONSTRAINT DEALERS_BANKS_FK FOREIGN KEY (ID_BANKS) REFERENCES BANKS (ID),
	CONSTRAINT FK_DEALERS_CLIENTS FOREIGN KEY (ID_CLIENTS) REFERENCES CLIENTS (ID),
--	-- 2022-08-12-1900-kvn-large-account.sql
--	CONSTRAINT FK_DEALERS_SALES_AREA FOREIGN KEY (ID_SALES_AREA) REFERENCES SALES_AREA (ID),
--	CONSTRAINT FK_DEALERS_SALES_AREA_LA FOREIGN KEY (ID_SALES_AREA_LA) REFERENCES SALES_AREA (ID),
	CONSTRAINT DEALERS_IDX_LOGIN UNIQUE (LOGIN)
);

CREATE TABLE LICENSES (
	ID                         BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	CLIENT_NAME                VARCHAR(80) NOT NULL,
	CLIENT_DISPLAY_NAME        VARCHAR(80),                    -- 2021-01-21-1800-kvn-display-name.sql
	CLIENT_ADDRESS             VARCHAR(80),
	CLIENT_EMAIL               VARCHAR(50),
	CLIENT_PHONE               VARCHAR(50),
	COMMENTS                   CLOB,
	HARD_CODE                  VARCHAR(30) NOT NULL,
	ACT_CODE                   VARCHAR(30) NOT NULL,
	LB_CONNECTIONS             SMALLINT,
	REM_CONNECTIONS            SMALLINT,
	OM_CONNECTIONS             SMALLINT,
	HQ_CONNECTIONS             SMALLINT,
	BO_CONNECTIONS             SMALLINT,
	POS_CONNECTIONS            SMALLINT,
	PS_CONNECTIONS             SMALLINT,                       -- 2016-12-27-1517-kvn-ps_connections.sql
	HE_CONNECTIONS             SMALLINT,                       -- 2021-05-14-1900-kvn-hht-eft.sql
	ACT_CHECK_PERIOD           SMALLINT,
	START_DATE                 DATE NOT NULL,
	ISSUE_DATE                 DATE NOT NULL,
	EXPIRED_DATE               DATE,
	FUNC_LIMITED               BIGINT,
	STATUS                     SMALLINT NOT NULL,
	ID_DEALERS                 BIGINT,
	IS_ACTIVE                  SMALLINT,
	CONFIRM_UID                VARCHAR(40),
	REQUEST_DATE               TIMESTAMP,
	CONFIRM_DATE               TIMESTAMP,
	REQUEST_PHASE              CHAR(4),
	ID_LICENSES_PREV           BIGINT,
	REQUEST_TYPE               CHAR(10),
	REQUEST_ACTIVE             SMALLINT,
	REQUEST_TABLENO            INTEGER,
	REQUEST_ORDER              CLOB,
	EMERGENCY_AVAILABLE        SMALLINT,
	ID_BILL                    BIGINT,
	INVOICE_REQUIRED           SMALLINT,
	INVOICE_DATE               TIMESTAMP,
	DEALER_COMMENTS            CLOB,
	NON_PROLONGABLE            SMALLINT,
	NON_APPROVED               SMALLINT,
	INVOICE_NUMBER             BIGINT,
	TEMP_EXPIRED_DATE          DATE,
	IS_ACTIVE_MODIFIED         TIMESTAMP,
	IS_ACTIVE_MODIFIER         VARCHAR(30),
	DEFINITIVE_MAX_VERSION     INTEGER,                        -- 2016-02-02-1610-kvn-definitive_max_version.sql
	AVAILABLE_REINSTALLATIONS  INTEGER,                        -- 2016-02-02-1620-kvn-licenses_online.sql
	INSTALLATION_TIME          TIMESTAMP,                      -- 2016-02-02-1620-kvn-licenses_online.sql
	CHAIN                      VARCHAR(50),                    -- 2017-04-25-1511-kvn-hwm.sql
	AUTO_PROLONGABLE           SMALLINT,                       -- 2019-05-28-1630-kvn-rental.sql
	PROLONG_PARAMS             CLOB,                           -- 2019-05-28-1630-kvn-rental.sql
	MIN_VERSION                INTEGER,                        -- 2021-01-21-1800-kvn-display-name.sql
	REQUEST_LARGE_ACCOUNT      SMALLINT,                       -- 2022-08-12-1900-kvn-large-account.sql
	LARGE_ACCOUNT              SMALLINT,                       -- 2022-08-12-1900-kvn-large-account.sql
	HWM_YEARLY                 SMALLINT,                       -- 2023-06-30-1234-kvn-hwm-yearly.sql
	HWM_FIRST                  SMALLINT,                       -- 2023-06-30-1234-kvn-hwm-yearly.sql
	EXTRA_DATA                 CLOB,                           -- 2023-11-30-1301-kvn-extra-data.sql

	CONSTRAINT LICENSES_PK PRIMARY KEY (ID),
-- TODO: CONSTRAINT FK_LICENSES_BILL FOREIGN KEY (ID_BILL) REFERENCES BILL (ID),
	CONSTRAINT FK_LICENSES_DEALERS FOREIGN KEY (ID_DEALERS) REFERENCES DEALERS (ID),
	CONSTRAINT FK_LICENSES_LICENSES FOREIGN KEY (ID_LICENSES_PREV) REFERENCES LICENSES (ID)
);
CREATE INDEX LICENSES_IDX_CONFIRM_UID ON LICENSES (CONFIRM_UID);
CREATE INDEX LICENSES_IDX_INVOICE_REQUIRED ON LICENSES (INVOICE_REQUIRED);
CREATE INDEX LICENSES_IDX_IS_ACTIVE ON LICENSES (IS_ACTIVE);
-- 2016-02-02-1600-kvn-hard_code_index.sql
CREATE INDEX LICENSES_IDX_HARD_CODE ON LICENSES (HARD_CODE);
-- 2017-04-25-1511-kvn-hwm.sql
CREATE INDEX LICENSES_IDX_CHAIN ON LICENSES (CHAIN);
--- 2020-07-27-0900-kvn-boost.sql
-- LICENSES.IS_BOOST => BIN_AND(STATUS,1024) > 0
--CREATE INDEX LICENSES_IDX_STATUS_BOOST ON LICENSES COMPUTED BY (BIN_AND(STATUS,1024));
CREATE INDEX LICENSES_IDX_REQUEST_ACTIVE ON LICENSES (REQUEST_ACTIVE);
-- 2019-05-28-1630-kvn-rental.sql
CREATE INDEX LICENSES_IDX_AUTO_PROLONGABLE ON LICENSES (AUTO_PROLONGABLE);

-- 2016-03-16-1330-kvn-licenses_online_split.sql
CREATE TABLE LICENSES_ONLINE(
	ID BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	ID_LICENSES BIGINT NOT NULL,

	ACTIVE_SIGNATURE VARCHAR(128),
	ACTIVE_PRODUCT_NAME VARCHAR(16),
	ACTIVE_PRODUCT_VERSION INTEGER,
	ACTIVE_DATABASE_INFO VARCHAR(128),

	ACTIVATION_TIME TIMESTAMP,
	ACTIVATION_COUNT INTEGER,
	ACTIVATION_PERIOD_BEGIN TIMESTAMP,
	ACTIVATION_PERIOD_END TIMESTAMP,

	FAILURE_COUNT INTEGER,
	LAST_FAILURE_TIME TIMESTAMP,
	LAST_FAILURE_COUNT INTEGER,
	LAST_FAILURE_FIELD VARCHAR(24),
	LAST_FAILURE_DATA VARCHAR(128),

	CONSTRAINT PK_LICENSES_ONLINE PRIMARY KEY (ID),
	CONSTRAINT FK_LICENSES_ONLINE_1 FOREIGN KEY (ID_LICENSES) REFERENCES LICENSES (ID)
);

CREATE TABLE APOSTILL_HDDCODES (
	ID               BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	ID_DEALERS       BIGINT,
	HDD_CODE         VARCHAR(100),
	TRIAL_ACT_DATE   DATE,
	NORMAL_ACT_DATE  DATE,
	HARD_CODE        VARCHAR(30),
	CONSTRAINT PK_APOSTILL_HDDCODES PRIMARY KEY (ID),
	CONSTRAINT FK_APOSTILL_HDDCODES_1 FOREIGN KEY (ID_DEALERS) REFERENCES DEALERS (ID)
);

CREATE TABLE HWM_INVOICE (
	ID BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	ID_DEALERS BIGINT NOT NULL,
	CHAIN VARCHAR(50),
	YEARLY SMALLINT,            -- 2023-06-30-1234-kvn-hwm-yearly.sql
	FIRST_YEAR SMALLINT,        -- 2023-11-29-1234-kvn-hwm-yearly-fix.sql
	CALC_MONTH DATE NOT NULL,
	ORDER_REQUIRED SMALLINT,
	ID_BILL BIGINT,
	INVOICE_REQUIRED SMALLINT,
	INVOICE_DATE TIMESTAMP,
	INVOICE_NUMBER BIGINT
);

ALTER TABLE HWM_INVOICE ADD CONSTRAINT HWM_INVOICE_PK PRIMARY KEY (ID);
ALTER TABLE HWM_INVOICE ADD CONSTRAINT HWM_INVOICE_FK_DEALERS FOREIGN KEY (ID_DEALERS) REFERENCES DEALERS (ID);
-- TODO: ALTER TABLE HWM_INVOICE ADD CONSTRAINT HWM_INVOICE_FK_BILL FOREIGN KEY (ID_BILL) REFERENCES BILL (ID);

CREATE INDEX HWM_INVOICE_IDX_CALC_MONTH ON HWM_INVOICE (CALC_MONTH);
CREATE INDEX HWM_INVOICE_IDX_ORDER_REQUIRED ON HWM_INVOICE (ORDER_REQUIRED);
CREATE INDEX HWM_INVOICE_IDX_INVOICE_REQUIRE ON HWM_INVOICE (INVOICE_REQUIRED);

CREATE TABLE HWM_INVOICE_ITEM (
	ID BIGINT NOT NULL DEFAULT (SELECT ID FROM GENERATE_TABLE_ID),
	ID_HWM_INVOICE BIGINT NOT NULL,
	ID_LICENSES BIGINT NOT NULL
);

ALTER TABLE HWM_INVOICE_ITEM ADD CONSTRAINT HWM_INVOICE_ITEM_PK PRIMARY KEY (ID);
ALTER TABLE HWM_INVOICE_ITEM ADD CONSTRAINT HWM_INVOICE_ITEM_FK_HWM_INVOICE FOREIGN KEY (ID_HWM_INVOICE) REFERENCES HWM_INVOICE (ID);
ALTER TABLE HWM_INVOICE_ITEM ADD CONSTRAINT HWM_INVOICE_ITEM_FK_LICENSES FOREIGN KEY (ID_LICENSES) REFERENCES LICENSES (ID);
