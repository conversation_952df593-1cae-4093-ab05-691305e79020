<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>org.openapitools</groupId>
    <artifactId>openapi-java-client</artifactId>
    <packaging>jar</packaging>
    <name>openapi-java-client</name>
    <version>v1.0</version>
    <url>https://github.com/openapitools/openapi-generator</url>
    <description>OpenAPI Java</description>
    <scm>
        <connection>scm:git:**************:openapitools/openapi-generator.git</connection>
        <developerConnection>scm:git:**************:openapitools/openapi-generator.git</developerConnection>
        <url>https://github.com/openapitools/openapi-generator</url>
    </scm>

    <licenses>
        <license>
            <name>Unlicense</name>
            <url>http://unlicense.org</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>OpenAPI-Generator Contributors</name>
            <email><EMAIL></email>
            <organization>OpenAPITools.org</organization>
            <organizationUrl>http://openapitools.org</organizationUrl>
        </developer>
    </developers>

    <build>
        <plugins>
           <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <fork>true</fork>
                    <meminitial>128m</meminitial>
                    <maxmem>512m</maxmem>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-J-Xss4m</arg><!-- Compiling the generated JSON.java file may require larger stack size. -->
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>2.2.0</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <systemProperties>
                        <property>
                            <name>loggerPath</name>
                            <value>conf/log4j.properties</value>
                        </property>
                    </systemProperties>
                    <argLine>-Xms512m -Xmx1500m</argLine>
                    <parallel>methods</parallel>
                    <threadCount>10</threadCount>
                </configuration>
                <dependencies>
                    <!--Custom provider and engine for Junit 5 to surefire-->
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit-version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- attach test jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>add_sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/java</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add_test_sources</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/test/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <doclint>none</doclint>
                    <tags>
                        <tag>
                            <name>http.response.details</name>
                            <placement>a</placement>
                            <head>Http Response Details:</head>
                        </tag>
                    </tags>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Use spotless plugin to automatically format code, remove unused import, etc
                 To apply changes directly to the file, run `mvn spotless:apply`
                 Ref: https://github.com/diffplug/spotless/tree/main/plugin-maven
            -->
            <plugin>
              <groupId>com.diffplug.spotless</groupId>
              <artifactId>spotless-maven-plugin</artifactId>
              <version>${spotless.version}</version>
              <configuration>
                <formats>
                  <!-- you can define as many formats as you want, each is independent -->
                  <format>
                    <!-- define the files to apply to -->
                    <includes>
                      <include>.gitignore</include>
                    </includes>
                    <!-- define the steps to apply to those files -->
                    <trimTrailingWhitespace/>
                    <endWithNewline/>
                    <indent>
                      <spaces>true</spaces> <!-- or <tabs>true</tabs> -->
                      <spacesPerTab>4</spacesPerTab> <!-- optional, default is 4 -->
                    </indent>
                  </format>
                </formats>
                <!-- define a language-specific format -->
                <java>
                  <!-- no need to specify files, inferred automatically, but you can if you want -->

                  <!-- apply a specific flavor of google-java-format and reflow long strings -->
                  <googleJavaFormat>
                    <version>1.8</version>
                    <style>AOSP</style>
                    <reflowLongStrings>true</reflowLongStrings>
                  </googleJavaFormat>

                  <removeUnusedImports/>
                  <importOrder/>

                </java>
              </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>sign-artifacts</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.0.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <!-- @Nullable annotation -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp-version}</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
            <version>${okhttp-version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>${gson-version}</version>
        </dependency>
        <dependency>
            <groupId>io.gsonfire</groupId>
            <artifactId>gson-fire</artifactId>
            <version>${gson-fire-version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3-version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>${jakarta-annotation-version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>jsr311-api</artifactId>
            <version>${jsr311-api-version}</version>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>${javax.ws.rs-api-version}</version>
        </dependency>
        <!-- test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-runner</artifactId>
            <version>${junit-platform-runner.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>${mockito-core-version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <gson-fire-version>1.8.5</gson-fire-version>
        <swagger-annotations-version>1.6.6</swagger-annotations-version>
        <okhttp-version>4.10.0</okhttp-version>
        <gson-version>2.9.1</gson-version>
        <commons-lang3-version>3.12.0</commons-lang3-version>
        <jakarta-annotation-version>1.3.5</jakarta-annotation-version>
        <junit-version>5.9.1</junit-version>
        <junit-platform-runner.version>1.9.1</junit-platform-runner.version>
        <mockito-core-version>3.12.4</mockito-core-version>
        <javax.ws.rs-api-version>2.1.1</javax.ws.rs-api-version>
        <jsr311-api-version>1.1.1</jsr311-api-version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spotless.version>2.27.2</spotless.version>
    </properties>
</project>
