/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * TaxonomyCloudStoreConfiguration
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TaxonomyCloudStoreConfiguration {
  public static final String SERIALIZED_NAME_COMPRESS = "compress";
  @SerializedName(SERIALIZED_NAME_COMPRESS)
  private Boolean compress;

  public TaxonomyCloudStoreConfiguration() {
  }

  public TaxonomyCloudStoreConfiguration compress(Boolean compress) {
    
    this.compress = compress;
    return this;
  }

   /**
   * Get compress
   * @return compress
  **/
  @javax.annotation.Nullable
  public Boolean getCompress() {
    return compress;
  }


  public void setCompress(Boolean compress) {
    this.compress = compress;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaxonomyCloudStoreConfiguration taxonomyCloudStoreConfiguration = (TaxonomyCloudStoreConfiguration) o;
    return Objects.equals(this.compress, taxonomyCloudStoreConfiguration.compress);
  }

  @Override
  public int hashCode() {
    return Objects.hash(compress);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaxonomyCloudStoreConfiguration {\n");
    sb.append("    compress: ").append(toIndentedString(compress)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("compress");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TaxonomyCloudStoreConfiguration
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TaxonomyCloudStoreConfiguration.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TaxonomyCloudStoreConfiguration is not found in the empty JSON string", TaxonomyCloudStoreConfiguration.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TaxonomyCloudStoreConfiguration.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TaxonomyCloudStoreConfiguration` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TaxonomyCloudStoreConfiguration.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TaxonomyCloudStoreConfiguration' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TaxonomyCloudStoreConfiguration> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TaxonomyCloudStoreConfiguration.class));

       return (TypeAdapter<T>) new TypeAdapter<TaxonomyCloudStoreConfiguration>() {
           @Override
           public void write(JsonWriter out, TaxonomyCloudStoreConfiguration value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TaxonomyCloudStoreConfiguration read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TaxonomyCloudStoreConfiguration given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TaxonomyCloudStoreConfiguration
  * @throws IOException if the JSON string is invalid with respect to TaxonomyCloudStoreConfiguration
  */
  public static TaxonomyCloudStoreConfiguration fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TaxonomyCloudStoreConfiguration.class);
  }

 /**
  * Convert an instance of TaxonomyCloudStoreConfiguration to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

