# NotificationApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10NotificationsGet**](NotificationApi.md#apiV10NotificationsGet) | **GET** /api/v1.0/notifications | Get all notifications for the authenticated user |
| [**apiV10NotificationsInfoGet**](NotificationApi.md#apiV10NotificationsInfoGet) | **GET** /api/v1.0/notifications/info | GetNotificationsInfo |
| [**apiV10NotificationsNotificationIdDelete**](NotificationApi.md#apiV10NotificationsNotificationIdDelete) | **DELETE** /api/v1.0/notifications/{notificationId} | Delete Notification |
| [**apiV10NotificationsNotificationIdReadPut**](NotificationApi.md#apiV10NotificationsNotificationIdReadPut) | **PUT** /api/v1.0/notifications/{notificationId}/read | Mark notification as read |


<a id="apiV10NotificationsGet"></a>
# **apiV10NotificationsGet**
> NotificationResult apiV10NotificationsGet(fromTimestamp)

Get all notifications for the authenticated user

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.NotificationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    NotificationApi apiInstance = new NotificationApi(defaultClient);
    Long fromTimestamp = 56L; // Long | if set the only notifications created before this date will be shown
    try {
      NotificationResult result = apiInstance.apiV10NotificationsGet(fromTimestamp);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling NotificationApi#apiV10NotificationsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **fromTimestamp** | **Long**| if set the only notifications created before this date will be shown | [optional] |

### Return type

[**NotificationResult**](NotificationResult.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10NotificationsInfoGet"></a>
# **apiV10NotificationsInfoGet**
> NotificationsInfo apiV10NotificationsInfoGet()

GetNotificationsInfo

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.NotificationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    NotificationApi apiInstance = new NotificationApi(defaultClient);
    try {
      NotificationsInfo result = apiInstance.apiV10NotificationsInfoGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling NotificationApi#apiV10NotificationsInfoGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**NotificationsInfo**](NotificationsInfo.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10NotificationsNotificationIdDelete"></a>
# **apiV10NotificationsNotificationIdDelete**
> apiV10NotificationsNotificationIdDelete(notificationId, notificationType)

Delete Notification

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.NotificationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    NotificationApi apiInstance = new NotificationApi(defaultClient);
    Long notificationId = 56L; // Long | Notification ID
    NotificationType notificationType = NotificationType.fromValue("export"); // NotificationType | Notification type (fallback is export)
    try {
      apiInstance.apiV10NotificationsNotificationIdDelete(notificationId, notificationType);
    } catch (ApiException e) {
      System.err.println("Exception when calling NotificationApi#apiV10NotificationsNotificationIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **notificationId** | **Long**| Notification ID | |
| **notificationType** | [**NotificationType**](.md)| Notification type (fallback is export) | [optional] [enum: export, clearingRun] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10NotificationsNotificationIdReadPut"></a>
# **apiV10NotificationsNotificationIdReadPut**
> apiV10NotificationsNotificationIdReadPut(notificationId, notificationType)

Mark notification as read

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.NotificationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    NotificationApi apiInstance = new NotificationApi(defaultClient);
    Long notificationId = 56L; // Long | 
    NotificationType notificationType = NotificationType.fromValue("export"); // NotificationType | 
    try {
      apiInstance.apiV10NotificationsNotificationIdReadPut(notificationId, notificationType);
    } catch (ApiException e) {
      System.err.println("Exception when calling NotificationApi#apiV10NotificationsNotificationIdReadPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **notificationId** | **Long**|  | |
| **notificationType** | [**NotificationType**](.md)|  | [optional] [enum: export, clearingRun] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

