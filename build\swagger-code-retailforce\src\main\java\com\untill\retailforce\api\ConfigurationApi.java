/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.AccessLicenseConfigurationInfo;
import com.untill.retailforce.model.Certificate;
import com.untill.retailforce.model.CertificateFormat;
import com.untill.retailforce.model.CertificateModel;
import java.io.File;
import com.untill.retailforce.model.FiscalClient;
import com.untill.retailforce.model.FiscalClientConfiguration;
import com.untill.retailforce.model.FiscalClientConfigurationModel;
import com.untill.retailforce.model.GuidExtendedSimpleCountryObject;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.IdentificationType;
import java.util.UUID;
import com.untill.retailforce.model.ValidationError;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ConfigurationApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ConfigurationApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ConfigurationApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ConfigurationAccessLicenseConfigurationInfoGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAccessLicenseConfigurationInfoGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/access-license/configuration/info";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationAccessLicenseConfigurationInfoGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationAccessLicenseConfigurationInfoGetCall(_callback);

    }

    /**
     * Returns supported access license configuration parameters in the cloud user interface.
     * 
     * @return List&lt;AccessLicenseConfigurationInfo&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<AccessLicenseConfigurationInfo> apiV10ConfigurationAccessLicenseConfigurationInfoGet() throws ApiException {
        ApiResponse<List<AccessLicenseConfigurationInfo>> localVarResp = apiV10ConfigurationAccessLicenseConfigurationInfoGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns supported access license configuration parameters in the cloud user interface.
     * 
     * @return ApiResponse&lt;List&lt;AccessLicenseConfigurationInfo&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<AccessLicenseConfigurationInfo>> apiV10ConfigurationAccessLicenseConfigurationInfoGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationAccessLicenseConfigurationInfoGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<AccessLicenseConfigurationInfo>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns supported access license configuration parameters in the cloud user interface. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationAccessLicenseConfigurationInfoGetAsync(final ApiCallback<List<AccessLicenseConfigurationInfo>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationAccessLicenseConfigurationInfoGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<AccessLicenseConfigurationInfo>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost
     * @param entityType At the moment only \&quot;Distributor\&quot; is supported. (required)
     * @param entityId The id of the entity where the certificate should be stored. (required)
     * @param csrRequestId The id of the request. (required)
     * @param certificateName The name of the certificate in the store. (required)
     * @param certFile  (required)
     * @param additionalCertFile  (optional)
     * @param rootCertFile  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostCall(String entityType, UUID entityId, UUID csrRequestId, String certificateName, File certFile, File additionalCertFile, File rootCertFile, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/certificate/csr/{entityType}/{entityId}/{csrRequestId}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()))
            .replace("{" + "csrRequestId" + "}", localVarApiClient.escapeString(csrRequestId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (certFile != null) {
            localVarFormParams.put("certFile", certFile);
        }

        if (additionalCertFile != null) {
            localVarFormParams.put("additionalCertFile", additionalCertFile);
        }

        if (rootCertFile != null) {
            localVarFormParams.put("rootCertFile", rootCertFile);
        }

        if (certificateName != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("certificateName", certificateName));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostValidateBeforeCall(String entityType, UUID entityId, UUID csrRequestId, String certificateName, File certFile, File additionalCertFile, File rootCertFile, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(Async)");
        }

        // verify the required parameter 'csrRequestId' is set
        if (csrRequestId == null) {
            throw new ApiException("Missing the required parameter 'csrRequestId' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(Async)");
        }

        // verify the required parameter 'certificateName' is set
        if (certificateName == null) {
            throw new ApiException("Missing the required parameter 'certificateName' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(Async)");
        }

        // verify the required parameter 'certFile' is set
        if (certFile == null) {
            throw new ApiException("Missing the required parameter 'certFile' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(Async)");
        }

        return apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostCall(entityType, entityId, csrRequestId, certificateName, certFile, additionalCertFile, rootCertFile, _callback);

    }

    /**
     * Stores external certificate (with prior created certificate signing request (csr)).
     * 
     * @param entityType At the moment only \&quot;Distributor\&quot; is supported. (required)
     * @param entityId The id of the entity where the certificate should be stored. (required)
     * @param csrRequestId The id of the request. (required)
     * @param certificateName The name of the certificate in the store. (required)
     * @param certFile  (required)
     * @param additionalCertFile  (optional)
     * @param rootCertFile  (optional)
     * @return CertificateModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CertificateModel apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPost(String entityType, UUID entityId, UUID csrRequestId, String certificateName, File certFile, File additionalCertFile, File rootCertFile) throws ApiException {
        ApiResponse<CertificateModel> localVarResp = apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostWithHttpInfo(entityType, entityId, csrRequestId, certificateName, certFile, additionalCertFile, rootCertFile);
        return localVarResp.getData();
    }

    /**
     * Stores external certificate (with prior created certificate signing request (csr)).
     * 
     * @param entityType At the moment only \&quot;Distributor\&quot; is supported. (required)
     * @param entityId The id of the entity where the certificate should be stored. (required)
     * @param csrRequestId The id of the request. (required)
     * @param certificateName The name of the certificate in the store. (required)
     * @param certFile  (required)
     * @param additionalCertFile  (optional)
     * @param rootCertFile  (optional)
     * @return ApiResponse&lt;CertificateModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CertificateModel> apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostWithHttpInfo(String entityType, UUID entityId, UUID csrRequestId, String certificateName, File certFile, File additionalCertFile, File rootCertFile) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostValidateBeforeCall(entityType, entityId, csrRequestId, certificateName, certFile, additionalCertFile, rootCertFile, null);
        Type localVarReturnType = new TypeToken<CertificateModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Stores external certificate (with prior created certificate signing request (csr)). (asynchronously)
     * 
     * @param entityType At the moment only \&quot;Distributor\&quot; is supported. (required)
     * @param entityId The id of the entity where the certificate should be stored. (required)
     * @param csrRequestId The id of the request. (required)
     * @param certificateName The name of the certificate in the store. (required)
     * @param certFile  (required)
     * @param additionalCertFile  (optional)
     * @param rootCertFile  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostAsync(String entityType, UUID entityId, UUID csrRequestId, String certificateName, File certFile, File additionalCertFile, File rootCertFile, final ApiCallback<CertificateModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationCertificateCsrEntityTypeEntityIdCsrRequestIdPostValidateBeforeCall(entityType, entityId, csrRequestId, certificateName, certFile, additionalCertFile, rootCertFile, _callback);
        Type localVarReturnType = new TypeToken<CertificateModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet
     * @param entityType At the moment only \&quot;Distributor\&quot; is supported. (required)
     * @param entityId The id of the entity where the certificate should be stored. (required)
     * @param subjectInfo Additional information to the subject of the certificate request. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetCall(String entityType, UUID entityId, String subjectInfo, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/certificate/csr/{entityType}/{entityId}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (subjectInfo != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("subjectInfo", subjectInfo));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetValidateBeforeCall(String entityType, UUID entityId, String subjectInfo, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet(Async)");
        }

        return apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetCall(entityType, entityId, subjectInfo, _callback);

    }

    /**
     * Creates a certificate request file (csr) for the given distributor.
     * The certificate request depends on the organization structure if organization or company data is returned.
     * @param entityType At the moment only \&quot;Distributor\&quot; is supported. (required)
     * @param entityId The id of the entity where the certificate should be stored. (required)
     * @param subjectInfo Additional information to the subject of the certificate request. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationCertificateCsrEntityTypeEntityIdGet(String entityType, UUID entityId, String subjectInfo) throws ApiException {
        apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetWithHttpInfo(entityType, entityId, subjectInfo);
    }

    /**
     * Creates a certificate request file (csr) for the given distributor.
     * The certificate request depends on the organization structure if organization or company data is returned.
     * @param entityType At the moment only \&quot;Distributor\&quot; is supported. (required)
     * @param entityId The id of the entity where the certificate should be stored. (required)
     * @param subjectInfo Additional information to the subject of the certificate request. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetWithHttpInfo(String entityType, UUID entityId, String subjectInfo) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetValidateBeforeCall(entityType, entityId, subjectInfo, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Creates a certificate request file (csr) for the given distributor. (asynchronously)
     * The certificate request depends on the organization structure if organization or company data is returned.
     * @param entityType At the moment only \&quot;Distributor\&quot; is supported. (required)
     * @param entityId The id of the entity where the certificate should be stored. (required)
     * @param subjectInfo Additional information to the subject of the certificate request. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetAsync(String entityType, UUID entityId, String subjectInfo, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationCertificateCsrEntityTypeEntityIdGetValidateBeforeCall(entityType, entityId, subjectInfo, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet
     * @param entityType  (required)
     * @param entityId  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetCall(String entityType, UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/certificate/csr/{entityType}/{entityId}/open"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetValidateBeforeCall(String entityType, UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet(Async)");
        }

        return apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetCall(entityType, entityId, _callback);

    }

    /**
     * Get open csrs
     * 
     * @param entityType  (required)
     * @param entityId  (required)
     * @return List&lt;GuidSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<GuidSimpleObject> apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGet(String entityType, UUID entityId) throws ApiException {
        ApiResponse<List<GuidSimpleObject>> localVarResp = apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetWithHttpInfo(entityType, entityId);
        return localVarResp.getData();
    }

    /**
     * Get open csrs
     * 
     * @param entityType  (required)
     * @param entityId  (required)
     * @return ApiResponse&lt;List&lt;GuidSimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<GuidSimpleObject>> apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetWithHttpInfo(String entityType, UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetValidateBeforeCall(entityType, entityId, null);
        Type localVarReturnType = new TypeToken<List<GuidSimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get open csrs (asynchronously)
     * 
     * @param entityType  (required)
     * @param entityId  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetAsync(String entityType, UUID entityId, final ApiCallback<List<GuidSimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationCertificateCsrEntityTypeEntityIdOpenGetValidateBeforeCall(entityType, entityId, _callback);
        Type localVarReturnType = new TypeToken<List<GuidSimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationCertificateEntityTypeEntityIdDelete
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param certificateEntityId The certificate id (if null &#39;Default&#39; is used). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdDeleteCall(String entityType, UUID entityId, UUID certificateEntityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/certificate/{entityType}/{entityId}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (certificateEntityId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("certificateEntityId", certificateEntityId));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdDeleteValidateBeforeCall(String entityType, UUID entityId, UUID certificateEntityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10ConfigurationCertificateEntityTypeEntityIdDelete(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10ConfigurationCertificateEntityTypeEntityIdDelete(Async)");
        }

        return apiV10ConfigurationCertificateEntityTypeEntityIdDeleteCall(entityType, entityId, certificateEntityId, _callback);

    }

    /**
     * Removes the given external certificate from the store.
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param certificateEntityId The certificate id (if null &#39;Default&#39; is used). (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationCertificateEntityTypeEntityIdDelete(String entityType, UUID entityId, UUID certificateEntityId) throws ApiException {
        apiV10ConfigurationCertificateEntityTypeEntityIdDeleteWithHttpInfo(entityType, entityId, certificateEntityId);
    }

    /**
     * Removes the given external certificate from the store.
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param certificateEntityId The certificate id (if null &#39;Default&#39; is used). (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationCertificateEntityTypeEntityIdDeleteWithHttpInfo(String entityType, UUID entityId, UUID certificateEntityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationCertificateEntityTypeEntityIdDeleteValidateBeforeCall(entityType, entityId, certificateEntityId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Removes the given external certificate from the store. (asynchronously)
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param certificateEntityId The certificate id (if null &#39;Default&#39; is used). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdDeleteAsync(String entityType, UUID entityId, UUID certificateEntityId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationCertificateEntityTypeEntityIdDeleteValidateBeforeCall(entityType, entityId, certificateEntityId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationCertificateEntityTypeEntityIdFilePost
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate. (required)
     * @param certificateName The name of the certificate. If null \&quot;Default\&quot; is used. (optional)
     * @param certificatePassword  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdFilePostCall(String entityType, UUID entityId, String certificateName, String certificatePassword, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/certificate/{entityType}/{entityId}/file"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (certificatePassword != null) {
            localVarFormParams.put("certificatePassword", certificatePassword);
        }

        if (certificateName != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("certificateName", certificateName));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdFilePostValidateBeforeCall(String entityType, UUID entityId, String certificateName, String certificatePassword, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10ConfigurationCertificateEntityTypeEntityIdFilePost(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10ConfigurationCertificateEntityTypeEntityIdFilePost(Async)");
        }

        return apiV10ConfigurationCertificateEntityTypeEntityIdFilePostCall(entityType, entityId, certificateName, certificatePassword, _callback);

    }

    /**
     * Returns the external certificate for download.
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate. (required)
     * @param certificateName The name of the certificate. If null \&quot;Default\&quot; is used. (optional)
     * @param certificatePassword  (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationCertificateEntityTypeEntityIdFilePost(String entityType, UUID entityId, String certificateName, String certificatePassword) throws ApiException {
        apiV10ConfigurationCertificateEntityTypeEntityIdFilePostWithHttpInfo(entityType, entityId, certificateName, certificatePassword);
    }

    /**
     * Returns the external certificate for download.
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate. (required)
     * @param certificateName The name of the certificate. If null \&quot;Default\&quot; is used. (optional)
     * @param certificatePassword  (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationCertificateEntityTypeEntityIdFilePostWithHttpInfo(String entityType, UUID entityId, String certificateName, String certificatePassword) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationCertificateEntityTypeEntityIdFilePostValidateBeforeCall(entityType, entityId, certificateName, certificatePassword, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Returns the external certificate for download. (asynchronously)
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate. (required)
     * @param certificateName The name of the certificate. If null \&quot;Default\&quot; is used. (optional)
     * @param certificatePassword  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdFilePostAsync(String entityType, UUID entityId, String certificateName, String certificatePassword, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationCertificateEntityTypeEntityIdFilePostValidateBeforeCall(entityType, entityId, certificateName, certificatePassword, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationCertificateEntityTypeEntityIdGet
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdGetCall(String entityType, UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/certificate/{entityType}/{entityId}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdGetValidateBeforeCall(String entityType, UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10ConfigurationCertificateEntityTypeEntityIdGet(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10ConfigurationCertificateEntityTypeEntityIdGet(Async)");
        }

        return apiV10ConfigurationCertificateEntityTypeEntityIdGetCall(entityType, entityId, _callback);

    }

    /**
     * Returns all external certificates for the given entity.
     * Value of private key encrypted will not be returned from this method.
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @return List&lt;CertificateModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<CertificateModel> apiV10ConfigurationCertificateEntityTypeEntityIdGet(String entityType, UUID entityId) throws ApiException {
        ApiResponse<List<CertificateModel>> localVarResp = apiV10ConfigurationCertificateEntityTypeEntityIdGetWithHttpInfo(entityType, entityId);
        return localVarResp.getData();
    }

    /**
     * Returns all external certificates for the given entity.
     * Value of private key encrypted will not be returned from this method.
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @return ApiResponse&lt;List&lt;CertificateModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<CertificateModel>> apiV10ConfigurationCertificateEntityTypeEntityIdGetWithHttpInfo(String entityType, UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationCertificateEntityTypeEntityIdGetValidateBeforeCall(entityType, entityId, null);
        Type localVarReturnType = new TypeToken<List<CertificateModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all external certificates for the given entity. (asynchronously)
     * Value of private key encrypted will not be returned from this method.
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdGetAsync(String entityType, UUID entityId, final ApiCallback<List<CertificateModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationCertificateEntityTypeEntityIdGetValidateBeforeCall(entityType, entityId, _callback);
        Type localVarReturnType = new TypeToken<List<CertificateModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationCertificateEntityTypeEntityIdPost
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param certificateFormat A RetailForce.Fiscalisation.Signing.CertificateFormat enum value defining the certificate format. Default is RetailForce.Fiscalisation.Signing.CertificateFormat.Pfx. (optional)
     * @param _file  (optional)
     * @param certificatePassword  (optional)
     * @param certificateName  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdPostCall(String entityType, UUID entityId, CertificateFormat certificateFormat, File _file, String certificatePassword, String certificateName, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/certificate/{entityType}/{entityId}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (_file != null) {
            localVarFormParams.put("file", _file);
        }

        if (certificatePassword != null) {
            localVarFormParams.put("certificatePassword", certificatePassword);
        }

        if (certificateName != null) {
            localVarFormParams.put("certificateName", certificateName);
        }

        if (certificateFormat != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("certificateFormat", certificateFormat));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdPostValidateBeforeCall(String entityType, UUID entityId, CertificateFormat certificateFormat, File _file, String certificatePassword, String certificateName, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10ConfigurationCertificateEntityTypeEntityIdPost(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10ConfigurationCertificateEntityTypeEntityIdPost(Async)");
        }

        return apiV10ConfigurationCertificateEntityTypeEntityIdPostCall(entityType, entityId, certificateFormat, _file, certificatePassword, certificateName, _callback);

    }

    /**
     * Stores the given certificate to the certificate store.
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param certificateFormat A RetailForce.Fiscalisation.Signing.CertificateFormat enum value defining the certificate format. Default is RetailForce.Fiscalisation.Signing.CertificateFormat.Pfx. (optional)
     * @param _file  (optional)
     * @param certificatePassword  (optional)
     * @param certificateName  (optional)
     * @return CertificateModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public CertificateModel apiV10ConfigurationCertificateEntityTypeEntityIdPost(String entityType, UUID entityId, CertificateFormat certificateFormat, File _file, String certificatePassword, String certificateName) throws ApiException {
        ApiResponse<CertificateModel> localVarResp = apiV10ConfigurationCertificateEntityTypeEntityIdPostWithHttpInfo(entityType, entityId, certificateFormat, _file, certificatePassword, certificateName);
        return localVarResp.getData();
    }

    /**
     * Stores the given certificate to the certificate store.
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param certificateFormat A RetailForce.Fiscalisation.Signing.CertificateFormat enum value defining the certificate format. Default is RetailForce.Fiscalisation.Signing.CertificateFormat.Pfx. (optional)
     * @param _file  (optional)
     * @param certificatePassword  (optional)
     * @param certificateName  (optional)
     * @return ApiResponse&lt;CertificateModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<CertificateModel> apiV10ConfigurationCertificateEntityTypeEntityIdPostWithHttpInfo(String entityType, UUID entityId, CertificateFormat certificateFormat, File _file, String certificatePassword, String certificateName) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationCertificateEntityTypeEntityIdPostValidateBeforeCall(entityType, entityId, certificateFormat, _file, certificatePassword, certificateName, null);
        Type localVarReturnType = new TypeToken<CertificateModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Stores the given certificate to the certificate store. (asynchronously)
     * 
     * @param entityType The entity type of the external certificate. (required)
     * @param entityId The entityId of the external certificate (ORGANIZATION;COMPANY;STORE;TERMINAL;SUPPLIER). (required)
     * @param certificateFormat A RetailForce.Fiscalisation.Signing.CertificateFormat enum value defining the certificate format. Default is RetailForce.Fiscalisation.Signing.CertificateFormat.Pfx. (optional)
     * @param _file  (optional)
     * @param certificatePassword  (optional)
     * @param certificateName  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateEntityTypeEntityIdPostAsync(String entityType, UUID entityId, CertificateFormat certificateFormat, File _file, String certificatePassword, String certificateName, final ApiCallback<CertificateModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationCertificateEntityTypeEntityIdPostValidateBeforeCall(entityType, entityId, certificateFormat, _file, certificatePassword, certificateName, _callback);
        Type localVarReturnType = new TypeToken<CertificateModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationCertificateTerminalIdGet
     * @param terminalId The terminal id where the certificate is requested. (required)
     * @param certificateName The name of the certificate to download; if null the default certificate is downloaded. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateTerminalIdGetCall(UUID terminalId, String certificateName, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/certificate/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (certificateName != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("certificateName", certificateName));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationCertificateTerminalIdGetValidateBeforeCall(UUID terminalId, String certificateName, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationCertificateTerminalIdGet(Async)");
        }

        return apiV10ConfigurationCertificateTerminalIdGetCall(terminalId, certificateName, _callback);

    }

    /**
     * Returns the certificate structure for an external certificate for the client.
     * 
     * @param terminalId The terminal id where the certificate is requested. (required)
     * @param certificateName The name of the certificate to download; if null the default certificate is downloaded. (optional)
     * @return Certificate
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public Certificate apiV10ConfigurationCertificateTerminalIdGet(UUID terminalId, String certificateName) throws ApiException {
        ApiResponse<Certificate> localVarResp = apiV10ConfigurationCertificateTerminalIdGetWithHttpInfo(terminalId, certificateName);
        return localVarResp.getData();
    }

    /**
     * Returns the certificate structure for an external certificate for the client.
     * 
     * @param terminalId The terminal id where the certificate is requested. (required)
     * @param certificateName The name of the certificate to download; if null the default certificate is downloaded. (optional)
     * @return ApiResponse&lt;Certificate&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Certificate> apiV10ConfigurationCertificateTerminalIdGetWithHttpInfo(UUID terminalId, String certificateName) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationCertificateTerminalIdGetValidateBeforeCall(terminalId, certificateName, null);
        Type localVarReturnType = new TypeToken<Certificate>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the certificate structure for an external certificate for the client. (asynchronously)
     * 
     * @param terminalId The terminal id where the certificate is requested. (required)
     * @param certificateName The name of the certificate to download; if null the default certificate is downloaded. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationCertificateTerminalIdGetAsync(UUID terminalId, String certificateName, final ApiCallback<Certificate> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationCertificateTerminalIdGetValidateBeforeCall(terminalId, certificateName, _callback);
        Type localVarReturnType = new TypeToken<Certificate>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationConfigurationIdDelete
     * @param configurationId The id of the configuration to delete. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationConfigurationIdDeleteCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/{configurationId}"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationConfigurationIdDeleteValidateBeforeCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10ConfigurationConfigurationIdDelete(Async)");
        }

        return apiV10ConfigurationConfigurationIdDeleteCall(configurationId, _callback);

    }

    /**
     * Deletes a configuration in the cloud.
     * 
     * @param configurationId The id of the configuration to delete. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationConfigurationIdDelete(UUID configurationId) throws ApiException {
        apiV10ConfigurationConfigurationIdDeleteWithHttpInfo(configurationId);
    }

    /**
     * Deletes a configuration in the cloud.
     * 
     * @param configurationId The id of the configuration to delete. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationConfigurationIdDeleteWithHttpInfo(UUID configurationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationConfigurationIdDeleteValidateBeforeCall(configurationId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes a configuration in the cloud. (asynchronously)
     * 
     * @param configurationId The id of the configuration to delete. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationConfigurationIdDeleteAsync(UUID configurationId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationConfigurationIdDeleteValidateBeforeCall(configurationId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions
     * @param configurationId The id of the configuration where the special mail configuration was done. (required)
     * @param testEmail The email address where the test mail should be send. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsCall(UUID configurationId, String testEmail, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/{configurationId}/digitalReceipt/mail"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (testEmail != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("testEmail", testEmail));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "OPTIONS", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsValidateBeforeCall(UUID configurationId, String testEmail, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions(Async)");
        }

        return apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsCall(configurationId, testEmail, _callback);

    }

    /**
     * If special configuration for digital receipt mail sending is configured, you can test this here.
     * 
     * @param configurationId The id of the configuration where the special mail configuration was done. (required)
     * @param testEmail The email address where the test mail should be send. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationConfigurationIdDigitalReceiptMailOptions(UUID configurationId, String testEmail) throws ApiException {
        apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsWithHttpInfo(configurationId, testEmail);
    }

    /**
     * If special configuration for digital receipt mail sending is configured, you can test this here.
     * 
     * @param configurationId The id of the configuration where the special mail configuration was done. (required)
     * @param testEmail The email address where the test mail should be send. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsWithHttpInfo(UUID configurationId, String testEmail) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsValidateBeforeCall(configurationId, testEmail, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * If special configuration for digital receipt mail sending is configured, you can test this here. (asynchronously)
     * 
     * @param configurationId The id of the configuration where the special mail configuration was done. (required)
     * @param testEmail The email address where the test mail should be send. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsAsync(UUID configurationId, String testEmail, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationConfigurationIdDigitalReceiptMailOptionsValidateBeforeCall(configurationId, testEmail, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationConfigurationIdGet
     * @param configurationId The id of the configuration which is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationConfigurationIdGetCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/{configurationId}"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationConfigurationIdGetValidateBeforeCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10ConfigurationConfigurationIdGet(Async)");
        }

        return apiV10ConfigurationConfigurationIdGetCall(configurationId, _callback);

    }

    /**
     * Returns a configuration requested by id for the authenticated user.
     * 
     * @param configurationId The id of the configuration which is requested. (required)
     * @return FiscalClientConfigurationModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public FiscalClientConfigurationModel apiV10ConfigurationConfigurationIdGet(UUID configurationId) throws ApiException {
        ApiResponse<FiscalClientConfigurationModel> localVarResp = apiV10ConfigurationConfigurationIdGetWithHttpInfo(configurationId);
        return localVarResp.getData();
    }

    /**
     * Returns a configuration requested by id for the authenticated user.
     * 
     * @param configurationId The id of the configuration which is requested. (required)
     * @return ApiResponse&lt;FiscalClientConfigurationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<FiscalClientConfigurationModel> apiV10ConfigurationConfigurationIdGetWithHttpInfo(UUID configurationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationConfigurationIdGetValidateBeforeCall(configurationId, null);
        Type localVarReturnType = new TypeToken<FiscalClientConfigurationModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a configuration requested by id for the authenticated user. (asynchronously)
     * 
     * @param configurationId The id of the configuration which is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationConfigurationIdGetAsync(UUID configurationId, final ApiCallback<FiscalClientConfigurationModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationConfigurationIdGetValidateBeforeCall(configurationId, _callback);
        Type localVarReturnType = new TypeToken<FiscalClientConfigurationModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationConfigurationIdPut
     * @param configurationId The id of the configuration to update. (required)
     * @param fiscalClientConfiguration The data of the configuration to update. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationConfigurationIdPutCall(UUID configurationId, FiscalClientConfiguration fiscalClientConfiguration, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = fiscalClientConfiguration;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/{configurationId}"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationConfigurationIdPutValidateBeforeCall(UUID configurationId, FiscalClientConfiguration fiscalClientConfiguration, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10ConfigurationConfigurationIdPut(Async)");
        }

        // verify the required parameter 'fiscalClientConfiguration' is set
        if (fiscalClientConfiguration == null) {
            throw new ApiException("Missing the required parameter 'fiscalClientConfiguration' when calling apiV10ConfigurationConfigurationIdPut(Async)");
        }

        return apiV10ConfigurationConfigurationIdPutCall(configurationId, fiscalClientConfiguration, _callback);

    }

    /**
     * Updates a configuration in the retail cloud service.
     * 
     * @param configurationId The id of the configuration to update. (required)
     * @param fiscalClientConfiguration The data of the configuration to update. (required)
     * @return FiscalClientConfigurationModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public FiscalClientConfigurationModel apiV10ConfigurationConfigurationIdPut(UUID configurationId, FiscalClientConfiguration fiscalClientConfiguration) throws ApiException {
        ApiResponse<FiscalClientConfigurationModel> localVarResp = apiV10ConfigurationConfigurationIdPutWithHttpInfo(configurationId, fiscalClientConfiguration);
        return localVarResp.getData();
    }

    /**
     * Updates a configuration in the retail cloud service.
     * 
     * @param configurationId The id of the configuration to update. (required)
     * @param fiscalClientConfiguration The data of the configuration to update. (required)
     * @return ApiResponse&lt;FiscalClientConfigurationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<FiscalClientConfigurationModel> apiV10ConfigurationConfigurationIdPutWithHttpInfo(UUID configurationId, FiscalClientConfiguration fiscalClientConfiguration) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationConfigurationIdPutValidateBeforeCall(configurationId, fiscalClientConfiguration, null);
        Type localVarReturnType = new TypeToken<FiscalClientConfigurationModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates a configuration in the retail cloud service. (asynchronously)
     * 
     * @param configurationId The id of the configuration to update. (required)
     * @param fiscalClientConfiguration The data of the configuration to update. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationConfigurationIdPutAsync(UUID configurationId, FiscalClientConfiguration fiscalClientConfiguration, final ApiCallback<FiscalClientConfigurationModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationConfigurationIdPutValidateBeforeCall(configurationId, fiscalClientConfiguration, _callback);
        Type localVarReturnType = new TypeToken<FiscalClientConfigurationModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationFiscalClientGet
     * @param organisationId The id of the organisation. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param restore True if the configuration should be loaded in a restore process; default is false. (optional, default to false)
     * @param cloudFiscalisation True if the configuration is downloaded for cloud fiscalisation client; otherwise false. Default is false. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Appropriate license (GetConfiguration) does not exist for this client. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> Requested organization, store or terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter organisationId is Guid.Empty or parameter storeNumber / terminalNumber are set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 501 </td><td> Method is called to create client for cloud fiscalisation, but fiscal country does not support cloud fiscalisation. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalClientGetCall(UUID organisationId, String storeNumber, String terminalNumber, Boolean restore, Boolean cloudFiscalisation, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/fiscalClient";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (storeNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeNumber", storeNumber));
        }

        if (terminalNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("terminalNumber", terminalNumber));
        }

        if (restore != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("restore", restore));
        }

        if (cloudFiscalisation != null) {
            localVarHeaderParams.put("cloudFiscalisation", localVarApiClient.parameterToString(cloudFiscalisation));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationFiscalClientGetValidateBeforeCall(UUID organisationId, String storeNumber, String terminalNumber, Boolean restore, Boolean cloudFiscalisation, final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationFiscalClientGetCall(organisationId, storeNumber, terminalNumber, restore, cloudFiscalisation, _callback);

    }

    /**
     * Function for TrustedFiscalModule to download fiscal client information (CreateClientByCloud).
     * 
     * @param organisationId The id of the organisation. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param restore True if the configuration should be loaded in a restore process; default is false. (optional, default to false)
     * @param cloudFiscalisation True if the configuration is downloaded for cloud fiscalisation client; otherwise false. Default is false. (optional, default to false)
     * @return FiscalClient
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Appropriate license (GetConfiguration) does not exist for this client. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> Requested organization, store or terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter organisationId is Guid.Empty or parameter storeNumber / terminalNumber are set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 501 </td><td> Method is called to create client for cloud fiscalisation, but fiscal country does not support cloud fiscalisation. </td><td>  -  </td></tr>
     </table>
     */
    public FiscalClient apiV10ConfigurationFiscalClientGet(UUID organisationId, String storeNumber, String terminalNumber, Boolean restore, Boolean cloudFiscalisation) throws ApiException {
        ApiResponse<FiscalClient> localVarResp = apiV10ConfigurationFiscalClientGetWithHttpInfo(organisationId, storeNumber, terminalNumber, restore, cloudFiscalisation);
        return localVarResp.getData();
    }

    /**
     * Function for TrustedFiscalModule to download fiscal client information (CreateClientByCloud).
     * 
     * @param organisationId The id of the organisation. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param restore True if the configuration should be loaded in a restore process; default is false. (optional, default to false)
     * @param cloudFiscalisation True if the configuration is downloaded for cloud fiscalisation client; otherwise false. Default is false. (optional, default to false)
     * @return ApiResponse&lt;FiscalClient&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Appropriate license (GetConfiguration) does not exist for this client. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> Requested organization, store or terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter organisationId is Guid.Empty or parameter storeNumber / terminalNumber are set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 501 </td><td> Method is called to create client for cloud fiscalisation, but fiscal country does not support cloud fiscalisation. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<FiscalClient> apiV10ConfigurationFiscalClientGetWithHttpInfo(UUID organisationId, String storeNumber, String terminalNumber, Boolean restore, Boolean cloudFiscalisation) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationFiscalClientGetValidateBeforeCall(organisationId, storeNumber, terminalNumber, restore, cloudFiscalisation, null);
        Type localVarReturnType = new TypeToken<FiscalClient>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Function for TrustedFiscalModule to download fiscal client information (CreateClientByCloud). (asynchronously)
     * 
     * @param organisationId The id of the organisation. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param restore True if the configuration should be loaded in a restore process; default is false. (optional, default to false)
     * @param cloudFiscalisation True if the configuration is downloaded for cloud fiscalisation client; otherwise false. Default is false. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> OK </td><td>  -  </td></tr>
        <tr><td> 402 </td><td> Appropriate license (GetConfiguration) does not exist for this client. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> Requested organization, store or terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter organisationId is Guid.Empty or parameter storeNumber / terminalNumber are set to null or empty string. </td><td>  -  </td></tr>
        <tr><td> 501 </td><td> Method is called to create client for cloud fiscalisation, but fiscal country does not support cloud fiscalisation. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalClientGetAsync(UUID organisationId, String storeNumber, String terminalNumber, Boolean restore, Boolean cloudFiscalisation, final ApiCallback<FiscalClient> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationFiscalClientGetValidateBeforeCall(organisationId, storeNumber, terminalNumber, restore, cloudFiscalisation, _callback);
        Type localVarReturnType = new TypeToken<FiscalClient>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationFiscalClientPatch
     * @param organisationId The id of the organization. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalClientPatchCall(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/fiscalClient";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (storeNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeNumber", storeNumber));
        }

        if (terminalNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("terminalNumber", terminalNumber));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PATCH", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationFiscalClientPatchValidateBeforeCall(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationFiscalClientPatchCall(organisationId, storeNumber, terminalNumber, _callback);

    }

    /**
     * Function for TrustedFiscalModule to commit fiscal client configuration download.
     * 
     * @param organisationId The id of the organization. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationFiscalClientPatch(UUID organisationId, String storeNumber, String terminalNumber) throws ApiException {
        apiV10ConfigurationFiscalClientPatchWithHttpInfo(organisationId, storeNumber, terminalNumber);
    }

    /**
     * Function for TrustedFiscalModule to commit fiscal client configuration download.
     * 
     * @param organisationId The id of the organization. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationFiscalClientPatchWithHttpInfo(UUID organisationId, String storeNumber, String terminalNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationFiscalClientPatchValidateBeforeCall(organisationId, storeNumber, terminalNumber, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Function for TrustedFiscalModule to commit fiscal client configuration download. (asynchronously)
     * 
     * @param organisationId The id of the organization. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalClientPatchAsync(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationFiscalClientPatchValidateBeforeCall(organisationId, storeNumber, terminalNumber, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationFiscalClientValidateGet
     * @param organisationId The id of the organisation. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalClientValidateGetCall(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/fiscalClient/validate";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organisationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organisationId", organisationId));
        }

        if (storeNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeNumber", storeNumber));
        }

        if (terminalNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("terminalNumber", terminalNumber));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationFiscalClientValidateGetValidateBeforeCall(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationFiscalClientValidateGetCall(organisationId, storeNumber, terminalNumber, _callback);

    }

    /**
     * Function to validate the fiscal client configured in the cloud.
     * 
     * @param organisationId The id of the organisation. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @return List&lt;ValidationError&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<ValidationError> apiV10ConfigurationFiscalClientValidateGet(UUID organisationId, String storeNumber, String terminalNumber) throws ApiException {
        ApiResponse<List<ValidationError>> localVarResp = apiV10ConfigurationFiscalClientValidateGetWithHttpInfo(organisationId, storeNumber, terminalNumber);
        return localVarResp.getData();
    }

    /**
     * Function to validate the fiscal client configured in the cloud.
     * 
     * @param organisationId The id of the organisation. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @return ApiResponse&lt;List&lt;ValidationError&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ValidationError>> apiV10ConfigurationFiscalClientValidateGetWithHttpInfo(UUID organisationId, String storeNumber, String terminalNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationFiscalClientValidateGetValidateBeforeCall(organisationId, storeNumber, terminalNumber, null);
        Type localVarReturnType = new TypeToken<List<ValidationError>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Function to validate the fiscal client configured in the cloud. (asynchronously)
     * 
     * @param organisationId The id of the organisation. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalClientValidateGetAsync(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback<List<ValidationError>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationFiscalClientValidateGetValidateBeforeCall(organisationId, storeNumber, terminalNumber, _callback);
        Type localVarReturnType = new TypeToken<List<ValidationError>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationFiscalClientValidateTerminalIdGet
     * @param terminalId The id of the terminal (uniqueClientId). (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalClientValidateTerminalIdGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/fiscalClient/validate/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationFiscalClientValidateTerminalIdGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationFiscalClientValidateTerminalIdGet(Async)");
        }

        return apiV10ConfigurationFiscalClientValidateTerminalIdGetCall(terminalId, _callback);

    }

    /**
     * Function to validate the fiscal client configured in the cloud.
     * 
     * @param terminalId The id of the terminal (uniqueClientId). (required)
     * @return List&lt;ValidationError&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<ValidationError> apiV10ConfigurationFiscalClientValidateTerminalIdGet(UUID terminalId) throws ApiException {
        ApiResponse<List<ValidationError>> localVarResp = apiV10ConfigurationFiscalClientValidateTerminalIdGetWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Function to validate the fiscal client configured in the cloud.
     * 
     * @param terminalId The id of the terminal (uniqueClientId). (required)
     * @return ApiResponse&lt;List&lt;ValidationError&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ValidationError>> apiV10ConfigurationFiscalClientValidateTerminalIdGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationFiscalClientValidateTerminalIdGetValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<List<ValidationError>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Function to validate the fiscal client configured in the cloud. (asynchronously)
     * 
     * @param terminalId The id of the terminal (uniqueClientId). (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalClientValidateTerminalIdGetAsync(UUID terminalId, final ApiCallback<List<ValidationError>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationFiscalClientValidateTerminalIdGetValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<List<ValidationError>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationFiscalclientPost
     * @param fiscalClient The fiscal client to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalclientPostCall(FiscalClient fiscalClient, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = fiscalClient;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/fiscalclient";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationFiscalclientPostValidateBeforeCall(FiscalClient fiscalClient, final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationFiscalclientPostCall(fiscalClient, _callback);

    }

    /**
     * Creates a client configuration based on fiscal client data.
     * 
     * @param fiscalClient The fiscal client to create. (optional)
     * @return FiscalClient
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public FiscalClient apiV10ConfigurationFiscalclientPost(FiscalClient fiscalClient) throws ApiException {
        ApiResponse<FiscalClient> localVarResp = apiV10ConfigurationFiscalclientPostWithHttpInfo(fiscalClient);
        return localVarResp.getData();
    }

    /**
     * Creates a client configuration based on fiscal client data.
     * 
     * @param fiscalClient The fiscal client to create. (optional)
     * @return ApiResponse&lt;FiscalClient&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<FiscalClient> apiV10ConfigurationFiscalclientPostWithHttpInfo(FiscalClient fiscalClient) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationFiscalclientPostValidateBeforeCall(fiscalClient, null);
        Type localVarReturnType = new TypeToken<FiscalClient>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a client configuration based on fiscal client data. (asynchronously)
     * 
     * @param fiscalClient The fiscal client to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationFiscalclientPostAsync(FiscalClient fiscalClient, final ApiCallback<FiscalClient> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationFiscalclientPostValidateBeforeCall(fiscalClient, _callback);
        Type localVarReturnType = new TypeToken<FiscalClient>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationGet
     * @param entityId The id of the entity we are going to load all possible configurations for. (optional)
     * @param allCountries if set load all countries for this entity (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationGetCall(UUID entityId, Boolean allCountries, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (entityId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityId", entityId));
        }

        if (allCountries != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("allCountries", allCountries));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationGetValidateBeforeCall(UUID entityId, Boolean allCountries, final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationGetCall(entityId, allCountries, _callback);

    }

    /**
     * Returns all configurations for the given organisation for the authenticated user.
     * All countries options only works on organisation level.
     * @param entityId The id of the entity we are going to load all possible configurations for. (optional)
     * @param allCountries if set load all countries for this entity (optional)
     * @return List&lt;GuidExtendedSimpleCountryObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<GuidExtendedSimpleCountryObject> apiV10ConfigurationGet(UUID entityId, Boolean allCountries) throws ApiException {
        ApiResponse<List<GuidExtendedSimpleCountryObject>> localVarResp = apiV10ConfigurationGetWithHttpInfo(entityId, allCountries);
        return localVarResp.getData();
    }

    /**
     * Returns all configurations for the given organisation for the authenticated user.
     * All countries options only works on organisation level.
     * @param entityId The id of the entity we are going to load all possible configurations for. (optional)
     * @param allCountries if set load all countries for this entity (optional)
     * @return ApiResponse&lt;List&lt;GuidExtendedSimpleCountryObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<GuidExtendedSimpleCountryObject>> apiV10ConfigurationGetWithHttpInfo(UUID entityId, Boolean allCountries) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationGetValidateBeforeCall(entityId, allCountries, null);
        Type localVarReturnType = new TypeToken<List<GuidExtendedSimpleCountryObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all configurations for the given organisation for the authenticated user. (asynchronously)
     * All countries options only works on organisation level.
     * @param entityId The id of the entity we are going to load all possible configurations for. (optional)
     * @param allCountries if set load all countries for this entity (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationGetAsync(UUID entityId, Boolean allCountries, final ApiCallback<List<GuidExtendedSimpleCountryObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationGetValidateBeforeCall(entityId, allCountries, _callback);
        Type localVarReturnType = new TypeToken<List<GuidExtendedSimpleCountryObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet
     * @param organisationId The organisation id of the terminal of the requested configuration. (required)
     * @param storeNumber The store number of the terminal of the requested configuration. (required)
     * @param terminalNumber The terminal number of the terminal of the requested configuration. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetCall(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/id/{organisationId}/{storeNumber}/{terminalNumber}"
            .replace("{" + "organisationId" + "}", localVarApiClient.escapeString(organisationId.toString()))
            .replace("{" + "storeNumber" + "}", localVarApiClient.escapeString(storeNumber.toString()))
            .replace("{" + "terminalNumber" + "}", localVarApiClient.escapeString(terminalNumber.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetValidateBeforeCall(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organisationId' is set
        if (organisationId == null) {
            throw new ApiException("Missing the required parameter 'organisationId' when calling apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet(Async)");
        }

        // verify the required parameter 'storeNumber' is set
        if (storeNumber == null) {
            throw new ApiException("Missing the required parameter 'storeNumber' when calling apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet(Async)");
        }

        // verify the required parameter 'terminalNumber' is set
        if (terminalNumber == null) {
            throw new ApiException("Missing the required parameter 'terminalNumber' when calling apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet(Async)");
        }

        return apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetCall(organisationId, storeNumber, terminalNumber, _callback);

    }

    /**
     * Returns the configuration id for the requested terminal.
     * Only returns the configuration id of the configuration stored at terminal level. No inheritance used.
     * @param organisationId The organisation id of the terminal of the requested configuration. (required)
     * @param storeNumber The store number of the terminal of the requested configuration. (required)
     * @param terminalNumber The terminal number of the terminal of the requested configuration. (required)
     * @return UUID
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public UUID apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGet(UUID organisationId, String storeNumber, String terminalNumber) throws ApiException {
        ApiResponse<UUID> localVarResp = apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetWithHttpInfo(organisationId, storeNumber, terminalNumber);
        return localVarResp.getData();
    }

    /**
     * Returns the configuration id for the requested terminal.
     * Only returns the configuration id of the configuration stored at terminal level. No inheritance used.
     * @param organisationId The organisation id of the terminal of the requested configuration. (required)
     * @param storeNumber The store number of the terminal of the requested configuration. (required)
     * @param terminalNumber The terminal number of the terminal of the requested configuration. (required)
     * @return ApiResponse&lt;UUID&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<UUID> apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetWithHttpInfo(UUID organisationId, String storeNumber, String terminalNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetValidateBeforeCall(organisationId, storeNumber, terminalNumber, null);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the configuration id for the requested terminal. (asynchronously)
     * Only returns the configuration id of the configuration stored at terminal level. No inheritance used.
     * @param organisationId The organisation id of the terminal of the requested configuration. (required)
     * @param storeNumber The store number of the terminal of the requested configuration. (required)
     * @param terminalNumber The terminal number of the terminal of the requested configuration. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetAsync(UUID organisationId, String storeNumber, String terminalNumber, final ApiCallback<UUID> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationIdOrganisationIdStoreNumberTerminalNumberGetValidateBeforeCall(organisationId, storeNumber, terminalNumber, _callback);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationOrganisationCompanyGet
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationOrganisationCompanyGetCall(IdentificationType identificationType, String identification, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/organisation/company";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (identificationType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("identificationType", identificationType));
        }

        if (identification != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("identification", identification));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationOrganisationCompanyGetValidateBeforeCall(IdentificationType identificationType, String identification, final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationOrganisationCompanyGetCall(identificationType, identification, _callback);

    }

    /**
     * Returns the guid for a organization by company companyidentification.
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @return UUID
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public UUID apiV10ConfigurationOrganisationCompanyGet(IdentificationType identificationType, String identification) throws ApiException {
        ApiResponse<UUID> localVarResp = apiV10ConfigurationOrganisationCompanyGetWithHttpInfo(identificationType, identification);
        return localVarResp.getData();
    }

    /**
     * Returns the guid for a organization by company companyidentification.
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @return ApiResponse&lt;UUID&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<UUID> apiV10ConfigurationOrganisationCompanyGetWithHttpInfo(IdentificationType identificationType, String identification) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationOrganisationCompanyGetValidateBeforeCall(identificationType, identification, null);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the guid for a organization by company companyidentification. (asynchronously)
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationOrganisationCompanyGetAsync(IdentificationType identificationType, String identification, final ApiCallback<UUID> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationOrganisationCompanyGetValidateBeforeCall(identificationType, identification, _callback);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationOrganisationGet
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationOrganisationGetCall(IdentificationType identificationType, String identification, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/organisation";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (identificationType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("identificationType", identificationType));
        }

        if (identification != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("identification", identification));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationOrganisationGetValidateBeforeCall(IdentificationType identificationType, String identification, final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationOrganisationGetCall(identificationType, identification, _callback);

    }

    /**
     * Returns the guid for a organization.
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @return UUID
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public UUID apiV10ConfigurationOrganisationGet(IdentificationType identificationType, String identification) throws ApiException {
        ApiResponse<UUID> localVarResp = apiV10ConfigurationOrganisationGetWithHttpInfo(identificationType, identification);
        return localVarResp.getData();
    }

    /**
     * Returns the guid for a organization.
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @return ApiResponse&lt;UUID&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<UUID> apiV10ConfigurationOrganisationGetWithHttpInfo(IdentificationType identificationType, String identification) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationOrganisationGetValidateBeforeCall(identificationType, identification, null);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the guid for a organization. (asynchronously)
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationOrganisationGetAsync(IdentificationType identificationType, String identification, final ApiCallback<UUID> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationOrganisationGetValidateBeforeCall(identificationType, identification, _callback);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationPost
     * @param fiscalClientConfiguration A RetailForce.Cloud.Model.Configuration.FiscalClientConfiguration object representing the configuration to create. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationPostCall(FiscalClientConfiguration fiscalClientConfiguration, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = fiscalClientConfiguration;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationPostValidateBeforeCall(FiscalClientConfiguration fiscalClientConfiguration, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'fiscalClientConfiguration' is set
        if (fiscalClientConfiguration == null) {
            throw new ApiException("Missing the required parameter 'fiscalClientConfiguration' when calling apiV10ConfigurationPost(Async)");
        }

        return apiV10ConfigurationPostCall(fiscalClientConfiguration, _callback);

    }

    /**
     * Create configuration in the retail cloud service.
     * 
     * @param fiscalClientConfiguration A RetailForce.Cloud.Model.Configuration.FiscalClientConfiguration object representing the configuration to create. (required)
     * @return FiscalClientConfigurationModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public FiscalClientConfigurationModel apiV10ConfigurationPost(FiscalClientConfiguration fiscalClientConfiguration) throws ApiException {
        ApiResponse<FiscalClientConfigurationModel> localVarResp = apiV10ConfigurationPostWithHttpInfo(fiscalClientConfiguration);
        return localVarResp.getData();
    }

    /**
     * Create configuration in the retail cloud service.
     * 
     * @param fiscalClientConfiguration A RetailForce.Cloud.Model.Configuration.FiscalClientConfiguration object representing the configuration to create. (required)
     * @return ApiResponse&lt;FiscalClientConfigurationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<FiscalClientConfigurationModel> apiV10ConfigurationPostWithHttpInfo(FiscalClientConfiguration fiscalClientConfiguration) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationPostValidateBeforeCall(fiscalClientConfiguration, null);
        Type localVarReturnType = new TypeToken<FiscalClientConfigurationModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Create configuration in the retail cloud service. (asynchronously)
     * 
     * @param fiscalClientConfiguration A RetailForce.Cloud.Model.Configuration.FiscalClientConfiguration object representing the configuration to create. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationPostAsync(FiscalClientConfiguration fiscalClientConfiguration, final ApiCallback<FiscalClientConfigurationModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationPostValidateBeforeCall(fiscalClientConfiguration, _callback);
        Type localVarReturnType = new TypeToken<FiscalClientConfigurationModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationSignatureGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/signature";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationSignatureGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationSignatureGetCall(_callback);

    }

    /**
     * Returns the public certificate for retailforce signature CA certificate (CER Format - base64 encoded, key length 4096 bit).
     * 
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated. </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10ConfigurationSignatureGet() throws ApiException {
        ApiResponse<String> localVarResp = apiV10ConfigurationSignatureGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns the public certificate for retailforce signature CA certificate (CER Format - base64 encoded, key length 4096 bit).
     * 
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10ConfigurationSignatureGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationSignatureGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the public certificate for retailforce signature CA certificate (CER Format - base64 encoded, key length 4096 bit). (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureGetAsync(final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationSignatureGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationSignatureOrganizationIdGet
     * @param organizationId The organization id of the organization where the certificate is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdGetCall(UUID organizationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/signature/{organizationId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationSignatureOrganizationIdGetValidateBeforeCall(UUID organizationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ConfigurationSignatureOrganizationIdGet(Async)");
        }

        return apiV10ConfigurationSignatureOrganizationIdGetCall(organizationId, _callback);

    }

    /**
     * Returns the public certificate generated for the organization (CER Format - base64 encoded, key length 2048 bit).
     * 
     * @param organizationId The organization id of the organization where the certificate is requested. (required)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10ConfigurationSignatureOrganizationIdGet(UUID organizationId) throws ApiException {
        ApiResponse<String> localVarResp = apiV10ConfigurationSignatureOrganizationIdGetWithHttpInfo(organizationId);
        return localVarResp.getData();
    }

    /**
     * Returns the public certificate generated for the organization (CER Format - base64 encoded, key length 2048 bit).
     * 
     * @param organizationId The organization id of the organization where the certificate is requested. (required)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10ConfigurationSignatureOrganizationIdGetWithHttpInfo(UUID organizationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdGetValidateBeforeCall(organizationId, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the public certificate generated for the organization (CER Format - base64 encoded, key length 2048 bit). (asynchronously)
     * 
     * @param organizationId The organization id of the organization where the certificate is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdGetAsync(UUID organizationId, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdGetValidateBeforeCall(organizationId, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationSignatureOrganizationIdPatch
     * @param organizationId The id of organization of the certificate to validate. (required)
     * @param signatureData The string which was signed. (optional)
     * @param signature The signature for the string which was signed. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> OrganizationId or signatureData is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdPatchCall(UUID organizationId, String signatureData, String signature, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/signature/{organizationId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (signatureData != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("signatureData", signatureData));
        }

        if (signature != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("signature", signature));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PATCH", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationSignatureOrganizationIdPatchValidateBeforeCall(UUID organizationId, String signatureData, String signature, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ConfigurationSignatureOrganizationIdPatch(Async)");
        }

        return apiV10ConfigurationSignatureOrganizationIdPatchCall(organizationId, signatureData, signature, _callback);

    }

    /**
     * Validates the given signature data with the signature using the organization certificate.
     * 
     * @param organizationId The id of organization of the certificate to validate. (required)
     * @param signatureData The string which was signed. (optional)
     * @param signature The signature for the string which was signed. (optional)
     * @return Boolean
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> OrganizationId or signatureData is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public Boolean apiV10ConfigurationSignatureOrganizationIdPatch(UUID organizationId, String signatureData, String signature) throws ApiException {
        ApiResponse<Boolean> localVarResp = apiV10ConfigurationSignatureOrganizationIdPatchWithHttpInfo(organizationId, signatureData, signature);
        return localVarResp.getData();
    }

    /**
     * Validates the given signature data with the signature using the organization certificate.
     * 
     * @param organizationId The id of organization of the certificate to validate. (required)
     * @param signatureData The string which was signed. (optional)
     * @param signature The signature for the string which was signed. (optional)
     * @return ApiResponse&lt;Boolean&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> OrganizationId or signatureData is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Boolean> apiV10ConfigurationSignatureOrganizationIdPatchWithHttpInfo(UUID organizationId, String signatureData, String signature) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdPatchValidateBeforeCall(organizationId, signatureData, signature, null);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Validates the given signature data with the signature using the organization certificate. (asynchronously)
     * 
     * @param organizationId The id of organization of the certificate to validate. (required)
     * @param signatureData The string which was signed. (optional)
     * @param signature The signature for the string which was signed. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> OrganizationId or signatureData is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdPatchAsync(UUID organizationId, String signatureData, String signature, final ApiCallback<Boolean> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdPatchValidateBeforeCall(organizationId, signatureData, signature, _callback);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationSignatureOrganizationIdPost
     * @param organizationId The id of organization of the certificate to validate. (required)
     * @param body The certificate in pem format as a string. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdPostCall(UUID organizationId, String body, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = body;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/signature/{organizationId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationSignatureOrganizationIdPostValidateBeforeCall(UUID organizationId, String body, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ConfigurationSignatureOrganizationIdPost(Async)");
        }

        return apiV10ConfigurationSignatureOrganizationIdPostCall(organizationId, body, _callback);

    }

    /**
     * Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).
     * 
     * @param organizationId The id of organization of the certificate to validate. (required)
     * @param body The certificate in pem format as a string. (optional)
     * @return Boolean
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
     </table>
     */
    public Boolean apiV10ConfigurationSignatureOrganizationIdPost(UUID organizationId, String body) throws ApiException {
        ApiResponse<Boolean> localVarResp = apiV10ConfigurationSignatureOrganizationIdPostWithHttpInfo(organizationId, body);
        return localVarResp.getData();
    }

    /**
     * Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).
     * 
     * @param organizationId The id of organization of the certificate to validate. (required)
     * @param body The certificate in pem format as a string. (optional)
     * @return ApiResponse&lt;Boolean&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Boolean> apiV10ConfigurationSignatureOrganizationIdPostWithHttpInfo(UUID organizationId, String body) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdPostValidateBeforeCall(organizationId, body, null);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Validates the given certificate if it is valid (Certificate signature, correct organization, valid time). (asynchronously)
     * 
     * @param organizationId The id of organization of the certificate to validate. (required)
     * @param body The certificate in pem format as a string. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Organization is not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdPostAsync(UUID organizationId, String body, final ApiCallback<Boolean> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdPostValidateBeforeCall(organizationId, body, _callback);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationSignatureOrganizationIdTerminalIdGet
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to terminal is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Generation of the certificate failed. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdGetCall(UUID organizationId, UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/signature/{organizationId}/{terminalId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()))
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdGetValidateBeforeCall(UUID organizationId, UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ConfigurationSignatureOrganizationIdTerminalIdGet(Async)");
        }

        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationSignatureOrganizationIdTerminalIdGet(Async)");
        }

        return apiV10ConfigurationSignatureOrganizationIdTerminalIdGetCall(organizationId, terminalId, _callback);

    }

    /**
     * Returns the certificate for the terminal (pfx Format - base64 encoded, key length 1024 bit).
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to terminal is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Generation of the certificate failed. </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10ConfigurationSignatureOrganizationIdTerminalIdGet(UUID organizationId, UUID terminalId) throws ApiException {
        ApiResponse<String> localVarResp = apiV10ConfigurationSignatureOrganizationIdTerminalIdGetWithHttpInfo(organizationId, terminalId);
        return localVarResp.getData();
    }

    /**
     * Returns the certificate for the terminal (pfx Format - base64 encoded, key length 1024 bit).
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to terminal is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Generation of the certificate failed. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10ConfigurationSignatureOrganizationIdTerminalIdGetWithHttpInfo(UUID organizationId, UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdTerminalIdGetValidateBeforeCall(organizationId, terminalId, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the certificate for the terminal (pfx Format - base64 encoded, key length 1024 bit). (asynchronously)
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to terminal is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Generation of the certificate failed. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdGetAsync(UUID organizationId, UUID terminalId, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdTerminalIdGetValidateBeforeCall(organizationId, terminalId, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param signatureData The string which was signed. (optional)
     * @param signature The signature for the string which was signed. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> OrganizationId or TerminalId is null or signatureData is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchCall(UUID organizationId, UUID terminalId, String signatureData, String signature, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/signature/{organizationId}/{terminalId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()))
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (signatureData != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("signatureData", signatureData));
        }

        if (signature != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("signature", signature));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PATCH", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchValidateBeforeCall(UUID organizationId, UUID terminalId, String signatureData, String signature, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch(Async)");
        }

        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch(Async)");
        }

        return apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchCall(organizationId, terminalId, signatureData, signature, _callback);

    }

    /**
     * Validates the given signature data with the signature using the organization certificate.
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param signatureData The string which was signed. (optional)
     * @param signature The signature for the string which was signed. (optional)
     * @return Boolean
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> OrganizationId or TerminalId is null or signatureData is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public Boolean apiV10ConfigurationSignatureOrganizationIdTerminalIdPatch(UUID organizationId, UUID terminalId, String signatureData, String signature) throws ApiException {
        ApiResponse<Boolean> localVarResp = apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchWithHttpInfo(organizationId, terminalId, signatureData, signature);
        return localVarResp.getData();
    }

    /**
     * Validates the given signature data with the signature using the organization certificate.
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param signatureData The string which was signed. (optional)
     * @param signature The signature for the string which was signed. (optional)
     * @return ApiResponse&lt;Boolean&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> OrganizationId or TerminalId is null or signatureData is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Boolean> apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchWithHttpInfo(UUID organizationId, UUID terminalId, String signatureData, String signature) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchValidateBeforeCall(organizationId, terminalId, signatureData, signature, null);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Validates the given signature data with the signature using the organization certificate. (asynchronously)
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param signatureData The string which was signed. (optional)
     * @param signature The signature for the string which was signed. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> OrganizationId or TerminalId is null or signatureData is null or empty string. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchAsync(UUID organizationId, UUID terminalId, String signatureData, String signature, final ApiCallback<Boolean> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdTerminalIdPatchValidateBeforeCall(organizationId, terminalId, signatureData, signature, _callback);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationSignatureOrganizationIdTerminalIdPost
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param body The certificate in pem format as a string. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdPostCall(UUID organizationId, UUID terminalId, String body, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = body;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/signature/{organizationId}/{terminalId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()))
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdPostValidateBeforeCall(UUID organizationId, UUID terminalId, String body, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ConfigurationSignatureOrganizationIdTerminalIdPost(Async)");
        }

        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationSignatureOrganizationIdTerminalIdPost(Async)");
        }

        return apiV10ConfigurationSignatureOrganizationIdTerminalIdPostCall(organizationId, terminalId, body, _callback);

    }

    /**
     * Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param body The certificate in pem format as a string. (optional)
     * @return Boolean
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
     </table>
     */
    public Boolean apiV10ConfigurationSignatureOrganizationIdTerminalIdPost(UUID organizationId, UUID terminalId, String body) throws ApiException {
        ApiResponse<Boolean> localVarResp = apiV10ConfigurationSignatureOrganizationIdTerminalIdPostWithHttpInfo(organizationId, terminalId, body);
        return localVarResp.getData();
    }

    /**
     * Validates the given certificate if it is valid (Certificate signature, correct organization, valid time).
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param body The certificate in pem format as a string. (optional)
     * @return ApiResponse&lt;Boolean&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Boolean> apiV10ConfigurationSignatureOrganizationIdTerminalIdPostWithHttpInfo(UUID organizationId, UUID terminalId, String body) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdTerminalIdPostValidateBeforeCall(organizationId, terminalId, body, null);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Validates the given certificate if it is valid (Certificate signature, correct organization, valid time). (asynchronously)
     * 
     * @param organizationId The id of the organization where the terminal belongs. (required)
     * @param terminalId The id of the terminal where the certificate is requested. (required)
     * @param body The certificate in pem format as a string. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Nobody is authenticated or access to organization is not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Terminal is not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationSignatureOrganizationIdTerminalIdPostAsync(UUID organizationId, UUID terminalId, String body, final ApiCallback<Boolean> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationSignatureOrganizationIdTerminalIdPostValidateBeforeCall(organizationId, terminalId, body, _callback);
        Type localVarReturnType = new TypeToken<Boolean>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
