# MasterDatsaOrganisationsApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MasterdataOrganisationsSimpleGet**](MasterDatsaOrganisationsApi.md#apiV10MasterdataOrganisationsSimpleGet) | **GET** /api/v1.0/masterdata/organisations/simple | Returns all organisations for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1. |


<a id="apiV10MasterdataOrganisationsSimpleGet"></a>
# **apiV10MasterdataOrganisationsSimpleGet**
> GuidExtendedSimpleCountryObjectPageResultModel apiV10MasterdataOrganisationsSimpleGet(pageOffset, pageSize, searchString, distributorId)

Returns all organisations for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDatsaOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDatsaOrganisationsApi apiInstance = new MasterDatsaOrganisationsApi(defaultClient);
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    UUID distributorId = UUID.randomUUID(); // UUID | Optional. Filter for distributor if necessary.
    try {
      GuidExtendedSimpleCountryObjectPageResultModel result = apiInstance.apiV10MasterdataOrganisationsSimpleGet(pageOffset, pageSize, searchString, distributorId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDatsaOrganisationsApi#apiV10MasterdataOrganisationsSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |
| **distributorId** | **UUID**| Optional. Filter for distributor if necessary. | [optional] |

### Return type

[**GuidExtendedSimpleCountryObjectPageResultModel**](GuidExtendedSimpleCountryObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

