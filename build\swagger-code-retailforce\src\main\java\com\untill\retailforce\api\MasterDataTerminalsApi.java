/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.DownloadLinkPageResultModel;
import com.untill.retailforce.model.GuidEntityVersionPageResultModel;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import com.untill.retailforce.model.IdentificationType;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.Terminal;
import com.untill.retailforce.model.TerminalInsightModel;
import com.untill.retailforce.model.TerminalModel;
import com.untill.retailforce.model.TerminalModelPageResultModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class MasterDataTerminalsApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public MasterDataTerminalsApi() {
        this(Configuration.getDefaultApiClient());
    }

    public MasterDataTerminalsApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10MasterdataTerminalsGet
     * @param storeId The store id of the requested terminals. If null all terminals are returned. (optional)
     * @param organizationId The possible organization id of the requested terminals. If null all terminals are returned. (optional)
     * @param companyId The possible company id of the requested terminals. If null all terminals are returned. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsGetCall(UUID storeId, UUID organizationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (storeId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeId", storeId));
        }

        if (organizationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organizationId", organizationId));
        }

        if (companyId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("companyId", companyId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsGetValidateBeforeCall(UUID storeId, UUID organizationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataTerminalsGetCall(storeId, organizationId, companyId, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all terminals for the requested store for the authenticated user.
     * At least one of organizationId, companyId or storeId parameter must have a value.
     * @param storeId The store id of the requested terminals. If null all terminals are returned. (optional)
     * @param organizationId The possible organization id of the requested terminals. If null all terminals are returned. (optional)
     * @param companyId The possible company id of the requested terminals. If null all terminals are returned. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return TerminalModelPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TerminalModelPageResultModel apiV10MasterdataTerminalsGet(UUID storeId, UUID organizationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<TerminalModelPageResultModel> localVarResp = apiV10MasterdataTerminalsGetWithHttpInfo(storeId, organizationId, companyId, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all terminals for the requested store for the authenticated user.
     * At least one of organizationId, companyId or storeId parameter must have a value.
     * @param storeId The store id of the requested terminals. If null all terminals are returned. (optional)
     * @param organizationId The possible organization id of the requested terminals. If null all terminals are returned. (optional)
     * @param companyId The possible company id of the requested terminals. If null all terminals are returned. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;TerminalModelPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TerminalModelPageResultModel> apiV10MasterdataTerminalsGetWithHttpInfo(UUID storeId, UUID organizationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsGetValidateBeforeCall(storeId, organizationId, companyId, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<TerminalModelPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all terminals for the requested store for the authenticated user. (asynchronously)
     * At least one of organizationId, companyId or storeId parameter must have a value.
     * @param storeId The store id of the requested terminals. If null all terminals are returned. (optional)
     * @param organizationId The possible organization id of the requested terminals. If null all terminals are returned. (optional)
     * @param companyId The possible company id of the requested terminals. If null all terminals are returned. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsGetAsync(UUID storeId, UUID organizationId, UUID companyId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<TerminalModelPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsGetValidateBeforeCall(storeId, organizationId, companyId, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<TerminalModelPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsPost
     * @param terminal The terminal to create. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsPostCall(Terminal terminal, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = terminal;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsPostValidateBeforeCall(Terminal terminal, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataTerminalsPostCall(terminal, _callback);

    }

    /**
     * Creates a terminal in the cloud store.
     * If RetailForce.Cloud.Model.Terminal.TerminalId set to System.Guid.Empty, then the terminal id will be generated by the service.
     * @param terminal The terminal to create. (optional)
     * @return TerminalModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TerminalModel apiV10MasterdataTerminalsPost(Terminal terminal) throws ApiException {
        ApiResponse<TerminalModel> localVarResp = apiV10MasterdataTerminalsPostWithHttpInfo(terminal);
        return localVarResp.getData();
    }

    /**
     * Creates a terminal in the cloud store.
     * If RetailForce.Cloud.Model.Terminal.TerminalId set to System.Guid.Empty, then the terminal id will be generated by the service.
     * @param terminal The terminal to create. (optional)
     * @return ApiResponse&lt;TerminalModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TerminalModel> apiV10MasterdataTerminalsPostWithHttpInfo(Terminal terminal) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsPostValidateBeforeCall(terminal, null);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a terminal in the cloud store. (asynchronously)
     * If RetailForce.Cloud.Model.Terminal.TerminalId set to System.Guid.Empty, then the terminal id will be generated by the service.
     * @param terminal The terminal to create. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsPostAsync(Terminal terminal, final ApiCallback<TerminalModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsPostValidateBeforeCall(terminal, _callback);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsSimpleGet
     * @param storeId The store id of the requested store. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsSimpleGetCall(UUID storeId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/simple";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (storeId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeId", storeId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchString != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchString", searchString));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsSimpleGetValidateBeforeCall(UUID storeId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataTerminalsSimpleGetCall(storeId, pageOffset, pageSize, searchString, _callback);

    }

    /**
     * Returns all terminals as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested store for the authenticated user.
     * 
     * @param storeId The store id of the requested store. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return GuidSimpleObjectPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidSimpleObjectPageResultModel apiV10MasterdataTerminalsSimpleGet(UUID storeId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        ApiResponse<GuidSimpleObjectPageResultModel> localVarResp = apiV10MasterdataTerminalsSimpleGetWithHttpInfo(storeId, pageOffset, pageSize, searchString);
        return localVarResp.getData();
    }

    /**
     * Returns all terminals as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested store for the authenticated user.
     * 
     * @param storeId The store id of the requested store. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @return ApiResponse&lt;GuidSimpleObjectPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidSimpleObjectPageResultModel> apiV10MasterdataTerminalsSimpleGetWithHttpInfo(UUID storeId, Integer pageOffset, Integer pageSize, String searchString) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsSimpleGetValidateBeforeCall(storeId, pageOffset, pageSize, searchString, null);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all terminals as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested store for the authenticated user. (asynchronously)
     * 
     * @param storeId The store id of the requested store. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchString A space delimited string with search parameters to search for entities. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsSimpleGetAsync(UUID storeId, Integer pageOffset, Integer pageSize, String searchString, final ApiCallback<GuidSimpleObjectPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsSimpleGetValidateBeforeCall(storeId, pageOffset, pageSize, searchString, _callback);
        Type localVarReturnType = new TypeToken<GuidSimpleObjectPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdArchivePost
     * @param terminalId The terminal which has to be archived. (required)
     * @param reactivate Optional. True if the archived terminal should be reactivated. You have to be an administrative user. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdArchivePostCall(UUID terminalId, Boolean reactivate, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/archive"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (reactivate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("reactivate", reactivate));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdArchivePostValidateBeforeCall(UUID terminalId, Boolean reactivate, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdArchivePost(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdArchivePostCall(terminalId, reactivate, _callback);

    }

    /**
     * Deactivates and archives the given terminal (cannot be undone).
     * 
     * @param terminalId The terminal which has to be archived. (required)
     * @param reactivate Optional. True if the archived terminal should be reactivated. You have to be an administrative user. (optional, default to false)
     * @return TerminalModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TerminalModel apiV10MasterdataTerminalsTerminalIdArchivePost(UUID terminalId, Boolean reactivate) throws ApiException {
        ApiResponse<TerminalModel> localVarResp = apiV10MasterdataTerminalsTerminalIdArchivePostWithHttpInfo(terminalId, reactivate);
        return localVarResp.getData();
    }

    /**
     * Deactivates and archives the given terminal (cannot be undone).
     * 
     * @param terminalId The terminal which has to be archived. (required)
     * @param reactivate Optional. True if the archived terminal should be reactivated. You have to be an administrative user. (optional, default to false)
     * @return ApiResponse&lt;TerminalModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TerminalModel> apiV10MasterdataTerminalsTerminalIdArchivePostWithHttpInfo(UUID terminalId, Boolean reactivate) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdArchivePostValidateBeforeCall(terminalId, reactivate, null);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Deactivates and archives the given terminal (cannot be undone). (asynchronously)
     * 
     * @param terminalId The terminal which has to be archived. (required)
     * @param reactivate Optional. True if the archived terminal should be reactivated. You have to be an administrative user. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdArchivePostAsync(UUID terminalId, Boolean reactivate, final ApiCallback<TerminalModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdArchivePostValidateBeforeCall(terminalId, reactivate, _callback);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdDeactivatePut
     * @param terminalId The terminal to deactivate. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdDeactivatePutCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/deactivate"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdDeactivatePutValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdDeactivatePut(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdDeactivatePutCall(terminalId, _callback);

    }

    /**
     * Deactivates a terminal temporarly (season functionality).
     * 
     * @param terminalId The terminal to deactivate. (required)
     * @return TerminalModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TerminalModel apiV10MasterdataTerminalsTerminalIdDeactivatePut(UUID terminalId) throws ApiException {
        ApiResponse<TerminalModel> localVarResp = apiV10MasterdataTerminalsTerminalIdDeactivatePutWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Deactivates a terminal temporarly (season functionality).
     * 
     * @param terminalId The terminal to deactivate. (required)
     * @return ApiResponse&lt;TerminalModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TerminalModel> apiV10MasterdataTerminalsTerminalIdDeactivatePutWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdDeactivatePutValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Deactivates a terminal temporarly (season functionality). (asynchronously)
     * 
     * @param terminalId The terminal to deactivate. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdDeactivatePutAsync(UUID terminalId, final ApiCallback<TerminalModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdDeactivatePutValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdDecommissionDatePost
     * @param terminalId Terminal being decommissioned. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdDecommissionDatePostCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/decommissionDate"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdDecommissionDatePostValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdDecommissionDatePost(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdDecommissionDatePostCall(terminalId, _callback);

    }

    /**
     * Set date of decommissioning for selected terminal in the cloud database.
     * 
     * @param terminalId Terminal being decommissioned. (required)
     * @return OffsetDateTime
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public OffsetDateTime apiV10MasterdataTerminalsTerminalIdDecommissionDatePost(UUID terminalId) throws ApiException {
        ApiResponse<OffsetDateTime> localVarResp = apiV10MasterdataTerminalsTerminalIdDecommissionDatePostWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Set date of decommissioning for selected terminal in the cloud database.
     * 
     * @param terminalId Terminal being decommissioned. (required)
     * @return ApiResponse&lt;OffsetDateTime&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<OffsetDateTime> apiV10MasterdataTerminalsTerminalIdDecommissionDatePostWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdDecommissionDatePostValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<OffsetDateTime>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Set date of decommissioning for selected terminal in the cloud database. (asynchronously)
     * 
     * @param terminalId Terminal being decommissioned. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdDecommissionDatePostAsync(UUID terminalId, final ApiCallback<OffsetDateTime> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdDecommissionDatePostValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<OffsetDateTime>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdDelete
     * @param terminalId The id of the terminal to delete. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdDeleteCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdDeleteValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdDelete(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdDeleteCall(terminalId, _callback);

    }

    /**
     * Deletes a terminal from the cloud store.
     * 
     * @param terminalId The id of the terminal to delete. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataTerminalsTerminalIdDelete(UUID terminalId) throws ApiException {
        apiV10MasterdataTerminalsTerminalIdDeleteWithHttpInfo(terminalId);
    }

    /**
     * Deletes a terminal from the cloud store.
     * 
     * @param terminalId The id of the terminal to delete. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataTerminalsTerminalIdDeleteWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdDeleteValidateBeforeCall(terminalId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes a terminal from the cloud store. (asynchronously)
     * 
     * @param terminalId The id of the terminal to delete. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdDeleteAsync(UUID terminalId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdDeleteValidateBeforeCall(terminalId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdFiscaldataDelete
     * @param terminalId The terminal id of the terminal where the data has to be cleaned. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/fiscaldata"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdFiscaldataDelete(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteCall(terminalId, _callback);

    }

    /**
     * Deletes the cloud storage data of the test terminal. If called on a productive terminal exception / error will be raised.
     * The terminal itself will not be deleted.
     * @param terminalId The terminal id of the terminal where the data has to be cleaned. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataTerminalsTerminalIdFiscaldataDelete(UUID terminalId) throws ApiException {
        apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteWithHttpInfo(terminalId);
    }

    /**
     * Deletes the cloud storage data of the test terminal. If called on a productive terminal exception / error will be raised.
     * The terminal itself will not be deleted.
     * @param terminalId The terminal id of the terminal where the data has to be cleaned. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteValidateBeforeCall(terminalId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes the cloud storage data of the test terminal. If called on a productive terminal exception / error will be raised. (asynchronously)
     * The terminal itself will not be deleted.
     * @param terminalId The terminal id of the terminal where the data has to be cleaned. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteAsync(UUID terminalId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteValidateBeforeCall(terminalId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdGet
     * @param terminalId The id of the requested terminal. (required)
     * @param entityVersion load specific version (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdGetCall(UUID terminalId, String entityVersion, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (entityVersion != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityVersion", entityVersion));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdGetValidateBeforeCall(UUID terminalId, String entityVersion, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdGet(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdGetCall(terminalId, entityVersion, _callback);

    }

    /**
     * Returns the requested terminal for the authenticated user.
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @param entityVersion load specific version (optional)
     * @return TerminalModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TerminalModel apiV10MasterdataTerminalsTerminalIdGet(UUID terminalId, String entityVersion) throws ApiException {
        ApiResponse<TerminalModel> localVarResp = apiV10MasterdataTerminalsTerminalIdGetWithHttpInfo(terminalId, entityVersion);
        return localVarResp.getData();
    }

    /**
     * Returns the requested terminal for the authenticated user.
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @param entityVersion load specific version (optional)
     * @return ApiResponse&lt;TerminalModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TerminalModel> apiV10MasterdataTerminalsTerminalIdGetWithHttpInfo(UUID terminalId, String entityVersion) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdGetValidateBeforeCall(terminalId, entityVersion, null);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the requested terminal for the authenticated user. (asynchronously)
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @param entityVersion load specific version (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdGetAsync(UUID terminalId, String entityVersion, final ApiCallback<TerminalModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdGetValidateBeforeCall(terminalId, entityVersion, _callback);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdGet_0
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdGet_0Call(IdentificationType identificationType, String identification, String storeNumber, String terminalNumber, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/terminal/id";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (identificationType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("identificationType", identificationType));
        }

        if (identification != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("identification", identification));
        }

        if (storeNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeNumber", storeNumber));
        }

        if (terminalNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("terminalNumber", terminalNumber));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdGet_0ValidateBeforeCall(IdentificationType identificationType, String identification, String storeNumber, String terminalNumber, final ApiCallback _callback) throws ApiException {
        return apiV10MasterdataTerminalsTerminalIdGet_0Call(identificationType, identification, storeNumber, terminalNumber, _callback);

    }

    /**
     * Returns the terminal id for the requested parameter.
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @return UUID
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public UUID apiV10MasterdataTerminalsTerminalIdGet_0(IdentificationType identificationType, String identification, String storeNumber, String terminalNumber) throws ApiException {
        ApiResponse<UUID> localVarResp = apiV10MasterdataTerminalsTerminalIdGet_0WithHttpInfo(identificationType, identification, storeNumber, terminalNumber);
        return localVarResp.getData();
    }

    /**
     * Returns the terminal id for the requested parameter.
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @return ApiResponse&lt;UUID&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<UUID> apiV10MasterdataTerminalsTerminalIdGet_0WithHttpInfo(IdentificationType identificationType, String identification, String storeNumber, String terminalNumber) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdGet_0ValidateBeforeCall(identificationType, identification, storeNumber, terminalNumber, null);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the terminal id for the requested parameter. (asynchronously)
     * 
     * @param identificationType The type of the identification for the organisation id. (optional)
     * @param identification The identification. (optional)
     * @param storeNumber The store number for the client configuration. (optional)
     * @param terminalNumber The terminal number for the client configuration. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdGet_0Async(IdentificationType identificationType, String identification, String storeNumber, String terminalNumber, final ApiCallback<UUID> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdGet_0ValidateBeforeCall(identificationType, identification, storeNumber, terminalNumber, _callback);
        Type localVarReturnType = new TypeToken<UUID>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost
     * @param terminalId The id of the terminal where the global short id should be returned. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/globalShortId"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostCall(terminalId, _callback);

    }

    /**
     * Generates a new or returns the already existing global short id for the terminal.
     * 
     * @param terminalId The id of the terminal where the global short id should be returned. (required)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost(UUID terminalId) throws ApiException {
        ApiResponse<String> localVarResp = apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Generates a new or returns the already existing global short id for the terminal.
     * 
     * @param terminalId The id of the terminal where the global short id should be returned. (required)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Generates a new or returns the already existing global short id for the terminal. (asynchronously)
     * 
     * @param terminalId The id of the terminal where the global short id should be returned. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostAsync(UUID terminalId, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdHead
     * @param terminalId The id of the requested terminal. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdHeadCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "HEAD", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdHeadValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdHead(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdHeadCall(terminalId, _callback);

    }

    /**
     * Test if access to terminal is allowed.
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10MasterdataTerminalsTerminalIdHead(UUID terminalId) throws ApiException {
        apiV10MasterdataTerminalsTerminalIdHeadWithHttpInfo(terminalId);
    }

    /**
     * Test if access to terminal is allowed.
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10MasterdataTerminalsTerminalIdHeadWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdHeadValidateBeforeCall(terminalId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Test if access to terminal is allowed. (asynchronously)
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdHeadAsync(UUID terminalId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdHeadValidateBeforeCall(terminalId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdInsightsGet
     * @param terminalId The id of the requested terminal. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdInsightsGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/insights"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdInsightsGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdInsightsGet(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdInsightsGetCall(terminalId, _callback);

    }

    /**
     * Returns terminal insights
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @return TerminalInsightModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TerminalInsightModel apiV10MasterdataTerminalsTerminalIdInsightsGet(UUID terminalId) throws ApiException {
        ApiResponse<TerminalInsightModel> localVarResp = apiV10MasterdataTerminalsTerminalIdInsightsGetWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Returns terminal insights
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @return ApiResponse&lt;TerminalInsightModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TerminalInsightModel> apiV10MasterdataTerminalsTerminalIdInsightsGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdInsightsGetValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<TerminalInsightModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns terminal insights (asynchronously)
     * 
     * @param terminalId The id of the requested terminal. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdInsightsGetAsync(UUID terminalId, final ApiCallback<TerminalInsightModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdInsightsGetValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<TerminalInsightModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet
     * @param terminalId The id of the terminal where the global short id should be returned. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/iotAccessKey"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetCall(terminalId, _callback);

    }

    /**
     * Generates a new or returns the already existing encrypted iot access key for the terminal.
     * 
     * @param terminalId The id of the terminal where the global short id should be returned. (required)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet(UUID terminalId) throws ApiException {
        ApiResponse<String> localVarResp = apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Generates a new or returns the already existing encrypted iot access key for the terminal.
     * 
     * @param terminalId The id of the terminal where the global short id should be returned. (required)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Generates a new or returns the already existing encrypted iot access key for the terminal. (asynchronously)
     * 
     * @param terminalId The id of the terminal where the global short id should be returned. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetAsync(UUID terminalId, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdPut
     * @param terminalId The terminal id of terminal to update. (required)
     * @param terminal The data of the terminal to update. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdPutCall(UUID terminalId, Terminal terminal, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = terminal;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdPutValidateBeforeCall(UUID terminalId, Terminal terminal, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdPut(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdPutCall(terminalId, terminal, _callback);

    }

    /**
     * Updates the terminal in the cloud store.
     * 
     * @param terminalId The terminal id of terminal to update. (required)
     * @param terminal The data of the terminal to update. (optional)
     * @return TerminalModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public TerminalModel apiV10MasterdataTerminalsTerminalIdPut(UUID terminalId, Terminal terminal) throws ApiException {
        ApiResponse<TerminalModel> localVarResp = apiV10MasterdataTerminalsTerminalIdPutWithHttpInfo(terminalId, terminal);
        return localVarResp.getData();
    }

    /**
     * Updates the terminal in the cloud store.
     * 
     * @param terminalId The terminal id of terminal to update. (required)
     * @param terminal The data of the terminal to update. (optional)
     * @return ApiResponse&lt;TerminalModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<TerminalModel> apiV10MasterdataTerminalsTerminalIdPutWithHttpInfo(UUID terminalId, Terminal terminal) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdPutValidateBeforeCall(terminalId, terminal, null);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates the terminal in the cloud store. (asynchronously)
     * 
     * @param terminalId The terminal id of terminal to update. (required)
     * @param terminal The data of the terminal to update. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdPutAsync(UUID terminalId, Terminal terminal, final ApiCallback<TerminalModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdPutValidateBeforeCall(terminalId, terminal, _callback);
        Type localVarReturnType = new TypeToken<TerminalModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdSupportPackagesGet
     * @param terminalId The id of the terminal to request the support packages. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdSupportPackagesGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/supportPackages"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdSupportPackagesGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdSupportPackagesGet(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdSupportPackagesGetCall(terminalId, _callback);

    }

    /**
     * Returns the last 15 support packages if available.
     * 
     * @param terminalId The id of the terminal to request the support packages. (required)
     * @return DownloadLinkPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public DownloadLinkPageResultModel apiV10MasterdataTerminalsTerminalIdSupportPackagesGet(UUID terminalId) throws ApiException {
        ApiResponse<DownloadLinkPageResultModel> localVarResp = apiV10MasterdataTerminalsTerminalIdSupportPackagesGetWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Returns the last 15 support packages if available.
     * 
     * @param terminalId The id of the terminal to request the support packages. (required)
     * @return ApiResponse&lt;DownloadLinkPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<DownloadLinkPageResultModel> apiV10MasterdataTerminalsTerminalIdSupportPackagesGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdSupportPackagesGetValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<DownloadLinkPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the last 15 support packages if available. (asynchronously)
     * 
     * @param terminalId The id of the terminal to request the support packages. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdSupportPackagesGetAsync(UUID terminalId, final ApiCallback<DownloadLinkPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdSupportPackagesGetValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<DownloadLinkPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10MasterdataTerminalsTerminalIdVersionsGet
     * @param terminalId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdVersionsGetCall(UUID terminalId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/masterdata/terminals/{terminalId}/versions"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10MasterdataTerminalsTerminalIdVersionsGetValidateBeforeCall(UUID terminalId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10MasterdataTerminalsTerminalIdVersionsGet(Async)");
        }

        return apiV10MasterdataTerminalsTerminalIdVersionsGetCall(terminalId, pageOffset, pageSize, _callback);

    }

    /**
     * Get terminal versions
     * 
     * @param terminalId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return GuidEntityVersionPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public GuidEntityVersionPageResultModel apiV10MasterdataTerminalsTerminalIdVersionsGet(UUID terminalId, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<GuidEntityVersionPageResultModel> localVarResp = apiV10MasterdataTerminalsTerminalIdVersionsGetWithHttpInfo(terminalId, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Get terminal versions
     * 
     * @param terminalId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @return ApiResponse&lt;GuidEntityVersionPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<GuidEntityVersionPageResultModel> apiV10MasterdataTerminalsTerminalIdVersionsGetWithHttpInfo(UUID terminalId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdVersionsGetValidateBeforeCall(terminalId, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<GuidEntityVersionPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get terminal versions (asynchronously)
     * 
     * @param terminalId  (required)
     * @param pageOffset  (optional)
     * @param pageSize  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10MasterdataTerminalsTerminalIdVersionsGetAsync(UUID terminalId, Integer pageOffset, Integer pageSize, final ApiCallback<GuidEntityVersionPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10MasterdataTerminalsTerminalIdVersionsGetValidateBeforeCall(terminalId, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<GuidEntityVersionPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
