/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.Company;
import com.untill.retailforce.model.CompanyModel;
import com.untill.retailforce.model.CompanyModelPageResultModel;
import com.untill.retailforce.model.GuidEntityVersionPageResultModel;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MasterDataCompaniesApi
 */
@Disabled
public class MasterDataCompaniesApiTest {

    private final MasterDataCompaniesApi api = new MasterDataCompaniesApi();

    /**
     * Deletes a company from the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataCompaniesCompanyIdDeleteTest() throws ApiException {
        UUID companyId = null;
        api.apiV10MasterdataCompaniesCompanyIdDelete(companyId);
        // TODO: test validations
    }

    /**
     * Returns the requested company by id.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataCompaniesCompanyIdGetTest() throws ApiException {
        UUID companyId = null;
        String entityVersion = null;
        CompanyModel response = api.apiV10MasterdataCompaniesCompanyIdGet(companyId, entityVersion);
        // TODO: test validations
    }

    /**
     * Updates a company in the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataCompaniesCompanyIdPutTest() throws ApiException {
        UUID companyId = null;
        Company company = null;
        CompanyModel response = api.apiV10MasterdataCompaniesCompanyIdPut(companyId, company);
        // TODO: test validations
    }

    /**
     * Get company versions
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataCompaniesCompanyIdVersionsGetTest() throws ApiException {
        UUID companyId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        GuidEntityVersionPageResultModel response = api.apiV10MasterdataCompaniesCompanyIdVersionsGet(companyId, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Returns all companies for the requested organisation for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataCompaniesGetTest() throws ApiException {
        UUID organisationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        CompanyModelPageResultModel response = api.apiV10MasterdataCompaniesGet(organisationId, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Returns the company id (if applicable) of the requested store.
     *
     * Not every store belongs to a company, therefore it possible that no companyid is returned.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataCompaniesIdGetTest() throws ApiException {
        UUID organizationId = null;
        String storeNumber = null;
        UUID response = api.apiV10MasterdataCompaniesIdGet(organizationId, storeNumber);
        // TODO: test validations
    }

    /**
     * Creates a new company in the cloud store.
     *
     * If RetailForce.Cloud.Model.Company.CompanyId set to System.Guid.Empty, then the company id will be generated by the service.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataCompaniesPostTest() throws ApiException {
        Company company = null;
        CompanyModel response = api.apiV10MasterdataCompaniesPost(company);
        // TODO: test validations
    }

    /**
     * Returns all companies as a RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested organisation for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataCompaniesSimpleGetTest() throws ApiException {
        UUID organisationId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        GuidSimpleObjectPageResultModel response = api.apiV10MasterdataCompaniesSimpleGet(organisationId, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

}
