

# SignatureDevice

Represents a signature device for an organization / company in Austria.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**type** | **SecurityCertificateType** |  |  [optional] |
|**certificateSerial** | **String** | The serial number of the certificate (hex notation) |  [optional] |
|**certificateValidTo** | **OffsetDateTime** | Date/time of the end of the certificate serial (if available) |  [optional] |
|**isDeactivated** | **Boolean** | True if the signature device was deactivated (not decommissioned) (temporarily) (Ausfall) |  [optional] |
|**isDecommissioned** | **Boolean** | True if the signature device was decommissioned (Abmeldung, weil gestohlen, etc.). |  [optional] |
|**lastFonCheck** | **OffsetDateTime** | Date/time of the last check with fon. |  [optional] |
|**finOnAnnounced** | **OffsetDateTime** | Date/time when the signature device was announced to fon. |  [optional] |



