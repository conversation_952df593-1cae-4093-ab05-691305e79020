# MasterDataOrganisationsApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10MasterdataOrganisationsGet**](MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsGet) | **GET** /api/v1.0/masterdata/organisations | Returns all organisations for the authenticated user. |
| [**apiV10MasterdataOrganisationsOrganisationIdDelete**](MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganisationIdDelete) | **DELETE** /api/v1.0/masterdata/organisations/{organisationId} | Deletes an organisation from cloud store. |
| [**apiV10MasterdataOrganisationsOrganisationIdGet**](MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganisationIdGet) | **GET** /api/v1.0/masterdata/organisations/{organisationId} | Returns the organisation requested by the given id. |
| [**apiV10MasterdataOrganisationsOrganisationIdPut**](MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganisationIdPut) | **PUT** /api/v1.0/masterdata/organisations/{organisationId} | Updates an organisation in the cloud store. |
| [**apiV10MasterdataOrganisationsOrganisationIdVersionsGet**](MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganisationIdVersionsGet) | **GET** /api/v1.0/masterdata/organisations/{organisationId}/versions | Get organisation versions |
| [**apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet**](MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet) | **GET** /api/v1.0/masterdata/organisations/{organizationId}/documentTypes | Returns all custom document types for the requested organization. |
| [**apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut**](MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut) | **PUT** /api/v1.0/masterdata/organisations/{organizationId}/documentTypes | Updates the list of custom document types for the given organization. |
| [**apiV10MasterdataOrganisationsPost**](MasterDataOrganisationsApi.md#apiV10MasterdataOrganisationsPost) | **POST** /api/v1.0/masterdata/organisations | Creates a new organization in the cloud store. |


<a id="apiV10MasterdataOrganisationsGet"></a>
# **apiV10MasterdataOrganisationsGet**
> OrganisationModelPageResultModel apiV10MasterdataOrganisationsGet(pageOffset, pageSize, searchString, organisationId, distributorId)

Returns all organisations for the authenticated user.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataOrganisationsApi apiInstance = new MasterDataOrganisationsApi(defaultClient);
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    String searchString = "searchString_example"; // String | A space delimited string with search parameters to search for entities.
    UUID organisationId = UUID.randomUUID(); // UUID | Optional. For organisation view in portal to filter only actual active organisation.
    UUID distributorId = UUID.randomUUID(); // UUID | Optional. For optional distributor filter.
    try {
      OrganisationModelPageResultModel result = apiInstance.apiV10MasterdataOrganisationsGet(pageOffset, pageSize, searchString, organisationId, distributorId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataOrganisationsApi#apiV10MasterdataOrganisationsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |
| **searchString** | **String**| A space delimited string with search parameters to search for entities. | [optional] |
| **organisationId** | **UUID**| Optional. For organisation view in portal to filter only actual active organisation. | [optional] |
| **distributorId** | **UUID**| Optional. For optional distributor filter. | [optional] |

### Return type

[**OrganisationModelPageResultModel**](OrganisationModelPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataOrganisationsOrganisationIdDelete"></a>
# **apiV10MasterdataOrganisationsOrganisationIdDelete**
> apiV10MasterdataOrganisationsOrganisationIdDelete(organisationId, pageOffset, pageSize)

Deletes an organisation from cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataOrganisationsApi apiInstance = new MasterDataOrganisationsApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the organisation which should be deleted.
    Integer pageOffset = 56; // Integer | 
    Integer pageSize = 56; // Integer | 
    try {
      apiInstance.apiV10MasterdataOrganisationsOrganisationIdDelete(organisationId, pageOffset, pageSize);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataOrganisationsApi#apiV10MasterdataOrganisationsOrganisationIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the organisation which should be deleted. | |
| **pageOffset** | **Integer**|  | [optional] |
| **pageSize** | **Integer**|  | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataOrganisationsOrganisationIdGet"></a>
# **apiV10MasterdataOrganisationsOrganisationIdGet**
> OrganisationModel apiV10MasterdataOrganisationsOrganisationIdGet(organisationId, entityVersion)

Returns the organisation requested by the given id.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataOrganisationsApi apiInstance = new MasterDataOrganisationsApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the requested organisation.
    String entityVersion = "entityVersion_example"; // String | load specific version
    try {
      OrganisationModel result = apiInstance.apiV10MasterdataOrganisationsOrganisationIdGet(organisationId, entityVersion);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataOrganisationsApi#apiV10MasterdataOrganisationsOrganisationIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the requested organisation. | |
| **entityVersion** | **String**| load specific version | [optional] |

### Return type

[**OrganisationModel**](OrganisationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataOrganisationsOrganisationIdPut"></a>
# **apiV10MasterdataOrganisationsOrganisationIdPut**
> OrganisationModel apiV10MasterdataOrganisationsOrganisationIdPut(organisationId, organisation)

Updates an organisation in the cloud store.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataOrganisationsApi apiInstance = new MasterDataOrganisationsApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the organisation to be updated.
    Organisation organisation = new Organisation(); // Organisation | The organisation object to update the organisation.
    try {
      OrganisationModel result = apiInstance.apiV10MasterdataOrganisationsOrganisationIdPut(organisationId, organisation);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataOrganisationsApi#apiV10MasterdataOrganisationsOrganisationIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the organisation to be updated. | |
| **organisation** | [**Organisation**](Organisation.md)| The organisation object to update the organisation. | [optional] |

### Return type

[**OrganisationModel**](OrganisationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataOrganisationsOrganisationIdVersionsGet"></a>
# **apiV10MasterdataOrganisationsOrganisationIdVersionsGet**
> GuidEntityVersionPageResultModel apiV10MasterdataOrganisationsOrganisationIdVersionsGet(organisationId, pageOffset, pageSize)

Get organisation versions

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataOrganisationsApi apiInstance = new MasterDataOrganisationsApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | 
    Integer pageOffset = 56; // Integer | 
    Integer pageSize = 56; // Integer | 
    try {
      GuidEntityVersionPageResultModel result = apiInstance.apiV10MasterdataOrganisationsOrganisationIdVersionsGet(organisationId, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataOrganisationsApi#apiV10MasterdataOrganisationsOrganisationIdVersionsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**|  | |
| **pageOffset** | **Integer**|  | [optional] |
| **pageSize** | **Integer**|  | [optional] |

### Return type

[**GuidEntityVersionPageResultModel**](GuidEntityVersionPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet"></a>
# **apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet**
> List&lt;Int32SimpleObject&gt; apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet(organizationId, allDocumentTypes)

Returns all custom document types for the requested organization.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataOrganisationsApi apiInstance = new MasterDataOrganisationsApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization id for this request.
    Boolean allDocumentTypes = false; // Boolean | 
    try {
      List<Int32SimpleObject> result = apiInstance.apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet(organizationId, allDocumentTypes);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataOrganisationsApi#apiV10MasterdataOrganisationsOrganizationIdDocumentTypesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization id for this request. | |
| **allDocumentTypes** | **Boolean**|  | [optional] [default to false] |

### Return type

[**List&lt;Int32SimpleObject&gt;**](Int32SimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut"></a>
# **apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut**
> apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut(organizationId, int32SimpleObject)

Updates the list of custom document types for the given organization.

Important: You have to send the whole list of custom document types; otherwise existing custom document types will be deleted.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataOrganisationsApi apiInstance = new MasterDataOrganisationsApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The organization id for this request.
    List<Int32SimpleObject> int32SimpleObject = Arrays.asList(); // List<Int32SimpleObject> | A list of custom document to store to the backend.
    try {
      apiInstance.apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut(organizationId, int32SimpleObject);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataOrganisationsApi#apiV10MasterdataOrganisationsOrganizationIdDocumentTypesPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The organization id for this request. | |
| **int32SimpleObject** | [**List&lt;Int32SimpleObject&gt;**](Int32SimpleObject.md)| A list of custom document to store to the backend. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10MasterdataOrganisationsPost"></a>
# **apiV10MasterdataOrganisationsPost**
> OrganisationModel apiV10MasterdataOrganisationsPost(organisation)

Creates a new organization in the cloud store.

If RetailForce.Cloud.Model.Organisation.OrganisationId set to System.Guid.Empty, then the organization id will be generated by the service.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.MasterDataOrganisationsApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    MasterDataOrganisationsApi apiInstance = new MasterDataOrganisationsApi(defaultClient);
    Organisation organisation = new Organisation(); // Organisation | The organization to create.
    try {
      OrganisationModel result = apiInstance.apiV10MasterdataOrganisationsPost(organisation);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling MasterDataOrganisationsApi#apiV10MasterdataOrganisationsPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisation** | [**Organisation**](Organisation.md)| The organization to create. | [optional] |

### Return type

[**OrganisationModel**](OrganisationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

