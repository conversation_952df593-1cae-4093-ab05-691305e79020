/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Contract for supplier.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SupplierContract {
  public static final String SERIALIZED_NAME_SUPPLIER_ID = "supplierId";
  @SerializedName(SERIALIZED_NAME_SUPPLIER_ID)
  private UUID supplierId;

  public static final String SERIALIZED_NAME_CONTRACT_ID = "contractId";
  @SerializedName(SERIALIZED_NAME_CONTRACT_ID)
  private UUID contractId;

  public static final String SERIALIZED_NAME_CONTRACT_TYPE_ID = "contractTypeId";
  @SerializedName(SERIALIZED_NAME_CONTRACT_TYPE_ID)
  private UUID contractTypeId;

  public static final String SERIALIZED_NAME_CAPTION = "caption";
  @SerializedName(SERIALIZED_NAME_CAPTION)
  private String caption;

  public static final String SERIALIZED_NAME_START_DATE = "startDate";
  @SerializedName(SERIALIZED_NAME_START_DATE)
  private OffsetDateTime startDate;

  public static final String SERIALIZED_NAME_END_DATE = "endDate";
  @SerializedName(SERIALIZED_NAME_END_DATE)
  private OffsetDateTime endDate;

  public static final String SERIALIZED_NAME_MAIN_DUE_DATE = "mainDueDate";
  @SerializedName(SERIALIZED_NAME_MAIN_DUE_DATE)
  private OffsetDateTime mainDueDate;

  public static final String SERIALIZED_NAME_CONTRACT_CHANGE_ID = "contractChangeId";
  @SerializedName(SERIALIZED_NAME_CONTRACT_CHANGE_ID)
  private UUID contractChangeId;

  public static final String SERIALIZED_NAME_CONTRACT_CHANGE_DATE = "contractChangeDate";
  @SerializedName(SERIALIZED_NAME_CONTRACT_CHANGE_DATE)
  private OffsetDateTime contractChangeDate;

  public SupplierContract() {
  }

  public SupplierContract supplierId(UUID supplierId) {
    
    this.supplierId = supplierId;
    return this;
  }

   /**
   * The id of the supplier for this contract.
   * @return supplierId
  **/
  @javax.annotation.Nullable
  public UUID getSupplierId() {
    return supplierId;
  }


  public void setSupplierId(UUID supplierId) {
    this.supplierId = supplierId;
  }


  public SupplierContract contractId(UUID contractId) {
    
    this.contractId = contractId;
    return this;
  }

   /**
   * The id of the contract.
   * @return contractId
  **/
  @javax.annotation.Nullable
  public UUID getContractId() {
    return contractId;
  }


  public void setContractId(UUID contractId) {
    this.contractId = contractId;
  }


  public SupplierContract contractTypeId(UUID contractTypeId) {
    
    this.contractTypeId = contractTypeId;
    return this;
  }

   /**
   * The id of the type of the contract.
   * @return contractTypeId
  **/
  @javax.annotation.Nullable
  public UUID getContractTypeId() {
    return contractTypeId;
  }


  public void setContractTypeId(UUID contractTypeId) {
    this.contractTypeId = contractTypeId;
  }


  public SupplierContract caption(String caption) {
    
    this.caption = caption;
    return this;
  }

   /**
   * The caption of the contract.
   * @return caption
  **/
  @javax.annotation.Nullable
  public String getCaption() {
    return caption;
  }


  public void setCaption(String caption) {
    this.caption = caption;
  }


  public SupplierContract startDate(OffsetDateTime startDate) {
    
    this.startDate = startDate;
    return this;
  }

   /**
   * The start date of the contract.
   * @return startDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getStartDate() {
    return startDate;
  }


  public void setStartDate(OffsetDateTime startDate) {
    this.startDate = startDate;
  }


  public SupplierContract endDate(OffsetDateTime endDate) {
    
    this.endDate = endDate;
    return this;
  }

   /**
   * The end date of the contract.
   * @return endDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getEndDate() {
    return endDate;
  }


  public void setEndDate(OffsetDateTime endDate) {
    this.endDate = endDate;
  }


  public SupplierContract mainDueDate(OffsetDateTime mainDueDate) {
    
    this.mainDueDate = mainDueDate;
    return this;
  }

   /**
   * The main due date of the contract.
   * @return mainDueDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getMainDueDate() {
    return mainDueDate;
  }


  public void setMainDueDate(OffsetDateTime mainDueDate) {
    this.mainDueDate = mainDueDate;
  }


  public SupplierContract contractChangeId(UUID contractChangeId) {
    
    this.contractChangeId = contractChangeId;
    return this;
  }

   /**
   * A unique id of contract change. If contract is uploaded again, this id is changed.
   * @return contractChangeId
  **/
  @javax.annotation.Nullable
  public UUID getContractChangeId() {
    return contractChangeId;
  }


  public void setContractChangeId(UUID contractChangeId) {
    this.contractChangeId = contractChangeId;
  }


  public SupplierContract contractChangeDate(OffsetDateTime contractChangeDate) {
    
    this.contractChangeDate = contractChangeDate;
    return this;
  }

   /**
   * The change date of the contract.
   * @return contractChangeDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getContractChangeDate() {
    return contractChangeDate;
  }


  public void setContractChangeDate(OffsetDateTime contractChangeDate) {
    this.contractChangeDate = contractChangeDate;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SupplierContract supplierContract = (SupplierContract) o;
    return Objects.equals(this.supplierId, supplierContract.supplierId) &&
        Objects.equals(this.contractId, supplierContract.contractId) &&
        Objects.equals(this.contractTypeId, supplierContract.contractTypeId) &&
        Objects.equals(this.caption, supplierContract.caption) &&
        Objects.equals(this.startDate, supplierContract.startDate) &&
        Objects.equals(this.endDate, supplierContract.endDate) &&
        Objects.equals(this.mainDueDate, supplierContract.mainDueDate) &&
        Objects.equals(this.contractChangeId, supplierContract.contractChangeId) &&
        Objects.equals(this.contractChangeDate, supplierContract.contractChangeDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(supplierId, contractId, contractTypeId, caption, startDate, endDate, mainDueDate, contractChangeId, contractChangeDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SupplierContract {\n");
    sb.append("    supplierId: ").append(toIndentedString(supplierId)).append("\n");
    sb.append("    contractId: ").append(toIndentedString(contractId)).append("\n");
    sb.append("    contractTypeId: ").append(toIndentedString(contractTypeId)).append("\n");
    sb.append("    caption: ").append(toIndentedString(caption)).append("\n");
    sb.append("    startDate: ").append(toIndentedString(startDate)).append("\n");
    sb.append("    endDate: ").append(toIndentedString(endDate)).append("\n");
    sb.append("    mainDueDate: ").append(toIndentedString(mainDueDate)).append("\n");
    sb.append("    contractChangeId: ").append(toIndentedString(contractChangeId)).append("\n");
    sb.append("    contractChangeDate: ").append(toIndentedString(contractChangeDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("supplierId");
    openapiFields.add("contractId");
    openapiFields.add("contractTypeId");
    openapiFields.add("caption");
    openapiFields.add("startDate");
    openapiFields.add("endDate");
    openapiFields.add("mainDueDate");
    openapiFields.add("contractChangeId");
    openapiFields.add("contractChangeDate");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to SupplierContract
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!SupplierContract.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in SupplierContract is not found in the empty JSON string", SupplierContract.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!SupplierContract.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `SupplierContract` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("supplierId") != null && !jsonObj.get("supplierId").isJsonNull()) && !jsonObj.get("supplierId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `supplierId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("supplierId").toString()));
      }
      if ((jsonObj.get("contractId") != null && !jsonObj.get("contractId").isJsonNull()) && !jsonObj.get("contractId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `contractId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("contractId").toString()));
      }
      if ((jsonObj.get("contractTypeId") != null && !jsonObj.get("contractTypeId").isJsonNull()) && !jsonObj.get("contractTypeId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `contractTypeId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("contractTypeId").toString()));
      }
      if ((jsonObj.get("caption") != null && !jsonObj.get("caption").isJsonNull()) && !jsonObj.get("caption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption").toString()));
      }
      if ((jsonObj.get("contractChangeId") != null && !jsonObj.get("contractChangeId").isJsonNull()) && !jsonObj.get("contractChangeId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `contractChangeId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("contractChangeId").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!SupplierContract.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'SupplierContract' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<SupplierContract> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(SupplierContract.class));

       return (TypeAdapter<T>) new TypeAdapter<SupplierContract>() {
           @Override
           public void write(JsonWriter out, SupplierContract value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public SupplierContract read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of SupplierContract given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of SupplierContract
  * @throws IOException if the JSON string is invalid with respect to SupplierContract
  */
  public static SupplierContract fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, SupplierContract.class);
  }

 /**
  * Convert an instance of SupplierContract to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

