

# StoreModel

Viewmodel object for store object (extending RetailForce.Cloud.Model.Store object.

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**organisationCaption** | **String** | The caption of the organisation the stores belongs. |  [optional] |
|**companyCaption** | **String** | The caption of the company the stores belongs (if the store is attached to a company). |  [optional] |
|**companyIdentification** | **String** | The primary identification of the company / organisation to the stores belongs. |  [optional] |
|**fiscalCountry** | **FiscalCountry** |  |  [optional] |
|**terminalCount** | **Integer** | The number of terminals in this store. |  [optional] |
|**alert** | **Boolean** | True if any of the systems belonging to this store has an alert. Otherwise false. |  [optional] |
|**canEdit** | **Boolean** | True if the store can be edited. Otherwise false. |  [optional] |
|**canDelete** | **Boolean** | True if the store can be deleted. Otherwise false. |  [optional] |
|**configurationCaption** | **String** | The caption of the configuration assigned to the store. |  [optional] |
|**newConfigurationCaption** | **String** | The caption of a possible new configuration assigned to the store. |  [optional] |
|**isHidden** | **Boolean** | True if the terminal is archived |  [optional] [readonly] |
|**version** | **String** | Version (validFrom &#x3D;&gt; UTC ticks, as string for precision) |  [optional] [readonly] |
|**storeId** | **UUID** | The id of the store. |  [optional] |
|**organisationId** | **UUID** | The id of the organisation to which the store belongs. |  [optional] |
|**companyId** | **UUID** | The id of the company to which the store belongs. Can be null. |  [optional] |
|**storeNumber** | **String** | The number of the store. |  [optional] |
|**caption** | **String** | The caption of store. |  [optional] |
|**fiscalRegion** | **String** | The fiscal region for this store (only applicable if the country supports fiscal regions). |  [optional] |
|**telephone** | **String** | Gets or sets the telephon nummber of the store. |  [optional] |
|**fax** | **String** | Gets or sets the mobile number of the store. |  [optional] |
|**eMail** | **String** | Gets or sets the email of the store. |  [optional] |
|**openingDate** | **OffsetDateTime** | The opening date of the store. |  [optional] |
|**closingDate** | **OffsetDateTime** | The closing date of the store. |  [optional] |
|**hiddenDate** | **OffsetDateTime** | The date when the store was hidden from the standard view. |  [optional] |
|**updatedByPrincipalId** | **UUID** | Updated by PrincipalId |  [optional] |
|**clientConfigurationId** | **UUID** | The used configuration for this object. |  [optional] |
|**newClientConfigurationId** | **UUID** | The new configuration for this object valid from RetailForce.Cloud.Model.Address.NewClientConfigurationValidFrom.  &lt;remark&gt;The RetailForce.Cloud.Model.Address.NewClientConfigurationValidFrom must have a value otherwise the normal clientConfiguration will be used.&lt;/remark&gt; |  [optional] |
|**newClientConfigurationValidFrom** | **OffsetDateTime** | The valid date for the RetailForce.Cloud.Model.Address.NewClientConfigurationId. |  [optional] |
|**street** | **String** |  |  |
|**streetNumber** | **String** |  |  |
|**postalCode** | **String** |  |  |
|**city** | **String** |  |  |
|**community** | **String** |  |  [optional] |
|**countryCode** | **String** |  |  |



