/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Represents a store in the cloud.
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class Store {
  public static final String SERIALIZED_NAME_STORE_ID = "storeId";
  @SerializedName(SERIALIZED_NAME_STORE_ID)
  private UUID storeId;

  public static final String SERIALIZED_NAME_ORGANISATION_ID = "organisationId";
  @SerializedName(SERIALIZED_NAME_ORGANISATION_ID)
  private UUID organisationId;

  public static final String SERIALIZED_NAME_COMPANY_ID = "companyId";
  @SerializedName(SERIALIZED_NAME_COMPANY_ID)
  private UUID companyId;

  public static final String SERIALIZED_NAME_STORE_NUMBER = "storeNumber";
  @SerializedName(SERIALIZED_NAME_STORE_NUMBER)
  private String storeNumber;

  public static final String SERIALIZED_NAME_CAPTION = "caption";
  @SerializedName(SERIALIZED_NAME_CAPTION)
  private String caption;

  public static final String SERIALIZED_NAME_FISCAL_REGION = "fiscalRegion";
  @SerializedName(SERIALIZED_NAME_FISCAL_REGION)
  private String fiscalRegion;

  public static final String SERIALIZED_NAME_TELEPHONE = "telephone";
  @SerializedName(SERIALIZED_NAME_TELEPHONE)
  private String telephone;

  public static final String SERIALIZED_NAME_FAX = "fax";
  @SerializedName(SERIALIZED_NAME_FAX)
  private String fax;

  public static final String SERIALIZED_NAME_E_MAIL = "eMail";
  @SerializedName(SERIALIZED_NAME_E_MAIL)
  private String eMail;

  public static final String SERIALIZED_NAME_OPENING_DATE = "openingDate";
  @SerializedName(SERIALIZED_NAME_OPENING_DATE)
  private OffsetDateTime openingDate;

  public static final String SERIALIZED_NAME_CLOSING_DATE = "closingDate";
  @SerializedName(SERIALIZED_NAME_CLOSING_DATE)
  private OffsetDateTime closingDate;

  public static final String SERIALIZED_NAME_HIDDEN_DATE = "hiddenDate";
  @SerializedName(SERIALIZED_NAME_HIDDEN_DATE)
  private OffsetDateTime hiddenDate;

  public static final String SERIALIZED_NAME_UPDATED_BY_PRINCIPAL_ID = "updatedByPrincipalId";
  @SerializedName(SERIALIZED_NAME_UPDATED_BY_PRINCIPAL_ID)
  private UUID updatedByPrincipalId;

  public static final String SERIALIZED_NAME_CLIENT_CONFIGURATION_ID = "clientConfigurationId";
  @SerializedName(SERIALIZED_NAME_CLIENT_CONFIGURATION_ID)
  private UUID clientConfigurationId;

  public static final String SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_ID = "newClientConfigurationId";
  @SerializedName(SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_ID)
  private UUID newClientConfigurationId;

  public static final String SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_VALID_FROM = "newClientConfigurationValidFrom";
  @SerializedName(SERIALIZED_NAME_NEW_CLIENT_CONFIGURATION_VALID_FROM)
  private OffsetDateTime newClientConfigurationValidFrom;

  public static final String SERIALIZED_NAME_STREET = "street";
  @SerializedName(SERIALIZED_NAME_STREET)
  private String street;

  public static final String SERIALIZED_NAME_STREET_NUMBER = "streetNumber";
  @SerializedName(SERIALIZED_NAME_STREET_NUMBER)
  private String streetNumber;

  public static final String SERIALIZED_NAME_POSTAL_CODE = "postalCode";
  @SerializedName(SERIALIZED_NAME_POSTAL_CODE)
  private String postalCode;

  public static final String SERIALIZED_NAME_CITY = "city";
  @SerializedName(SERIALIZED_NAME_CITY)
  private String city;

  public static final String SERIALIZED_NAME_COMMUNITY = "community";
  @SerializedName(SERIALIZED_NAME_COMMUNITY)
  private String community;

  public static final String SERIALIZED_NAME_COUNTRY_CODE = "countryCode";
  @SerializedName(SERIALIZED_NAME_COUNTRY_CODE)
  private String countryCode;

  public Store() {
  }

  public Store storeId(UUID storeId) {
    
    this.storeId = storeId;
    return this;
  }

   /**
   * The id of the store.
   * @return storeId
  **/
  @javax.annotation.Nullable
  public UUID getStoreId() {
    return storeId;
  }


  public void setStoreId(UUID storeId) {
    this.storeId = storeId;
  }


  public Store organisationId(UUID organisationId) {
    
    this.organisationId = organisationId;
    return this;
  }

   /**
   * The id of the organisation to which the store belongs.
   * @return organisationId
  **/
  @javax.annotation.Nullable
  public UUID getOrganisationId() {
    return organisationId;
  }


  public void setOrganisationId(UUID organisationId) {
    this.organisationId = organisationId;
  }


  public Store companyId(UUID companyId) {
    
    this.companyId = companyId;
    return this;
  }

   /**
   * The id of the company to which the store belongs. Can be null.
   * @return companyId
  **/
  @javax.annotation.Nullable
  public UUID getCompanyId() {
    return companyId;
  }


  public void setCompanyId(UUID companyId) {
    this.companyId = companyId;
  }


  public Store storeNumber(String storeNumber) {
    
    this.storeNumber = storeNumber;
    return this;
  }

   /**
   * The number of the store.
   * @return storeNumber
  **/
  @javax.annotation.Nullable
  public String getStoreNumber() {
    return storeNumber;
  }


  public void setStoreNumber(String storeNumber) {
    this.storeNumber = storeNumber;
  }


  public Store caption(String caption) {
    
    this.caption = caption;
    return this;
  }

   /**
   * The caption of store.
   * @return caption
  **/
  @javax.annotation.Nullable
  public String getCaption() {
    return caption;
  }


  public void setCaption(String caption) {
    this.caption = caption;
  }


  public Store fiscalRegion(String fiscalRegion) {
    
    this.fiscalRegion = fiscalRegion;
    return this;
  }

   /**
   * The fiscal region for this store (only applicable if the country supports fiscal regions).
   * @return fiscalRegion
  **/
  @javax.annotation.Nullable
  public String getFiscalRegion() {
    return fiscalRegion;
  }


  public void setFiscalRegion(String fiscalRegion) {
    this.fiscalRegion = fiscalRegion;
  }


  public Store telephone(String telephone) {
    
    this.telephone = telephone;
    return this;
  }

   /**
   * Gets or sets the telephon nummber of the store.
   * @return telephone
  **/
  @javax.annotation.Nullable
  public String getTelephone() {
    return telephone;
  }


  public void setTelephone(String telephone) {
    this.telephone = telephone;
  }


  public Store fax(String fax) {
    
    this.fax = fax;
    return this;
  }

   /**
   * Gets or sets the mobile number of the store.
   * @return fax
  **/
  @javax.annotation.Nullable
  public String getFax() {
    return fax;
  }


  public void setFax(String fax) {
    this.fax = fax;
  }


  public Store eMail(String eMail) {
    
    this.eMail = eMail;
    return this;
  }

   /**
   * Gets or sets the email of the store.
   * @return eMail
  **/
  @javax.annotation.Nullable
  public String geteMail() {
    return eMail;
  }


  public void seteMail(String eMail) {
    this.eMail = eMail;
  }


  public Store openingDate(OffsetDateTime openingDate) {
    
    this.openingDate = openingDate;
    return this;
  }

   /**
   * The opening date of the store.
   * @return openingDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getOpeningDate() {
    return openingDate;
  }


  public void setOpeningDate(OffsetDateTime openingDate) {
    this.openingDate = openingDate;
  }


  public Store closingDate(OffsetDateTime closingDate) {
    
    this.closingDate = closingDate;
    return this;
  }

   /**
   * The closing date of the store.
   * @return closingDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getClosingDate() {
    return closingDate;
  }


  public void setClosingDate(OffsetDateTime closingDate) {
    this.closingDate = closingDate;
  }


  public Store hiddenDate(OffsetDateTime hiddenDate) {
    
    this.hiddenDate = hiddenDate;
    return this;
  }

   /**
   * The date when the store was hidden from the standard view.
   * @return hiddenDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getHiddenDate() {
    return hiddenDate;
  }


  public void setHiddenDate(OffsetDateTime hiddenDate) {
    this.hiddenDate = hiddenDate;
  }


  public Store updatedByPrincipalId(UUID updatedByPrincipalId) {
    
    this.updatedByPrincipalId = updatedByPrincipalId;
    return this;
  }

   /**
   * Updated by PrincipalId
   * @return updatedByPrincipalId
  **/
  @javax.annotation.Nullable
  public UUID getUpdatedByPrincipalId() {
    return updatedByPrincipalId;
  }


  public void setUpdatedByPrincipalId(UUID updatedByPrincipalId) {
    this.updatedByPrincipalId = updatedByPrincipalId;
  }


  public Store clientConfigurationId(UUID clientConfigurationId) {
    
    this.clientConfigurationId = clientConfigurationId;
    return this;
  }

   /**
   * The used configuration for this object.
   * @return clientConfigurationId
  **/
  @javax.annotation.Nullable
  public UUID getClientConfigurationId() {
    return clientConfigurationId;
  }


  public void setClientConfigurationId(UUID clientConfigurationId) {
    this.clientConfigurationId = clientConfigurationId;
  }


  public Store newClientConfigurationId(UUID newClientConfigurationId) {
    
    this.newClientConfigurationId = newClientConfigurationId;
    return this;
  }

   /**
   * The new configuration for this object valid from RetailForce.Cloud.Model.Address.NewClientConfigurationValidFrom.  &lt;remark&gt;The RetailForce.Cloud.Model.Address.NewClientConfigurationValidFrom must have a value otherwise the normal clientConfiguration will be used.&lt;/remark&gt;
   * @return newClientConfigurationId
  **/
  @javax.annotation.Nullable
  public UUID getNewClientConfigurationId() {
    return newClientConfigurationId;
  }


  public void setNewClientConfigurationId(UUID newClientConfigurationId) {
    this.newClientConfigurationId = newClientConfigurationId;
  }


  public Store newClientConfigurationValidFrom(OffsetDateTime newClientConfigurationValidFrom) {
    
    this.newClientConfigurationValidFrom = newClientConfigurationValidFrom;
    return this;
  }

   /**
   * The valid date for the RetailForce.Cloud.Model.Address.NewClientConfigurationId.
   * @return newClientConfigurationValidFrom
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getNewClientConfigurationValidFrom() {
    return newClientConfigurationValidFrom;
  }


  public void setNewClientConfigurationValidFrom(OffsetDateTime newClientConfigurationValidFrom) {
    this.newClientConfigurationValidFrom = newClientConfigurationValidFrom;
  }


  public Store street(String street) {
    
    this.street = street;
    return this;
  }

   /**
   * Get street
   * @return street
  **/
  @javax.annotation.Nonnull
  public String getStreet() {
    return street;
  }


  public void setStreet(String street) {
    this.street = street;
  }


  public Store streetNumber(String streetNumber) {
    
    this.streetNumber = streetNumber;
    return this;
  }

   /**
   * Get streetNumber
   * @return streetNumber
  **/
  @javax.annotation.Nonnull
  public String getStreetNumber() {
    return streetNumber;
  }


  public void setStreetNumber(String streetNumber) {
    this.streetNumber = streetNumber;
  }


  public Store postalCode(String postalCode) {
    
    this.postalCode = postalCode;
    return this;
  }

   /**
   * Get postalCode
   * @return postalCode
  **/
  @javax.annotation.Nonnull
  public String getPostalCode() {
    return postalCode;
  }


  public void setPostalCode(String postalCode) {
    this.postalCode = postalCode;
  }


  public Store city(String city) {
    
    this.city = city;
    return this;
  }

   /**
   * Get city
   * @return city
  **/
  @javax.annotation.Nonnull
  public String getCity() {
    return city;
  }


  public void setCity(String city) {
    this.city = city;
  }


  public Store community(String community) {
    
    this.community = community;
    return this;
  }

   /**
   * Get community
   * @return community
  **/
  @javax.annotation.Nullable
  public String getCommunity() {
    return community;
  }


  public void setCommunity(String community) {
    this.community = community;
  }


  public Store countryCode(String countryCode) {
    
    this.countryCode = countryCode;
    return this;
  }

   /**
   * Get countryCode
   * @return countryCode
  **/
  @javax.annotation.Nonnull
  public String getCountryCode() {
    return countryCode;
  }


  public void setCountryCode(String countryCode) {
    this.countryCode = countryCode;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Store store = (Store) o;
    return Objects.equals(this.storeId, store.storeId) &&
        Objects.equals(this.organisationId, store.organisationId) &&
        Objects.equals(this.companyId, store.companyId) &&
        Objects.equals(this.storeNumber, store.storeNumber) &&
        Objects.equals(this.caption, store.caption) &&
        Objects.equals(this.fiscalRegion, store.fiscalRegion) &&
        Objects.equals(this.telephone, store.telephone) &&
        Objects.equals(this.fax, store.fax) &&
        Objects.equals(this.eMail, store.eMail) &&
        Objects.equals(this.openingDate, store.openingDate) &&
        Objects.equals(this.closingDate, store.closingDate) &&
        Objects.equals(this.hiddenDate, store.hiddenDate) &&
        Objects.equals(this.updatedByPrincipalId, store.updatedByPrincipalId) &&
        Objects.equals(this.clientConfigurationId, store.clientConfigurationId) &&
        Objects.equals(this.newClientConfigurationId, store.newClientConfigurationId) &&
        Objects.equals(this.newClientConfigurationValidFrom, store.newClientConfigurationValidFrom) &&
        Objects.equals(this.street, store.street) &&
        Objects.equals(this.streetNumber, store.streetNumber) &&
        Objects.equals(this.postalCode, store.postalCode) &&
        Objects.equals(this.city, store.city) &&
        Objects.equals(this.community, store.community) &&
        Objects.equals(this.countryCode, store.countryCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(storeId, organisationId, companyId, storeNumber, caption, fiscalRegion, telephone, fax, eMail, openingDate, closingDate, hiddenDate, updatedByPrincipalId, clientConfigurationId, newClientConfigurationId, newClientConfigurationValidFrom, street, streetNumber, postalCode, city, community, countryCode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Store {\n");
    sb.append("    storeId: ").append(toIndentedString(storeId)).append("\n");
    sb.append("    organisationId: ").append(toIndentedString(organisationId)).append("\n");
    sb.append("    companyId: ").append(toIndentedString(companyId)).append("\n");
    sb.append("    storeNumber: ").append(toIndentedString(storeNumber)).append("\n");
    sb.append("    caption: ").append(toIndentedString(caption)).append("\n");
    sb.append("    fiscalRegion: ").append(toIndentedString(fiscalRegion)).append("\n");
    sb.append("    telephone: ").append(toIndentedString(telephone)).append("\n");
    sb.append("    fax: ").append(toIndentedString(fax)).append("\n");
    sb.append("    eMail: ").append(toIndentedString(eMail)).append("\n");
    sb.append("    openingDate: ").append(toIndentedString(openingDate)).append("\n");
    sb.append("    closingDate: ").append(toIndentedString(closingDate)).append("\n");
    sb.append("    hiddenDate: ").append(toIndentedString(hiddenDate)).append("\n");
    sb.append("    updatedByPrincipalId: ").append(toIndentedString(updatedByPrincipalId)).append("\n");
    sb.append("    clientConfigurationId: ").append(toIndentedString(clientConfigurationId)).append("\n");
    sb.append("    newClientConfigurationId: ").append(toIndentedString(newClientConfigurationId)).append("\n");
    sb.append("    newClientConfigurationValidFrom: ").append(toIndentedString(newClientConfigurationValidFrom)).append("\n");
    sb.append("    street: ").append(toIndentedString(street)).append("\n");
    sb.append("    streetNumber: ").append(toIndentedString(streetNumber)).append("\n");
    sb.append("    postalCode: ").append(toIndentedString(postalCode)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("    community: ").append(toIndentedString(community)).append("\n");
    sb.append("    countryCode: ").append(toIndentedString(countryCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("storeId");
    openapiFields.add("organisationId");
    openapiFields.add("companyId");
    openapiFields.add("storeNumber");
    openapiFields.add("caption");
    openapiFields.add("fiscalRegion");
    openapiFields.add("telephone");
    openapiFields.add("fax");
    openapiFields.add("eMail");
    openapiFields.add("openingDate");
    openapiFields.add("closingDate");
    openapiFields.add("hiddenDate");
    openapiFields.add("updatedByPrincipalId");
    openapiFields.add("clientConfigurationId");
    openapiFields.add("newClientConfigurationId");
    openapiFields.add("newClientConfigurationValidFrom");
    openapiFields.add("street");
    openapiFields.add("streetNumber");
    openapiFields.add("postalCode");
    openapiFields.add("city");
    openapiFields.add("community");
    openapiFields.add("countryCode");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
    openapiRequiredFields.add("street");
    openapiRequiredFields.add("streetNumber");
    openapiRequiredFields.add("postalCode");
    openapiRequiredFields.add("city");
    openapiRequiredFields.add("countryCode");
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to Store
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!Store.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in Store is not found in the empty JSON string", Store.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!Store.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `Store` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }

      // check to make sure all required properties/fields are present in the JSON string
      for (String requiredField : Store.openapiRequiredFields) {
        if (jsonObj.get(requiredField) == null) {
          throw new IllegalArgumentException(String.format("The required field `%s` is not found in the JSON string: %s", requiredField, jsonObj.toString()));
        }
      }
      if ((jsonObj.get("storeId") != null && !jsonObj.get("storeId").isJsonNull()) && !jsonObj.get("storeId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeId").toString()));
      }
      if ((jsonObj.get("organisationId") != null && !jsonObj.get("organisationId").isJsonNull()) && !jsonObj.get("organisationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `organisationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("organisationId").toString()));
      }
      if ((jsonObj.get("companyId") != null && !jsonObj.get("companyId").isJsonNull()) && !jsonObj.get("companyId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `companyId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("companyId").toString()));
      }
      if ((jsonObj.get("storeNumber") != null && !jsonObj.get("storeNumber").isJsonNull()) && !jsonObj.get("storeNumber").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeNumber` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeNumber").toString()));
      }
      if ((jsonObj.get("caption") != null && !jsonObj.get("caption").isJsonNull()) && !jsonObj.get("caption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption").toString()));
      }
      if ((jsonObj.get("fiscalRegion") != null && !jsonObj.get("fiscalRegion").isJsonNull()) && !jsonObj.get("fiscalRegion").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `fiscalRegion` to be a primitive type in the JSON string but got `%s`", jsonObj.get("fiscalRegion").toString()));
      }
      if ((jsonObj.get("telephone") != null && !jsonObj.get("telephone").isJsonNull()) && !jsonObj.get("telephone").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `telephone` to be a primitive type in the JSON string but got `%s`", jsonObj.get("telephone").toString()));
      }
      if ((jsonObj.get("fax") != null && !jsonObj.get("fax").isJsonNull()) && !jsonObj.get("fax").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `fax` to be a primitive type in the JSON string but got `%s`", jsonObj.get("fax").toString()));
      }
      if ((jsonObj.get("eMail") != null && !jsonObj.get("eMail").isJsonNull()) && !jsonObj.get("eMail").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `eMail` to be a primitive type in the JSON string but got `%s`", jsonObj.get("eMail").toString()));
      }
      if ((jsonObj.get("updatedByPrincipalId") != null && !jsonObj.get("updatedByPrincipalId").isJsonNull()) && !jsonObj.get("updatedByPrincipalId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `updatedByPrincipalId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("updatedByPrincipalId").toString()));
      }
      if ((jsonObj.get("clientConfigurationId") != null && !jsonObj.get("clientConfigurationId").isJsonNull()) && !jsonObj.get("clientConfigurationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `clientConfigurationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("clientConfigurationId").toString()));
      }
      if ((jsonObj.get("newClientConfigurationId") != null && !jsonObj.get("newClientConfigurationId").isJsonNull()) && !jsonObj.get("newClientConfigurationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `newClientConfigurationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("newClientConfigurationId").toString()));
      }
      if (!jsonObj.get("street").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `street` to be a primitive type in the JSON string but got `%s`", jsonObj.get("street").toString()));
      }
      if (!jsonObj.get("streetNumber").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `streetNumber` to be a primitive type in the JSON string but got `%s`", jsonObj.get("streetNumber").toString()));
      }
      if (!jsonObj.get("postalCode").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `postalCode` to be a primitive type in the JSON string but got `%s`", jsonObj.get("postalCode").toString()));
      }
      if (!jsonObj.get("city").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `city` to be a primitive type in the JSON string but got `%s`", jsonObj.get("city").toString()));
      }
      if ((jsonObj.get("community") != null && !jsonObj.get("community").isJsonNull()) && !jsonObj.get("community").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `community` to be a primitive type in the JSON string but got `%s`", jsonObj.get("community").toString()));
      }
      if (!jsonObj.get("countryCode").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `countryCode` to be a primitive type in the JSON string but got `%s`", jsonObj.get("countryCode").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!Store.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'Store' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<Store> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(Store.class));

       return (TypeAdapter<T>) new TypeAdapter<Store>() {
           @Override
           public void write(JsonWriter out, Store value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public Store read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of Store given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of Store
  * @throws IOException if the JSON string is invalid with respect to Store
  */
  public static Store fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, Store.class);
  }

 /**
  * Convert an instance of Store to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

