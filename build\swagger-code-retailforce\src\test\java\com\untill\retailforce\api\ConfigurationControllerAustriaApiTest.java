/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.BoolResponse;
import com.untill.retailforce.model.CashRegisterDropoutReason;
import com.untill.retailforce.model.FonCredentials;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.ResultResponse;
import com.untill.retailforce.model.SecurityCertificateDropoutReason;
import com.untill.retailforce.model.SecurityCertificateIssuer;
import com.untill.retailforce.model.SecurityCertificateType;
import com.untill.retailforce.model.SignDeviceDriverInfo;
import com.untill.retailforce.model.StringSimpleObject;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ConfigurationControllerAustriaApi
 */
@Disabled
public class ConfigurationControllerAustriaApiTest {

    private final ConfigurationControllerAustriaApi api = new ConfigurationControllerAustriaApi();

    /**
     * Checks if an authentication can be done with the given fon credentials.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonCheckFonCredentialsPutTest() throws ApiException {
        FonCredentials fonCredentials = null;
        BoolResponse response = api.apiV10ConfigurationAtFonCheckFonCredentialsPut(fonCredentials);
        // TODO: test validations
    }

    /**
     * Updates a the aes key for the given terminal in the retailforce database (or adds).
     *
     * This method does not update the aes key at fon (Finanzonline).  Handle with care: Settings this value to a wrong value will result in an error when recovering fiscal client with RestoreByCloud and decrypting of turnover counter of dep of this client will no longer be possible.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdAesKeyPostTest() throws ApiException {
        UUID clientId = null;
        String body = null;
        api.apiV10ConfigurationAtFonClientIdAesKeyPost(clientId, body);
        // TODO: test validations
    }

    /**
     * Clears fon cache variable on server (for faster fon communication).
     *
     * Removes both (Test and Productive) cache objects.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCacheDeleteTest() throws ApiException {
        UUID clientId = null;
        BoolResponse response = api.apiV10ConfigurationAtFonClientIdCacheDelete(clientId);
        // TODO: test validations
    }

    /**
     * Unregister cash register at fon service. You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCashregisterDeleteTest() throws ApiException {
        UUID clientId = null;
        String cashRegisterId = null;
        CashRegisterDropoutReason reason = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime requestDateTime = null;
        Boolean isTest = null;
        ResultResponse response = api.apiV10ConfigurationAtFonClientIdCashregisterDelete(clientId, cashRegisterId, reason, fromDate, requestDateTime, isTest);
        // TODO: test validations
    }

    /**
     * Checks the cash register state with the FON Service. You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCashregisterGetTest() throws ApiException {
        UUID clientId = null;
        String cashRegisterId = null;
        Boolean expectedResponse = null;
        Boolean isTest = null;
        BoolResponse response = api.apiV10ConfigurationAtFonClientIdCashregisterGet(clientId, cashRegisterId, expectedResponse, isTest);
        // TODO: test validations
    }

    /**
     * Register of cash register system. You have to be authenticated.
     *
     * Security patch: do not use longer query parameter aesKey. Use request body instead (You have to send the aeskeyBody with single quotes: &#39;{aesKey}&#39;).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCashregisterPostTest() throws ApiException {
        UUID clientId = null;
        String cashRegisterId = null;
        String aesKey = null;
        OffsetDateTime requestDateTime = null;
        Boolean isTest = null;
        String body = null;
        ResultResponse response = api.apiV10ConfigurationAtFonClientIdCashregisterPost(clientId, cashRegisterId, aesKey, requestDateTime, isTest, body);
        // TODO: test validations
    }

    /**
     * Recommissioning of cash register system
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCashregisterPutTest() throws ApiException {
        UUID clientId = null;
        String cashRegisterId = null;
        OffsetDateTime recommissionDate = null;
        OffsetDateTime requestDateTime = null;
        Boolean isTest = null;
        ResultResponse response = api.apiV10ConfigurationAtFonClientIdCashregisterPut(clientId, cashRegisterId, recommissionDate, requestDateTime, isTest);
        // TODO: test validations
    }

    /**
     * Unregister cash register at fon service. You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCashregisterSimpleDeleteTest() throws ApiException {
        UUID clientId = null;
        CashRegisterDropoutReason reason = null;
        OffsetDateTime requestDateTime = null;
        BoolResponse response = api.apiV10ConfigurationAtFonClientIdCashregisterSimpleDelete(clientId, reason, requestDateTime);
        // TODO: test validations
    }

    /**
     * Checks the cash register state with the FON Service. You have to be authenticated. The format for the cash registerid is storeNumber/terminalNumber.
     *
     * The expected result is true.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCashregisterSimpleGetTest() throws ApiException {
        UUID clientId = null;
        BoolResponse response = api.apiV10ConfigurationAtFonClientIdCashregisterSimpleGet(clientId);
        // TODO: test validations
    }

    /**
     * Unregister a security certificate at fon (Finanzonline). You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCertificateDeleteTest() throws ApiException {
        UUID clientId = null;
        String securityCertificate = null;
        SecurityCertificateDropoutReason reason = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime requestDateTime = null;
        Boolean isTest = null;
        String logMessage = null;
        ResultResponse response = api.apiV10ConfigurationAtFonClientIdCertificateDelete(clientId, securityCertificate, reason, fromDate, requestDateTime, isTest, logMessage);
        // TODO: test validations
    }

    /**
     * Returns true if the given security certificate is activated (IN_BETRIEB) at fon (Finanzonline). You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCertificateGetTest() throws ApiException {
        UUID clientId = null;
        String securityCertificate = null;
        Boolean expectedResponse = null;
        Boolean isTest = null;
        BoolResponse response = api.apiV10ConfigurationAtFonClientIdCertificateGet(clientId, securityCertificate, expectedResponse, isTest);
        // TODO: test validations
    }

    /**
     * Register a security certificate at fon (Finanzonline). You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCertificatePostTest() throws ApiException {
        UUID clientId = null;
        String securityCertificate = null;
        SecurityCertificateType certificateType = null;
        SecurityCertificateIssuer issuer = null;
        OffsetDateTime requestDateTime = null;
        Boolean isTest = null;
        ResultResponse response = api.apiV10ConfigurationAtFonClientIdCertificatePost(clientId, securityCertificate, certificateType, issuer, requestDateTime, isTest);
        // TODO: test validations
    }

    /**
     * Recommissioning a security certificate at fon (Finanzonline). You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdCertificatePutTest() throws ApiException {
        UUID clientId = null;
        String securityCertificate = null;
        OffsetDateTime recommissionDate = null;
        OffsetDateTime requestDateTime = null;
        Boolean isTest = null;
        String logMessage = null;
        ResultResponse response = api.apiV10ConfigurationAtFonClientIdCertificatePut(clientId, securityCertificate, recommissionDate, requestDateTime, isTest, logMessage);
        // TODO: test validations
    }

    /**
     * Does document validation on fon service. You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdDocumentValidateGetTest() throws ApiException {
        UUID clientId = null;
        String depDocument = null;
        String annualReceiptYear = null;
        Boolean isTest = null;
        BoolResponse response = api.apiV10ConfigurationAtFonClientIdDocumentValidateGet(clientId, depDocument, annualReceiptYear, isTest);
        // TODO: test validations
    }

    /**
     * Does document validation on fon service. You have to be authenticated.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdDocumentValidateLastGetTest() throws ApiException {
        UUID clientId = null;
        BoolResponse response = api.apiV10ConfigurationAtFonClientIdDocumentValidateLastGet(clientId);
        // TODO: test validations
    }

    /**
     * Returns whether the client has fon access (if the data is configured and license is exi
     *
     * In possibility of having no license no exception is raised, but appropiate message is returned.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonClientIdGetTest() throws ApiException {
        UUID clientId = null;
        BoolResponse response = api.apiV10ConfigurationAtFonClientIdGet(clientId);
        // TODO: test validations
    }

    /**
     * Returns the possible drop out reasons for cash register drop out.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtFonDropoutReasonsGetTest() throws ApiException {
        List<StringSimpleObject> response = api.apiV10ConfigurationAtFonDropoutReasonsGet();
        // TODO: test validations
    }

    /**
     * Tests configured hsm connections for the given client.
     *
     * In normal case only one connection is configured.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtHsmTerminalIdPatchTest() throws ApiException {
        UUID terminalId = null;
        Boolean response = api.apiV10ConfigurationAtHsmTerminalIdPatch(terminalId);
        // TODO: test validations
    }

    /**
     * Returns supported smart card drivers for configuration in the cloud user interface.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationAtSignDeviceDriverInfoGetTest() throws ApiException {
        List<SignDeviceDriverInfo> response = api.apiV10ConfigurationAtSignDeviceDriverInfoGet();
        // TODO: test validations
    }

}
