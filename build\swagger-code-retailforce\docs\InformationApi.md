# InformationApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10InformationDocumenttypesSimpleGet**](InformationApi.md#apiV10InformationDocumenttypesSimpleGet) | **GET** /api/v1.0/information/documenttypes/simple | Returns the available document types for fiscalisation and/or digital receipt. |
| [**apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet**](InformationApi.md#apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet) | **GET** /api/v1.0/information/fiscalCountryProperties/{fiscalCountry}/fiscalregions | Returns the fiscal regions for the given fiscal country. |
| [**apiV10InformationFiscalCountryPropertiesFiscalCountryGet**](InformationApi.md#apiV10InformationFiscalCountryPropertiesFiscalCountryGet) | **GET** /api/v1.0/information/fiscalCountryProperties/{fiscalCountry} | Returns fiscal country properties for the given country. |
| [**apiV10InformationFiscalCountryPropertiesGet**](InformationApi.md#apiV10InformationFiscalCountryPropertiesGet) | **GET** /api/v1.0/information/fiscalCountryProperties | Returns all fiscal countries properties for all available fiscal countries. |
| [**apiV10InformationHelpEnumTypeNameEnumValueGet**](InformationApi.md#apiV10InformationHelpEnumTypeNameEnumValueGet) | **GET** /api/v1.0/information/help/enum/{typeName}/{enumValue} | Returns type help information for the requested enum value. |
| [**apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet**](InformationApi.md#apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet) | **GET** /api/v1.0/information/help/fiscalCountryProperties/{fiscalCountry}/{documentType} | Returns the mapping of business transaction types for the given document type and fiscal country. |
| [**apiV10InformationHelpTypeNameGet**](InformationApi.md#apiV10InformationHelpTypeNameGet) | **GET** /api/v1.0/information/help/{typeName} | Returns help information for the given type. |
| [**apiV10InformationHelpTypeNamePropertyNameGet**](InformationApi.md#apiV10InformationHelpTypeNamePropertyNameGet) | **GET** /api/v1.0/information/help/{typeName}/{propertyName} | Returns the help information for the given type and property. |
| [**apiV10InformationMessageTerminalIdPost**](InformationApi.md#apiV10InformationMessageTerminalIdPost) | **POST** /api/v1.0/information/message/{terminalId} | Send a message to the contact of the given terminal. |
| [**apiV10InformationReleasesGet**](InformationApi.md#apiV10InformationReleasesGet) | **GET** /api/v1.0/information/releases | Returns releases stored in the system. |
| [**apiV10InformationTerminalIdVersionGet**](InformationApi.md#apiV10InformationTerminalIdVersionGet) | **GET** /api/v1.0/information/{terminalId}/version | Returns the actual version of the terminal (used sw version of client). |
| [**apiV10InformationTerminalIdVersionPut**](InformationApi.md#apiV10InformationTerminalIdVersionPut) | **PUT** /api/v1.0/information/{terminalId}/version | Sets the actual version of the terminal in the cloud. |
| [**apiV10InformationVersionGet**](InformationApi.md#apiV10InformationVersionGet) | **GET** /api/v1.0/information/version | Returns the actual version (SW) of the software. |


<a id="apiV10InformationDocumenttypesSimpleGet"></a>
# **apiV10InformationDocumenttypesSimpleGet**
> Int32SimpleObjectPageResultModel apiV10InformationDocumenttypesSimpleGet(organizationId)

Returns the available document types for fiscalisation and/or digital receipt.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | 
    try {
      Int32SimpleObjectPageResultModel result = apiInstance.apiV10InformationDocumenttypesSimpleGet(organizationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationDocumenttypesSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**|  | [optional] |

### Return type

[**Int32SimpleObjectPageResultModel**](Int32SimpleObjectPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet"></a>
# **apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet**
> List&lt;String&gt; apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet(fiscalCountry)

Returns the fiscal regions for the given fiscal country.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    FiscalCountry fiscalCountry = FiscalCountry.fromValue("[0] = Germany"); // FiscalCountry | The fiscal country to fetch the fiscal regions.
    try {
      List<String> result = apiInstance.apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet(fiscalCountry);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **fiscalCountry** | [**FiscalCountry**](.md)| The fiscal country to fetch the fiscal regions. | [enum: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark, [5] = Sweden, [6] = Slovenia, [7] = Poland, [8] = Serbia, [9] = Italy, [10] = Croatia, [12] = Romania, [13] = Slovakia, [15] = Lithuania, [16] = Hungary, [17] = Portugal, [18] = Spain, [23] = Canada, [28] = Saudi Arabia, [99999] = NoFiscalisation] |

### Return type

**List&lt;String&gt;**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10InformationFiscalCountryPropertiesFiscalCountryGet"></a>
# **apiV10InformationFiscalCountryPropertiesFiscalCountryGet**
> IFiscalCountryProperties apiV10InformationFiscalCountryPropertiesFiscalCountryGet(fiscalCountry)

Returns fiscal country properties for the given country.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    FiscalCountry fiscalCountry = FiscalCountry.fromValue("[0] = Germany"); // FiscalCountry | The fiscal country where the fiscal country properties are requested.
    try {
      IFiscalCountryProperties result = apiInstance.apiV10InformationFiscalCountryPropertiesFiscalCountryGet(fiscalCountry);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationFiscalCountryPropertiesFiscalCountryGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **fiscalCountry** | [**FiscalCountry**](.md)| The fiscal country where the fiscal country properties are requested. | [enum: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark, [5] = Sweden, [6] = Slovenia, [7] = Poland, [8] = Serbia, [9] = Italy, [10] = Croatia, [12] = Romania, [13] = Slovakia, [15] = Lithuania, [16] = Hungary, [17] = Portugal, [18] = Spain, [23] = Canada, [28] = Saudi Arabia, [99999] = NoFiscalisation] |

### Return type

[**IFiscalCountryProperties**](IFiscalCountryProperties.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10InformationFiscalCountryPropertiesGet"></a>
# **apiV10InformationFiscalCountryPropertiesGet**
> IFiscalCountryPropertiesPageResultModel apiV10InformationFiscalCountryPropertiesGet()

Returns all fiscal countries properties for all available fiscal countries.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    try {
      IFiscalCountryPropertiesPageResultModel result = apiInstance.apiV10InformationFiscalCountryPropertiesGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationFiscalCountryPropertiesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**IFiscalCountryPropertiesPageResultModel**](IFiscalCountryPropertiesPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10InformationHelpEnumTypeNameEnumValueGet"></a>
# **apiV10InformationHelpEnumTypeNameEnumValueGet**
> HelpInformation apiV10InformationHelpEnumTypeNameEnumValueGet(typeName, enumValue)

Returns type help information for the requested enum value.

Typename is without namespace information.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    String typeName = "typeName_example"; // String | The type name of the enum. Use it without namespace information.
    String enumValue = "enumValue_example"; // String | The name of the value where the help information is requested.
    try {
      HelpInformation result = apiInstance.apiV10InformationHelpEnumTypeNameEnumValueGet(typeName, enumValue);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationHelpEnumTypeNameEnumValueGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **typeName** | **String**| The type name of the enum. Use it without namespace information. | |
| **enumValue** | **String**| The name of the value where the help information is requested. | |

### Return type

[**HelpInformation**](HelpInformation.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | OK |  -  |
| **404** | Type not found. |  -  |
| **422** | Typename or enumValue was set to null or empty string. |  -  |

<a id="apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet"></a>
# **apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet**
> BusinessTransactionTypeDocumentTypeMapping apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet(fiscalCountry, documentType)

Returns the mapping of business transaction types for the given document type and fiscal country.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    FiscalCountry fiscalCountry = FiscalCountry.fromValue("[0] = Germany"); // FiscalCountry | The fiscal country where the mapping is requested.
    DocumentType documentType = DocumentType.fromValue("[0] = Receipt"); // DocumentType | The document type where the mapping is requested.
    try {
      BusinessTransactionTypeDocumentTypeMapping result = apiInstance.apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet(fiscalCountry, documentType);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **fiscalCountry** | [**FiscalCountry**](.md)| The fiscal country where the mapping is requested. | [enum: [0] = Germany, [1] = Austria, [2] = France, [3] = Bulgaria, [4] = Denmark, [5] = Sweden, [6] = Slovenia, [7] = Poland, [8] = Serbia, [9] = Italy, [10] = Croatia, [12] = Romania, [13] = Slovakia, [15] = Lithuania, [16] = Hungary, [17] = Portugal, [18] = Spain, [23] = Canada, [28] = Saudi Arabia, [99999] = NoFiscalisation] |
| **documentType** | [**DocumentType**](.md)| The document type where the mapping is requested. | [enum: [0] = Receipt, [1] = Invoice, [2] = DeliveryNote, [10] = PayOut, [11] = PayIn, [12] = ProformaInvoice, [13] = CustomerOrder, [14] = PreliminaryReceipt, [15] = PaymentReceipt, [80] = LongTermOrder, [90] = OpeningBalance, [98] = CashCheck, [99] = EndOfDay, [100] = Inventory, [101] = Purchase, [1000] = NullReceipt, [1001] = PrintingReceipt, [1100] = MiscellaneousNonFiscal] |

### Return type

[**BusinessTransactionTypeDocumentTypeMapping**](BusinessTransactionTypeDocumentTypeMapping.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | OK |  -  |
| **422** | Fiscal country properties not found. |  -  |

<a id="apiV10InformationHelpTypeNameGet"></a>
# **apiV10InformationHelpTypeNameGet**
> Map&lt;String, HelpInformation&gt; apiV10InformationHelpTypeNameGet(typeName)

Returns help information for the given type.

Typename is without namespace information.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    String typeName = "typeName_example"; // String | The type where the help information is requested. Use it without namespace information.
    try {
      Map<String, HelpInformation> result = apiInstance.apiV10InformationHelpTypeNameGet(typeName);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationHelpTypeNameGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **typeName** | **String**| The type where the help information is requested. Use it without namespace information. | |

### Return type

[**Map&lt;String, HelpInformation&gt;**](HelpInformation.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | OK |  -  |
| **404** | Type not found. |  -  |
| **422** | Typename was set to null or empty string. |  -  |

<a id="apiV10InformationHelpTypeNamePropertyNameGet"></a>
# **apiV10InformationHelpTypeNamePropertyNameGet**
> HelpInformation apiV10InformationHelpTypeNamePropertyNameGet(typeName, propertyName)

Returns the help information for the given type and property.

Typename is without namespace information.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    String typeName = "typeName_example"; // String | The type where the help information is requested. Use it without namespace information.
    String propertyName = "propertyName_example"; // String | The property name where the help information is requested.
    try {
      HelpInformation result = apiInstance.apiV10InformationHelpTypeNamePropertyNameGet(typeName, propertyName);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationHelpTypeNamePropertyNameGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **typeName** | **String**| The type where the help information is requested. Use it without namespace information. | |
| **propertyName** | **String**| The property name where the help information is requested. | |

### Return type

[**HelpInformation**](HelpInformation.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | OK |  -  |
| **404** | Type not found. |  -  |
| **422** | Typename or property name was set to null or empty string. |  -  |

<a id="apiV10InformationMessageTerminalIdPost"></a>
# **apiV10InformationMessageTerminalIdPost**
> Boolean apiV10InformationMessageTerminalIdPost(terminalId, contactType, messageApiModel)

Send a message to the contact of the given terminal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The id of the terminal where the message should be sent (the contact of the terminal).
    TerminalContactType contactType = TerminalContactType.fromValue("primaryContact"); // TerminalContactType | The type of the contact configured at organization level (primary or technical contact). If omitted primary contact is used; if technical contact is not configured, primary contact is used.
    MessageApiModel messageApiModel = new MessageApiModel(); // MessageApiModel | The message to send.
    try {
      Boolean result = apiInstance.apiV10InformationMessageTerminalIdPost(terminalId, contactType, messageApiModel);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationMessageTerminalIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The id of the terminal where the message should be sent (the contact of the terminal). | |
| **contactType** | [**TerminalContactType**](.md)| The type of the contact configured at organization level (primary or technical contact). If omitted primary contact is used; if technical contact is not configured, primary contact is used. | [optional] [enum: primaryContact, technicalContact] |
| **messageApiModel** | [**MessageApiModel**](MessageApiModel.md)| The message to send. | [optional] |

### Return type

**Boolean**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10InformationReleasesGet"></a>
# **apiV10InformationReleasesGet**
> List&lt;Release&gt; apiV10InformationReleasesGet(maxEntries, fromDate)

Returns releases stored in the system.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    Integer maxEntries = 56; // Integer | Maximum count of entries
    OffsetDateTime fromDate = OffsetDateTime.now(); // OffsetDateTime | Filter for release date after from date.
    try {
      List<Release> result = apiInstance.apiV10InformationReleasesGet(maxEntries, fromDate);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationReleasesGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **maxEntries** | **Integer**| Maximum count of entries | [optional] |
| **fromDate** | **OffsetDateTime**| Filter for release date after from date. | [optional] |

### Return type

[**List&lt;Release&gt;**](Release.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10InformationTerminalIdVersionGet"></a>
# **apiV10InformationTerminalIdVersionGet**
> String apiV10InformationTerminalIdVersionGet(terminalId)

Returns the actual version of the terminal (used sw version of client).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id where the sw version is requested.
    try {
      String result = apiInstance.apiV10InformationTerminalIdVersionGet(terminalId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationTerminalIdVersionGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id where the sw version is requested. | |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10InformationTerminalIdVersionPut"></a>
# **apiV10InformationTerminalIdVersionPut**
> apiV10InformationTerminalIdVersionPut(terminalId, clientVersion, upgradeTime)

Sets the actual version of the terminal in the cloud.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    UUID terminalId = UUID.randomUUID(); // UUID | The terminal id where the sw version should be set.
    String clientVersion = "clientVersion_example"; // String | The version which should be set.
    String upgradeTime = "upgradeTime_example"; // String | The time when the version upgrade was done. Format: !:RetailForce.Cloud.AzureBlob.Common.Constants.SERVICE_DATETIMEFORMATUTC.
    try {
      apiInstance.apiV10InformationTerminalIdVersionPut(terminalId, clientVersion, upgradeTime);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationTerminalIdVersionPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **terminalId** | **UUID**| The terminal id where the sw version should be set. | |
| **clientVersion** | **String**| The version which should be set. | [optional] |
| **upgradeTime** | **String**| The time when the version upgrade was done. Format: !:RetailForce.Cloud.AzureBlob.Common.Constants.SERVICE_DATETIMEFORMATUTC. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10InformationVersionGet"></a>
# **apiV10InformationVersionGet**
> String apiV10InformationVersionGet()

Returns the actual version (SW) of the software.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.InformationApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    InformationApi apiInstance = new InformationApi(defaultClient);
    try {
      String result = apiInstance.apiV10InformationVersionGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling InformationApi#apiV10InformationVersionGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

