/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for TseAnnouncementCreateResult
 */
public class TseAnnouncementCreateResultTest {
    private final TseAnnouncementCreateResult model = new TseAnnouncementCreateResult();

    /**
     * Model tests for TseAnnouncementCreateResult
     */
    @Test
    public void testTseAnnouncementCreateResult() {
        // TODO: test TseAnnouncementCreateResult
    }

    /**
     * Test the property 'successfulSubmissionCount'
     */
    @Test
    public void successfulSubmissionCountTest() {
        // TODO: test successfulSubmissionCount
    }

    /**
     * Test the property 'failedSubmissionCount'
     */
    @Test
    public void failedSubmissionCountTest() {
        // TODO: test failedSubmissionCount
    }

    /**
     * Test the property 'taxPayerValidationErrors'
     */
    @Test
    public void taxPayerValidationErrorsTest() {
        // TODO: test taxPayerValidationErrors
    }

    /**
     * Test the property 'submissionValidationErrors'
     */
    @Test
    public void submissionValidationErrorsTest() {
        // TODO: test submissionValidationErrors
    }

}
