/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.TseType;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * TseInformationOverview
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class TseInformationOverview {
  public static final String SERIALIZED_NAME_TSE_SERIAL_HEX = "tseSerialHex";
  @SerializedName(SERIALIZED_NAME_TSE_SERIAL_HEX)
  private String tseSerialHex;

  public static final String SERIALIZED_NAME_DISTRIBUTOR_ID = "distributorId";
  @SerializedName(SERIALIZED_NAME_DISTRIBUTOR_ID)
  private UUID distributorId;

  public static final String SERIALIZED_NAME_DISTRIBUTOR_NAME = "distributorName";
  @SerializedName(SERIALIZED_NAME_DISTRIBUTOR_NAME)
  private String distributorName;

  public static final String SERIALIZED_NAME_ORGANIZATION_NAME = "organizationName";
  @SerializedName(SERIALIZED_NAME_ORGANIZATION_NAME)
  private String organizationName;

  public static final String SERIALIZED_NAME_ORGANIZATION_ID = "organizationId";
  @SerializedName(SERIALIZED_NAME_ORGANIZATION_ID)
  private UUID organizationId;

  public static final String SERIALIZED_NAME_STORE_NAME = "storeName";
  @SerializedName(SERIALIZED_NAME_STORE_NAME)
  private String storeName;

  public static final String SERIALIZED_NAME_STORE_ID = "storeId";
  @SerializedName(SERIALIZED_NAME_STORE_ID)
  private UUID storeId;

  public static final String SERIALIZED_NAME_TERMINAL_NAME = "terminalName";
  @SerializedName(SERIALIZED_NAME_TERMINAL_NAME)
  private String terminalName;

  public static final String SERIALIZED_NAME_TERMINAL_ID = "terminalId";
  @SerializedName(SERIALIZED_NAME_TERMINAL_ID)
  private UUID terminalId;

  public static final String SERIALIZED_NAME_CERTIFICATE_EXPIRATION_DATE = "certificateExpirationDate";
  @SerializedName(SERIALIZED_NAME_CERTIFICATE_EXPIRATION_DATE)
  private OffsetDateTime certificateExpirationDate;

  public static final String SERIALIZED_NAME_CREATED_SIGNATURES = "createdSignatures";
  @SerializedName(SERIALIZED_NAME_CREATED_SIGNATURES)
  private Integer createdSignatures;

  public static final String SERIALIZED_NAME_BSI_CERTIFICATION_ID = "bsiCertificationId";
  @SerializedName(SERIALIZED_NAME_BSI_CERTIFICATION_ID)
  private String bsiCertificationId;

  public static final String SERIALIZED_NAME_TYPE = "type";
  @SerializedName(SERIALIZED_NAME_TYPE)
  private TseType type;

  public static final String SERIALIZED_NAME_INITIALIZATION_DATE = "initializationDate";
  @SerializedName(SERIALIZED_NAME_INITIALIZATION_DATE)
  private OffsetDateTime initializationDate;

  public TseInformationOverview() {
  }

  public TseInformationOverview tseSerialHex(String tseSerialHex) {
    
    this.tseSerialHex = tseSerialHex;
    return this;
  }

   /**
   * Get tseSerialHex
   * @return tseSerialHex
  **/
  @javax.annotation.Nullable
  public String getTseSerialHex() {
    return tseSerialHex;
  }


  public void setTseSerialHex(String tseSerialHex) {
    this.tseSerialHex = tseSerialHex;
  }


  public TseInformationOverview distributorId(UUID distributorId) {
    
    this.distributorId = distributorId;
    return this;
  }

   /**
   * Get distributorId
   * @return distributorId
  **/
  @javax.annotation.Nullable
  public UUID getDistributorId() {
    return distributorId;
  }


  public void setDistributorId(UUID distributorId) {
    this.distributorId = distributorId;
  }


  public TseInformationOverview distributorName(String distributorName) {
    
    this.distributorName = distributorName;
    return this;
  }

   /**
   * Get distributorName
   * @return distributorName
  **/
  @javax.annotation.Nullable
  public String getDistributorName() {
    return distributorName;
  }


  public void setDistributorName(String distributorName) {
    this.distributorName = distributorName;
  }


  public TseInformationOverview organizationName(String organizationName) {
    
    this.organizationName = organizationName;
    return this;
  }

   /**
   * Get organizationName
   * @return organizationName
  **/
  @javax.annotation.Nullable
  public String getOrganizationName() {
    return organizationName;
  }


  public void setOrganizationName(String organizationName) {
    this.organizationName = organizationName;
  }


  public TseInformationOverview organizationId(UUID organizationId) {
    
    this.organizationId = organizationId;
    return this;
  }

   /**
   * Get organizationId
   * @return organizationId
  **/
  @javax.annotation.Nullable
  public UUID getOrganizationId() {
    return organizationId;
  }


  public void setOrganizationId(UUID organizationId) {
    this.organizationId = organizationId;
  }


  public TseInformationOverview storeName(String storeName) {
    
    this.storeName = storeName;
    return this;
  }

   /**
   * Get storeName
   * @return storeName
  **/
  @javax.annotation.Nullable
  public String getStoreName() {
    return storeName;
  }


  public void setStoreName(String storeName) {
    this.storeName = storeName;
  }


  public TseInformationOverview storeId(UUID storeId) {
    
    this.storeId = storeId;
    return this;
  }

   /**
   * Get storeId
   * @return storeId
  **/
  @javax.annotation.Nullable
  public UUID getStoreId() {
    return storeId;
  }


  public void setStoreId(UUID storeId) {
    this.storeId = storeId;
  }


  public TseInformationOverview terminalName(String terminalName) {
    
    this.terminalName = terminalName;
    return this;
  }

   /**
   * Get terminalName
   * @return terminalName
  **/
  @javax.annotation.Nullable
  public String getTerminalName() {
    return terminalName;
  }


  public void setTerminalName(String terminalName) {
    this.terminalName = terminalName;
  }


  public TseInformationOverview terminalId(UUID terminalId) {
    
    this.terminalId = terminalId;
    return this;
  }

   /**
   * Get terminalId
   * @return terminalId
  **/
  @javax.annotation.Nullable
  public UUID getTerminalId() {
    return terminalId;
  }


  public void setTerminalId(UUID terminalId) {
    this.terminalId = terminalId;
  }


  public TseInformationOverview certificateExpirationDate(OffsetDateTime certificateExpirationDate) {
    
    this.certificateExpirationDate = certificateExpirationDate;
    return this;
  }

   /**
   * Get certificateExpirationDate
   * @return certificateExpirationDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getCertificateExpirationDate() {
    return certificateExpirationDate;
  }


  public void setCertificateExpirationDate(OffsetDateTime certificateExpirationDate) {
    this.certificateExpirationDate = certificateExpirationDate;
  }


  public TseInformationOverview createdSignatures(Integer createdSignatures) {
    
    this.createdSignatures = createdSignatures;
    return this;
  }

   /**
   * Get createdSignatures
   * @return createdSignatures
  **/
  @javax.annotation.Nullable
  public Integer getCreatedSignatures() {
    return createdSignatures;
  }


  public void setCreatedSignatures(Integer createdSignatures) {
    this.createdSignatures = createdSignatures;
  }


  public TseInformationOverview bsiCertificationId(String bsiCertificationId) {
    
    this.bsiCertificationId = bsiCertificationId;
    return this;
  }

   /**
   * Get bsiCertificationId
   * @return bsiCertificationId
  **/
  @javax.annotation.Nullable
  public String getBsiCertificationId() {
    return bsiCertificationId;
  }


  public void setBsiCertificationId(String bsiCertificationId) {
    this.bsiCertificationId = bsiCertificationId;
  }


  public TseInformationOverview type(TseType type) {
    
    this.type = type;
    return this;
  }

   /**
   * Get type
   * @return type
  **/
  @javax.annotation.Nullable
  public TseType getType() {
    return type;
  }


  public void setType(TseType type) {
    this.type = type;
  }


  public TseInformationOverview initializationDate(OffsetDateTime initializationDate) {
    
    this.initializationDate = initializationDate;
    return this;
  }

   /**
   * Get initializationDate
   * @return initializationDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getInitializationDate() {
    return initializationDate;
  }


  public void setInitializationDate(OffsetDateTime initializationDate) {
    this.initializationDate = initializationDate;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TseInformationOverview tseInformationOverview = (TseInformationOverview) o;
    return Objects.equals(this.tseSerialHex, tseInformationOverview.tseSerialHex) &&
        Objects.equals(this.distributorId, tseInformationOverview.distributorId) &&
        Objects.equals(this.distributorName, tseInformationOverview.distributorName) &&
        Objects.equals(this.organizationName, tseInformationOverview.organizationName) &&
        Objects.equals(this.organizationId, tseInformationOverview.organizationId) &&
        Objects.equals(this.storeName, tseInformationOverview.storeName) &&
        Objects.equals(this.storeId, tseInformationOverview.storeId) &&
        Objects.equals(this.terminalName, tseInformationOverview.terminalName) &&
        Objects.equals(this.terminalId, tseInformationOverview.terminalId) &&
        Objects.equals(this.certificateExpirationDate, tseInformationOverview.certificateExpirationDate) &&
        Objects.equals(this.createdSignatures, tseInformationOverview.createdSignatures) &&
        Objects.equals(this.bsiCertificationId, tseInformationOverview.bsiCertificationId) &&
        Objects.equals(this.type, tseInformationOverview.type) &&
        Objects.equals(this.initializationDate, tseInformationOverview.initializationDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tseSerialHex, distributorId, distributorName, organizationName, organizationId, storeName, storeId, terminalName, terminalId, certificateExpirationDate, createdSignatures, bsiCertificationId, type, initializationDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TseInformationOverview {\n");
    sb.append("    tseSerialHex: ").append(toIndentedString(tseSerialHex)).append("\n");
    sb.append("    distributorId: ").append(toIndentedString(distributorId)).append("\n");
    sb.append("    distributorName: ").append(toIndentedString(distributorName)).append("\n");
    sb.append("    organizationName: ").append(toIndentedString(organizationName)).append("\n");
    sb.append("    organizationId: ").append(toIndentedString(organizationId)).append("\n");
    sb.append("    storeName: ").append(toIndentedString(storeName)).append("\n");
    sb.append("    storeId: ").append(toIndentedString(storeId)).append("\n");
    sb.append("    terminalName: ").append(toIndentedString(terminalName)).append("\n");
    sb.append("    terminalId: ").append(toIndentedString(terminalId)).append("\n");
    sb.append("    certificateExpirationDate: ").append(toIndentedString(certificateExpirationDate)).append("\n");
    sb.append("    createdSignatures: ").append(toIndentedString(createdSignatures)).append("\n");
    sb.append("    bsiCertificationId: ").append(toIndentedString(bsiCertificationId)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    initializationDate: ").append(toIndentedString(initializationDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("tseSerialHex");
    openapiFields.add("distributorId");
    openapiFields.add("distributorName");
    openapiFields.add("organizationName");
    openapiFields.add("organizationId");
    openapiFields.add("storeName");
    openapiFields.add("storeId");
    openapiFields.add("terminalName");
    openapiFields.add("terminalId");
    openapiFields.add("certificateExpirationDate");
    openapiFields.add("createdSignatures");
    openapiFields.add("bsiCertificationId");
    openapiFields.add("type");
    openapiFields.add("initializationDate");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to TseInformationOverview
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!TseInformationOverview.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in TseInformationOverview is not found in the empty JSON string", TseInformationOverview.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!TseInformationOverview.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `TseInformationOverview` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("tseSerialHex") != null && !jsonObj.get("tseSerialHex").isJsonNull()) && !jsonObj.get("tseSerialHex").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `tseSerialHex` to be a primitive type in the JSON string but got `%s`", jsonObj.get("tseSerialHex").toString()));
      }
      if ((jsonObj.get("distributorId") != null && !jsonObj.get("distributorId").isJsonNull()) && !jsonObj.get("distributorId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `distributorId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("distributorId").toString()));
      }
      if ((jsonObj.get("distributorName") != null && !jsonObj.get("distributorName").isJsonNull()) && !jsonObj.get("distributorName").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `distributorName` to be a primitive type in the JSON string but got `%s`", jsonObj.get("distributorName").toString()));
      }
      if ((jsonObj.get("organizationName") != null && !jsonObj.get("organizationName").isJsonNull()) && !jsonObj.get("organizationName").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `organizationName` to be a primitive type in the JSON string but got `%s`", jsonObj.get("organizationName").toString()));
      }
      if ((jsonObj.get("organizationId") != null && !jsonObj.get("organizationId").isJsonNull()) && !jsonObj.get("organizationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `organizationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("organizationId").toString()));
      }
      if ((jsonObj.get("storeName") != null && !jsonObj.get("storeName").isJsonNull()) && !jsonObj.get("storeName").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeName` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeName").toString()));
      }
      if ((jsonObj.get("storeId") != null && !jsonObj.get("storeId").isJsonNull()) && !jsonObj.get("storeId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `storeId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("storeId").toString()));
      }
      if ((jsonObj.get("terminalName") != null && !jsonObj.get("terminalName").isJsonNull()) && !jsonObj.get("terminalName").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `terminalName` to be a primitive type in the JSON string but got `%s`", jsonObj.get("terminalName").toString()));
      }
      if ((jsonObj.get("terminalId") != null && !jsonObj.get("terminalId").isJsonNull()) && !jsonObj.get("terminalId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `terminalId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("terminalId").toString()));
      }
      if ((jsonObj.get("bsiCertificationId") != null && !jsonObj.get("bsiCertificationId").isJsonNull()) && !jsonObj.get("bsiCertificationId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `bsiCertificationId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("bsiCertificationId").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!TseInformationOverview.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'TseInformationOverview' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<TseInformationOverview> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(TseInformationOverview.class));

       return (TypeAdapter<T>) new TypeAdapter<TseInformationOverview>() {
           @Override
           public void write(JsonWriter out, TseInformationOverview value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public TseInformationOverview read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of TseInformationOverview given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of TseInformationOverview
  * @throws IOException if the JSON string is invalid with respect to TseInformationOverview
  */
  public static TseInformationOverview fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, TseInformationOverview.class);
  }

 /**
  * Convert an instance of TseInformationOverview to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

