/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.TseType;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for TseInformationOverview
 */
public class TseInformationOverviewTest {
    private final TseInformationOverview model = new TseInformationOverview();

    /**
     * Model tests for TseInformationOverview
     */
    @Test
    public void testTseInformationOverview() {
        // TODO: test TseInformationOverview
    }

    /**
     * Test the property 'tseSerialHex'
     */
    @Test
    public void tseSerialHexTest() {
        // TODO: test tseSerialHex
    }

    /**
     * Test the property 'distributorId'
     */
    @Test
    public void distributorIdTest() {
        // TODO: test distributorId
    }

    /**
     * Test the property 'distributorName'
     */
    @Test
    public void distributorNameTest() {
        // TODO: test distributorName
    }

    /**
     * Test the property 'organizationName'
     */
    @Test
    public void organizationNameTest() {
        // TODO: test organizationName
    }

    /**
     * Test the property 'organizationId'
     */
    @Test
    public void organizationIdTest() {
        // TODO: test organizationId
    }

    /**
     * Test the property 'storeName'
     */
    @Test
    public void storeNameTest() {
        // TODO: test storeName
    }

    /**
     * Test the property 'storeId'
     */
    @Test
    public void storeIdTest() {
        // TODO: test storeId
    }

    /**
     * Test the property 'terminalName'
     */
    @Test
    public void terminalNameTest() {
        // TODO: test terminalName
    }

    /**
     * Test the property 'terminalId'
     */
    @Test
    public void terminalIdTest() {
        // TODO: test terminalId
    }

    /**
     * Test the property 'certificateExpirationDate'
     */
    @Test
    public void certificateExpirationDateTest() {
        // TODO: test certificateExpirationDate
    }

    /**
     * Test the property 'createdSignatures'
     */
    @Test
    public void createdSignaturesTest() {
        // TODO: test createdSignatures
    }

    /**
     * Test the property 'bsiCertificationId'
     */
    @Test
    public void bsiCertificationIdTest() {
        // TODO: test bsiCertificationId
    }

    /**
     * Test the property 'type'
     */
    @Test
    public void typeTest() {
        // TODO: test type
    }

    /**
     * Test the property 'initializationDate'
     */
    @Test
    public void initializationDateTest() {
        // TODO: test initializationDate
    }

}
