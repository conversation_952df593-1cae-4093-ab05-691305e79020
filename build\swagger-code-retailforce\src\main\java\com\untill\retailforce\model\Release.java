/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Release information
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class Release {
  public static final String SERIALIZED_NAME_RELEASE_VERSION = "releaseVersion";
  @SerializedName(SERIALIZED_NAME_RELEASE_VERSION)
  private String releaseVersion;

  public static final String SERIALIZED_NAME_RELEASE_DATE = "releaseDate";
  @SerializedName(SERIALIZED_NAME_RELEASE_DATE)
  private OffsetDateTime releaseDate;

  public static final String SERIALIZED_NAME_RELEASE_NOTE_URL = "releaseNoteUrl";
  @SerializedName(SERIALIZED_NAME_RELEASE_NOTE_URL)
  private String releaseNoteUrl;

  public Release() {
  }

  public Release releaseVersion(String releaseVersion) {
    
    this.releaseVersion = releaseVersion;
    return this;
  }

   /**
   * 
   * @return releaseVersion
  **/
  @javax.annotation.Nullable
  public String getReleaseVersion() {
    return releaseVersion;
  }


  public void setReleaseVersion(String releaseVersion) {
    this.releaseVersion = releaseVersion;
  }


  public Release releaseDate(OffsetDateTime releaseDate) {
    
    this.releaseDate = releaseDate;
    return this;
  }

   /**
   * 
   * @return releaseDate
  **/
  @javax.annotation.Nullable
  public OffsetDateTime getReleaseDate() {
    return releaseDate;
  }


  public void setReleaseDate(OffsetDateTime releaseDate) {
    this.releaseDate = releaseDate;
  }


  public Release releaseNoteUrl(String releaseNoteUrl) {
    
    this.releaseNoteUrl = releaseNoteUrl;
    return this;
  }

   /**
   * Get releaseNoteUrl
   * @return releaseNoteUrl
  **/
  @javax.annotation.Nullable
  public String getReleaseNoteUrl() {
    return releaseNoteUrl;
  }


  public void setReleaseNoteUrl(String releaseNoteUrl) {
    this.releaseNoteUrl = releaseNoteUrl;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Release release = (Release) o;
    return Objects.equals(this.releaseVersion, release.releaseVersion) &&
        Objects.equals(this.releaseDate, release.releaseDate) &&
        Objects.equals(this.releaseNoteUrl, release.releaseNoteUrl);
  }

  @Override
  public int hashCode() {
    return Objects.hash(releaseVersion, releaseDate, releaseNoteUrl);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Release {\n");
    sb.append("    releaseVersion: ").append(toIndentedString(releaseVersion)).append("\n");
    sb.append("    releaseDate: ").append(toIndentedString(releaseDate)).append("\n");
    sb.append("    releaseNoteUrl: ").append(toIndentedString(releaseNoteUrl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("releaseVersion");
    openapiFields.add("releaseDate");
    openapiFields.add("releaseNoteUrl");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to Release
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!Release.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in Release is not found in the empty JSON string", Release.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!Release.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `Release` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("releaseVersion") != null && !jsonObj.get("releaseVersion").isJsonNull()) && !jsonObj.get("releaseVersion").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `releaseVersion` to be a primitive type in the JSON string but got `%s`", jsonObj.get("releaseVersion").toString()));
      }
      if ((jsonObj.get("releaseNoteUrl") != null && !jsonObj.get("releaseNoteUrl").isJsonNull()) && !jsonObj.get("releaseNoteUrl").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `releaseNoteUrl` to be a primitive type in the JSON string but got `%s`", jsonObj.get("releaseNoteUrl").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!Release.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'Release' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<Release> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(Release.class));

       return (TypeAdapter<T>) new TypeAdapter<Release>() {
           @Override
           public void write(JsonWriter out, Release value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public Release read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of Release given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of Release
  * @throws IOException if the JSON string is invalid with respect to Release
  */
  public static Release fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, Release.class);
  }

 /**
  * Convert an instance of Release to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

