/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MoveToCloudApi
 */
@Disabled
public class MoveToCloudApiTest {

    private final MoveToCloudApi api = new MoveToCloudApi();

    /**
     * Finalize the move from local to cloud for this terminal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MoveToCloudTerminalIdFinalizePutTest() throws ApiException {
        UUID terminalId = null;
        Boolean response = api.apiV10MoveToCloudTerminalIdFinalizePut(terminalId);
        // TODO: test validations
    }

    /**
     * Starts the move from local to cloud for this terminal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MoveToCloudTerminalIdStartPostTest() throws ApiException {
        UUID terminalId = null;
        Boolean response = api.apiV10MoveToCloudTerminalIdStartPost(terminalId);
        // TODO: test validations
    }

    /**
     * Get the state of the move 2 cloud task.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MoveToCloudTerminalIdStateGetTest() throws ApiException {
        UUID terminalId = null;
        String response = api.apiV10MoveToCloudTerminalIdStateGet(terminalId);
        // TODO: test validations
    }

}
