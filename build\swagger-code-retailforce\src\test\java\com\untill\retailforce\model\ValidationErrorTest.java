/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.ErrorLevel;
import java.io.IOException;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for ValidationError
 */
public class ValidationErrorTest {
    private final ValidationError model = new ValidationError();

    /**
     * Model tests for ValidationError
     */
    @Test
    public void testValidationError() {
        // TODO: test ValidationError
    }

    /**
     * Test the property 'errorLevel'
     */
    @Test
    public void errorLevelTest() {
        // TODO: test errorLevel
    }

    /**
     * Test the property 'errorText'
     */
    @Test
    public void errorTextTest() {
        // TODO: test errorText
    }

    /**
     * Test the property 'errorSource'
     */
    @Test
    public void errorSourceTest() {
        // TODO: test errorSource
    }

}
