/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for User
 */
public class UserTest {
    private final User model = new User();

    /**
     * Model tests for User
     */
    @Test
    public void testUser() {
        // TODO: test User
    }

    /**
     * Test the property 'id'
     */
    @Test
    public void idTest() {
        // TODO: test id
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'firstName'
     */
    @Test
    public void firstNameTest() {
        // TODO: test firstName
    }

    /**
     * Test the property 'lastName'
     */
    @Test
    public void lastNameTest() {
        // TODO: test lastName
    }

    /**
     * Test the property 'taxNumber'
     */
    @Test
    public void taxNumberTest() {
        // TODO: test taxNumber
    }

    /**
     * Test the property 'dateOfEntry'
     */
    @Test
    public void dateOfEntryTest() {
        // TODO: test dateOfEntry
    }

}
