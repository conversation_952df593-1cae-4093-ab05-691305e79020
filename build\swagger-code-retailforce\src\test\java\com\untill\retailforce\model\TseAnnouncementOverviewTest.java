/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.TseAnnouncementProgress;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for TseAnnouncementOverview
 */
public class TseAnnouncementOverviewTest {
    private final TseAnnouncementOverview model = new TseAnnouncementOverview();

    /**
     * Model tests for TseAnnouncementOverview
     */
    @Test
    public void testTseAnnouncementOverview() {
        // TODO: test TseAnnouncementOverview
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'tseAnnouncementId'
     */
    @Test
    public void tseAnnouncementIdTest() {
        // TODO: test tseAnnouncementId
    }

    /**
     * Test the property 'storeId'
     */
    @Test
    public void storeIdTest() {
        // TODO: test storeId
    }

    /**
     * Test the property 'processStart'
     */
    @Test
    public void processStartTest() {
        // TODO: test processStart
    }

    /**
     * Test the property 'errorText'
     */
    @Test
    public void errorTextTest() {
        // TODO: test errorText
    }

    /**
     * Test the property 'isTest'
     */
    @Test
    public void isTestTest() {
        // TODO: test isTest
    }

    /**
     * Test the property 'status'
     */
    @Test
    public void statusTest() {
        // TODO: test status
    }

}
