# SecurityApi

All URIs are relative to *http://localhost*

| Method | HTTP request | Description |
|------------- | ------------- | -------------|
| [**apiV10SecurityAccessEntityIdDelete**](SecurityApi.md#apiV10SecurityAccessEntityIdDelete) | **DELETE** /api/v1.0/security/access/{entityId} | Removes an principal access from an entity. |
| [**apiV10SecurityAccessEntityIdGet**](SecurityApi.md#apiV10SecurityAccessEntityIdGet) | **GET** /api/v1.0/security/access/{entityId} | Returns a list of principals which have access to the given entity. Entity must be of type organization or distributor. |
| [**apiV10SecurityApikeyEntityIdGet**](SecurityApi.md#apiV10SecurityApikeyEntityIdGet) | **GET** /api/v1.0/security/apikey/{entityId} | Returns all api keys for the given entity (organisation or distributor). The given entity must exist (organisation or distributor). |
| [**apiV10SecurityApikeyEntityIdPost**](SecurityApi.md#apiV10SecurityApikeyEntityIdPost) | **POST** /api/v1.0/security/apikey/{entityId} | Creates an api key for an organisation or a distributor. The given entity must exist (organisation or distributor). |
| [**apiV10SecurityAssignEntityTypeEntityIdPut**](SecurityApi.md#apiV10SecurityAssignEntityTypeEntityIdPut) | **PUT** /api/v1.0/security/assign/{entityType}/{entityId} | Self assign the current user to an entity, without the whole invitation process  this is only allowed for retail force users |
| [**apiV10SecurityInvitationEntityTypeEntityIdPost**](SecurityApi.md#apiV10SecurityInvitationEntityTypeEntityIdPost) | **POST** /api/v1.0/security/invitation/{entityType}/{entityId} | Creates a new invitation in the backend. |
| [**apiV10SecurityInvitationGet**](SecurityApi.md#apiV10SecurityInvitationGet) | **GET** /api/v1.0/security/invitation | Returns all open invitation which where sent for current organisation/distributor where authenticated principal has access. |
| [**apiV10SecurityInvitationInvitationIdAcceptPut**](SecurityApi.md#apiV10SecurityInvitationInvitationIdAcceptPut) | **PUT** /api/v1.0/security/invitation/{invitationId}/accept | Accepts an invitation. |
| [**apiV10SecurityInvitationInvitationIdDeclinePut**](SecurityApi.md#apiV10SecurityInvitationInvitationIdDeclinePut) | **PUT** /api/v1.0/security/invitation/{invitationId}/decline | Declines an invitation. |
| [**apiV10SecurityInvitationInvitationIdDelete**](SecurityApi.md#apiV10SecurityInvitationInvitationIdDelete) | **DELETE** /api/v1.0/security/invitation/{invitationId} | Deletes an existing invitation. If invitation does not exists, nothing happens. |
| [**apiV10SecurityInvitationInvitationIdInfoGet**](SecurityApi.md#apiV10SecurityInvitationInvitationIdInfoGet) | **GET** /api/v1.0/security/invitation/{invitationId}/info | Returns an information if the invitation exists, the email of the invited user and if the user already exists in the backend. |
| [**apiV10SecurityInvitationInvitationIdPut**](SecurityApi.md#apiV10SecurityInvitationInvitationIdPut) | **PUT** /api/v1.0/security/invitation/{invitationId} | Resends an existing invitation. |
| [**apiV10SecurityInvitationUserGet**](SecurityApi.md#apiV10SecurityInvitationUserGet) | **GET** /api/v1.0/security/invitation/user | Returns all open invitations for the authenticated principal. |
| [**apiV10SecurityPasswordResetLinkPost**](SecurityApi.md#apiV10SecurityPasswordResetLinkPost) | **POST** /api/v1.0/security/password/reset/link | Send / Resend password reset email |
| [**apiV10SecurityPasswordResetTokenHead**](SecurityApi.md#apiV10SecurityPasswordResetTokenHead) | **HEAD** /api/v1.0/security/password/reset/{token} | Send / Resend password reset email |
| [**apiV10SecurityPasswordResetTokenPost**](SecurityApi.md#apiV10SecurityPasswordResetTokenPost) | **POST** /api/v1.0/security/password/reset/{token} | Reset the password based on a reset password token |
| [**apiV10SecurityPermissionsDisplayGet**](SecurityApi.md#apiV10SecurityPermissionsDisplayGet) | **GET** /api/v1.0/security/permissions/display | Get display permissions |
| [**apiV10SecurityPermissionsGet**](SecurityApi.md#apiV10SecurityPermissionsGet) | **GET** /api/v1.0/security/permissions | Get entity permissions for requested type. |
| [**apiV10SecurityPrincipalGet**](SecurityApi.md#apiV10SecurityPrincipalGet) | **GET** /api/v1.0/security/principal | Returns the user information about the actual logged on user. |
| [**apiV10SecurityPrincipalLockPrincipalIdPut**](SecurityApi.md#apiV10SecurityPrincipalLockPrincipalIdPut) | **PUT** /api/v1.0/security/principal/lock/{principalId} | Possiblity to lock a principal. |
| [**apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet**](SecurityApi.md#apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet) | **GET** /api/v1.0/security/principal/organisation/{organisationId}/simple | Returns all principals which are available for this organization. |
| [**apiV10SecurityPrincipalPictureGet**](SecurityApi.md#apiV10SecurityPrincipalPictureGet) | **GET** /api/v1.0/security/principal/picture | Loads the user profile image as url or dataUrl (can be optimized to return multiple sizes at once with retina, but then it would be better to use a CDN than generating all of them) |
| [**apiV10SecurityPrincipalPicturePost**](SecurityApi.md#apiV10SecurityPrincipalPicturePost) | **POST** /api/v1.0/security/principal/picture | Return profile picture url or data url |
| [**apiV10SecurityPrincipalPost**](SecurityApi.md#apiV10SecurityPrincipalPost) | **POST** /api/v1.0/security/principal | Creates the principal with the given registration model and does authentication (refresh token cookie). |
| [**apiV10SecurityPrincipalPut**](SecurityApi.md#apiV10SecurityPrincipalPut) | **PUT** /api/v1.0/security/principal | Updates principal master data in the data store. |
| [**apiV10SecurityPrincipalUnlockPrincipalIdPut**](SecurityApi.md#apiV10SecurityPrincipalUnlockPrincipalIdPut) | **PUT** /api/v1.0/security/principal/unlock/{principalId} | Unlocks the given principal. |
| [**apiV10SecuritySecretPost**](SecurityApi.md#apiV10SecuritySecretPost) | **POST** /api/v1.0/security/secret | Changes the secret for the given authentication key. |


<a id="apiV10SecurityAccessEntityIdDelete"></a>
# **apiV10SecurityAccessEntityIdDelete**
> apiV10SecurityAccessEntityIdDelete(entityId, principalId)

Removes an principal access from an entity.

If principal access was inherited from parent distributor this method will fail.                To add a principal again to an entity you have to invite the principal again with the invitation.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | The id of the entity to remove the access of the principal.
    UUID principalId = UUID.randomUUID(); // UUID | The id of the principal to remove access.
    try {
      apiInstance.apiV10SecurityAccessEntityIdDelete(entityId, principalId);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityAccessEntityIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| The id of the entity to remove the access of the principal. | |
| **principalId** | **UUID**| The id of the principal to remove access. | [optional] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityAccessEntityIdGet"></a>
# **apiV10SecurityAccessEntityIdGet**
> EntitySecurityPageResultModel apiV10SecurityAccessEntityIdGet(entityId, pageOffset, pageSize)

Returns a list of principals which have access to the given entity. Entity must be of type organization or distributor.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | The entity for the request. Entity must be of type organization or distributor.
    Integer pageOffset = 56; // Integer | page number => if null we start with the first page (starting at 0).
    Integer pageSize = 56; // Integer | max number of items we expect in the result => if null there is no limit.
    try {
      EntitySecurityPageResultModel result = apiInstance.apiV10SecurityAccessEntityIdGet(entityId, pageOffset, pageSize);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityAccessEntityIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| The entity for the request. Entity must be of type organization or distributor. | |
| **pageOffset** | **Integer**| page number &#x3D;&gt; if null we start with the first page (starting at 0). | [optional] |
| **pageSize** | **Integer**| max number of items we expect in the result &#x3D;&gt; if null there is no limit. | [optional] |

### Return type

[**EntitySecurityPageResultModel**](EntitySecurityPageResultModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityApikeyEntityIdGet"></a>
# **apiV10SecurityApikeyEntityIdGet**
> List&lt;ApiKey&gt; apiV10SecurityApikeyEntityIdGet(entityId)

Returns all api keys for the given entity (organisation or distributor). The given entity must exist (organisation or distributor).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | The entity for which the api keys are requested. The given entity must exist (organisation or distributor).
    try {
      List<ApiKey> result = apiInstance.apiV10SecurityApikeyEntityIdGet(entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityApikeyEntityIdGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| The entity for which the api keys are requested. The given entity must exist (organisation or distributor). | |

### Return type

[**List&lt;ApiKey&gt;**](ApiKey.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityApikeyEntityIdPost"></a>
# **apiV10SecurityApikeyEntityIdPost**
> ApiKey apiV10SecurityApikeyEntityIdPost(entityId)

Creates an api key for an organisation or a distributor. The given entity must exist (organisation or distributor).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | The entity for which the api key is requested. The given entity must exist (organisation or distributor).
    try {
      ApiKey result = apiInstance.apiV10SecurityApikeyEntityIdPost(entityId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityApikeyEntityIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**| The entity for which the api key is requested. The given entity must exist (organisation or distributor). | |

### Return type

[**ApiKey**](ApiKey.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityAssignEntityTypeEntityIdPut"></a>
# **apiV10SecurityAssignEntityTypeEntityIdPut**
> apiV10SecurityAssignEntityTypeEntityIdPut(entityId, entityType)

Self assign the current user to an entity, without the whole invitation process  this is only allowed for retail force users

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID entityId = UUID.randomUUID(); // UUID | 
    EntityTypes entityType = EntityTypes.fromValue("organisation"); // EntityTypes | 
    try {
      apiInstance.apiV10SecurityAssignEntityTypeEntityIdPut(entityId, entityType);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityAssignEntityTypeEntityIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityId** | **UUID**|  | |
| **entityType** | [**EntityTypes**](.md)|  | [enum: organisation, distributor, distributorContract, configuration, invitation, import, company, store, terminal, profile, configurationLicense, userAssignment, supportPackage, receipt, billingDistributor, billingOrganisation, billingPricing, certificate, backupdata, entityParameter, releases, license, accessLicense, signatureDevice, upload, supplier, supplierContract, accessLicenseContract, supportTicket, timelog, tseInformation, clearingRun, tseAnnouncement] |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |
| **403** | Forbidden. |  -  |
| **422** | Thrown if parameter entityId or entityType is set to System.Guid.Empty. |  -  |

<a id="apiV10SecurityInvitationEntityTypeEntityIdPost"></a>
# **apiV10SecurityInvitationEntityTypeEntityIdPost**
> InvitationModel apiV10SecurityInvitationEntityTypeEntityIdPost(entityType, entityId, email)

Creates a new invitation in the backend.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    String entityType = "entityType_example"; // String | The type of the invitation. Must be Organisation|Distributor. Otherwise an exception is raised.
    UUID entityId = UUID.randomUUID(); // UUID | The entity for the invitation (Organisation or Distributor).
    String email = "email_example"; // String | The email of the person to invite.
    try {
      InvitationModel result = apiInstance.apiV10SecurityInvitationEntityTypeEntityIdPost(entityType, entityId, email);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityInvitationEntityTypeEntityIdPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **entityType** | **String**| The type of the invitation. Must be Organisation|Distributor. Otherwise an exception is raised. | |
| **entityId** | **UUID**| The entity for the invitation (Organisation or Distributor). | |
| **email** | **String**| The email of the person to invite. | |

### Return type

[**InvitationModel**](InvitationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |
| **403** | Entity access not allowed. |  -  |
| **404** | Entity was not found. |  -  |
| **406** | Invitation was already accepted (user exists in organisation/distributor). |  -  |
| **409** | Invitation was already sent for email/entity combination. |  -  |
| **422** | Thrown if parameter entityId is set to System.Guid.Empty or parameter email or parameter entityType is set to null or empty string or if parameter entityType is not of the following values: Organisation|Distributor. |  -  |

<a id="apiV10SecurityInvitationGet"></a>
# **apiV10SecurityInvitationGet**
> List&lt;InvitationModel&gt; apiV10SecurityInvitationGet()

Returns all open invitation which where sent for current organisation/distributor where authenticated principal has access.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    try {
      List<InvitationModel> result = apiInstance.apiV10SecurityInvitationGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityInvitationGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;InvitationModel&gt;**](InvitationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |

<a id="apiV10SecurityInvitationInvitationIdAcceptPut"></a>
# **apiV10SecurityInvitationInvitationIdAcceptPut**
> apiV10SecurityInvitationInvitationIdAcceptPut(invitationId)

Accepts an invitation.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID invitationId = UUID.randomUUID(); // UUID | The id of the invitation which will be accepted.
    try {
      apiInstance.apiV10SecurityInvitationInvitationIdAcceptPut(invitationId);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityInvitationInvitationIdAcceptPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **invitationId** | **UUID**| The id of the invitation which will be accepted. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |
| **404** | Entity was not found (already accepted or never created). |  -  |
| **410** | Invitation expired. |  -  |
| **422** | Thrown if parameter invitationId is set to System.Guid.Empty. |  -  |

<a id="apiV10SecurityInvitationInvitationIdDeclinePut"></a>
# **apiV10SecurityInvitationInvitationIdDeclinePut**
> apiV10SecurityInvitationInvitationIdDeclinePut(invitationId)

Declines an invitation.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID invitationId = UUID.randomUUID(); // UUID | The id of the invitation which will be declined.
    try {
      apiInstance.apiV10SecurityInvitationInvitationIdDeclinePut(invitationId);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityInvitationInvitationIdDeclinePut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **invitationId** | **UUID**| The id of the invitation which will be declined. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |
| **404** | Entity was not found (already accepted or never created). |  -  |
| **410** | Invitation expired. |  -  |
| **422** | Thrown if parameter invitationId is set to System.Guid.Empty. |  -  |

<a id="apiV10SecurityInvitationInvitationIdDelete"></a>
# **apiV10SecurityInvitationInvitationIdDelete**
> apiV10SecurityInvitationInvitationIdDelete(invitationId)

Deletes an existing invitation. If invitation does not exists, nothing happens.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID invitationId = UUID.randomUUID(); // UUID | The id of the invitation which should be deleted.
    try {
      apiInstance.apiV10SecurityInvitationInvitationIdDelete(invitationId);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityInvitationInvitationIdDelete");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **invitationId** | **UUID**| The id of the invitation which should be deleted. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |
| **403** | Forbidden. |  -  |
| **422** | Thrown if parameter invitationId is set to System.Guid.Empty. |  -  |

<a id="apiV10SecurityInvitationInvitationIdInfoGet"></a>
# **apiV10SecurityInvitationInvitationIdInfoGet**
> InvitationInfoModel apiV10SecurityInvitationInvitationIdInfoGet(invitationId)

Returns an information if the invitation exists, the email of the invited user and if the user already exists in the backend.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID invitationId = UUID.randomUUID(); // UUID | The id of the requested invitation.
    try {
      InvitationInfoModel result = apiInstance.apiV10SecurityInvitationInvitationIdInfoGet(invitationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityInvitationInvitationIdInfoGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **invitationId** | **UUID**| The id of the requested invitation. | |

### Return type

[**InvitationInfoModel**](InvitationInfoModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |
| **404** | Entity was not found (already accepted or never created). |  -  |
| **410** | Invitation expired. |  -  |
| **422** | Thrown if parameter invitationId is set to System.Guid.Empty. |  -  |

<a id="apiV10SecurityInvitationInvitationIdPut"></a>
# **apiV10SecurityInvitationInvitationIdPut**
> InvitationModel apiV10SecurityInvitationInvitationIdPut(invitationId)

Resends an existing invitation.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID invitationId = UUID.randomUUID(); // UUID | The invitation which should be sent again.
    try {
      InvitationModel result = apiInstance.apiV10SecurityInvitationInvitationIdPut(invitationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityInvitationInvitationIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **invitationId** | **UUID**| The invitation which should be sent again. | |

### Return type

[**InvitationModel**](InvitationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |
| **403** | Forbidden. |  -  |
| **404** | Not Found. |  -  |
| **422** | Thrown if parameter invitationId is set to System.Guid.Empty. |  -  |

<a id="apiV10SecurityInvitationUserGet"></a>
# **apiV10SecurityInvitationUserGet**
> List&lt;InvitationModel&gt; apiV10SecurityInvitationUserGet()

Returns all open invitations for the authenticated principal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    try {
      List<InvitationModel> result = apiInstance.apiV10SecurityInvitationUserGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityInvitationUserGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**List&lt;InvitationModel&gt;**](InvitationModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **401** | Not authorized. |  -  |

<a id="apiV10SecurityPasswordResetLinkPost"></a>
# **apiV10SecurityPasswordResetLinkPost**
> apiV10SecurityPasswordResetLinkPost(email)

Send / Resend password reset email

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    String email = "email_example"; // String | email address of the account where to reset the password
    try {
      apiInstance.apiV10SecurityPasswordResetLinkPost(email);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPasswordResetLinkPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **email** | **String**| email address of the account where to reset the password | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPasswordResetTokenHead"></a>
# **apiV10SecurityPasswordResetTokenHead**
> apiV10SecurityPasswordResetTokenHead(token)

Send / Resend password reset email

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    String token = "token_example"; // String | password reset token
    try {
      apiInstance.apiV10SecurityPasswordResetTokenHead(token);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPasswordResetTokenHead");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **token** | **String**| password reset token | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPasswordResetTokenPost"></a>
# **apiV10SecurityPasswordResetTokenPost**
> String apiV10SecurityPasswordResetTokenPost(token, email, newPassword)

Reset the password based on a reset password token

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    String token = "token_example"; // String | The token to change the secret.
    String email = "email_example"; // String | 
    String newPassword = "newPassword_example"; // String | 
    try {
      String result = apiInstance.apiV10SecurityPasswordResetTokenPost(token, email, newPassword);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPasswordResetTokenPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **token** | **String**| The token to change the secret. | |
| **email** | **String**|  | |
| **newPassword** | **String**|  | |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPermissionsDisplayGet"></a>
# **apiV10SecurityPermissionsDisplayGet**
> List&lt;EntityTypes&gt; apiV10SecurityPermissionsDisplayGet(organizationId)

Get display permissions

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the organization to switch.
    try {
      List<EntityTypes> result = apiInstance.apiV10SecurityPermissionsDisplayGet(organizationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPermissionsDisplayGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organizationId** | **UUID**| The id of the organization to switch. | [optional] |

### Return type

[**List&lt;EntityTypes&gt;**](EntityTypes.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPermissionsGet"></a>
# **apiV10SecurityPermissionsGet**
> List&lt;EntityPermissions&gt; apiV10SecurityPermissionsGet(type, entityId, organizationId, entityVersion)

Get entity permissions for requested type.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    EntityTypes type = EntityTypes.fromValue("organisation"); // EntityTypes | The type of the entity.
    String entityId = "entityId_example"; // String | The id of the entity where the permissions are requested.
    UUID organizationId = UUID.randomUUID(); // UUID | The id of the actual organization.
    String entityVersion = "entityVersion_example"; // String | load a specific version of an entity (version from as utc ticks as string for precision)
    try {
      List<EntityPermissions> result = apiInstance.apiV10SecurityPermissionsGet(type, entityId, organizationId, entityVersion);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPermissionsGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **type** | [**EntityTypes**](.md)| The type of the entity. | [enum: organisation, distributor, distributorContract, configuration, invitation, import, company, store, terminal, profile, configurationLicense, userAssignment, supportPackage, receipt, billingDistributor, billingOrganisation, billingPricing, certificate, backupdata, entityParameter, releases, license, accessLicense, signatureDevice, upload, supplier, supplierContract, accessLicenseContract, supportTicket, timelog, tseInformation, clearingRun, tseAnnouncement] |
| **entityId** | **String**| The id of the entity where the permissions are requested. | [optional] |
| **organizationId** | **UUID**| The id of the actual organization. | [optional] |
| **entityVersion** | **String**| load a specific version of an entity (version from as utc ticks as string for precision) | [optional] |

### Return type

[**List&lt;EntityPermissions&gt;**](EntityPermissions.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPrincipalGet"></a>
# **apiV10SecurityPrincipalGet**
> Principal apiV10SecurityPrincipalGet()

Returns the user information about the actual logged on user.

You must be authenticated to use this method.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    try {
      Principal result = apiInstance.apiV10SecurityPrincipalGet();
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPrincipalGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

[**Principal**](Principal.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPrincipalLockPrincipalIdPut"></a>
# **apiV10SecurityPrincipalLockPrincipalIdPut**
> apiV10SecurityPrincipalLockPrincipalIdPut(principalId)

Possiblity to lock a principal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID principalId = UUID.randomUUID(); // UUID | The id of the principal to lock.
    try {
      apiInstance.apiV10SecurityPrincipalLockPrincipalIdPut(principalId);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPrincipalLockPrincipalIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **principalId** | **UUID**| The id of the principal to lock. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet"></a>
# **apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet**
> List&lt;GuidSimpleObject&gt; apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet(organisationId)

Returns all principals which are available for this organization.

Returns only direct associated principals (not associated with distributor).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID organisationId = UUID.randomUUID(); // UUID | The id of the organization where the principals are requested.
    try {
      List<GuidSimpleObject> result = apiInstance.apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet(organisationId);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **organisationId** | **UUID**| The id of the organization where the principals are requested. | |

### Return type

[**List&lt;GuidSimpleObject&gt;**](GuidSimpleObject.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPrincipalPictureGet"></a>
# **apiV10SecurityPrincipalPictureGet**
> ProfilePictureModel apiV10SecurityPrincipalPictureGet(thumbnail)

Loads the user profile image as url or dataUrl (can be optimized to return multiple sizes at once with retina, but then it would be better to use a CDN than generating all of them)

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    Boolean thumbnail = true; // Boolean | thumbnail is not the original size, if necessary replace this flag with a size enum
    try {
      ProfilePictureModel result = apiInstance.apiV10SecurityPrincipalPictureGet(thumbnail);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPrincipalPictureGet");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **thumbnail** | **Boolean**| thumbnail is not the original size, if necessary replace this flag with a size enum | [optional] |

### Return type

[**ProfilePictureModel**](ProfilePictureModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPrincipalPicturePost"></a>
# **apiV10SecurityPrincipalPicturePost**
> ProfilePictureModel apiV10SecurityPrincipalPicturePost(_file)

Return profile picture url or data url

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    File _file = new File("/path/to/file"); // File | 
    try {
      ProfilePictureModel result = apiInstance.apiV10SecurityPrincipalPicturePost(_file);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPrincipalPicturePost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **_file** | **File**|  | [optional] |

### Return type

[**ProfilePictureModel**](ProfilePictureModel.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPrincipalPost"></a>
# **apiV10SecurityPrincipalPost**
> String apiV10SecurityPrincipalPost(registrationModel)

Creates the principal with the given registration model and does authentication (refresh token cookie).

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    RegistrationModel registrationModel = new RegistrationModel(); // RegistrationModel | The data for creating a principal.
    try {
      String result = apiInstance.apiV10SecurityPrincipalPost(registrationModel);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPrincipalPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **registrationModel** | [**RegistrationModel**](RegistrationModel.md)| The data for creating a principal. | [optional] |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |
| **400** | EMail does not correspond to invitation token. |  -  |
| **404** | Invitation token not found. |  -  |
| **409** | User already exists. |  -  |
| **422** | ArgumentException (model &#x3D; null, email is no email, password strength not acceptable |  -  |
| **500** | Claimprovider was not found. |  -  |

<a id="apiV10SecurityPrincipalPut"></a>
# **apiV10SecurityPrincipalPut**
> Principal apiV10SecurityPrincipalPut(principal)

Updates principal master data in the data store.

The principal must exist.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    Principal principal = new Principal(); // Principal | The new principal data.
    try {
      Principal result = apiInstance.apiV10SecurityPrincipalPut(principal);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPrincipalPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **principal** | [**Principal**](Principal.md)| The new principal data. | [optional] |

### Return type

[**Principal**](Principal.md)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: application/json-patch+json, application/json, text/json, application/*+json
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecurityPrincipalUnlockPrincipalIdPut"></a>
# **apiV10SecurityPrincipalUnlockPrincipalIdPut**
> apiV10SecurityPrincipalUnlockPrincipalIdPut(principalId)

Unlocks the given principal.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    UUID principalId = UUID.randomUUID(); // UUID | The id of the principal to unlock.
    try {
      apiInstance.apiV10SecurityPrincipalUnlockPrincipalIdPut(principalId);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecurityPrincipalUnlockPrincipalIdPut");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **principalId** | **UUID**| The id of the principal to unlock. | |

### Return type

null (empty response body)

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

<a id="apiV10SecuritySecretPost"></a>
# **apiV10SecuritySecretPost**
> String apiV10SecuritySecretPost(authenticationKey, oldSecret, newSecret)

Changes the secret for the given authentication key.

### Example
```java
// Import classes:
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.auth.*;
import com.untill.retailforce.models.*;
import com.untill.retailforce.api.SecurityApi;

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("http://localhost");
    
    // Configure API key authorization: Bearer
    ApiKeyAuth Bearer = (ApiKeyAuth) defaultClient.getAuthentication("Bearer");
    Bearer.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //Bearer.setApiKeyPrefix("Token");

    SecurityApi apiInstance = new SecurityApi(defaultClient);
    String authenticationKey = "authenticationKey_example"; // String | 
    String oldSecret = "oldSecret_example"; // String | 
    String newSecret = "newSecret_example"; // String | 
    try {
      String result = apiInstance.apiV10SecuritySecretPost(authenticationKey, oldSecret, newSecret);
      System.out.println(result);
    } catch (ApiException e) {
      System.err.println("Exception when calling SecurityApi#apiV10SecuritySecretPost");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
```

### Parameters

| Name | Type | Description  | Notes |
|------------- | ------------- | ------------- | -------------|
| **authenticationKey** | **String**|  | [optional] |
| **oldSecret** | **String**|  | [optional] |
| **newSecret** | **String**|  | [optional] |

### Return type

**String**

### Authorization

[Bearer](../README.md#Bearer)

### HTTP request headers

 - **Content-Type**: multipart/form-data
 - **Accept**: text/plain, application/json, text/json

### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
| **200** | Success |  -  |

