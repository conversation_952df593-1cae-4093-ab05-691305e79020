/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import java.io.File;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import com.untill.retailforce.model.Supplier;
import com.untill.retailforce.model.SupplierContract;
import com.untill.retailforce.model.SupplierContractModel;
import com.untill.retailforce.model.SupplierContractModelPageResultModel;
import com.untill.retailforce.model.SupplierModel;
import com.untill.retailforce.model.SupplierModelPageResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MasterDataSuppliersApi
 */
@Disabled
public class MasterDataSuppliersApiTest {

    private final MasterDataSuppliersApi api = new MasterDataSuppliersApi();

    /**
     * Returns a list of RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 objects representing all available supplier contract types.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersContractsTypeSimpleGetTest() throws ApiException {
        List<GuidSimpleObject> response = api.apiV10MasterdataSuppliersContractsTypeSimpleGet();
        // TODO: test validations
    }

    /**
     * Returns all Suppliers for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        SupplierModelPageResultModel response = api.apiV10MasterdataSuppliersGet(pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Creates a new supplier in the cloud store.
     *
     * If RetailForce.Cloud.Model.Supplier.SupplierId set to System.Guid.Empty, then the supplier id will be generated by the service.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersPostTest() throws ApiException {
        Supplier supplier = null;
        SupplierModel response = api.apiV10MasterdataSuppliersPost(supplier);
        // TODO: test validations
    }

    /**
     * Returns all Suppliers for the authenticated user as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSimpleGetTest() throws ApiException {
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        GuidSimpleObjectPageResultModel response = api.apiV10MasterdataSuppliersSimpleGet(pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Deletes a contract.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdContractContractIdDeleteTest() throws ApiException {
        UUID supplierId = null;
        UUID contractId = null;
        api.apiV10MasterdataSuppliersSupplierIdContractContractIdDelete(supplierId, contractId);
        // TODO: test validations
    }

    /**
     * Removes a contract pdf from an existing contract.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDeleteTest() throws ApiException {
        UUID supplierId = null;
        UUID contractId = null;
        api.apiV10MasterdataSuppliersSupplierIdContractContractIdPdfDelete(supplierId, contractId);
        // TODO: test validations
    }

    /**
     * Adds a contract pdf file to an existing contract.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPostTest() throws ApiException {
        UUID supplierId = null;
        UUID contractId = null;
        File pdf = null;
        api.apiV10MasterdataSuppliersSupplierIdContractContractIdPdfPost(supplierId, contractId, pdf);
        // TODO: test validations
    }

    /**
     * Updates the given contract of the supplier.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdContractContractIdPutTest() throws ApiException {
        UUID supplierId = null;
        UUID contractId = null;
        SupplierContract supplierContract = null;
        SupplierContractModel response = api.apiV10MasterdataSuppliersSupplierIdContractContractIdPut(supplierId, contractId, supplierContract);
        // TODO: test validations
    }

    /**
     * Creates a new contract for a supplier.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdContractPostTest() throws ApiException {
        UUID supplierId = null;
        SupplierContract supplierContract = null;
        SupplierContractModel response = api.apiV10MasterdataSuppliersSupplierIdContractPost(supplierId, supplierContract);
        // TODO: test validations
    }

    /**
     * Returns the requested contract.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdContractsContractIdGetTest() throws ApiException {
        UUID supplierId = null;
        UUID contractId = null;
        SupplierContractModel response = api.apiV10MasterdataSuppliersSupplierIdContractsContractIdGet(supplierId, contractId);
        // TODO: test validations
    }

    /**
     * Returns all contracts for a supplier.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdContractsGetTest() throws ApiException {
        UUID supplierId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        SupplierContractModelPageResultModel response = api.apiV10MasterdataSuppliersSupplierIdContractsGet(supplierId, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Deletes an supplier from cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdDeleteTest() throws ApiException {
        UUID supplierId = null;
        api.apiV10MasterdataSuppliersSupplierIdDelete(supplierId);
        // TODO: test validations
    }

    /**
     * Returns the supplier requested by the given id.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdGetTest() throws ApiException {
        UUID supplierId = null;
        SupplierModel response = api.apiV10MasterdataSuppliersSupplierIdGet(supplierId);
        // TODO: test validations
    }

    /**
     * Updates an supplier in the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdPutTest() throws ApiException {
        UUID supplierId = null;
        Supplier supplier = null;
        SupplierModel response = api.apiV10MasterdataSuppliersSupplierIdPut(supplierId, supplier);
        // TODO: test validations
    }

    /**
     * Returns a simple supplierRetailForce.Cloud.Model.Helper.SimpleObject&#x60;1.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataSuppliersSupplierIdSimpleGetTest() throws ApiException {
        UUID supplierId = null;
        GuidSimpleObject response = api.apiV10MasterdataSuppliersSupplierIdSimpleGet(supplierId);
        // TODO: test validations
    }

}
