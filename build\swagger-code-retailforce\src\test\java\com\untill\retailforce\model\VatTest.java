/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for Vat
 */
public class VatTest {
    private final Vat model = new Vat();

    /**
     * Model tests for Vat
     */
    @Test
    public void testVat() {
        // TODO: test Vat
    }

    /**
     * Test the property 'vatPercent'
     */
    @Test
    public void vatPercentTest() {
        // TODO: test vatPercent
    }

    /**
     * Test the property 'vatPercent2'
     */
    @Test
    public void vatPercent2Test() {
        // TODO: test vatPercent2
    }

    /**
     * Test the property 'vatPercents'
     */
    @Test
    public void vatPercentsTest() {
        // TODO: test vatPercents
    }

    /**
     * Test the property 'vatIdentification'
     */
    @Test
    public void vatIdentificationTest() {
        // TODO: test vatIdentification
    }

    /**
     * Test the property 'caption'
     */
    @Test
    public void captionTest() {
        // TODO: test caption
    }

    /**
     * Test the property 'captionEN'
     */
    @Test
    public void captionENTest() {
        // TODO: test captionEN
    }

    /**
     * Test the property 'skipVatPercentageValidation'
     */
    @Test
    public void skipVatPercentageValidationTest() {
        // TODO: test skipVatPercentageValidation
    }

    /**
     * Test the property 'validFrom'
     */
    @Test
    public void validFromTest() {
        // TODO: test validFrom
    }

    /**
     * Test the property 'validTo'
     */
    @Test
    public void validToTest() {
        // TODO: test validTo
    }

}
