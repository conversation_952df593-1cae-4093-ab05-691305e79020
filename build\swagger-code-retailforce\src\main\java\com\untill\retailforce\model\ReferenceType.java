/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.annotations.SerializedName;

import java.io.IOException;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

/**
 * Gets or Sets ReferenceType
 */
@JsonAdapter(ReferenceType.Adapter.class)
public enum ReferenceType {
  
  _0_CANCELLATION("[0] = Cancellation"),
  
  _1_PREDECESSOR("[1] = Predecessor"),
  
  _2_REPRINT("[2] = Reprint");

  private String value;

  ReferenceType(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  public static ReferenceType fromValue(String value) {
    for (ReferenceType b : ReferenceType.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }

  public static class Adapter extends TypeAdapter<ReferenceType> {
    @Override
    public void write(final JsonWriter jsonWriter, final ReferenceType enumeration) throws IOException {
      jsonWriter.value(enumeration.getValue());
    }

    @Override
    public ReferenceType read(final JsonReader jsonReader) throws IOException {
      String value = jsonReader.nextString();
      return ReferenceType.fromValue(value);
    }
  }
}

