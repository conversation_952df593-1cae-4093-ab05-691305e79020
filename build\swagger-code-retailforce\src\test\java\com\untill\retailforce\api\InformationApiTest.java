/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.BusinessTransactionTypeDocumentTypeMapping;
import com.untill.retailforce.model.DocumentType;
import com.untill.retailforce.model.FiscalCountry;
import com.untill.retailforce.model.HelpInformation;
import com.untill.retailforce.model.IFiscalCountryProperties;
import com.untill.retailforce.model.IFiscalCountryPropertiesPageResultModel;
import com.untill.retailforce.model.Int32SimpleObjectPageResultModel;
import com.untill.retailforce.model.MessageApiModel;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.Release;
import com.untill.retailforce.model.TerminalContactType;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for InformationApi
 */
@Disabled
public class InformationApiTest {

    private final InformationApi api = new InformationApi();

    /**
     * Returns the available document types for fiscalisation and/or digital receipt.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationDocumenttypesSimpleGetTest() throws ApiException {
        UUID organizationId = null;
        Int32SimpleObjectPageResultModel response = api.apiV10InformationDocumenttypesSimpleGet(organizationId);
        // TODO: test validations
    }

    /**
     * Returns the fiscal regions for the given fiscal country.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGetTest() throws ApiException {
        FiscalCountry fiscalCountry = null;
        List<String> response = api.apiV10InformationFiscalCountryPropertiesFiscalCountryFiscalregionsGet(fiscalCountry);
        // TODO: test validations
    }

    /**
     * Returns fiscal country properties for the given country.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationFiscalCountryPropertiesFiscalCountryGetTest() throws ApiException {
        FiscalCountry fiscalCountry = null;
        IFiscalCountryProperties response = api.apiV10InformationFiscalCountryPropertiesFiscalCountryGet(fiscalCountry);
        // TODO: test validations
    }

    /**
     * Returns all fiscal countries properties for all available fiscal countries.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationFiscalCountryPropertiesGetTest() throws ApiException {
        IFiscalCountryPropertiesPageResultModel response = api.apiV10InformationFiscalCountryPropertiesGet();
        // TODO: test validations
    }

    /**
     * Returns type help information for the requested enum value.
     *
     * Typename is without namespace information.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationHelpEnumTypeNameEnumValueGetTest() throws ApiException {
        String typeName = null;
        String enumValue = null;
        HelpInformation response = api.apiV10InformationHelpEnumTypeNameEnumValueGet(typeName, enumValue);
        // TODO: test validations
    }

    /**
     * Returns the mapping of business transaction types for the given document type and fiscal country.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGetTest() throws ApiException {
        FiscalCountry fiscalCountry = null;
        DocumentType documentType = null;
        BusinessTransactionTypeDocumentTypeMapping response = api.apiV10InformationHelpFiscalCountryPropertiesFiscalCountryDocumentTypeGet(fiscalCountry, documentType);
        // TODO: test validations
    }

    /**
     * Returns help information for the given type.
     *
     * Typename is without namespace information.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationHelpTypeNameGetTest() throws ApiException {
        String typeName = null;
        Map<String, HelpInformation> response = api.apiV10InformationHelpTypeNameGet(typeName);
        // TODO: test validations
    }

    /**
     * Returns the help information for the given type and property.
     *
     * Typename is without namespace information.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationHelpTypeNamePropertyNameGetTest() throws ApiException {
        String typeName = null;
        String propertyName = null;
        HelpInformation response = api.apiV10InformationHelpTypeNamePropertyNameGet(typeName, propertyName);
        // TODO: test validations
    }

    /**
     * Send a message to the contact of the given terminal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationMessageTerminalIdPostTest() throws ApiException {
        UUID terminalId = null;
        TerminalContactType contactType = null;
        MessageApiModel messageApiModel = null;
        Boolean response = api.apiV10InformationMessageTerminalIdPost(terminalId, contactType, messageApiModel);
        // TODO: test validations
    }

    /**
     * Returns releases stored in the system.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationReleasesGetTest() throws ApiException {
        Integer maxEntries = null;
        OffsetDateTime fromDate = null;
        List<Release> response = api.apiV10InformationReleasesGet(maxEntries, fromDate);
        // TODO: test validations
    }

    /**
     * Returns the actual version of the terminal (used sw version of client).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationTerminalIdVersionGetTest() throws ApiException {
        UUID terminalId = null;
        String response = api.apiV10InformationTerminalIdVersionGet(terminalId);
        // TODO: test validations
    }

    /**
     * Sets the actual version of the terminal in the cloud.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationTerminalIdVersionPutTest() throws ApiException {
        UUID terminalId = null;
        String clientVersion = null;
        String upgradeTime = null;
        api.apiV10InformationTerminalIdVersionPut(terminalId, clientVersion, upgradeTime);
        // TODO: test validations
    }

    /**
     * Returns the actual version (SW) of the software.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10InformationVersionGetTest() throws ApiException {
        String response = api.apiV10InformationVersionGet();
        // TODO: test validations
    }

}
