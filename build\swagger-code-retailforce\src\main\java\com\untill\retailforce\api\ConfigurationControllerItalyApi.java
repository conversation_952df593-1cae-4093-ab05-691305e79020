/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import java.io.File;
import com.untill.retailforce.model.PrinterDriverInfo;
import com.untill.retailforce.model.PrinterImageFile;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ConfigurationControllerItalyApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ConfigurationControllerItalyApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ConfigurationControllerItalyApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet
     * @param terminalId The terminalId of the configuration. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/it/clientConfiguration/{terminalId}/printerImages"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetValidateBeforeCall(UUID terminalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet(Async)");
        }

        return apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetCall(terminalId, _callback);

    }

    /**
     * Returns stored printer image ids
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @return List&lt;PrinterImageFile&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<PrinterImageFile> apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet(UUID terminalId) throws ApiException {
        ApiResponse<List<PrinterImageFile>> localVarResp = apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetWithHttpInfo(terminalId);
        return localVarResp.getData();
    }

    /**
     * Returns stored printer image ids
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @return ApiResponse&lt;List&lt;PrinterImageFile&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<PrinterImageFile>> apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetWithHttpInfo(UUID terminalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetValidateBeforeCall(terminalId, null);
        Type localVarReturnType = new TypeToken<List<PrinterImageFile>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns stored printer image ids (asynchronously)
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetAsync(UUID terminalId, final ApiCallback<List<PrinterImageFile>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetValidateBeforeCall(terminalId, _callback);
        Type localVarReturnType = new TypeToken<List<PrinterImageFile>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet
     * @param terminalId The terminalId of the configuration. (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetCall(UUID terminalId, Integer order, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/it/clientConfiguration/{terminalId}/printerImages/{order}"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()))
            .replace("{" + "order" + "}", localVarApiClient.escapeString(order.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetValidateBeforeCall(UUID terminalId, Integer order, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet(Async)");
        }

        // verify the required parameter 'order' is set
        if (order == null) {
            throw new ApiException("Missing the required parameter 'order' when calling apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet(Async)");
        }

        return apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetCall(terminalId, order, _callback);

    }

    /**
     * returns the requeste image
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @return File
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public File apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet(UUID terminalId, Integer order) throws ApiException {
        ApiResponse<File> localVarResp = apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetWithHttpInfo(terminalId, order);
        return localVarResp.getData();
    }

    /**
     * returns the requeste image
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @return ApiResponse&lt;File&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<File> apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetWithHttpInfo(UUID terminalId, Integer order) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetValidateBeforeCall(terminalId, order, null);
        Type localVarReturnType = new TypeToken<File>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * returns the requeste image (asynchronously)
     * 
     * @param terminalId The terminalId of the configuration. (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetAsync(UUID terminalId, Integer order, final ApiCallback<File> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetValidateBeforeCall(terminalId, order, _callback);
        Type localVarReturnType = new TypeToken<File>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationItConfigurationIdPrinterImagesGet
     * @param configurationId The id of the configuration (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesGetCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/it/{configurationId}/printerImages"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesGetValidateBeforeCall(UUID configurationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10ConfigurationItConfigurationIdPrinterImagesGet(Async)");
        }

        return apiV10ConfigurationItConfigurationIdPrinterImagesGetCall(configurationId, _callback);

    }

    /**
     * Returns stored printer image ids
     * 
     * @param configurationId The id of the configuration (required)
     * @return List&lt;Integer&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<Integer> apiV10ConfigurationItConfigurationIdPrinterImagesGet(UUID configurationId) throws ApiException {
        ApiResponse<List<Integer>> localVarResp = apiV10ConfigurationItConfigurationIdPrinterImagesGetWithHttpInfo(configurationId);
        return localVarResp.getData();
    }

    /**
     * Returns stored printer image ids
     * 
     * @param configurationId The id of the configuration (required)
     * @return ApiResponse&lt;List&lt;Integer&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<Integer>> apiV10ConfigurationItConfigurationIdPrinterImagesGetWithHttpInfo(UUID configurationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationItConfigurationIdPrinterImagesGetValidateBeforeCall(configurationId, null);
        Type localVarReturnType = new TypeToken<List<Integer>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns stored printer image ids (asynchronously)
     * 
     * @param configurationId The id of the configuration (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesGetAsync(UUID configurationId, final ApiCallback<List<Integer>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationItConfigurationIdPrinterImagesGetValidateBeforeCall(configurationId, _callback);
        Type localVarReturnType = new TypeToken<List<Integer>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteCall(UUID configurationId, Integer order, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/it/{configurationId}/printerImages/{order}"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()))
            .replace("{" + "order" + "}", localVarApiClient.escapeString(order.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteValidateBeforeCall(UUID configurationId, Integer order, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete(Async)");
        }

        // verify the required parameter 'order' is set
        if (order == null) {
            throw new ApiException("Missing the required parameter 'order' when calling apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete(Async)");
        }

        return apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteCall(configurationId, order, _callback);

    }

    /**
     * Delete printer image
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete(UUID configurationId, Integer order) throws ApiException {
        apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteWithHttpInfo(configurationId, order);
    }

    /**
     * Delete printer image
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteWithHttpInfo(UUID configurationId, Integer order) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteValidateBeforeCall(configurationId, order, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Delete printer image (asynchronously)
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteAsync(UUID configurationId, Integer order, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteValidateBeforeCall(configurationId, order, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetCall(UUID configurationId, Integer order, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/it/{configurationId}/printerImages/{order}"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()))
            .replace("{" + "order" + "}", localVarApiClient.escapeString(order.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetValidateBeforeCall(UUID configurationId, Integer order, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet(Async)");
        }

        // verify the required parameter 'order' is set
        if (order == null) {
            throw new ApiException("Missing the required parameter 'order' when calling apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet(Async)");
        }

        return apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetCall(configurationId, order, _callback);

    }

    /**
     * returns the requeste image
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @return File
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public File apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet(UUID configurationId, Integer order) throws ApiException {
        ApiResponse<File> localVarResp = apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetWithHttpInfo(configurationId, order);
        return localVarResp.getData();
    }

    /**
     * returns the requeste image
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @return ApiResponse&lt;File&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<File> apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetWithHttpInfo(UUID configurationId, Integer order) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetValidateBeforeCall(configurationId, order, null);
        Type localVarReturnType = new TypeToken<File>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * returns the requeste image (asynchronously)
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetAsync(UUID configurationId, Integer order, final ApiCallback<File> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetValidateBeforeCall(configurationId, order, _callback);
        Type localVarReturnType = new TypeToken<File>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _file  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostCall(UUID configurationId, Integer order, File _file, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/it/{configurationId}/printerImages/{order}"
            .replace("{" + "configurationId" + "}", localVarApiClient.escapeString(configurationId.toString()))
            .replace("{" + "order" + "}", localVarApiClient.escapeString(order.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (_file != null) {
            localVarFormParams.put("file", _file);
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostValidateBeforeCall(UUID configurationId, Integer order, File _file, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'configurationId' is set
        if (configurationId == null) {
            throw new ApiException("Missing the required parameter 'configurationId' when calling apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost(Async)");
        }

        // verify the required parameter 'order' is set
        if (order == null) {
            throw new ApiException("Missing the required parameter 'order' when calling apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost(Async)");
        }

        return apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostCall(configurationId, order, _file, _callback);

    }

    /**
     * Upload printer image
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _file  (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost(UUID configurationId, Integer order, File _file) throws ApiException {
        apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostWithHttpInfo(configurationId, order, _file);
    }

    /**
     * Upload printer image
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _file  (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostWithHttpInfo(UUID configurationId, Integer order, File _file) throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostValidateBeforeCall(configurationId, order, _file, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Upload printer image (asynchronously)
     * 
     * @param configurationId The id of the configuration (required)
     * @param order order of the requested image (only 0-9) are allowed (required)
     * @param _file  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostAsync(UUID configurationId, Integer order, File _file, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostValidateBeforeCall(configurationId, order, _file, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ConfigurationItPrinterDriverInfosGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItPrinterDriverInfosGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/configuration/it/printerDriverInfos";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ConfigurationItPrinterDriverInfosGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10ConfigurationItPrinterDriverInfosGetCall(_callback);

    }

    /**
     * Returns the printer driver infos.
     * 
     * @return List&lt;PrinterDriverInfo&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<PrinterDriverInfo> apiV10ConfigurationItPrinterDriverInfosGet() throws ApiException {
        ApiResponse<List<PrinterDriverInfo>> localVarResp = apiV10ConfigurationItPrinterDriverInfosGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns the printer driver infos.
     * 
     * @return ApiResponse&lt;List&lt;PrinterDriverInfo&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<PrinterDriverInfo>> apiV10ConfigurationItPrinterDriverInfosGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10ConfigurationItPrinterDriverInfosGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<PrinterDriverInfo>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the printer driver infos. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ConfigurationItPrinterDriverInfosGetAsync(final ApiCallback<List<PrinterDriverInfo>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ConfigurationItPrinterDriverInfosGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<PrinterDriverInfo>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
