/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import java.time.OffsetDateTime;
import com.untill.retailforce.model.ReceiptDataScrollResultModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ReceiptsApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ReceiptsApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ReceiptsApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ReceiptsSearchOrganizationIdGet
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param companyId The company id where the receipt have to searched. (optional)
     * @param storeId Filter value to filter result for stores. (optional)
     * @param terminalId Filter value to filter result for terminals. (optional)
     * @param dateFrom Filter value to filter result for book date (from). (optional)
     * @param dateTill Filter value to filter result for book date (till). (optional)
     * @param documentType Filter value to filter result for special document types. (optional)
     * @param amount Filter value to filter result for an amount (circa). range of 10 percentage will be added (optional)
     * @param partner Filter value to filter result for customer/partner data. (optional)
     * @param users Filter value to filter for one or more users (caption, id), delimiter &#39;,&#39; (optional)
     * @param items Filter value to filter for one or more items (itemId, itemcode), delimiter &#39;,&#39; (optional)
     * @param documentNumber Filter value to filter for the document number. (optional)
     * @param documentId Filter value to filter for the document id. (optional)
     * @param cancelled Filter value to filter for cancelled documents. (optional)
     * @param paymentCardNumberMasked Filter value to filter for one paymentcardnumber (Creditcard, Google/Apple/Samsung Pay). It is not allowed to send clear readable full card number! (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ReceiptsSearchOrganizationIdGetCall(UUID organizationId, UUID companyId, UUID storeId, UUID terminalId, OffsetDateTime dateFrom, OffsetDateTime dateTill, Integer documentType, String amount, String partner, String users, String items, String documentNumber, String documentId, Boolean cancelled, String paymentCardNumberMasked, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/receipts/search/{organizationId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (companyId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("companyId", companyId));
        }

        if (storeId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("storeId", storeId));
        }

        if (terminalId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("terminalId", terminalId));
        }

        if (dateFrom != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("dateFrom", dateFrom));
        }

        if (dateTill != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("dateTill", dateTill));
        }

        if (documentType != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("documentType", documentType));
        }

        if (amount != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("amount", amount));
        }

        if (partner != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("partner", partner));
        }

        if (users != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("users", users));
        }

        if (items != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("items", items));
        }

        if (documentNumber != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("documentNumber", documentNumber));
        }

        if (documentId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("documentId", documentId));
        }

        if (cancelled != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("cancelled", cancelled));
        }

        if (paymentCardNumberMasked != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("paymentCardNumberMasked", paymentCardNumberMasked));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ReceiptsSearchOrganizationIdGetValidateBeforeCall(UUID organizationId, UUID companyId, UUID storeId, UUID terminalId, OffsetDateTime dateFrom, OffsetDateTime dateTill, Integer documentType, String amount, String partner, String users, String items, String documentNumber, String documentId, Boolean cancelled, String paymentCardNumberMasked, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ReceiptsSearchOrganizationIdGet(Async)");
        }

        return apiV10ReceiptsSearchOrganizationIdGetCall(organizationId, companyId, storeId, terminalId, dateFrom, dateTill, documentType, amount, partner, users, items, documentNumber, documentId, cancelled, paymentCardNumberMasked, _callback);

    }

    /**
     * Method to search and display receipts from digital receipt store (digital receipt license necessary).
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param companyId The company id where the receipt have to searched. (optional)
     * @param storeId Filter value to filter result for stores. (optional)
     * @param terminalId Filter value to filter result for terminals. (optional)
     * @param dateFrom Filter value to filter result for book date (from). (optional)
     * @param dateTill Filter value to filter result for book date (till). (optional)
     * @param documentType Filter value to filter result for special document types. (optional)
     * @param amount Filter value to filter result for an amount (circa). range of 10 percentage will be added (optional)
     * @param partner Filter value to filter result for customer/partner data. (optional)
     * @param users Filter value to filter for one or more users (caption, id), delimiter &#39;,&#39; (optional)
     * @param items Filter value to filter for one or more items (itemId, itemcode), delimiter &#39;,&#39; (optional)
     * @param documentNumber Filter value to filter for the document number. (optional)
     * @param documentId Filter value to filter for the document id. (optional)
     * @param cancelled Filter value to filter for cancelled documents. (optional)
     * @param paymentCardNumberMasked Filter value to filter for one paymentcardnumber (Creditcard, Google/Apple/Samsung Pay). It is not allowed to send clear readable full card number! (optional)
     * @return ReceiptDataScrollResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ReceiptDataScrollResultModel apiV10ReceiptsSearchOrganizationIdGet(UUID organizationId, UUID companyId, UUID storeId, UUID terminalId, OffsetDateTime dateFrom, OffsetDateTime dateTill, Integer documentType, String amount, String partner, String users, String items, String documentNumber, String documentId, Boolean cancelled, String paymentCardNumberMasked) throws ApiException {
        ApiResponse<ReceiptDataScrollResultModel> localVarResp = apiV10ReceiptsSearchOrganizationIdGetWithHttpInfo(organizationId, companyId, storeId, terminalId, dateFrom, dateTill, documentType, amount, partner, users, items, documentNumber, documentId, cancelled, paymentCardNumberMasked);
        return localVarResp.getData();
    }

    /**
     * Method to search and display receipts from digital receipt store (digital receipt license necessary).
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param companyId The company id where the receipt have to searched. (optional)
     * @param storeId Filter value to filter result for stores. (optional)
     * @param terminalId Filter value to filter result for terminals. (optional)
     * @param dateFrom Filter value to filter result for book date (from). (optional)
     * @param dateTill Filter value to filter result for book date (till). (optional)
     * @param documentType Filter value to filter result for special document types. (optional)
     * @param amount Filter value to filter result for an amount (circa). range of 10 percentage will be added (optional)
     * @param partner Filter value to filter result for customer/partner data. (optional)
     * @param users Filter value to filter for one or more users (caption, id), delimiter &#39;,&#39; (optional)
     * @param items Filter value to filter for one or more items (itemId, itemcode), delimiter &#39;,&#39; (optional)
     * @param documentNumber Filter value to filter for the document number. (optional)
     * @param documentId Filter value to filter for the document id. (optional)
     * @param cancelled Filter value to filter for cancelled documents. (optional)
     * @param paymentCardNumberMasked Filter value to filter for one paymentcardnumber (Creditcard, Google/Apple/Samsung Pay). It is not allowed to send clear readable full card number! (optional)
     * @return ApiResponse&lt;ReceiptDataScrollResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ReceiptDataScrollResultModel> apiV10ReceiptsSearchOrganizationIdGetWithHttpInfo(UUID organizationId, UUID companyId, UUID storeId, UUID terminalId, OffsetDateTime dateFrom, OffsetDateTime dateTill, Integer documentType, String amount, String partner, String users, String items, String documentNumber, String documentId, Boolean cancelled, String paymentCardNumberMasked) throws ApiException {
        okhttp3.Call localVarCall = apiV10ReceiptsSearchOrganizationIdGetValidateBeforeCall(organizationId, companyId, storeId, terminalId, dateFrom, dateTill, documentType, amount, partner, users, items, documentNumber, documentId, cancelled, paymentCardNumberMasked, null);
        Type localVarReturnType = new TypeToken<ReceiptDataScrollResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Method to search and display receipts from digital receipt store (digital receipt license necessary). (asynchronously)
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param companyId The company id where the receipt have to searched. (optional)
     * @param storeId Filter value to filter result for stores. (optional)
     * @param terminalId Filter value to filter result for terminals. (optional)
     * @param dateFrom Filter value to filter result for book date (from). (optional)
     * @param dateTill Filter value to filter result for book date (till). (optional)
     * @param documentType Filter value to filter result for special document types. (optional)
     * @param amount Filter value to filter result for an amount (circa). range of 10 percentage will be added (optional)
     * @param partner Filter value to filter result for customer/partner data. (optional)
     * @param users Filter value to filter for one or more users (caption, id), delimiter &#39;,&#39; (optional)
     * @param items Filter value to filter for one or more items (itemId, itemcode), delimiter &#39;,&#39; (optional)
     * @param documentNumber Filter value to filter for the document number. (optional)
     * @param documentId Filter value to filter for the document id. (optional)
     * @param cancelled Filter value to filter for cancelled documents. (optional)
     * @param paymentCardNumberMasked Filter value to filter for one paymentcardnumber (Creditcard, Google/Apple/Samsung Pay). It is not allowed to send clear readable full card number! (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ReceiptsSearchOrganizationIdGetAsync(UUID organizationId, UUID companyId, UUID storeId, UUID terminalId, OffsetDateTime dateFrom, OffsetDateTime dateTill, Integer documentType, String amount, String partner, String users, String items, String documentNumber, String documentId, Boolean cancelled, String paymentCardNumberMasked, final ApiCallback<ReceiptDataScrollResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ReceiptsSearchOrganizationIdGetValidateBeforeCall(organizationId, companyId, storeId, terminalId, dateFrom, dateTill, documentType, amount, partner, users, items, documentNumber, documentId, cancelled, paymentCardNumberMasked, _callback);
        Type localVarReturnType = new TypeToken<ReceiptDataScrollResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ReceiptsSearchOrganizationIdPost
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param continuationToken  (optional)
     * @param query  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ReceiptsSearchOrganizationIdPostCall(UUID organizationId, String continuationToken, String query, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/receipts/search/{organizationId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (continuationToken != null) {
            localVarFormParams.put("continuationToken", continuationToken);
        }

        if (query != null) {
            localVarFormParams.put("query", query);
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ReceiptsSearchOrganizationIdPostValidateBeforeCall(UUID organizationId, String continuationToken, String query, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ReceiptsSearchOrganizationIdPost(Async)");
        }

        return apiV10ReceiptsSearchOrganizationIdPostCall(organizationId, continuationToken, query, _callback);

    }

    /**
     * Method to continue search by continuationToken.
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param continuationToken  (optional)
     * @param query  (optional)
     * @return ReceiptDataScrollResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ReceiptDataScrollResultModel apiV10ReceiptsSearchOrganizationIdPost(UUID organizationId, String continuationToken, String query) throws ApiException {
        ApiResponse<ReceiptDataScrollResultModel> localVarResp = apiV10ReceiptsSearchOrganizationIdPostWithHttpInfo(organizationId, continuationToken, query);
        return localVarResp.getData();
    }

    /**
     * Method to continue search by continuationToken.
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param continuationToken  (optional)
     * @param query  (optional)
     * @return ApiResponse&lt;ReceiptDataScrollResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ReceiptDataScrollResultModel> apiV10ReceiptsSearchOrganizationIdPostWithHttpInfo(UUID organizationId, String continuationToken, String query) throws ApiException {
        okhttp3.Call localVarCall = apiV10ReceiptsSearchOrganizationIdPostValidateBeforeCall(organizationId, continuationToken, query, null);
        Type localVarReturnType = new TypeToken<ReceiptDataScrollResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Method to continue search by continuationToken. (asynchronously)
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param continuationToken  (optional)
     * @param query  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ReceiptsSearchOrganizationIdPostAsync(UUID organizationId, String continuationToken, String query, final ApiCallback<ReceiptDataScrollResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ReceiptsSearchOrganizationIdPostValidateBeforeCall(organizationId, continuationToken, query, _callback);
        Type localVarReturnType = new TypeToken<ReceiptDataScrollResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ReceiptsSearchOrganizationIdProcessIdGet
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param processId The connecting process id. Must be sent by client. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ReceiptsSearchOrganizationIdProcessIdGetCall(UUID organizationId, String processId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/receipts/search/{organizationId}/{processId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()))
            .replace("{" + "processId" + "}", localVarApiClient.escapeString(processId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ReceiptsSearchOrganizationIdProcessIdGetValidateBeforeCall(UUID organizationId, String processId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10ReceiptsSearchOrganizationIdProcessIdGet(Async)");
        }

        // verify the required parameter 'processId' is set
        if (processId == null) {
            throw new ApiException("Missing the required parameter 'processId' when calling apiV10ReceiptsSearchOrganizationIdProcessIdGet(Async)");
        }

        return apiV10ReceiptsSearchOrganizationIdProcessIdGetCall(organizationId, processId, _callback);

    }

    /**
     * Method to search connected receipts (connected with processId, for instance to search for cancelled receipts and cancelling receipt).
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param processId The connecting process id. Must be sent by client. (required)
     * @return ReceiptDataScrollResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ReceiptDataScrollResultModel apiV10ReceiptsSearchOrganizationIdProcessIdGet(UUID organizationId, String processId) throws ApiException {
        ApiResponse<ReceiptDataScrollResultModel> localVarResp = apiV10ReceiptsSearchOrganizationIdProcessIdGetWithHttpInfo(organizationId, processId);
        return localVarResp.getData();
    }

    /**
     * Method to search connected receipts (connected with processId, for instance to search for cancelled receipts and cancelling receipt).
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param processId The connecting process id. Must be sent by client. (required)
     * @return ApiResponse&lt;ReceiptDataScrollResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ReceiptDataScrollResultModel> apiV10ReceiptsSearchOrganizationIdProcessIdGetWithHttpInfo(UUID organizationId, String processId) throws ApiException {
        okhttp3.Call localVarCall = apiV10ReceiptsSearchOrganizationIdProcessIdGetValidateBeforeCall(organizationId, processId, null);
        Type localVarReturnType = new TypeToken<ReceiptDataScrollResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Method to search connected receipts (connected with processId, for instance to search for cancelled receipts and cancelling receipt). (asynchronously)
     * 
     * @param organizationId The organization id where the receipt have to searched. (required)
     * @param processId The connecting process id. Must be sent by client. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ReceiptsSearchOrganizationIdProcessIdGetAsync(UUID organizationId, String processId, final ApiCallback<ReceiptDataScrollResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ReceiptsSearchOrganizationIdProcessIdGetValidateBeforeCall(organizationId, processId, _callback);
        Type localVarReturnType = new TypeToken<ReceiptDataScrollResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
