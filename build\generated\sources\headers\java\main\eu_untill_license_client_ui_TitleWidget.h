/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class eu_untill_license_client_ui_TitleWidget */

#ifndef _Included_eu_untill_license_client_ui_TitleWidget
#define _Included_eu_untill_license_client_ui_TitleWidget
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     eu_untill_license_client_ui_TitleWidget
 * Method:    getHostPageLocation
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_eu_untill_license_client_ui_TitleWidget_getHostPageLocation
  (JNIEnv *, jclass);

#ifdef __cplusplus
}
#endif
#endif
