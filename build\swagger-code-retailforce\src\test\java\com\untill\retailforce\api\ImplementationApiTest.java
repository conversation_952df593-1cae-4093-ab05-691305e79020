/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.AuditLogEntry;
import java.time.OffsetDateTime;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ImplementationApi
 */
@Disabled
public class ImplementationApiTest {

    private final ImplementationApi api = new ImplementationApi();

    /**
     * Returns audit log records from storage.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationTerminalIdAuditLogGetTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime fromDate = null;
        OffsetDateTime tillDate = null;
        Boolean orderDesc = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        List<AuditLogEntry> response = api.apiV10ImplementationTerminalIdAuditLogGet(terminalId, fromDate, tillDate, orderDesc, pageOffset, pageSize);
        // TODO: test validations
    }

    /**
     * Exports current client data from cloud archive.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ImplementationTerminalIdRestoreclientdataGetTest() throws ApiException {
        UUID terminalId = null;
        api.apiV10ImplementationTerminalIdRestoreclientdataGet(terminalId);
        // TODO: test validations
    }

}
