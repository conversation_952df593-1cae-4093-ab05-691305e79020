/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.CosmosDocument;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class DocumentApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public DocumentApi() {
        this(Configuration.getDefaultApiClient());
    }

    public DocumentApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10DocumentsDocumentOrganizationIdPost
     * @param organizationId The organization id of the organization to store this cosmosDocument. (required)
     * @param cosmosDocument The document / fiscal response to store to the cosmos database. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DocumentsDocumentOrganizationIdPostCall(UUID organizationId, CosmosDocument cosmosDocument, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = cosmosDocument;

        // create path and map variables
        String localVarPath = "/api/v1.0/documents/document/{organizationId}"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10DocumentsDocumentOrganizationIdPostValidateBeforeCall(UUID organizationId, CosmosDocument cosmosDocument, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10DocumentsDocumentOrganizationIdPost(Async)");
        }

        return apiV10DocumentsDocumentOrganizationIdPostCall(organizationId, cosmosDocument, _callback);

    }

    /**
     * Stores a document / fiscal response tuple as RetailForce.Fiscalisation.Model.Helper.CosmosDocument.
     * 
     * @param organizationId The organization id of the organization to store this cosmosDocument. (required)
     * @param cosmosDocument The document / fiscal response to store to the cosmos database. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10DocumentsDocumentOrganizationIdPost(UUID organizationId, CosmosDocument cosmosDocument) throws ApiException {
        apiV10DocumentsDocumentOrganizationIdPostWithHttpInfo(organizationId, cosmosDocument);
    }

    /**
     * Stores a document / fiscal response tuple as RetailForce.Fiscalisation.Model.Helper.CosmosDocument.
     * 
     * @param organizationId The organization id of the organization to store this cosmosDocument. (required)
     * @param cosmosDocument The document / fiscal response to store to the cosmos database. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10DocumentsDocumentOrganizationIdPostWithHttpInfo(UUID organizationId, CosmosDocument cosmosDocument) throws ApiException {
        okhttp3.Call localVarCall = apiV10DocumentsDocumentOrganizationIdPostValidateBeforeCall(organizationId, cosmosDocument, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Stores a document / fiscal response tuple as RetailForce.Fiscalisation.Model.Helper.CosmosDocument. (asynchronously)
     * 
     * @param organizationId The organization id of the organization to store this cosmosDocument. (required)
     * @param cosmosDocument The document / fiscal response to store to the cosmos database. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10DocumentsDocumentOrganizationIdPostAsync(UUID organizationId, CosmosDocument cosmosDocument, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10DocumentsDocumentOrganizationIdPostValidateBeforeCall(organizationId, cosmosDocument, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
}
