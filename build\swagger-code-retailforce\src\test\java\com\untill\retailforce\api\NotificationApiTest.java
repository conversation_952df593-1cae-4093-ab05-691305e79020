/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.NotificationResult;
import com.untill.retailforce.model.NotificationType;
import com.untill.retailforce.model.NotificationsInfo;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for NotificationApi
 */
@Disabled
public class NotificationApiTest {

    private final NotificationApi api = new NotificationApi();

    /**
     * Get all notifications for the authenticated user
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10NotificationsGetTest() throws ApiException {
        Long fromTimestamp = null;
        NotificationResult response = api.apiV10NotificationsGet(fromTimestamp);
        // TODO: test validations
    }

    /**
     * GetNotificationsInfo
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10NotificationsInfoGetTest() throws ApiException {
        NotificationsInfo response = api.apiV10NotificationsInfoGet();
        // TODO: test validations
    }

    /**
     * Delete Notification
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10NotificationsNotificationIdDeleteTest() throws ApiException {
        Long notificationId = null;
        NotificationType notificationType = null;
        api.apiV10NotificationsNotificationIdDelete(notificationId, notificationType);
        // TODO: test validations
    }

    /**
     * Mark notification as read
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10NotificationsNotificationIdReadPutTest() throws ApiException {
        Long notificationId = null;
        NotificationType notificationType = null;
        api.apiV10NotificationsNotificationIdReadPut(notificationId, notificationType);
        // TODO: test validations
    }

}
