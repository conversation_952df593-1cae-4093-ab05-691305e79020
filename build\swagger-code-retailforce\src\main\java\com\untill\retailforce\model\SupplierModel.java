/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * SupplierModel
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class SupplierModel {
  public static final String SERIALIZED_NAME_CAN_EDIT = "canEdit";
  @SerializedName(SERIALIZED_NAME_CAN_EDIT)
  private Boolean canEdit;

  public static final String SERIALIZED_NAME_CAN_DELETE = "canDelete";
  @SerializedName(SERIALIZED_NAME_CAN_DELETE)
  private Boolean canDelete;

  public static final String SERIALIZED_NAME_SUPPLIER_ID = "supplierId";
  @SerializedName(SERIALIZED_NAME_SUPPLIER_ID)
  private UUID supplierId;

  public static final String SERIALIZED_NAME_CAPTION = "caption";
  @SerializedName(SERIALIZED_NAME_CAPTION)
  private String caption;

  public static final String SERIALIZED_NAME_CAPTION2 = "caption2";
  @SerializedName(SERIALIZED_NAME_CAPTION2)
  private String caption2;

  public static final String SERIALIZED_NAME_WEB_URL = "webUrl";
  @SerializedName(SERIALIZED_NAME_WEB_URL)
  private String webUrl;

  public static final String SERIALIZED_NAME_VAT_NUMBER = "vatNumber";
  @SerializedName(SERIALIZED_NAME_VAT_NUMBER)
  private String vatNumber;

  public static final String SERIALIZED_NAME_E_MAIL = "eMail";
  @SerializedName(SERIALIZED_NAME_E_MAIL)
  private String eMail;

  public static final String SERIALIZED_NAME_STREET = "street";
  @SerializedName(SERIALIZED_NAME_STREET)
  private String street;

  public static final String SERIALIZED_NAME_STREET_NUMBER = "streetNumber";
  @SerializedName(SERIALIZED_NAME_STREET_NUMBER)
  private String streetNumber;

  public static final String SERIALIZED_NAME_POSTAL_CODE = "postalCode";
  @SerializedName(SERIALIZED_NAME_POSTAL_CODE)
  private String postalCode;

  public static final String SERIALIZED_NAME_CITY = "city";
  @SerializedName(SERIALIZED_NAME_CITY)
  private String city;

  public static final String SERIALIZED_NAME_COMMUNITY = "community";
  @SerializedName(SERIALIZED_NAME_COMMUNITY)
  private String community;

  public static final String SERIALIZED_NAME_COUNTRY_CODE = "countryCode";
  @SerializedName(SERIALIZED_NAME_COUNTRY_CODE)
  private String countryCode;

  public SupplierModel() {
  }

  public SupplierModel canEdit(Boolean canEdit) {
    
    this.canEdit = canEdit;
    return this;
  }

   /**
   * True if the supplier can be edited. Otherwise false.
   * @return canEdit
  **/
  @javax.annotation.Nullable
  public Boolean getCanEdit() {
    return canEdit;
  }


  public void setCanEdit(Boolean canEdit) {
    this.canEdit = canEdit;
  }


  public SupplierModel canDelete(Boolean canDelete) {
    
    this.canDelete = canDelete;
    return this;
  }

   /**
   * True if the supplier can be deleted. Otherwise false.
   * @return canDelete
  **/
  @javax.annotation.Nullable
  public Boolean getCanDelete() {
    return canDelete;
  }


  public void setCanDelete(Boolean canDelete) {
    this.canDelete = canDelete;
  }


  public SupplierModel supplierId(UUID supplierId) {
    
    this.supplierId = supplierId;
    return this;
  }

   /**
   * The id of the supplier
   * @return supplierId
  **/
  @javax.annotation.Nullable
  public UUID getSupplierId() {
    return supplierId;
  }


  public void setSupplierId(UUID supplierId) {
    this.supplierId = supplierId;
  }


  public SupplierModel caption(String caption) {
    
    this.caption = caption;
    return this;
  }

   /**
   * The caption of the supplier.
   * @return caption
  **/
  @javax.annotation.Nullable
  public String getCaption() {
    return caption;
  }


  public void setCaption(String caption) {
    this.caption = caption;
  }


  public SupplierModel caption2(String caption2) {
    
    this.caption2 = caption2;
    return this;
  }

   /**
   * The second caption of the supplier.
   * @return caption2
  **/
  @javax.annotation.Nullable
  public String getCaption2() {
    return caption2;
  }


  public void setCaption2(String caption2) {
    this.caption2 = caption2;
  }


  public SupplierModel webUrl(String webUrl) {
    
    this.webUrl = webUrl;
    return this;
  }

   /**
   * The url of the supplier.
   * @return webUrl
  **/
  @javax.annotation.Nullable
  public String getWebUrl() {
    return webUrl;
  }


  public void setWebUrl(String webUrl) {
    this.webUrl = webUrl;
  }


  public SupplierModel vatNumber(String vatNumber) {
    
    this.vatNumber = vatNumber;
    return this;
  }

   /**
   * Vat number
   * @return vatNumber
  **/
  @javax.annotation.Nullable
  public String getVatNumber() {
    return vatNumber;
  }


  public void setVatNumber(String vatNumber) {
    this.vatNumber = vatNumber;
  }


  public SupplierModel eMail(String eMail) {
    
    this.eMail = eMail;
    return this;
  }

   /**
   * The email of the supplier.
   * @return eMail
  **/
  @javax.annotation.Nullable
  public String geteMail() {
    return eMail;
  }


  public void seteMail(String eMail) {
    this.eMail = eMail;
  }


  public SupplierModel street(String street) {
    
    this.street = street;
    return this;
  }

   /**
   * Get street
   * @return street
  **/
  @javax.annotation.Nonnull
  public String getStreet() {
    return street;
  }


  public void setStreet(String street) {
    this.street = street;
  }


  public SupplierModel streetNumber(String streetNumber) {
    
    this.streetNumber = streetNumber;
    return this;
  }

   /**
   * Get streetNumber
   * @return streetNumber
  **/
  @javax.annotation.Nonnull
  public String getStreetNumber() {
    return streetNumber;
  }


  public void setStreetNumber(String streetNumber) {
    this.streetNumber = streetNumber;
  }


  public SupplierModel postalCode(String postalCode) {
    
    this.postalCode = postalCode;
    return this;
  }

   /**
   * Get postalCode
   * @return postalCode
  **/
  @javax.annotation.Nonnull
  public String getPostalCode() {
    return postalCode;
  }


  public void setPostalCode(String postalCode) {
    this.postalCode = postalCode;
  }


  public SupplierModel city(String city) {
    
    this.city = city;
    return this;
  }

   /**
   * Get city
   * @return city
  **/
  @javax.annotation.Nonnull
  public String getCity() {
    return city;
  }


  public void setCity(String city) {
    this.city = city;
  }


  public SupplierModel community(String community) {
    
    this.community = community;
    return this;
  }

   /**
   * Get community
   * @return community
  **/
  @javax.annotation.Nullable
  public String getCommunity() {
    return community;
  }


  public void setCommunity(String community) {
    this.community = community;
  }


  public SupplierModel countryCode(String countryCode) {
    
    this.countryCode = countryCode;
    return this;
  }

   /**
   * Get countryCode
   * @return countryCode
  **/
  @javax.annotation.Nonnull
  public String getCountryCode() {
    return countryCode;
  }


  public void setCountryCode(String countryCode) {
    this.countryCode = countryCode;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SupplierModel supplierModel = (SupplierModel) o;
    return Objects.equals(this.canEdit, supplierModel.canEdit) &&
        Objects.equals(this.canDelete, supplierModel.canDelete) &&
        Objects.equals(this.supplierId, supplierModel.supplierId) &&
        Objects.equals(this.caption, supplierModel.caption) &&
        Objects.equals(this.caption2, supplierModel.caption2) &&
        Objects.equals(this.webUrl, supplierModel.webUrl) &&
        Objects.equals(this.vatNumber, supplierModel.vatNumber) &&
        Objects.equals(this.eMail, supplierModel.eMail) &&
        Objects.equals(this.street, supplierModel.street) &&
        Objects.equals(this.streetNumber, supplierModel.streetNumber) &&
        Objects.equals(this.postalCode, supplierModel.postalCode) &&
        Objects.equals(this.city, supplierModel.city) &&
        Objects.equals(this.community, supplierModel.community) &&
        Objects.equals(this.countryCode, supplierModel.countryCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(canEdit, canDelete, supplierId, caption, caption2, webUrl, vatNumber, eMail, street, streetNumber, postalCode, city, community, countryCode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SupplierModel {\n");
    sb.append("    canEdit: ").append(toIndentedString(canEdit)).append("\n");
    sb.append("    canDelete: ").append(toIndentedString(canDelete)).append("\n");
    sb.append("    supplierId: ").append(toIndentedString(supplierId)).append("\n");
    sb.append("    caption: ").append(toIndentedString(caption)).append("\n");
    sb.append("    caption2: ").append(toIndentedString(caption2)).append("\n");
    sb.append("    webUrl: ").append(toIndentedString(webUrl)).append("\n");
    sb.append("    vatNumber: ").append(toIndentedString(vatNumber)).append("\n");
    sb.append("    eMail: ").append(toIndentedString(eMail)).append("\n");
    sb.append("    street: ").append(toIndentedString(street)).append("\n");
    sb.append("    streetNumber: ").append(toIndentedString(streetNumber)).append("\n");
    sb.append("    postalCode: ").append(toIndentedString(postalCode)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("    community: ").append(toIndentedString(community)).append("\n");
    sb.append("    countryCode: ").append(toIndentedString(countryCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("canEdit");
    openapiFields.add("canDelete");
    openapiFields.add("supplierId");
    openapiFields.add("caption");
    openapiFields.add("caption2");
    openapiFields.add("webUrl");
    openapiFields.add("vatNumber");
    openapiFields.add("eMail");
    openapiFields.add("street");
    openapiFields.add("streetNumber");
    openapiFields.add("postalCode");
    openapiFields.add("city");
    openapiFields.add("community");
    openapiFields.add("countryCode");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
    openapiRequiredFields.add("street");
    openapiRequiredFields.add("streetNumber");
    openapiRequiredFields.add("postalCode");
    openapiRequiredFields.add("city");
    openapiRequiredFields.add("countryCode");
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to SupplierModel
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!SupplierModel.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in SupplierModel is not found in the empty JSON string", SupplierModel.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!SupplierModel.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `SupplierModel` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }

      // check to make sure all required properties/fields are present in the JSON string
      for (String requiredField : SupplierModel.openapiRequiredFields) {
        if (jsonObj.get(requiredField) == null) {
          throw new IllegalArgumentException(String.format("The required field `%s` is not found in the JSON string: %s", requiredField, jsonObj.toString()));
        }
      }
      if ((jsonObj.get("supplierId") != null && !jsonObj.get("supplierId").isJsonNull()) && !jsonObj.get("supplierId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `supplierId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("supplierId").toString()));
      }
      if ((jsonObj.get("caption") != null && !jsonObj.get("caption").isJsonNull()) && !jsonObj.get("caption").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption").toString()));
      }
      if ((jsonObj.get("caption2") != null && !jsonObj.get("caption2").isJsonNull()) && !jsonObj.get("caption2").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `caption2` to be a primitive type in the JSON string but got `%s`", jsonObj.get("caption2").toString()));
      }
      if ((jsonObj.get("webUrl") != null && !jsonObj.get("webUrl").isJsonNull()) && !jsonObj.get("webUrl").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `webUrl` to be a primitive type in the JSON string but got `%s`", jsonObj.get("webUrl").toString()));
      }
      if ((jsonObj.get("vatNumber") != null && !jsonObj.get("vatNumber").isJsonNull()) && !jsonObj.get("vatNumber").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `vatNumber` to be a primitive type in the JSON string but got `%s`", jsonObj.get("vatNumber").toString()));
      }
      if ((jsonObj.get("eMail") != null && !jsonObj.get("eMail").isJsonNull()) && !jsonObj.get("eMail").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `eMail` to be a primitive type in the JSON string but got `%s`", jsonObj.get("eMail").toString()));
      }
      if (!jsonObj.get("street").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `street` to be a primitive type in the JSON string but got `%s`", jsonObj.get("street").toString()));
      }
      if (!jsonObj.get("streetNumber").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `streetNumber` to be a primitive type in the JSON string but got `%s`", jsonObj.get("streetNumber").toString()));
      }
      if (!jsonObj.get("postalCode").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `postalCode` to be a primitive type in the JSON string but got `%s`", jsonObj.get("postalCode").toString()));
      }
      if (!jsonObj.get("city").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `city` to be a primitive type in the JSON string but got `%s`", jsonObj.get("city").toString()));
      }
      if ((jsonObj.get("community") != null && !jsonObj.get("community").isJsonNull()) && !jsonObj.get("community").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `community` to be a primitive type in the JSON string but got `%s`", jsonObj.get("community").toString()));
      }
      if (!jsonObj.get("countryCode").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `countryCode` to be a primitive type in the JSON string but got `%s`", jsonObj.get("countryCode").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!SupplierModel.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'SupplierModel' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<SupplierModel> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(SupplierModel.class));

       return (TypeAdapter<T>) new TypeAdapter<SupplierModel>() {
           @Override
           public void write(JsonWriter out, SupplierModel value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public SupplierModel read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of SupplierModel given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of SupplierModel
  * @throws IOException if the JSON string is invalid with respect to SupplierModel
  */
  public static SupplierModel fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, SupplierModel.class);
  }

 /**
  * Convert an instance of SupplierModel to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

