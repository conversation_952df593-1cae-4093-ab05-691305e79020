/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.StringSimpleObject;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for StaticApi
 */
@Disabled
public class StaticApiTest {

    private final StaticApi api = new StaticApi();

    /**
     * Returns the file containing the content from the base64 string.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationBase64PostTest() throws ApiException {
        String body = null;
        api.apiV10ConfigurationBase64Post(body);
        // TODO: test validations
    }

    /**
     * Get Countries
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCountriesGetTest() throws ApiException {
        List<StringSimpleObject> response = api.apiV10ConfigurationCountriesGet();
        // TODO: test validations
    }

    /**
     * Returns the available culture infos.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationCultureInfosGetTest() throws ApiException {
        List<String> response = api.apiV10ConfigurationCultureInfosGet();
        // TODO: test validations
    }

}
