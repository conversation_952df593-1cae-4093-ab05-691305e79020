/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel;
import com.untill.retailforce.model.BillingLicenseCountBillingLicenseOverviewPageResultModel;
import com.untill.retailforce.model.BillingLicenseDetailPageResultModel;
import com.untill.retailforce.model.ClearingRun;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.OutstandingPayment;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class BillingApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public BillingApi() {
        this(Configuration.getDefaultApiClient());
    }

    public BillingApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10BillingClearingClearingIdGet
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of distributor). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdGetCall(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/clearing/{clearingId}"
            .replace("{" + "clearingId" + "}", localVarApiClient.escapeString(clearingId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingClearingClearingIdGetValidateBeforeCall(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clearingId' is set
        if (clearingId == null) {
            throw new ApiException("Missing the required parameter 'clearingId' when calling apiV10BillingClearingClearingIdGet(Async)");
        }

        return apiV10BillingClearingClearingIdGetCall(clearingId, distributorId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * Returns clearing license overview for distributor.
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of distributor). (optional)
     * @return BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingClearingClearingIdGet(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> localVarResp = apiV10BillingClearingClearingIdGetWithHttpInfo(clearingId, distributorId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * Returns clearing license overview for distributor.
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of distributor). (optional)
     * @return ApiResponse&lt;BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> apiV10BillingClearingClearingIdGetWithHttpInfo(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdGetValidateBeforeCall(clearingId, distributorId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns clearing license overview for distributor. (asynchronously)
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of distributor). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdGetAsync(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdGetValidateBeforeCall(clearingId, distributorId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingClearingClearingIdOrganizationGet
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null all organizations with authorization are fetched. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of organization). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdOrganizationGetCall(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/clearing/{clearingId}/organization"
            .replace("{" + "clearingId" + "}", localVarApiClient.escapeString(clearingId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingClearingClearingIdOrganizationGetValidateBeforeCall(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clearingId' is set
        if (clearingId == null) {
            throw new ApiException("Missing the required parameter 'clearingId' when calling apiV10BillingClearingClearingIdOrganizationGet(Async)");
        }

        return apiV10BillingClearingClearingIdOrganizationGetCall(clearingId, distributorId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * Returns clearing license overview for all organizations where the actual users is authorized.
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null all organizations with authorization are fetched. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of organization). (optional)
     * @return BillingLicenseCountBillingLicenseOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingClearingClearingIdOrganizationGet(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingLicenseCountBillingLicenseOverviewPageResultModel> localVarResp = apiV10BillingClearingClearingIdOrganizationGetWithHttpInfo(clearingId, distributorId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * Returns clearing license overview for all organizations where the actual users is authorized.
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null all organizations with authorization are fetched. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of organization). (optional)
     * @return ApiResponse&lt;BillingLicenseCountBillingLicenseOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingLicenseCountBillingLicenseOverviewPageResultModel> apiV10BillingClearingClearingIdOrganizationGetWithHttpInfo(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdOrganizationGetValidateBeforeCall(clearingId, distributorId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns clearing license overview for all organizations where the actual users is authorized. (asynchronously)
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null all organizations with authorization are fetched. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of organization). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdOrganizationGetAsync(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingLicenseCountBillingLicenseOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdOrganizationGetValidateBeforeCall(clearingId, distributorId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingClearingClearingIdOrganizationIdDetailGet
     * @param clearingId The Id of the clearing. (required)
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdOrganizationIdDetailGetCall(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/clearing/{clearingId}/{organizationId}/detail"
            .replace("{" + "clearingId" + "}", localVarApiClient.escapeString(clearingId.toString()))
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingClearingClearingIdOrganizationIdDetailGetValidateBeforeCall(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clearingId' is set
        if (clearingId == null) {
            throw new ApiException("Missing the required parameter 'clearingId' when calling apiV10BillingClearingClearingIdOrganizationIdDetailGet(Async)");
        }

        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10BillingClearingClearingIdOrganizationIdDetailGet(Async)");
        }

        return apiV10BillingClearingClearingIdOrganizationIdDetailGetCall(clearingId, organizationId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * Returns clearing license details for the requested organization.
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return BillingLicenseDetailPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingLicenseDetailPageResultModel apiV10BillingClearingClearingIdOrganizationIdDetailGet(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingLicenseDetailPageResultModel> localVarResp = apiV10BillingClearingClearingIdOrganizationIdDetailGetWithHttpInfo(clearingId, organizationId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * Returns clearing license details for the requested organization.
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return ApiResponse&lt;BillingLicenseDetailPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingLicenseDetailPageResultModel> apiV10BillingClearingClearingIdOrganizationIdDetailGetWithHttpInfo(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdOrganizationIdDetailGetValidateBeforeCall(clearingId, organizationId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingLicenseDetailPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns clearing license details for the requested organization. (asynchronously)
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdOrganizationIdDetailGetAsync(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingLicenseDetailPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdOrganizationIdDetailGetValidateBeforeCall(clearingId, organizationId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingLicenseDetailPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet
     * @param clearingId The Id of the clearing. (required)
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetCall(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/clearing/{clearingId}/{organizationId}/usage/detail"
            .replace("{" + "clearingId" + "}", localVarApiClient.escapeString(clearingId.toString()))
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetValidateBeforeCall(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clearingId' is set
        if (clearingId == null) {
            throw new ApiException("Missing the required parameter 'clearingId' when calling apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet(Async)");
        }

        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet(Async)");
        }

        return apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetCall(clearingId, organizationId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * returns the clearing detail usage for an organisation
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return BillingLicenseDetailPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingLicenseDetailPageResultModel apiV10BillingClearingClearingIdOrganizationIdUsageDetailGet(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingLicenseDetailPageResultModel> localVarResp = apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetWithHttpInfo(clearingId, organizationId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * returns the clearing detail usage for an organisation
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return ApiResponse&lt;BillingLicenseDetailPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingLicenseDetailPageResultModel> apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetWithHttpInfo(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetValidateBeforeCall(clearingId, organizationId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingLicenseDetailPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * returns the clearing detail usage for an organisation (asynchronously)
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetAsync(UUID clearingId, UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingLicenseDetailPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdOrganizationIdUsageDetailGetValidateBeforeCall(clearingId, organizationId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingLicenseDetailPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingClearingClearingIdOrganizationUsageGet
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdOrganizationUsageGetCall(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/clearing/{clearingId}/organization/usage"
            .replace("{" + "clearingId" + "}", localVarApiClient.escapeString(clearingId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingClearingClearingIdOrganizationUsageGetValidateBeforeCall(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clearingId' is set
        if (clearingId == null) {
            throw new ApiException("Missing the required parameter 'clearingId' when calling apiV10BillingClearingClearingIdOrganizationUsageGet(Async)");
        }

        return apiV10BillingClearingClearingIdOrganizationUsageGetCall(clearingId, distributorId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * Returns clearing license overview usage for all organizations where the actual users is authorized.
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return BillingLicenseCountBillingLicenseOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingClearingClearingIdOrganizationUsageGet(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingLicenseCountBillingLicenseOverviewPageResultModel> localVarResp = apiV10BillingClearingClearingIdOrganizationUsageGetWithHttpInfo(clearingId, distributorId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * Returns clearing license overview usage for all organizations where the actual users is authorized.
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return ApiResponse&lt;BillingLicenseCountBillingLicenseOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingLicenseCountBillingLicenseOverviewPageResultModel> apiV10BillingClearingClearingIdOrganizationUsageGetWithHttpInfo(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdOrganizationUsageGetValidateBeforeCall(clearingId, distributorId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns clearing license overview usage for all organizations where the actual users is authorized. (asynchronously)
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdOrganizationUsageGetAsync(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingLicenseCountBillingLicenseOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdOrganizationUsageGetValidateBeforeCall(clearingId, distributorId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingClearingClearingIdUsageGet
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdUsageGetCall(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/clearing/{clearingId}/usage"
            .replace("{" + "clearingId" + "}", localVarApiClient.escapeString(clearingId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingClearingClearingIdUsageGetValidateBeforeCall(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'clearingId' is set
        if (clearingId == null) {
            throw new ApiException("Missing the required parameter 'clearingId' when calling apiV10BillingClearingClearingIdUsageGet(Async)");
        }

        return apiV10BillingClearingClearingIdUsageGetCall(clearingId, distributorId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * Returns the billin license overview usage for the requested distributor
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingClearingClearingIdUsageGet(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> localVarResp = apiV10BillingClearingClearingIdUsageGetWithHttpInfo(clearingId, distributorId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * Returns the billin license overview usage for the requested distributor
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return ApiResponse&lt;BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> apiV10BillingClearingClearingIdUsageGetWithHttpInfo(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdUsageGetValidateBeforeCall(clearingId, distributorId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the billin license overview usage for the requested distributor (asynchronously)
     * 
     * @param clearingId The Id of the clearing. (required)
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingClearingIdUsageGetAsync(UUID clearingId, UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingClearingClearingIdUsageGetValidateBeforeCall(clearingId, distributorId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingClearingGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/clearing";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingClearingGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10BillingClearingGetCall(_callback);

    }

    /**
     * Returns all available clearing runs.
     * 
     * @return List&lt;ClearingRun&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<ClearingRun> apiV10BillingClearingGet() throws ApiException {
        ApiResponse<List<ClearingRun>> localVarResp = apiV10BillingClearingGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns all available clearing runs.
     * 
     * @return ApiResponse&lt;List&lt;ClearingRun&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ClearingRun>> apiV10BillingClearingGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingClearingGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<ClearingRun>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all available clearing runs. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingGetAsync(final ApiCallback<List<ClearingRun>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingClearingGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<ClearingRun>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingClearingPut
     * @param caption The caption of the clearing run. (required)
     * @param clearingRunDateTime  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingPutCall(String caption, OffsetDateTime clearingRunDateTime, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/clearing";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (caption != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("caption", caption));
        }

        if (clearingRunDateTime != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("clearingRunDateTime", clearingRunDateTime));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingClearingPutValidateBeforeCall(String caption, OffsetDateTime clearingRunDateTime, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'caption' is set
        if (caption == null) {
            throw new ApiException("Missing the required parameter 'caption' when calling apiV10BillingClearingPut(Async)");
        }

        return apiV10BillingClearingPutCall(caption, clearingRunDateTime, _callback);

    }

    /**
     * Creates a new clearing run.  Clearing run will be created in background
     * 
     * @param caption The caption of the clearing run. (required)
     * @param clearingRunDateTime  (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10BillingClearingPut(String caption, OffsetDateTime clearingRunDateTime) throws ApiException {
        apiV10BillingClearingPutWithHttpInfo(caption, clearingRunDateTime);
    }

    /**
     * Creates a new clearing run.  Clearing run will be created in background
     * 
     * @param caption The caption of the clearing run. (required)
     * @param clearingRunDateTime  (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10BillingClearingPutWithHttpInfo(String caption, OffsetDateTime clearingRunDateTime) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingClearingPutValidateBeforeCall(caption, clearingRunDateTime, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Creates a new clearing run.  Clearing run will be created in background (asynchronously)
     * 
     * @param caption The caption of the clearing run. (required)
     * @param clearingRunDateTime  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingClearingPutAsync(String caption, OffsetDateTime clearingRunDateTime, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingClearingPutValidateBeforeCall(caption, clearingRunDateTime, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingOverviewGet
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of distributor). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingOverviewGetCall(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/overview";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingOverviewGetValidateBeforeCall(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        return apiV10BillingOverviewGetCall(distributorId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * Returns billing license overview for distributor. [preview]
     * This controller is in preview state and subject to change in future.
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of distributor). (optional)
     * @return BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingOverviewGet(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> localVarResp = apiV10BillingOverviewGetWithHttpInfo(distributorId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * Returns billing license overview for distributor. [preview]
     * This controller is in preview state and subject to change in future.
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of distributor). (optional)
     * @return ApiResponse&lt;BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> apiV10BillingOverviewGetWithHttpInfo(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingOverviewGetValidateBeforeCall(distributorId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns billing license overview for distributor. [preview] (asynchronously)
     * This controller is in preview state and subject to change in future.
     * @param distributorId The distributor to fetch the license overview. If null first level of distributor is queried. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of distributor). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingOverviewGetAsync(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingOverviewGetValidateBeforeCall(distributorId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingOverviewOrganizationGet
     * @param distributorId The distributor to fetch the license overview. If null all organizations with authorization are fetched. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of organization). (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingOverviewOrganizationGetCall(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/overview/organization";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (distributorId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("distributorId", distributorId));
        }

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingOverviewOrganizationGetValidateBeforeCall(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        return apiV10BillingOverviewOrganizationGetCall(distributorId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * Returns billing license overview for all organizations where the actual users is authorized. [preview]
     * This controller is in preview state and subject to change in future.
     * @param distributorId The distributor to fetch the license overview. If null all organizations with authorization are fetched. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of organization). (optional)
     * @return BillingLicenseCountBillingLicenseOverviewPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingLicenseCountBillingLicenseOverviewPageResultModel apiV10BillingOverviewOrganizationGet(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingLicenseCountBillingLicenseOverviewPageResultModel> localVarResp = apiV10BillingOverviewOrganizationGetWithHttpInfo(distributorId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * Returns billing license overview for all organizations where the actual users is authorized. [preview]
     * This controller is in preview state and subject to change in future.
     * @param distributorId The distributor to fetch the license overview. If null all organizations with authorization are fetched. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of organization). (optional)
     * @return ApiResponse&lt;BillingLicenseCountBillingLicenseOverviewPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingLicenseCountBillingLicenseOverviewPageResultModel> apiV10BillingOverviewOrganizationGetWithHttpInfo(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingOverviewOrganizationGetValidateBeforeCall(distributorId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns billing license overview for all organizations where the actual users is authorized. [preview] (asynchronously)
     * This controller is in preview state and subject to change in future.
     * @param distributorId The distributor to fetch the license overview. If null all organizations with authorization are fetched. (optional)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: Caption of organization). (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingOverviewOrganizationGetAsync(UUID distributorId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingLicenseCountBillingLicenseOverviewPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingOverviewOrganizationGetValidateBeforeCall(distributorId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingLicenseCountBillingLicenseOverviewPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingOverviewOrganizationIdDetailGet
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingOverviewOrganizationIdDetailGetCall(UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/overview/{organizationId}/detail"
            .replace("{" + "organizationId" + "}", localVarApiClient.escapeString(organizationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        if (searchText != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("searchText", searchText));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingOverviewOrganizationIdDetailGetValidateBeforeCall(UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organizationId' is set
        if (organizationId == null) {
            throw new ApiException("Missing the required parameter 'organizationId' when calling apiV10BillingOverviewOrganizationIdDetailGet(Async)");
        }

        return apiV10BillingOverviewOrganizationIdDetailGetCall(organizationId, pageOffset, pageSize, searchText, _callback);

    }

    /**
     * Returns license details for the requested organization. [preview]
     * This controller is in preview state and subject to change in future.
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return BillingLicenseDetailPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public BillingLicenseDetailPageResultModel apiV10BillingOverviewOrganizationIdDetailGet(UUID organizationId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        ApiResponse<BillingLicenseDetailPageResultModel> localVarResp = apiV10BillingOverviewOrganizationIdDetailGetWithHttpInfo(organizationId, pageOffset, pageSize, searchText);
        return localVarResp.getData();
    }

    /**
     * Returns license details for the requested organization. [preview]
     * This controller is in preview state and subject to change in future.
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @return ApiResponse&lt;BillingLicenseDetailPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<BillingLicenseDetailPageResultModel> apiV10BillingOverviewOrganizationIdDetailGetWithHttpInfo(UUID organizationId, Integer pageOffset, Integer pageSize, String searchText) throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingOverviewOrganizationIdDetailGetValidateBeforeCall(organizationId, pageOffset, pageSize, searchText, null);
        Type localVarReturnType = new TypeToken<BillingLicenseDetailPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns license details for the requested organization. [preview] (asynchronously)
     * This controller is in preview state and subject to change in future.
     * @param organizationId The id of the organization where the license details are requested. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param searchText An optional search text to search for billing details (searched fields are: StoreNumber, StoreCaption, TerminalCaption) (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingOverviewOrganizationIdDetailGetAsync(UUID organizationId, Integer pageOffset, Integer pageSize, String searchText, final ApiCallback<BillingLicenseDetailPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingOverviewOrganizationIdDetailGetValidateBeforeCall(organizationId, pageOffset, pageSize, searchText, _callback);
        Type localVarReturnType = new TypeToken<BillingLicenseDetailPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10BillingPaymentOutstandingGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingPaymentOutstandingGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/billing/payment/outstanding";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10BillingPaymentOutstandingGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10BillingPaymentOutstandingGetCall(_callback);

    }

    /**
     * Log the acknowledgement of the outstanding payment message
     * 
     * @return List&lt;OutstandingPayment&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<OutstandingPayment> apiV10BillingPaymentOutstandingGet() throws ApiException {
        ApiResponse<List<OutstandingPayment>> localVarResp = apiV10BillingPaymentOutstandingGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Log the acknowledgement of the outstanding payment message
     * 
     * @return ApiResponse&lt;List&lt;OutstandingPayment&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<OutstandingPayment>> apiV10BillingPaymentOutstandingGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10BillingPaymentOutstandingGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<OutstandingPayment>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Log the acknowledgement of the outstanding payment message (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10BillingPaymentOutstandingGetAsync(final ApiCallback<List<OutstandingPayment>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10BillingPaymentOutstandingGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<OutstandingPayment>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
