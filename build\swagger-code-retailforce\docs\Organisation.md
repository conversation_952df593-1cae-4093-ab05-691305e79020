

# Organisation

Represents a organisation (master object for all objects).

## Properties

| Name | Type | Description | Notes |
|------------ | ------------- | ------------- | -------------|
|**organisationId** | **UUID** | The id of the organisation. |  [optional] |
|**distributorId** | **UUID** | The id of the corresponding distributor. |  [optional] |
|**distributorCaption** | **String** | The caption of the corresponding distributor. |  [optional] |
|**contactPrincipal** | **UUID** | The primary financial contact for the principal. |  [optional] |
|**technicalContactPrincipal** | **UUID** | The primary technical contact for the principal. |  [optional] |
|**eMail** | **String** | The email of the distributor. |  [optional] |
|**updatedByPrincipalId** | **UUID** | Updated by PrincipalId |  [optional] |
|**caption** | **String** | The caption. |  [optional] |
|**caption2** | **String** | The second caption for the organization (not used for RetailForce.Fiscalisation.Configuration.FiscalClient). |  [optional] |
|**description** | **String** | The description for the organization (not used for RetailForce.Fiscalisation.Configuration.FiscalClient). |  [optional] |
|**fiscalYearStartMonth** | **Integer** | The month of the start of the fiscal year. |  [optional] |
|**companyIdentification** | [**List&lt;CompanyIdentification&gt;**](CompanyIdentification.md) | A list of RetailForce.Cloud.Model.OrganizationCompanyBase.CompanyIdentification objects to identify the organisation. |  [optional] |
|**fiscalCountry** | **FiscalCountry** |  |  [optional] |
|**legalForm** | **String** | The legal form of the company. |  [optional] |
|**capital** | **Double** | The social capital of the company. |  [optional] |
|**isLegalPerson** | **Boolean** | IsLegalPerson (default value because of backward compatibility is always true) &#x3D;&gt; db nullable |  [optional] |
|**webAddress** | **String** | WebAddress |  [optional] |
|**personLegalForm** | **Integer** | Legal Form for LegalPerson (value is the id from FiscalCountryProperties.LegalForms.Id) |  [optional] |
|**birthdate** | **OffsetDateTime** | Birthdate |  [optional] |
|**firstname** | **String** | Firstname |  [optional] |
|**lastname** | **String** | Lastname |  [optional] |
|**namePrefix** | **String** | Name prefix |  [optional] |
|**nameSuffix** | **String** | Name suffix |  [optional] |
|**salutation** | **Salutation** |  |  [optional] |
|**title** | **String** | Title |  [optional] |
|**clientConfigurationId** | **UUID** | The used configuration for this object. |  [optional] |
|**newClientConfigurationId** | **UUID** | The new configuration for this object valid from RetailForce.Cloud.Model.Address.NewClientConfigurationValidFrom.  &lt;remark&gt;The RetailForce.Cloud.Model.Address.NewClientConfigurationValidFrom must have a value otherwise the normal clientConfiguration will be used.&lt;/remark&gt; |  [optional] |
|**newClientConfigurationValidFrom** | **OffsetDateTime** | The valid date for the RetailForce.Cloud.Model.Address.NewClientConfigurationId. |  [optional] |
|**street** | **String** |  |  |
|**streetNumber** | **String** |  |  |
|**postalCode** | **String** |  |  |
|**city** | **String** |  |  |
|**community** | **String** |  |  [optional] |
|**countryCode** | **String** |  |  |



