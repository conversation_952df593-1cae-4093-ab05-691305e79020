/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import java.io.File;
import com.untill.retailforce.model.PrinterDriverInfo;
import com.untill.retailforce.model.PrinterImageFile;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for ConfigurationControllerItalyApi
 */
@Disabled
public class ConfigurationControllerItalyApiTest {

    private final ConfigurationControllerItalyApi api = new ConfigurationControllerItalyApi();

    /**
     * Returns stored printer image ids
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGetTest() throws ApiException {
        UUID terminalId = null;
        List<PrinterImageFile> response = api.apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesGet(terminalId);
        // TODO: test validations
    }

    /**
     * returns the requeste image
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGetTest() throws ApiException {
        UUID terminalId = null;
        Integer order = null;
        File response = api.apiV10ConfigurationItClientConfigurationTerminalIdPrinterImagesOrderGet(terminalId, order);
        // TODO: test validations
    }

    /**
     * Returns stored printer image ids
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationItConfigurationIdPrinterImagesGetTest() throws ApiException {
        UUID configurationId = null;
        List<Integer> response = api.apiV10ConfigurationItConfigurationIdPrinterImagesGet(configurationId);
        // TODO: test validations
    }

    /**
     * Delete printer image
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationItConfigurationIdPrinterImagesOrderDeleteTest() throws ApiException {
        UUID configurationId = null;
        Integer order = null;
        api.apiV10ConfigurationItConfigurationIdPrinterImagesOrderDelete(configurationId, order);
        // TODO: test validations
    }

    /**
     * returns the requeste image
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationItConfigurationIdPrinterImagesOrderGetTest() throws ApiException {
        UUID configurationId = null;
        Integer order = null;
        File response = api.apiV10ConfigurationItConfigurationIdPrinterImagesOrderGet(configurationId, order);
        // TODO: test validations
    }

    /**
     * Upload printer image
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationItConfigurationIdPrinterImagesOrderPostTest() throws ApiException {
        UUID configurationId = null;
        Integer order = null;
        File _file = null;
        api.apiV10ConfigurationItConfigurationIdPrinterImagesOrderPost(configurationId, order, _file);
        // TODO: test validations
    }

    /**
     * Returns the printer driver infos.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10ConfigurationItPrinterDriverInfosGetTest() throws ApiException {
        List<PrinterDriverInfo> response = api.apiV10ConfigurationItPrinterDriverInfosGet();
        // TODO: test validations
    }

}
