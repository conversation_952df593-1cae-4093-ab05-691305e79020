/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import java.io.File;
import java.time.OffsetDateTime;
import java.util.UUID;
import com.untill.retailforce.model.ValidationError;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class ImplementationControllerDenmarkApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public ImplementationControllerDenmarkApi() {
        this(Configuration.getDefaultApiClient());
    }

    public ImplementationControllerDenmarkApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10ImplementationDkSaftValidatePost
     * @param zipFile  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter zip file not supplied. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDkSaftValidatePostCall(File zipFile, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/dk/saft/validate";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (zipFile != null) {
            localVarFormParams.put("zipFile", zipFile);
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDkSaftValidatePostValidateBeforeCall(File zipFile, final ApiCallback _callback) throws ApiException {
        return apiV10ImplementationDkSaftValidatePostCall(zipFile, _callback);

    }

    /**
     * Validates given zip file audit file (denmark saf-t) content.
     * 
     * @param zipFile  (optional)
     * @return List&lt;ValidationError&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter zip file not supplied. </td><td>  -  </td></tr>
     </table>
     */
    public List<ValidationError> apiV10ImplementationDkSaftValidatePost(File zipFile) throws ApiException {
        ApiResponse<List<ValidationError>> localVarResp = apiV10ImplementationDkSaftValidatePostWithHttpInfo(zipFile);
        return localVarResp.getData();
    }

    /**
     * Validates given zip file audit file (denmark saf-t) content.
     * 
     * @param zipFile  (optional)
     * @return ApiResponse&lt;List&lt;ValidationError&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter zip file not supplied. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ValidationError>> apiV10ImplementationDkSaftValidatePostWithHttpInfo(File zipFile) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDkSaftValidatePostValidateBeforeCall(zipFile, null);
        Type localVarReturnType = new TypeToken<List<ValidationError>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Validates given zip file audit file (denmark saf-t) content. (asynchronously)
     * 
     * @param zipFile  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter zip file not supplied. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDkSaftValidatePostAsync(File zipFile, final ApiCallback<List<ValidationError>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDkSaftValidatePostValidateBeforeCall(zipFile, _callback);
        Type localVarReturnType = new TypeToken<List<ValidationError>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10ImplementationDkTerminalIdSaftGet
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param exportRawData Optional. If true signature raw data is exported in cashTransaction.Desc (description field). Do not use for export to authorities, this feature is for signature verification analysis. (optional, default to false)
     * @param exportFilePerClosing Optional. If true a saf-t file will be exported per day. Otherwise, all days are exported in one file. (optional, default to false)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export or OCES3 certificate not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDkTerminalIdSaftGetCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean exportRawData, Boolean exportFilePerClosing, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/implementation/dk/{terminalId}/saft"
            .replace("{" + "terminalId" + "}", localVarApiClient.escapeString(terminalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (fromDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("fromDate", fromDate));
        }

        if (tillDate != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("tillDate", tillDate));
        }

        if (exportRawData != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("exportRawData", exportRawData));
        }

        if (exportFilePerClosing != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("exportFilePerClosing", exportFilePerClosing));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10ImplementationDkTerminalIdSaftGetValidateBeforeCall(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean exportRawData, Boolean exportFilePerClosing, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'terminalId' is set
        if (terminalId == null) {
            throw new ApiException("Missing the required parameter 'terminalId' when calling apiV10ImplementationDkTerminalIdSaftGet(Async)");
        }

        // verify the required parameter 'fromDate' is set
        if (fromDate == null) {
            throw new ApiException("Missing the required parameter 'fromDate' when calling apiV10ImplementationDkTerminalIdSaftGet(Async)");
        }

        // verify the required parameter 'tillDate' is set
        if (tillDate == null) {
            throw new ApiException("Missing the required parameter 'tillDate' when calling apiV10ImplementationDkTerminalIdSaftGet(Async)");
        }

        return apiV10ImplementationDkTerminalIdSaftGetCall(terminalId, fromDate, tillDate, exportRawData, exportFilePerClosing, _callback);

    }

    /**
     * Exports denmark saf-t format for the given terminal.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param exportRawData Optional. If true signature raw data is exported in cashTransaction.Desc (description field). Do not use for export to authorities, this feature is for signature verification analysis. (optional, default to false)
     * @param exportFilePerClosing Optional. If true a saf-t file will be exported per day. Otherwise, all days are exported in one file. (optional, default to false)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export or OCES3 certificate not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10ImplementationDkTerminalIdSaftGet(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean exportRawData, Boolean exportFilePerClosing) throws ApiException {
        apiV10ImplementationDkTerminalIdSaftGetWithHttpInfo(terminalId, fromDate, tillDate, exportRawData, exportFilePerClosing);
    }

    /**
     * Exports denmark saf-t format for the given terminal.
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param exportRawData Optional. If true signature raw data is exported in cashTransaction.Desc (description field). Do not use for export to authorities, this feature is for signature verification analysis. (optional, default to false)
     * @param exportFilePerClosing Optional. If true a saf-t file will be exported per day. Otherwise, all days are exported in one file. (optional, default to false)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export or OCES3 certificate not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10ImplementationDkTerminalIdSaftGetWithHttpInfo(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean exportRawData, Boolean exportFilePerClosing) throws ApiException {
        okhttp3.Call localVarCall = apiV10ImplementationDkTerminalIdSaftGetValidateBeforeCall(terminalId, fromDate, tillDate, exportRawData, exportFilePerClosing, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Exports denmark saf-t format for the given terminal. (asynchronously)
     * 
     * @param terminalId The terminal id of the terminal where the data should be exported. (required)
     * @param fromDate The start date of the export. (required)
     * @param tillDate The end date of the export. (required)
     * @param exportRawData Optional. If true signature raw data is exported in cashTransaction.Desc (description field). Do not use for export to authorities, this feature is for signature verification analysis. (optional, default to false)
     * @param exportFilePerClosing Optional. If true a saf-t file will be exported per day. Otherwise, all days are exported in one file. (optional, default to false)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Not allowed to access the given terminal. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> No data found for export or OCES3 certificate not found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Parameter mismatch, fromdata/tilldate not between 2015-12-31 and now, tilldate smaller than fromDate or terminalId &#x3D; Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10ImplementationDkTerminalIdSaftGetAsync(UUID terminalId, OffsetDateTime fromDate, OffsetDateTime tillDate, Boolean exportRawData, Boolean exportFilePerClosing, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10ImplementationDkTerminalIdSaftGetValidateBeforeCall(terminalId, fromDate, tillDate, exportRawData, exportFilePerClosing, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
}
