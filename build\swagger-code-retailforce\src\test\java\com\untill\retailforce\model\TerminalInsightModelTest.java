/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.StringSimpleObject;
import java.io.IOException;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for TerminalInsightModel
 */
public class TerminalInsightModelTest {
    private final TerminalInsightModel model = new TerminalInsightModel();

    /**
     * Model tests for TerminalInsightModel
     */
    @Test
    public void testTerminalInsightModel() {
        // TODO: test TerminalInsightModel
    }

    /**
     * Test the property 'terminalId'
     */
    @Test
    public void terminalIdTest() {
        // TODO: test terminalId
    }

    /**
     * Test the property 'isTest'
     */
    @Test
    public void isTestTest() {
        // TODO: test isTest
    }

    /**
     * Test the property 'fiscalClientVersion'
     */
    @Test
    public void fiscalClientVersionTest() {
        // TODO: test fiscalClientVersion
    }

    /**
     * Test the property 'versionUpdate'
     */
    @Test
    public void versionUpdateTest() {
        // TODO: test versionUpdate
    }

    /**
     * Test the property 'configurationUpdate'
     */
    @Test
    public void configurationUpdateTest() {
        // TODO: test configurationUpdate
    }

    /**
     * Test the property 'archiveDate'
     */
    @Test
    public void archiveDateTest() {
        // TODO: test archiveDate
    }

    /**
     * Test the property 'countryValues'
     */
    @Test
    public void countryValuesTest() {
        // TODO: test countryValues
    }

}
