/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiCallback;
import com.untill.retailforce.ApiClient;
import com.untill.retailforce.ApiException;
import com.untill.retailforce.ApiResponse;
import com.untill.retailforce.Configuration;
import com.untill.retailforce.Pair;
import com.untill.retailforce.ProgressRequestBody;
import com.untill.retailforce.ProgressResponseBody;

import com.google.gson.reflect.TypeToken;

import java.io.IOException;


import com.untill.retailforce.model.ApiKey;
import com.untill.retailforce.model.EntityPermissions;
import com.untill.retailforce.model.EntitySecurityPageResultModel;
import com.untill.retailforce.model.EntityTypes;
import java.io.File;
import com.untill.retailforce.model.GuidSimpleObject;
import com.untill.retailforce.model.InvitationInfoModel;
import com.untill.retailforce.model.InvitationModel;
import com.untill.retailforce.model.Principal;
import com.untill.retailforce.model.ProfilePictureModel;
import com.untill.retailforce.model.RegistrationModel;
import java.util.UUID;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.ws.rs.core.GenericType;

public class SecurityApi {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public SecurityApi() {
        this(Configuration.getDefaultApiClient());
    }

    public SecurityApi(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    /**
     * Build call for apiV10SecurityAccessEntityIdDelete
     * @param entityId The id of the entity to remove the access of the principal. (required)
     * @param principalId The id of the principal to remove access. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityAccessEntityIdDeleteCall(UUID entityId, UUID principalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/access/{entityId}"
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (principalId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("principalId", principalId));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityAccessEntityIdDeleteValidateBeforeCall(UUID entityId, UUID principalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10SecurityAccessEntityIdDelete(Async)");
        }

        return apiV10SecurityAccessEntityIdDeleteCall(entityId, principalId, _callback);

    }

    /**
     * Removes an principal access from an entity.
     * If principal access was inherited from parent distributor this method will fail.                To add a principal again to an entity you have to invite the principal again with the invitation.
     * @param entityId The id of the entity to remove the access of the principal. (required)
     * @param principalId The id of the principal to remove access. (optional)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityAccessEntityIdDelete(UUID entityId, UUID principalId) throws ApiException {
        apiV10SecurityAccessEntityIdDeleteWithHttpInfo(entityId, principalId);
    }

    /**
     * Removes an principal access from an entity.
     * If principal access was inherited from parent distributor this method will fail.                To add a principal again to an entity you have to invite the principal again with the invitation.
     * @param entityId The id of the entity to remove the access of the principal. (required)
     * @param principalId The id of the principal to remove access. (optional)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityAccessEntityIdDeleteWithHttpInfo(UUID entityId, UUID principalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityAccessEntityIdDeleteValidateBeforeCall(entityId, principalId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Removes an principal access from an entity. (asynchronously)
     * If principal access was inherited from parent distributor this method will fail.                To add a principal again to an entity you have to invite the principal again with the invitation.
     * @param entityId The id of the entity to remove the access of the principal. (required)
     * @param principalId The id of the principal to remove access. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityAccessEntityIdDeleteAsync(UUID entityId, UUID principalId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityAccessEntityIdDeleteValidateBeforeCall(entityId, principalId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityAccessEntityIdGet
     * @param entityId The entity for the request. Entity must be of type organization or distributor. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityAccessEntityIdGetCall(UUID entityId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/access/{entityId}"
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (pageOffset != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageOffset", pageOffset));
        }

        if (pageSize != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("pageSize", pageSize));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityAccessEntityIdGetValidateBeforeCall(UUID entityId, Integer pageOffset, Integer pageSize, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10SecurityAccessEntityIdGet(Async)");
        }

        return apiV10SecurityAccessEntityIdGetCall(entityId, pageOffset, pageSize, _callback);

    }

    /**
     * Returns a list of principals which have access to the given entity. Entity must be of type organization or distributor.
     * 
     * @param entityId The entity for the request. Entity must be of type organization or distributor. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return EntitySecurityPageResultModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public EntitySecurityPageResultModel apiV10SecurityAccessEntityIdGet(UUID entityId, Integer pageOffset, Integer pageSize) throws ApiException {
        ApiResponse<EntitySecurityPageResultModel> localVarResp = apiV10SecurityAccessEntityIdGetWithHttpInfo(entityId, pageOffset, pageSize);
        return localVarResp.getData();
    }

    /**
     * Returns a list of principals which have access to the given entity. Entity must be of type organization or distributor.
     * 
     * @param entityId The entity for the request. Entity must be of type organization or distributor. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @return ApiResponse&lt;EntitySecurityPageResultModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<EntitySecurityPageResultModel> apiV10SecurityAccessEntityIdGetWithHttpInfo(UUID entityId, Integer pageOffset, Integer pageSize) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityAccessEntityIdGetValidateBeforeCall(entityId, pageOffset, pageSize, null);
        Type localVarReturnType = new TypeToken<EntitySecurityPageResultModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns a list of principals which have access to the given entity. Entity must be of type organization or distributor. (asynchronously)
     * 
     * @param entityId The entity for the request. Entity must be of type organization or distributor. (required)
     * @param pageOffset page number &#x3D;&gt; if null we start with the first page (starting at 0). (optional)
     * @param pageSize max number of items we expect in the result &#x3D;&gt; if null there is no limit. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityAccessEntityIdGetAsync(UUID entityId, Integer pageOffset, Integer pageSize, final ApiCallback<EntitySecurityPageResultModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityAccessEntityIdGetValidateBeforeCall(entityId, pageOffset, pageSize, _callback);
        Type localVarReturnType = new TypeToken<EntitySecurityPageResultModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityApikeyEntityIdGet
     * @param entityId The entity for which the api keys are requested. The given entity must exist (organisation or distributor). (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityApikeyEntityIdGetCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/apikey/{entityId}"
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityApikeyEntityIdGetValidateBeforeCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10SecurityApikeyEntityIdGet(Async)");
        }

        return apiV10SecurityApikeyEntityIdGetCall(entityId, _callback);

    }

    /**
     * Returns all api keys for the given entity (organisation or distributor). The given entity must exist (organisation or distributor).
     * 
     * @param entityId The entity for which the api keys are requested. The given entity must exist (organisation or distributor). (required)
     * @return List&lt;ApiKey&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<ApiKey> apiV10SecurityApikeyEntityIdGet(UUID entityId) throws ApiException {
        ApiResponse<List<ApiKey>> localVarResp = apiV10SecurityApikeyEntityIdGetWithHttpInfo(entityId);
        return localVarResp.getData();
    }

    /**
     * Returns all api keys for the given entity (organisation or distributor). The given entity must exist (organisation or distributor).
     * 
     * @param entityId The entity for which the api keys are requested. The given entity must exist (organisation or distributor). (required)
     * @return ApiResponse&lt;List&lt;ApiKey&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<ApiKey>> apiV10SecurityApikeyEntityIdGetWithHttpInfo(UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityApikeyEntityIdGetValidateBeforeCall(entityId, null);
        Type localVarReturnType = new TypeToken<List<ApiKey>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all api keys for the given entity (organisation or distributor). The given entity must exist (organisation or distributor). (asynchronously)
     * 
     * @param entityId The entity for which the api keys are requested. The given entity must exist (organisation or distributor). (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityApikeyEntityIdGetAsync(UUID entityId, final ApiCallback<List<ApiKey>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityApikeyEntityIdGetValidateBeforeCall(entityId, _callback);
        Type localVarReturnType = new TypeToken<List<ApiKey>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityApikeyEntityIdPost
     * @param entityId The entity for which the api key is requested. The given entity must exist (organisation or distributor). (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityApikeyEntityIdPostCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/apikey/{entityId}"
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityApikeyEntityIdPostValidateBeforeCall(UUID entityId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10SecurityApikeyEntityIdPost(Async)");
        }

        return apiV10SecurityApikeyEntityIdPostCall(entityId, _callback);

    }

    /**
     * Creates an api key for an organisation or a distributor. The given entity must exist (organisation or distributor).
     * 
     * @param entityId The entity for which the api key is requested. The given entity must exist (organisation or distributor). (required)
     * @return ApiKey
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiKey apiV10SecurityApikeyEntityIdPost(UUID entityId) throws ApiException {
        ApiResponse<ApiKey> localVarResp = apiV10SecurityApikeyEntityIdPostWithHttpInfo(entityId);
        return localVarResp.getData();
    }

    /**
     * Creates an api key for an organisation or a distributor. The given entity must exist (organisation or distributor).
     * 
     * @param entityId The entity for which the api key is requested. The given entity must exist (organisation or distributor). (required)
     * @return ApiResponse&lt;ApiKey&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ApiKey> apiV10SecurityApikeyEntityIdPostWithHttpInfo(UUID entityId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityApikeyEntityIdPostValidateBeforeCall(entityId, null);
        Type localVarReturnType = new TypeToken<ApiKey>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates an api key for an organisation or a distributor. The given entity must exist (organisation or distributor). (asynchronously)
     * 
     * @param entityId The entity for which the api key is requested. The given entity must exist (organisation or distributor). (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityApikeyEntityIdPostAsync(UUID entityId, final ApiCallback<ApiKey> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityApikeyEntityIdPostValidateBeforeCall(entityId, _callback);
        Type localVarReturnType = new TypeToken<ApiKey>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityAssignEntityTypeEntityIdPut
     * @param entityId  (required)
     * @param entityType  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter entityId or entityType is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityAssignEntityTypeEntityIdPutCall(UUID entityId, EntityTypes entityType, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/assign/{entityType}/{entityId}"
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()))
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityAssignEntityTypeEntityIdPutValidateBeforeCall(UUID entityId, EntityTypes entityType, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10SecurityAssignEntityTypeEntityIdPut(Async)");
        }

        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10SecurityAssignEntityTypeEntityIdPut(Async)");
        }

        return apiV10SecurityAssignEntityTypeEntityIdPutCall(entityId, entityType, _callback);

    }

    /**
     * Self assign the current user to an entity, without the whole invitation process  this is only allowed for retail force users
     * 
     * @param entityId  (required)
     * @param entityType  (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter entityId or entityType is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityAssignEntityTypeEntityIdPut(UUID entityId, EntityTypes entityType) throws ApiException {
        apiV10SecurityAssignEntityTypeEntityIdPutWithHttpInfo(entityId, entityType);
    }

    /**
     * Self assign the current user to an entity, without the whole invitation process  this is only allowed for retail force users
     * 
     * @param entityId  (required)
     * @param entityType  (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter entityId or entityType is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityAssignEntityTypeEntityIdPutWithHttpInfo(UUID entityId, EntityTypes entityType) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityAssignEntityTypeEntityIdPutValidateBeforeCall(entityId, entityType, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Self assign the current user to an entity, without the whole invitation process  this is only allowed for retail force users (asynchronously)
     * 
     * @param entityId  (required)
     * @param entityType  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter entityId or entityType is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityAssignEntityTypeEntityIdPutAsync(UUID entityId, EntityTypes entityType, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityAssignEntityTypeEntityIdPutValidateBeforeCall(entityId, entityType, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityInvitationEntityTypeEntityIdPost
     * @param entityType The type of the invitation. Must be Organisation|Distributor. Otherwise an exception is raised. (required)
     * @param entityId The entity for the invitation (Organisation or Distributor). (required)
     * @param email The email of the person to invite. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Entity access not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> Invitation was already accepted (user exists in organisation/distributor). </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> Invitation was already sent for email/entity combination. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter entityId is set to System.Guid.Empty or parameter email or parameter entityType is set to null or empty string or if parameter entityType is not of the following values: Organisation|Distributor. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationEntityTypeEntityIdPostCall(String entityType, UUID entityId, String email, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/invitation/{entityType}/{entityId}"
            .replace("{" + "entityType" + "}", localVarApiClient.escapeString(entityType.toString()))
            .replace("{" + "entityId" + "}", localVarApiClient.escapeString(entityId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (email != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("email", email));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityInvitationEntityTypeEntityIdPostValidateBeforeCall(String entityType, UUID entityId, String email, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'entityType' is set
        if (entityType == null) {
            throw new ApiException("Missing the required parameter 'entityType' when calling apiV10SecurityInvitationEntityTypeEntityIdPost(Async)");
        }

        // verify the required parameter 'entityId' is set
        if (entityId == null) {
            throw new ApiException("Missing the required parameter 'entityId' when calling apiV10SecurityInvitationEntityTypeEntityIdPost(Async)");
        }

        // verify the required parameter 'email' is set
        if (email == null) {
            throw new ApiException("Missing the required parameter 'email' when calling apiV10SecurityInvitationEntityTypeEntityIdPost(Async)");
        }

        return apiV10SecurityInvitationEntityTypeEntityIdPostCall(entityType, entityId, email, _callback);

    }

    /**
     * Creates a new invitation in the backend.
     * 
     * @param entityType The type of the invitation. Must be Organisation|Distributor. Otherwise an exception is raised. (required)
     * @param entityId The entity for the invitation (Organisation or Distributor). (required)
     * @param email The email of the person to invite. (required)
     * @return InvitationModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Entity access not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> Invitation was already accepted (user exists in organisation/distributor). </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> Invitation was already sent for email/entity combination. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter entityId is set to System.Guid.Empty or parameter email or parameter entityType is set to null or empty string or if parameter entityType is not of the following values: Organisation|Distributor. </td><td>  -  </td></tr>
     </table>
     */
    public InvitationModel apiV10SecurityInvitationEntityTypeEntityIdPost(String entityType, UUID entityId, String email) throws ApiException {
        ApiResponse<InvitationModel> localVarResp = apiV10SecurityInvitationEntityTypeEntityIdPostWithHttpInfo(entityType, entityId, email);
        return localVarResp.getData();
    }

    /**
     * Creates a new invitation in the backend.
     * 
     * @param entityType The type of the invitation. Must be Organisation|Distributor. Otherwise an exception is raised. (required)
     * @param entityId The entity for the invitation (Organisation or Distributor). (required)
     * @param email The email of the person to invite. (required)
     * @return ApiResponse&lt;InvitationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Entity access not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> Invitation was already accepted (user exists in organisation/distributor). </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> Invitation was already sent for email/entity combination. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter entityId is set to System.Guid.Empty or parameter email or parameter entityType is set to null or empty string or if parameter entityType is not of the following values: Organisation|Distributor. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<InvitationModel> apiV10SecurityInvitationEntityTypeEntityIdPostWithHttpInfo(String entityType, UUID entityId, String email) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityInvitationEntityTypeEntityIdPostValidateBeforeCall(entityType, entityId, email, null);
        Type localVarReturnType = new TypeToken<InvitationModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates a new invitation in the backend. (asynchronously)
     * 
     * @param entityType The type of the invitation. Must be Organisation|Distributor. Otherwise an exception is raised. (required)
     * @param entityId The entity for the invitation (Organisation or Distributor). (required)
     * @param email The email of the person to invite. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Entity access not allowed. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found. </td><td>  -  </td></tr>
        <tr><td> 406 </td><td> Invitation was already accepted (user exists in organisation/distributor). </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> Invitation was already sent for email/entity combination. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter entityId is set to System.Guid.Empty or parameter email or parameter entityType is set to null or empty string or if parameter entityType is not of the following values: Organisation|Distributor. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationEntityTypeEntityIdPostAsync(String entityType, UUID entityId, String email, final ApiCallback<InvitationModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityInvitationEntityTypeEntityIdPostValidateBeforeCall(entityType, entityId, email, _callback);
        Type localVarReturnType = new TypeToken<InvitationModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityInvitationGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/invitation";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityInvitationGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10SecurityInvitationGetCall(_callback);

    }

    /**
     * Returns all open invitation which where sent for current organisation/distributor where authenticated principal has access.
     * 
     * @return List&lt;InvitationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
     </table>
     */
    public List<InvitationModel> apiV10SecurityInvitationGet() throws ApiException {
        ApiResponse<List<InvitationModel>> localVarResp = apiV10SecurityInvitationGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns all open invitation which where sent for current organisation/distributor where authenticated principal has access.
     * 
     * @return ApiResponse&lt;List&lt;InvitationModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<InvitationModel>> apiV10SecurityInvitationGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityInvitationGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<InvitationModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all open invitation which where sent for current organisation/distributor where authenticated principal has access. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationGetAsync(final ApiCallback<List<InvitationModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityInvitationGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<InvitationModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityInvitationInvitationIdAcceptPut
     * @param invitationId The id of the invitation which will be accepted. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdAcceptPutCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/invitation/{invitationId}/accept"
            .replace("{" + "invitationId" + "}", localVarApiClient.escapeString(invitationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityInvitationInvitationIdAcceptPutValidateBeforeCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'invitationId' is set
        if (invitationId == null) {
            throw new ApiException("Missing the required parameter 'invitationId' when calling apiV10SecurityInvitationInvitationIdAcceptPut(Async)");
        }

        return apiV10SecurityInvitationInvitationIdAcceptPutCall(invitationId, _callback);

    }

    /**
     * Accepts an invitation.
     * 
     * @param invitationId The id of the invitation which will be accepted. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityInvitationInvitationIdAcceptPut(UUID invitationId) throws ApiException {
        apiV10SecurityInvitationInvitationIdAcceptPutWithHttpInfo(invitationId);
    }

    /**
     * Accepts an invitation.
     * 
     * @param invitationId The id of the invitation which will be accepted. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityInvitationInvitationIdAcceptPutWithHttpInfo(UUID invitationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdAcceptPutValidateBeforeCall(invitationId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Accepts an invitation. (asynchronously)
     * 
     * @param invitationId The id of the invitation which will be accepted. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdAcceptPutAsync(UUID invitationId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdAcceptPutValidateBeforeCall(invitationId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityInvitationInvitationIdDeclinePut
     * @param invitationId The id of the invitation which will be declined. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdDeclinePutCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/invitation/{invitationId}/decline"
            .replace("{" + "invitationId" + "}", localVarApiClient.escapeString(invitationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityInvitationInvitationIdDeclinePutValidateBeforeCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'invitationId' is set
        if (invitationId == null) {
            throw new ApiException("Missing the required parameter 'invitationId' when calling apiV10SecurityInvitationInvitationIdDeclinePut(Async)");
        }

        return apiV10SecurityInvitationInvitationIdDeclinePutCall(invitationId, _callback);

    }

    /**
     * Declines an invitation.
     * 
     * @param invitationId The id of the invitation which will be declined. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityInvitationInvitationIdDeclinePut(UUID invitationId) throws ApiException {
        apiV10SecurityInvitationInvitationIdDeclinePutWithHttpInfo(invitationId);
    }

    /**
     * Declines an invitation.
     * 
     * @param invitationId The id of the invitation which will be declined. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityInvitationInvitationIdDeclinePutWithHttpInfo(UUID invitationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdDeclinePutValidateBeforeCall(invitationId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Declines an invitation. (asynchronously)
     * 
     * @param invitationId The id of the invitation which will be declined. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdDeclinePutAsync(UUID invitationId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdDeclinePutValidateBeforeCall(invitationId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityInvitationInvitationIdDelete
     * @param invitationId The id of the invitation which should be deleted. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdDeleteCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/invitation/{invitationId}"
            .replace("{" + "invitationId" + "}", localVarApiClient.escapeString(invitationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "DELETE", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityInvitationInvitationIdDeleteValidateBeforeCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'invitationId' is set
        if (invitationId == null) {
            throw new ApiException("Missing the required parameter 'invitationId' when calling apiV10SecurityInvitationInvitationIdDelete(Async)");
        }

        return apiV10SecurityInvitationInvitationIdDeleteCall(invitationId, _callback);

    }

    /**
     * Deletes an existing invitation. If invitation does not exists, nothing happens.
     * 
     * @param invitationId The id of the invitation which should be deleted. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityInvitationInvitationIdDelete(UUID invitationId) throws ApiException {
        apiV10SecurityInvitationInvitationIdDeleteWithHttpInfo(invitationId);
    }

    /**
     * Deletes an existing invitation. If invitation does not exists, nothing happens.
     * 
     * @param invitationId The id of the invitation which should be deleted. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityInvitationInvitationIdDeleteWithHttpInfo(UUID invitationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdDeleteValidateBeforeCall(invitationId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Deletes an existing invitation. If invitation does not exists, nothing happens. (asynchronously)
     * 
     * @param invitationId The id of the invitation which should be deleted. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdDeleteAsync(UUID invitationId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdDeleteValidateBeforeCall(invitationId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityInvitationInvitationIdInfoGet
     * @param invitationId The id of the requested invitation. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdInfoGetCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/invitation/{invitationId}/info"
            .replace("{" + "invitationId" + "}", localVarApiClient.escapeString(invitationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityInvitationInvitationIdInfoGetValidateBeforeCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'invitationId' is set
        if (invitationId == null) {
            throw new ApiException("Missing the required parameter 'invitationId' when calling apiV10SecurityInvitationInvitationIdInfoGet(Async)");
        }

        return apiV10SecurityInvitationInvitationIdInfoGetCall(invitationId, _callback);

    }

    /**
     * Returns an information if the invitation exists, the email of the invited user and if the user already exists in the backend.
     * 
     * @param invitationId The id of the requested invitation. (required)
     * @return InvitationInfoModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public InvitationInfoModel apiV10SecurityInvitationInvitationIdInfoGet(UUID invitationId) throws ApiException {
        ApiResponse<InvitationInfoModel> localVarResp = apiV10SecurityInvitationInvitationIdInfoGetWithHttpInfo(invitationId);
        return localVarResp.getData();
    }

    /**
     * Returns an information if the invitation exists, the email of the invited user and if the user already exists in the backend.
     * 
     * @param invitationId The id of the requested invitation. (required)
     * @return ApiResponse&lt;InvitationInfoModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<InvitationInfoModel> apiV10SecurityInvitationInvitationIdInfoGetWithHttpInfo(UUID invitationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdInfoGetValidateBeforeCall(invitationId, null);
        Type localVarReturnType = new TypeToken<InvitationInfoModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns an information if the invitation exists, the email of the invited user and if the user already exists in the backend. (asynchronously)
     * 
     * @param invitationId The id of the requested invitation. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Entity was not found (already accepted or never created). </td><td>  -  </td></tr>
        <tr><td> 410 </td><td> Invitation expired. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdInfoGetAsync(UUID invitationId, final ApiCallback<InvitationInfoModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdInfoGetValidateBeforeCall(invitationId, _callback);
        Type localVarReturnType = new TypeToken<InvitationInfoModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityInvitationInvitationIdPut
     * @param invitationId The invitation which should be sent again. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Not Found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdPutCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/invitation/{invitationId}"
            .replace("{" + "invitationId" + "}", localVarApiClient.escapeString(invitationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityInvitationInvitationIdPutValidateBeforeCall(UUID invitationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'invitationId' is set
        if (invitationId == null) {
            throw new ApiException("Missing the required parameter 'invitationId' when calling apiV10SecurityInvitationInvitationIdPut(Async)");
        }

        return apiV10SecurityInvitationInvitationIdPutCall(invitationId, _callback);

    }

    /**
     * Resends an existing invitation.
     * 
     * @param invitationId The invitation which should be sent again. (required)
     * @return InvitationModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Not Found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public InvitationModel apiV10SecurityInvitationInvitationIdPut(UUID invitationId) throws ApiException {
        ApiResponse<InvitationModel> localVarResp = apiV10SecurityInvitationInvitationIdPutWithHttpInfo(invitationId);
        return localVarResp.getData();
    }

    /**
     * Resends an existing invitation.
     * 
     * @param invitationId The invitation which should be sent again. (required)
     * @return ApiResponse&lt;InvitationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Not Found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<InvitationModel> apiV10SecurityInvitationInvitationIdPutWithHttpInfo(UUID invitationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdPutValidateBeforeCall(invitationId, null);
        Type localVarReturnType = new TypeToken<InvitationModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Resends an existing invitation. (asynchronously)
     * 
     * @param invitationId The invitation which should be sent again. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
        <tr><td> 403 </td><td> Forbidden. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Not Found. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> Thrown if parameter invitationId is set to System.Guid.Empty. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationInvitationIdPutAsync(UUID invitationId, final ApiCallback<InvitationModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityInvitationInvitationIdPutValidateBeforeCall(invitationId, _callback);
        Type localVarReturnType = new TypeToken<InvitationModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityInvitationUserGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationUserGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/invitation/user";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityInvitationUserGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10SecurityInvitationUserGetCall(_callback);

    }

    /**
     * Returns all open invitations for the authenticated principal.
     * 
     * @return List&lt;InvitationModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
     </table>
     */
    public List<InvitationModel> apiV10SecurityInvitationUserGet() throws ApiException {
        ApiResponse<List<InvitationModel>> localVarResp = apiV10SecurityInvitationUserGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns all open invitations for the authenticated principal.
     * 
     * @return ApiResponse&lt;List&lt;InvitationModel&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<InvitationModel>> apiV10SecurityInvitationUserGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityInvitationUserGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<List<InvitationModel>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all open invitations for the authenticated principal. (asynchronously)
     * 
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 401 </td><td> Not authorized. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityInvitationUserGetAsync(final ApiCallback<List<InvitationModel>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityInvitationUserGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<List<InvitationModel>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPasswordResetLinkPost
     * @param email email address of the account where to reset the password (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPasswordResetLinkPostCall(String email, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/password/reset/link";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (email != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("email", email));
        }

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPasswordResetLinkPostValidateBeforeCall(String email, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'email' is set
        if (email == null) {
            throw new ApiException("Missing the required parameter 'email' when calling apiV10SecurityPasswordResetLinkPost(Async)");
        }

        return apiV10SecurityPasswordResetLinkPostCall(email, _callback);

    }

    /**
     * Send / Resend password reset email
     * 
     * @param email email address of the account where to reset the password (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityPasswordResetLinkPost(String email) throws ApiException {
        apiV10SecurityPasswordResetLinkPostWithHttpInfo(email);
    }

    /**
     * Send / Resend password reset email
     * 
     * @param email email address of the account where to reset the password (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityPasswordResetLinkPostWithHttpInfo(String email) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPasswordResetLinkPostValidateBeforeCall(email, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Send / Resend password reset email (asynchronously)
     * 
     * @param email email address of the account where to reset the password (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPasswordResetLinkPostAsync(String email, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPasswordResetLinkPostValidateBeforeCall(email, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPasswordResetTokenHead
     * @param token password reset token (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPasswordResetTokenHeadCall(String token, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/password/reset/{token}"
            .replace("{" + "token" + "}", localVarApiClient.escapeString(token.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "HEAD", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPasswordResetTokenHeadValidateBeforeCall(String token, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'token' is set
        if (token == null) {
            throw new ApiException("Missing the required parameter 'token' when calling apiV10SecurityPasswordResetTokenHead(Async)");
        }

        return apiV10SecurityPasswordResetTokenHeadCall(token, _callback);

    }

    /**
     * Send / Resend password reset email
     * 
     * @param token password reset token (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityPasswordResetTokenHead(String token) throws ApiException {
        apiV10SecurityPasswordResetTokenHeadWithHttpInfo(token);
    }

    /**
     * Send / Resend password reset email
     * 
     * @param token password reset token (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityPasswordResetTokenHeadWithHttpInfo(String token) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPasswordResetTokenHeadValidateBeforeCall(token, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Send / Resend password reset email (asynchronously)
     * 
     * @param token password reset token (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPasswordResetTokenHeadAsync(String token, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPasswordResetTokenHeadValidateBeforeCall(token, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPasswordResetTokenPost
     * @param token The token to change the secret. (required)
     * @param email  (required)
     * @param newPassword  (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPasswordResetTokenPostCall(String token, String email, String newPassword, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/password/reset/{token}"
            .replace("{" + "token" + "}", localVarApiClient.escapeString(token.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (email != null) {
            localVarFormParams.put("email", email);
        }

        if (newPassword != null) {
            localVarFormParams.put("newPassword", newPassword);
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPasswordResetTokenPostValidateBeforeCall(String token, String email, String newPassword, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'token' is set
        if (token == null) {
            throw new ApiException("Missing the required parameter 'token' when calling apiV10SecurityPasswordResetTokenPost(Async)");
        }

        // verify the required parameter 'email' is set
        if (email == null) {
            throw new ApiException("Missing the required parameter 'email' when calling apiV10SecurityPasswordResetTokenPost(Async)");
        }

        // verify the required parameter 'newPassword' is set
        if (newPassword == null) {
            throw new ApiException("Missing the required parameter 'newPassword' when calling apiV10SecurityPasswordResetTokenPost(Async)");
        }

        return apiV10SecurityPasswordResetTokenPostCall(token, email, newPassword, _callback);

    }

    /**
     * Reset the password based on a reset password token
     * 
     * @param token The token to change the secret. (required)
     * @param email  (required)
     * @param newPassword  (required)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10SecurityPasswordResetTokenPost(String token, String email, String newPassword) throws ApiException {
        ApiResponse<String> localVarResp = apiV10SecurityPasswordResetTokenPostWithHttpInfo(token, email, newPassword);
        return localVarResp.getData();
    }

    /**
     * Reset the password based on a reset password token
     * 
     * @param token The token to change the secret. (required)
     * @param email  (required)
     * @param newPassword  (required)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10SecurityPasswordResetTokenPostWithHttpInfo(String token, String email, String newPassword) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPasswordResetTokenPostValidateBeforeCall(token, email, newPassword, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Reset the password based on a reset password token (asynchronously)
     * 
     * @param token The token to change the secret. (required)
     * @param email  (required)
     * @param newPassword  (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPasswordResetTokenPostAsync(String token, String email, String newPassword, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPasswordResetTokenPostValidateBeforeCall(token, email, newPassword, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPermissionsDisplayGet
     * @param organizationId The id of the organization to switch. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPermissionsDisplayGetCall(UUID organizationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/permissions/display";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (organizationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organizationId", organizationId));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPermissionsDisplayGetValidateBeforeCall(UUID organizationId, final ApiCallback _callback) throws ApiException {
        return apiV10SecurityPermissionsDisplayGetCall(organizationId, _callback);

    }

    /**
     * Get display permissions
     * 
     * @param organizationId The id of the organization to switch. (optional)
     * @return List&lt;EntityTypes&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<EntityTypes> apiV10SecurityPermissionsDisplayGet(UUID organizationId) throws ApiException {
        ApiResponse<List<EntityTypes>> localVarResp = apiV10SecurityPermissionsDisplayGetWithHttpInfo(organizationId);
        return localVarResp.getData();
    }

    /**
     * Get display permissions
     * 
     * @param organizationId The id of the organization to switch. (optional)
     * @return ApiResponse&lt;List&lt;EntityTypes&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<EntityTypes>> apiV10SecurityPermissionsDisplayGetWithHttpInfo(UUID organizationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPermissionsDisplayGetValidateBeforeCall(organizationId, null);
        Type localVarReturnType = new TypeToken<List<EntityTypes>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get display permissions (asynchronously)
     * 
     * @param organizationId The id of the organization to switch. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPermissionsDisplayGetAsync(UUID organizationId, final ApiCallback<List<EntityTypes>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPermissionsDisplayGetValidateBeforeCall(organizationId, _callback);
        Type localVarReturnType = new TypeToken<List<EntityTypes>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPermissionsGet
     * @param type The type of the entity. (required)
     * @param entityId The id of the entity where the permissions are requested. (optional)
     * @param organizationId The id of the actual organization. (optional)
     * @param entityVersion load a specific version of an entity (version from as utc ticks as string for precision) (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPermissionsGetCall(EntityTypes type, String entityId, UUID organizationId, String entityVersion, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/permissions";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (type != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("type", type));
        }

        if (entityId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityId", entityId));
        }

        if (organizationId != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("organizationId", organizationId));
        }

        if (entityVersion != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("entityVersion", entityVersion));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPermissionsGetValidateBeforeCall(EntityTypes type, String entityId, UUID organizationId, String entityVersion, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'type' is set
        if (type == null) {
            throw new ApiException("Missing the required parameter 'type' when calling apiV10SecurityPermissionsGet(Async)");
        }

        return apiV10SecurityPermissionsGetCall(type, entityId, organizationId, entityVersion, _callback);

    }

    /**
     * Get entity permissions for requested type.
     * 
     * @param type The type of the entity. (required)
     * @param entityId The id of the entity where the permissions are requested. (optional)
     * @param organizationId The id of the actual organization. (optional)
     * @param entityVersion load a specific version of an entity (version from as utc ticks as string for precision) (optional)
     * @return List&lt;EntityPermissions&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<EntityPermissions> apiV10SecurityPermissionsGet(EntityTypes type, String entityId, UUID organizationId, String entityVersion) throws ApiException {
        ApiResponse<List<EntityPermissions>> localVarResp = apiV10SecurityPermissionsGetWithHttpInfo(type, entityId, organizationId, entityVersion);
        return localVarResp.getData();
    }

    /**
     * Get entity permissions for requested type.
     * 
     * @param type The type of the entity. (required)
     * @param entityId The id of the entity where the permissions are requested. (optional)
     * @param organizationId The id of the actual organization. (optional)
     * @param entityVersion load a specific version of an entity (version from as utc ticks as string for precision) (optional)
     * @return ApiResponse&lt;List&lt;EntityPermissions&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<EntityPermissions>> apiV10SecurityPermissionsGetWithHttpInfo(EntityTypes type, String entityId, UUID organizationId, String entityVersion) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPermissionsGetValidateBeforeCall(type, entityId, organizationId, entityVersion, null);
        Type localVarReturnType = new TypeToken<List<EntityPermissions>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Get entity permissions for requested type. (asynchronously)
     * 
     * @param type The type of the entity. (required)
     * @param entityId The id of the entity where the permissions are requested. (optional)
     * @param organizationId The id of the actual organization. (optional)
     * @param entityVersion load a specific version of an entity (version from as utc ticks as string for precision) (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPermissionsGetAsync(EntityTypes type, String entityId, UUID organizationId, String entityVersion, final ApiCallback<List<EntityPermissions>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPermissionsGetValidateBeforeCall(type, entityId, organizationId, entityVersion, _callback);
        Type localVarReturnType = new TypeToken<List<EntityPermissions>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPrincipalGet
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalGetCall(final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/principal";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPrincipalGetValidateBeforeCall(final ApiCallback _callback) throws ApiException {
        return apiV10SecurityPrincipalGetCall(_callback);

    }

    /**
     * Returns the user information about the actual logged on user.
     * You must be authenticated to use this method.
     * @return Principal
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public Principal apiV10SecurityPrincipalGet() throws ApiException {
        ApiResponse<Principal> localVarResp = apiV10SecurityPrincipalGetWithHttpInfo();
        return localVarResp.getData();
    }

    /**
     * Returns the user information about the actual logged on user.
     * You must be authenticated to use this method.
     * @return ApiResponse&lt;Principal&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Principal> apiV10SecurityPrincipalGetWithHttpInfo() throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPrincipalGetValidateBeforeCall(null);
        Type localVarReturnType = new TypeToken<Principal>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns the user information about the actual logged on user. (asynchronously)
     * You must be authenticated to use this method.
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalGetAsync(final ApiCallback<Principal> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPrincipalGetValidateBeforeCall(_callback);
        Type localVarReturnType = new TypeToken<Principal>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPrincipalLockPrincipalIdPut
     * @param principalId The id of the principal to lock. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalLockPrincipalIdPutCall(UUID principalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/principal/lock/{principalId}"
            .replace("{" + "principalId" + "}", localVarApiClient.escapeString(principalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPrincipalLockPrincipalIdPutValidateBeforeCall(UUID principalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'principalId' is set
        if (principalId == null) {
            throw new ApiException("Missing the required parameter 'principalId' when calling apiV10SecurityPrincipalLockPrincipalIdPut(Async)");
        }

        return apiV10SecurityPrincipalLockPrincipalIdPutCall(principalId, _callback);

    }

    /**
     * Possiblity to lock a principal.
     * 
     * @param principalId The id of the principal to lock. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityPrincipalLockPrincipalIdPut(UUID principalId) throws ApiException {
        apiV10SecurityPrincipalLockPrincipalIdPutWithHttpInfo(principalId);
    }

    /**
     * Possiblity to lock a principal.
     * 
     * @param principalId The id of the principal to lock. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityPrincipalLockPrincipalIdPutWithHttpInfo(UUID principalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPrincipalLockPrincipalIdPutValidateBeforeCall(principalId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Possiblity to lock a principal. (asynchronously)
     * 
     * @param principalId The id of the principal to lock. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalLockPrincipalIdPutAsync(UUID principalId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPrincipalLockPrincipalIdPutValidateBeforeCall(principalId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet
     * @param organisationId The id of the organization where the principals are requested. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetCall(UUID organisationId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/principal/organisation/{organisationId}/simple"
            .replace("{" + "organisationId" + "}", localVarApiClient.escapeString(organisationId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetValidateBeforeCall(UUID organisationId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'organisationId' is set
        if (organisationId == null) {
            throw new ApiException("Missing the required parameter 'organisationId' when calling apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet(Async)");
        }

        return apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetCall(organisationId, _callback);

    }

    /**
     * Returns all principals which are available for this organization.
     * Returns only direct associated principals (not associated with distributor).
     * @param organisationId The id of the organization where the principals are requested. (required)
     * @return List&lt;GuidSimpleObject&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public List<GuidSimpleObject> apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGet(UUID organisationId) throws ApiException {
        ApiResponse<List<GuidSimpleObject>> localVarResp = apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetWithHttpInfo(organisationId);
        return localVarResp.getData();
    }

    /**
     * Returns all principals which are available for this organization.
     * Returns only direct associated principals (not associated with distributor).
     * @param organisationId The id of the organization where the principals are requested. (required)
     * @return ApiResponse&lt;List&lt;GuidSimpleObject&gt;&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<List<GuidSimpleObject>> apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetWithHttpInfo(UUID organisationId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetValidateBeforeCall(organisationId, null);
        Type localVarReturnType = new TypeToken<List<GuidSimpleObject>>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Returns all principals which are available for this organization. (asynchronously)
     * Returns only direct associated principals (not associated with distributor).
     * @param organisationId The id of the organization where the principals are requested. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetAsync(UUID organisationId, final ApiCallback<List<GuidSimpleObject>> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPrincipalOrganisationOrganisationIdSimpleGetValidateBeforeCall(organisationId, _callback);
        Type localVarReturnType = new TypeToken<List<GuidSimpleObject>>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPrincipalPictureGet
     * @param thumbnail thumbnail is not the original size, if necessary replace this flag with a size enum (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalPictureGetCall(Boolean thumbnail, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/principal/picture";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (thumbnail != null) {
            localVarQueryParams.addAll(localVarApiClient.parameterToPair("thumbnail", thumbnail));
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "GET", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPrincipalPictureGetValidateBeforeCall(Boolean thumbnail, final ApiCallback _callback) throws ApiException {
        return apiV10SecurityPrincipalPictureGetCall(thumbnail, _callback);

    }

    /**
     * Loads the user profile image as url or dataUrl (can be optimized to return multiple sizes at once with retina, but then it would be better to use a CDN than generating all of them)
     * 
     * @param thumbnail thumbnail is not the original size, if necessary replace this flag with a size enum (optional)
     * @return ProfilePictureModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ProfilePictureModel apiV10SecurityPrincipalPictureGet(Boolean thumbnail) throws ApiException {
        ApiResponse<ProfilePictureModel> localVarResp = apiV10SecurityPrincipalPictureGetWithHttpInfo(thumbnail);
        return localVarResp.getData();
    }

    /**
     * Loads the user profile image as url or dataUrl (can be optimized to return multiple sizes at once with retina, but then it would be better to use a CDN than generating all of them)
     * 
     * @param thumbnail thumbnail is not the original size, if necessary replace this flag with a size enum (optional)
     * @return ApiResponse&lt;ProfilePictureModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ProfilePictureModel> apiV10SecurityPrincipalPictureGetWithHttpInfo(Boolean thumbnail) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPrincipalPictureGetValidateBeforeCall(thumbnail, null);
        Type localVarReturnType = new TypeToken<ProfilePictureModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Loads the user profile image as url or dataUrl (can be optimized to return multiple sizes at once with retina, but then it would be better to use a CDN than generating all of them) (asynchronously)
     * 
     * @param thumbnail thumbnail is not the original size, if necessary replace this flag with a size enum (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalPictureGetAsync(Boolean thumbnail, final ApiCallback<ProfilePictureModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPrincipalPictureGetValidateBeforeCall(thumbnail, _callback);
        Type localVarReturnType = new TypeToken<ProfilePictureModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPrincipalPicturePost
     * @param _file  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalPicturePostCall(File _file, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/principal/picture";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (_file != null) {
            localVarFormParams.put("file", _file);
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPrincipalPicturePostValidateBeforeCall(File _file, final ApiCallback _callback) throws ApiException {
        return apiV10SecurityPrincipalPicturePostCall(_file, _callback);

    }

    /**
     * Return profile picture url or data url
     * 
     * @param _file  (optional)
     * @return ProfilePictureModel
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ProfilePictureModel apiV10SecurityPrincipalPicturePost(File _file) throws ApiException {
        ApiResponse<ProfilePictureModel> localVarResp = apiV10SecurityPrincipalPicturePostWithHttpInfo(_file);
        return localVarResp.getData();
    }

    /**
     * Return profile picture url or data url
     * 
     * @param _file  (optional)
     * @return ApiResponse&lt;ProfilePictureModel&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<ProfilePictureModel> apiV10SecurityPrincipalPicturePostWithHttpInfo(File _file) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPrincipalPicturePostValidateBeforeCall(_file, null);
        Type localVarReturnType = new TypeToken<ProfilePictureModel>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Return profile picture url or data url (asynchronously)
     * 
     * @param _file  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalPicturePostAsync(File _file, final ApiCallback<ProfilePictureModel> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPrincipalPicturePostValidateBeforeCall(_file, _callback);
        Type localVarReturnType = new TypeToken<ProfilePictureModel>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPrincipalPost
     * @param registrationModel The data for creating a principal. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> EMail does not correspond to invitation token. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Invitation token not found. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> User already exists. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> ArgumentException (model &#x3D; null, email is no email, password strength not acceptable </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Claimprovider was not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalPostCall(RegistrationModel registrationModel, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = registrationModel;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/principal";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPrincipalPostValidateBeforeCall(RegistrationModel registrationModel, final ApiCallback _callback) throws ApiException {
        return apiV10SecurityPrincipalPostCall(registrationModel, _callback);

    }

    /**
     * Creates the principal with the given registration model and does authentication (refresh token cookie).
     * 
     * @param registrationModel The data for creating a principal. (optional)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> EMail does not correspond to invitation token. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Invitation token not found. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> User already exists. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> ArgumentException (model &#x3D; null, email is no email, password strength not acceptable </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Claimprovider was not found. </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10SecurityPrincipalPost(RegistrationModel registrationModel) throws ApiException {
        ApiResponse<String> localVarResp = apiV10SecurityPrincipalPostWithHttpInfo(registrationModel);
        return localVarResp.getData();
    }

    /**
     * Creates the principal with the given registration model and does authentication (refresh token cookie).
     * 
     * @param registrationModel The data for creating a principal. (optional)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> EMail does not correspond to invitation token. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Invitation token not found. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> User already exists. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> ArgumentException (model &#x3D; null, email is no email, password strength not acceptable </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Claimprovider was not found. </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10SecurityPrincipalPostWithHttpInfo(RegistrationModel registrationModel) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPrincipalPostValidateBeforeCall(registrationModel, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Creates the principal with the given registration model and does authentication (refresh token cookie). (asynchronously)
     * 
     * @param registrationModel The data for creating a principal. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
        <tr><td> 400 </td><td> EMail does not correspond to invitation token. </td><td>  -  </td></tr>
        <tr><td> 404 </td><td> Invitation token not found. </td><td>  -  </td></tr>
        <tr><td> 409 </td><td> User already exists. </td><td>  -  </td></tr>
        <tr><td> 422 </td><td> ArgumentException (model &#x3D; null, email is no email, password strength not acceptable </td><td>  -  </td></tr>
        <tr><td> 500 </td><td> Claimprovider was not found. </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalPostAsync(RegistrationModel registrationModel, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPrincipalPostValidateBeforeCall(registrationModel, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPrincipalPut
     * @param principal The new principal data. (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalPutCall(Principal principal, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = principal;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/principal";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "application/json-patch+json",
            "application/json",
            "text/json",
            "application/*+json"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPrincipalPutValidateBeforeCall(Principal principal, final ApiCallback _callback) throws ApiException {
        return apiV10SecurityPrincipalPutCall(principal, _callback);

    }

    /**
     * Updates principal master data in the data store.
     * The principal must exist.
     * @param principal The new principal data. (optional)
     * @return Principal
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public Principal apiV10SecurityPrincipalPut(Principal principal) throws ApiException {
        ApiResponse<Principal> localVarResp = apiV10SecurityPrincipalPutWithHttpInfo(principal);
        return localVarResp.getData();
    }

    /**
     * Updates principal master data in the data store.
     * The principal must exist.
     * @param principal The new principal data. (optional)
     * @return ApiResponse&lt;Principal&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Principal> apiV10SecurityPrincipalPutWithHttpInfo(Principal principal) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPrincipalPutValidateBeforeCall(principal, null);
        Type localVarReturnType = new TypeToken<Principal>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Updates principal master data in the data store. (asynchronously)
     * The principal must exist.
     * @param principal The new principal data. (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalPutAsync(Principal principal, final ApiCallback<Principal> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPrincipalPutValidateBeforeCall(principal, _callback);
        Type localVarReturnType = new TypeToken<Principal>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecurityPrincipalUnlockPrincipalIdPut
     * @param principalId The id of the principal to unlock. (required)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalUnlockPrincipalIdPutCall(UUID principalId, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/principal/unlock/{principalId}"
            .replace("{" + "principalId" + "}", localVarApiClient.escapeString(principalId.toString()));

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        final String[] localVarAccepts = {
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "PUT", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecurityPrincipalUnlockPrincipalIdPutValidateBeforeCall(UUID principalId, final ApiCallback _callback) throws ApiException {
        // verify the required parameter 'principalId' is set
        if (principalId == null) {
            throw new ApiException("Missing the required parameter 'principalId' when calling apiV10SecurityPrincipalUnlockPrincipalIdPut(Async)");
        }

        return apiV10SecurityPrincipalUnlockPrincipalIdPutCall(principalId, _callback);

    }

    /**
     * Unlocks the given principal.
     * 
     * @param principalId The id of the principal to unlock. (required)
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public void apiV10SecurityPrincipalUnlockPrincipalIdPut(UUID principalId) throws ApiException {
        apiV10SecurityPrincipalUnlockPrincipalIdPutWithHttpInfo(principalId);
    }

    /**
     * Unlocks the given principal.
     * 
     * @param principalId The id of the principal to unlock. (required)
     * @return ApiResponse&lt;Void&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<Void> apiV10SecurityPrincipalUnlockPrincipalIdPutWithHttpInfo(UUID principalId) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecurityPrincipalUnlockPrincipalIdPutValidateBeforeCall(principalId, null);
        return localVarApiClient.execute(localVarCall);
    }

    /**
     * Unlocks the given principal. (asynchronously)
     * 
     * @param principalId The id of the principal to unlock. (required)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecurityPrincipalUnlockPrincipalIdPutAsync(UUID principalId, final ApiCallback<Void> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecurityPrincipalUnlockPrincipalIdPutValidateBeforeCall(principalId, _callback);
        localVarApiClient.executeAsync(localVarCall, _callback);
        return localVarCall;
    }
    /**
     * Build call for apiV10SecuritySecretPost
     * @param authenticationKey  (optional)
     * @param oldSecret  (optional)
     * @param newSecret  (optional)
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecuritySecretPostCall(String authenticationKey, String oldSecret, String newSecret, final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] {  };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = null;

        // create path and map variables
        String localVarPath = "/api/v1.0/security/secret";

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        if (authenticationKey != null) {
            localVarFormParams.put("authenticationKey", authenticationKey);
        }

        if (oldSecret != null) {
            localVarFormParams.put("oldSecret", oldSecret);
        }

        if (newSecret != null) {
            localVarFormParams.put("newSecret", newSecret);
        }

        final String[] localVarAccepts = {
            "text/plain",
            "application/json",
            "text/json"
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }

        String[] localVarAuthNames = new String[] { "Bearer" };
        return localVarApiClient.buildCall(basePath, localVarPath, "POST", localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    @SuppressWarnings("rawtypes")
    private okhttp3.Call apiV10SecuritySecretPostValidateBeforeCall(String authenticationKey, String oldSecret, String newSecret, final ApiCallback _callback) throws ApiException {
        return apiV10SecuritySecretPostCall(authenticationKey, oldSecret, newSecret, _callback);

    }

    /**
     * Changes the secret for the given authentication key.
     * 
     * @param authenticationKey  (optional)
     * @param oldSecret  (optional)
     * @param newSecret  (optional)
     * @return String
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public String apiV10SecuritySecretPost(String authenticationKey, String oldSecret, String newSecret) throws ApiException {
        ApiResponse<String> localVarResp = apiV10SecuritySecretPostWithHttpInfo(authenticationKey, oldSecret, newSecret);
        return localVarResp.getData();
    }

    /**
     * Changes the secret for the given authentication key.
     * 
     * @param authenticationKey  (optional)
     * @param oldSecret  (optional)
     * @param newSecret  (optional)
     * @return ApiResponse&lt;String&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public ApiResponse<String> apiV10SecuritySecretPostWithHttpInfo(String authenticationKey, String oldSecret, String newSecret) throws ApiException {
        okhttp3.Call localVarCall = apiV10SecuritySecretPostValidateBeforeCall(authenticationKey, oldSecret, newSecret, null);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
    }

    /**
     * Changes the secret for the given authentication key. (asynchronously)
     * 
     * @param authenticationKey  (optional)
     * @param oldSecret  (optional)
     * @param newSecret  (optional)
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     * @http.response.details
     <table summary="Response Details" border="1">
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        <tr><td> 200 </td><td> Success </td><td>  -  </td></tr>
     </table>
     */
    public okhttp3.Call apiV10SecuritySecretPostAsync(String authenticationKey, String oldSecret, String newSecret, final ApiCallback<String> _callback) throws ApiException {

        okhttp3.Call localVarCall = apiV10SecuritySecretPostValidateBeforeCall(authenticationKey, oldSecret, newSecret, _callback);
        Type localVarReturnType = new TypeToken<String>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);
        return localVarCall;
    }
}
