/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.time.OffsetDateTime;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for Release
 */
public class ReleaseTest {
    private final Release model = new Release();

    /**
     * Model tests for Release
     */
    @Test
    public void testRelease() {
        // TODO: test Release
    }

    /**
     * Test the property 'releaseVersion'
     */
    @Test
    public void releaseVersionTest() {
        // TODO: test releaseVersion
    }

    /**
     * Test the property 'releaseDate'
     */
    @Test
    public void releaseDateTest() {
        // TODO: test releaseDate
    }

    /**
     * Test the property 'releaseNoteUrl'
     */
    @Test
    public void releaseNoteUrlTest() {
        // TODO: test releaseNoteUrl
    }

}
