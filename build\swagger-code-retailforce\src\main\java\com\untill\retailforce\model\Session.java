/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;
import java.util.UUID;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonArray;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapterFactory;
import com.google.gson.reflect.TypeToken;
import com.google.gson.TypeAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import java.io.IOException;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import com.untill.retailforce.JSON;

/**
 * Session
 */
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen")
public class Session {
  public static final String SERIALIZED_NAME_USERNAME = "username";
  @SerializedName(SERIALIZED_NAME_USERNAME)
  private String username;

  public static final String SERIALIZED_NAME_EMAIL = "email";
  @SerializedName(SERIALIZED_NAME_EMAIL)
  private String email;

  public static final String SERIALIZED_NAME_PROFILE_PICTURE = "profilePicture";
  @SerializedName(SERIALIZED_NAME_PROFILE_PICTURE)
  private String profilePicture;

  public static final String SERIALIZED_NAME_DISTRIBUTOR_ID = "distributorId";
  @SerializedName(SERIALIZED_NAME_DISTRIBUTOR_ID)
  private UUID distributorId;

  public static final String SERIALIZED_NAME_SUPPLIER_ID = "supplierId";
  @SerializedName(SERIALIZED_NAME_SUPPLIER_ID)
  private UUID supplierId;

  public static final String SERIALIZED_NAME_IS_RETAIL_FORCE_USER = "isRetailForceUser";
  @SerializedName(SERIALIZED_NAME_IS_RETAIL_FORCE_USER)
  private Boolean isRetailForceUser;

  public Session() {
  }

  public Session username(String username) {
    
    this.username = username;
    return this;
  }

   /**
   * Get username
   * @return username
  **/
  @javax.annotation.Nullable
  public String getUsername() {
    return username;
  }


  public void setUsername(String username) {
    this.username = username;
  }


  public Session email(String email) {
    
    this.email = email;
    return this;
  }

   /**
   * Get email
   * @return email
  **/
  @javax.annotation.Nullable
  public String getEmail() {
    return email;
  }


  public void setEmail(String email) {
    this.email = email;
  }


  public Session profilePicture(String profilePicture) {
    
    this.profilePicture = profilePicture;
    return this;
  }

   /**
   * Get profilePicture
   * @return profilePicture
  **/
  @javax.annotation.Nullable
  public String getProfilePicture() {
    return profilePicture;
  }


  public void setProfilePicture(String profilePicture) {
    this.profilePicture = profilePicture;
  }


  public Session distributorId(UUID distributorId) {
    
    this.distributorId = distributorId;
    return this;
  }

   /**
   * Get distributorId
   * @return distributorId
  **/
  @javax.annotation.Nullable
  public UUID getDistributorId() {
    return distributorId;
  }


  public void setDistributorId(UUID distributorId) {
    this.distributorId = distributorId;
  }


  public Session supplierId(UUID supplierId) {
    
    this.supplierId = supplierId;
    return this;
  }

   /**
   * Get supplierId
   * @return supplierId
  **/
  @javax.annotation.Nullable
  public UUID getSupplierId() {
    return supplierId;
  }


  public void setSupplierId(UUID supplierId) {
    this.supplierId = supplierId;
  }


  public Session isRetailForceUser(Boolean isRetailForceUser) {
    
    this.isRetailForceUser = isRetailForceUser;
    return this;
  }

   /**
   * Get isRetailForceUser
   * @return isRetailForceUser
  **/
  @javax.annotation.Nullable
  public Boolean getIsRetailForceUser() {
    return isRetailForceUser;
  }


  public void setIsRetailForceUser(Boolean isRetailForceUser) {
    this.isRetailForceUser = isRetailForceUser;
  }



  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Session session = (Session) o;
    return Objects.equals(this.username, session.username) &&
        Objects.equals(this.email, session.email) &&
        Objects.equals(this.profilePicture, session.profilePicture) &&
        Objects.equals(this.distributorId, session.distributorId) &&
        Objects.equals(this.supplierId, session.supplierId) &&
        Objects.equals(this.isRetailForceUser, session.isRetailForceUser);
  }

  @Override
  public int hashCode() {
    return Objects.hash(username, email, profilePicture, distributorId, supplierId, isRetailForceUser);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Session {\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    profilePicture: ").append(toIndentedString(profilePicture)).append("\n");
    sb.append("    distributorId: ").append(toIndentedString(distributorId)).append("\n");
    sb.append("    supplierId: ").append(toIndentedString(supplierId)).append("\n");
    sb.append("    isRetailForceUser: ").append(toIndentedString(isRetailForceUser)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }


  public static HashSet<String> openapiFields;
  public static HashSet<String> openapiRequiredFields;

  static {
    // a set of all properties/fields (JSON key names)
    openapiFields = new HashSet<String>();
    openapiFields.add("username");
    openapiFields.add("email");
    openapiFields.add("profilePicture");
    openapiFields.add("distributorId");
    openapiFields.add("supplierId");
    openapiFields.add("isRetailForceUser");

    // a set of required properties/fields (JSON key names)
    openapiRequiredFields = new HashSet<String>();
  }

 /**
  * Validates the JSON Object and throws an exception if issues found
  *
  * @param jsonObj JSON Object
  * @throws IOException if the JSON Object is invalid with respect to Session
  */
  public static void validateJsonObject(JsonObject jsonObj) throws IOException {
      if (jsonObj == null) {
        if (!Session.openapiRequiredFields.isEmpty()) { // has required fields but JSON object is null
          throw new IllegalArgumentException(String.format("The required field(s) %s in Session is not found in the empty JSON string", Session.openapiRequiredFields.toString()));
        }
      }

      Set<Entry<String, JsonElement>> entries = jsonObj.entrySet();
      // check to see if the JSON string contains additional fields
      for (Entry<String, JsonElement> entry : entries) {
        if (!Session.openapiFields.contains(entry.getKey())) {
          throw new IllegalArgumentException(String.format("The field `%s` in the JSON string is not defined in the `Session` properties. JSON: %s", entry.getKey(), jsonObj.toString()));
        }
      }
      if ((jsonObj.get("username") != null && !jsonObj.get("username").isJsonNull()) && !jsonObj.get("username").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `username` to be a primitive type in the JSON string but got `%s`", jsonObj.get("username").toString()));
      }
      if ((jsonObj.get("email") != null && !jsonObj.get("email").isJsonNull()) && !jsonObj.get("email").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `email` to be a primitive type in the JSON string but got `%s`", jsonObj.get("email").toString()));
      }
      if ((jsonObj.get("profilePicture") != null && !jsonObj.get("profilePicture").isJsonNull()) && !jsonObj.get("profilePicture").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `profilePicture` to be a primitive type in the JSON string but got `%s`", jsonObj.get("profilePicture").toString()));
      }
      if ((jsonObj.get("distributorId") != null && !jsonObj.get("distributorId").isJsonNull()) && !jsonObj.get("distributorId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `distributorId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("distributorId").toString()));
      }
      if ((jsonObj.get("supplierId") != null && !jsonObj.get("supplierId").isJsonNull()) && !jsonObj.get("supplierId").isJsonPrimitive()) {
        throw new IllegalArgumentException(String.format("Expected the field `supplierId` to be a primitive type in the JSON string but got `%s`", jsonObj.get("supplierId").toString()));
      }
  }

  public static class CustomTypeAdapterFactory implements TypeAdapterFactory {
    @SuppressWarnings("unchecked")
    @Override
    public <T> TypeAdapter<T> create(Gson gson, TypeToken<T> type) {
       if (!Session.class.isAssignableFrom(type.getRawType())) {
         return null; // this class only serializes 'Session' and its subtypes
       }
       final TypeAdapter<JsonElement> elementAdapter = gson.getAdapter(JsonElement.class);
       final TypeAdapter<Session> thisAdapter
                        = gson.getDelegateAdapter(this, TypeToken.get(Session.class));

       return (TypeAdapter<T>) new TypeAdapter<Session>() {
           @Override
           public void write(JsonWriter out, Session value) throws IOException {
             JsonObject obj = thisAdapter.toJsonTree(value).getAsJsonObject();
             elementAdapter.write(out, obj);
           }

           @Override
           public Session read(JsonReader in) throws IOException {
             JsonObject jsonObj = elementAdapter.read(in).getAsJsonObject();
             validateJsonObject(jsonObj);
             return thisAdapter.fromJsonTree(jsonObj);
           }

       }.nullSafe();
    }
  }

 /**
  * Create an instance of Session given an JSON string
  *
  * @param jsonString JSON string
  * @return An instance of Session
  * @throws IOException if the JSON string is invalid with respect to Session
  */
  public static Session fromJson(String jsonString) throws IOException {
    return JSON.getGson().fromJson(jsonString, Session.class);
  }

 /**
  * Convert an instance of Session to an JSON string
  *
  * @return JSON string
  */
  public String toJson() {
    return JSON.getGson().toJson(this);
  }
}

