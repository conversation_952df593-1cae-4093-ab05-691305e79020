/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.SecurityCertificateType;
import java.io.IOException;
import java.time.OffsetDateTime;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for SignatureDevice
 */
public class SignatureDeviceTest {
    private final SignatureDevice model = new SignatureDevice();

    /**
     * Model tests for SignatureDevice
     */
    @Test
    public void testSignatureDevice() {
        // TODO: test SignatureDevice
    }

    /**
     * Test the property 'type'
     */
    @Test
    public void typeTest() {
        // TODO: test type
    }

    /**
     * Test the property 'certificateSerial'
     */
    @Test
    public void certificateSerialTest() {
        // TODO: test certificateSerial
    }

    /**
     * Test the property 'certificateValidTo'
     */
    @Test
    public void certificateValidToTest() {
        // TODO: test certificateValidTo
    }

    /**
     * Test the property 'isDeactivated'
     */
    @Test
    public void isDeactivatedTest() {
        // TODO: test isDeactivated
    }

    /**
     * Test the property 'isDecommissioned'
     */
    @Test
    public void isDecommissionedTest() {
        // TODO: test isDecommissioned
    }

    /**
     * Test the property 'lastFonCheck'
     */
    @Test
    public void lastFonCheckTest() {
        // TODO: test lastFonCheck
    }

    /**
     * Test the property 'finOnAnnounced'
     */
    @Test
    public void finOnAnnouncedTest() {
        // TODO: test finOnAnnounced
    }

}
