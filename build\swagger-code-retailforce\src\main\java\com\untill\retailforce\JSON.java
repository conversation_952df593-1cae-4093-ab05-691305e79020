/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonParseException;
import com.google.gson.TypeAdapter;
import com.google.gson.internal.bind.util.ISO8601Utils;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.google.gson.JsonElement;
import io.gsonfire.GsonFireBuilder;
import io.gsonfire.TypeSelector;

import okio.ByteString;

import java.io.IOException;
import java.io.StringReader;
import java.lang.reflect.Type;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.HashMap;

/*
 * A JSON utility class
 *
 * NOTE: in the future, this class may be converted to static, which may break
 *       backward-compatibility
 */
public class JSON {
    private static Gson gson;
    private static boolean isLenientOnJson = false;
    private static DateTypeAdapter dateTypeAdapter = new DateTypeAdapter();
    private static SqlDateTypeAdapter sqlDateTypeAdapter = new SqlDateTypeAdapter();
    private static OffsetDateTimeTypeAdapter offsetDateTimeTypeAdapter = new OffsetDateTimeTypeAdapter();
    private static LocalDateTypeAdapter localDateTypeAdapter = new LocalDateTypeAdapter();
    private static ByteArrayAdapter byteArrayAdapter = new ByteArrayAdapter();

    @SuppressWarnings("unchecked")
    public static GsonBuilder createGson() {
        GsonFireBuilder fireBuilder = new GsonFireBuilder()
        ;
        GsonBuilder builder = fireBuilder.createGsonBuilder();
        return builder;
    }

    private static String getDiscriminatorValue(JsonElement readElement, String discriminatorField) {
        JsonElement element = readElement.getAsJsonObject().get(discriminatorField);
        if (null == element) {
            throw new IllegalArgumentException("missing discriminator field: <" + discriminatorField + ">");
        }
        return element.getAsString();
    }

    /**
     * Returns the Java class that implements the OpenAPI schema for the specified discriminator value.
     *
     * @param classByDiscriminatorValue The map of discriminator values to Java classes.
     * @param discriminatorValue The value of the OpenAPI discriminator in the input data.
     * @return The Java class that implements the OpenAPI schema
     */
    private static Class getClassByDiscriminator(Map classByDiscriminatorValue, String discriminatorValue) {
        Class clazz = (Class) classByDiscriminatorValue.get(discriminatorValue);
        if (null == clazz) {
            throw new IllegalArgumentException("cannot determine model class of name: <" + discriminatorValue + ">");
        }
        return clazz;
    }

    {
        GsonBuilder gsonBuilder = createGson();
        gsonBuilder.registerTypeAdapter(Date.class, dateTypeAdapter);
        gsonBuilder.registerTypeAdapter(java.sql.Date.class, sqlDateTypeAdapter);
        gsonBuilder.registerTypeAdapter(OffsetDateTime.class, offsetDateTimeTypeAdapter);
        gsonBuilder.registerTypeAdapter(LocalDate.class, localDateTypeAdapter);
        gsonBuilder.registerTypeAdapter(byte[].class, byteArrayAdapter);
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.AccessLicense.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.AccessLicenseAllocation.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.AccessLicenseConfigurationInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.AccessLicenseConfigurationParameter.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.AccessLicenseContract.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.AccessLicensePageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Address.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ApiKey.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.AuditLogEntry.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BackupData.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BackupDataPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingAccessLicenseCount.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingDistributorLicenseCount.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingDistributorLicenseCountBillingLicenseOverview.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingDistributorLicenseCountBillingLicenseOverviewPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingLicenseCount.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingLicenseCountBillingLicenseOverview.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingLicenseCountBillingLicenseOverviewPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingLicenseDetail.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BillingLicenseDetailPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BoolResponse.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.BusinessTransactionTypeDocumentTypeMapping.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.CashRegister.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Certificate.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.CertificateModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ClearingRun.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ClientConfigurationGermany.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.CloudParameter.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Company.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.CompanyIdentification.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.CompanyModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.CompanyModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ConfigLicenseModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ConfigurationParameter.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ControlUnitDriverInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.CosmosDocument.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DashboardTerminalDetail.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DashboardTerminalDetailPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DashboardTerminalOverview.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DashboardTerminalOverviewPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DigitalReceipt.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Distributor.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DistributorContract.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DistributorContractModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DistributorContractModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DistributorModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DistributorModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Document.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DocumentCoupon.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DocumentCouponTextLine.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DocumentPayment.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DocumentPaymentCardData.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DocumentPositionBase.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DocumentPositionReference.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DocumentReference.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DocumentTaxPosition.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DownloadLink.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.DownloadLinkPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.EntityParameterInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.EntitySecurity.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.EntitySecurityPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.FiscalClient.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.FiscalClientConfiguration.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.FiscalClientConfigurationModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.FiscalResponse.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.FonConnectionLogMessage.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.FonConnectionLogMessagePageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.FonCredentials.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidBreadCrumb.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidEntityVersion.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidEntityVersionPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidExtendedSimpleCountryObject.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidExtendedSimpleCountryObjectPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidHierarchicalSimpleObject.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidHierarchicalSimpleObjectPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidSimpleObject.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.GuidSimpleObjectPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.HelpInformation.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.IFiscalCountryProperties.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.IFiscalCountryPropertiesDocumentTypeBusinessTransactionTypeSupport0Receipt.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.IFiscalCountryPropertiesPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.IFiscalImplementationConfiguration.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.INotification.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ImportModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ImportModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Int32SimpleObject.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Int32SimpleObjectPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.InvitationInfoModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.InvitationModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.JwtLicenseClaim.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.LegalForm.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.License.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.LicenseDetailUsage.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.LicenseModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.LicenseModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.LicenseOption.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.MessageApiModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.NotificationResult.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.NotificationsInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.OfflineConfiguration.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.OnboardingFinishData.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.OnboardingFinishDistributorData.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Organisation.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.OrganisationModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.OrganisationModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.OutstandingPayment.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ParameterInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Partner.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.PaymentTerms.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Principal.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.PrinterDriverInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.PrinterImageFile.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ProfilePictureModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ReceiptData.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ReceiptDataScrollResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.RegistrationModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Release.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ResultResponse.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SearchResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SearchResultModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Session.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SignDeviceDriverInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SignatureDevice.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Software.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Store.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.StoreModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.StoreModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.StringSimpleObject.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.StringSimpleObjectPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Supplier.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SupplierContract.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SupplierContractModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SupplierContractModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SupplierModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SupplierModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SupportTicketModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SupportTicketModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.SupportTicketSimple.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TaxonomyCloudStoreConfiguration.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TaxonomyFileStoreConfiguration.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Terminal.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TerminalInsightModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TerminalModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TerminalModelPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TimelogOverviewModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TseAnnouncementCreateResult.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TseAnnouncementOverview.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TseAnnouncementOverviewPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TseAnnouncementStatusInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TseConfiguration.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TseDriverInfo.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TseInformationOverview.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.TseInformationOverviewPageResultModel.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.User.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.ValidationError.CustomTypeAdapterFactory());
        gsonBuilder.registerTypeAdapterFactory(new com.untill.retailforce.model.Vat.CustomTypeAdapterFactory());
        gson = gsonBuilder.create();
    }

    /**
     * Get Gson.
     *
     * @return Gson
     */
    public static Gson getGson() {
        return gson;
    }

    /**
     * Set Gson.
     *
     * @param gson Gson
     */
    public static void setGson(Gson gson) {
        JSON.gson = gson;
    }

    public static void setLenientOnJson(boolean lenientOnJson) {
        isLenientOnJson = lenientOnJson;
    }

    /**
     * Serialize the given Java object into JSON string.
     *
     * @param obj Object
     * @return String representation of the JSON
     */
    public static String serialize(Object obj) {
        return gson.toJson(obj);
    }

    /**
     * Deserialize the given JSON string to Java object.
     *
     * @param <T>        Type
     * @param body       The JSON string
     * @param returnType The type to deserialize into
     * @return The deserialized Java object
     */
    @SuppressWarnings("unchecked")
    public static <T> T deserialize(String body, Type returnType) {
        try {
            if (isLenientOnJson) {
                JsonReader jsonReader = new JsonReader(new StringReader(body));
                // see https://google-gson.googlecode.com/svn/trunk/gson/docs/javadocs/com/google/gson/stream/JsonReader.html#setLenient(boolean)
                jsonReader.setLenient(true);
                return gson.fromJson(jsonReader, returnType);
            } else {
                return gson.fromJson(body, returnType);
            }
        } catch (JsonParseException e) {
            // Fallback processing when failed to parse JSON form response body:
            // return the response body string directly for the String return type;
            if (returnType.equals(String.class)) {
                return (T) body;
            } else {
                throw (e);
            }
        }
    }

    /**
     * Gson TypeAdapter for Byte Array type
     */
    public static class ByteArrayAdapter extends TypeAdapter<byte[]> {

        @Override
        public void write(JsonWriter out, byte[] value) throws IOException {
            if (value == null) {
                out.nullValue();
            } else {
                out.value(ByteString.of(value).base64());
            }
        }

        @Override
        public byte[] read(JsonReader in) throws IOException {
            switch (in.peek()) {
                case NULL:
                    in.nextNull();
                    return null;
                default:
                    String bytesAsBase64 = in.nextString();
                    ByteString byteString = ByteString.decodeBase64(bytesAsBase64);
                    return byteString.toByteArray();
            }
        }
    }

    /**
     * Gson TypeAdapter for JSR310 OffsetDateTime type
     */
    public static class OffsetDateTimeTypeAdapter extends TypeAdapter<OffsetDateTime> {

        private DateTimeFormatter formatter;

        public OffsetDateTimeTypeAdapter() {
            this(DateTimeFormatter.ISO_OFFSET_DATE_TIME);
        }

        public OffsetDateTimeTypeAdapter(DateTimeFormatter formatter) {
            this.formatter = formatter;
        }

        public void setFormat(DateTimeFormatter dateFormat) {
            this.formatter = dateFormat;
        }

        @Override
        public void write(JsonWriter out, OffsetDateTime date) throws IOException {
            if (date == null) {
                out.nullValue();
            } else {
                out.value(formatter.format(date));
            }
        }

        @Override
        public OffsetDateTime read(JsonReader in) throws IOException {
            switch (in.peek()) {
                case NULL:
                    in.nextNull();
                    return null;
                default:
                    String date = in.nextString();
                    if (date.endsWith("+0000")) {
                        date = date.substring(0, date.length()-5) + "Z";
                    }
                    return OffsetDateTime.parse(date, formatter);
            }
        }
    }

    /**
     * Gson TypeAdapter for JSR310 LocalDate type
     */
    public static class LocalDateTypeAdapter extends TypeAdapter<LocalDate> {

        private DateTimeFormatter formatter;

        public LocalDateTypeAdapter() {
            this(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        public LocalDateTypeAdapter(DateTimeFormatter formatter) {
            this.formatter = formatter;
        }

        public void setFormat(DateTimeFormatter dateFormat) {
            this.formatter = dateFormat;
        }

        @Override
        public void write(JsonWriter out, LocalDate date) throws IOException {
            if (date == null) {
                out.nullValue();
            } else {
                out.value(formatter.format(date));
            }
        }

        @Override
        public LocalDate read(JsonReader in) throws IOException {
            switch (in.peek()) {
                case NULL:
                    in.nextNull();
                    return null;
                default:
                    String date = in.nextString();
                    return LocalDate.parse(date, formatter);
            }
        }
    }

    public static void setOffsetDateTimeFormat(DateTimeFormatter dateFormat) {
        offsetDateTimeTypeAdapter.setFormat(dateFormat);
    }

    public static void setLocalDateFormat(DateTimeFormatter dateFormat) {
        localDateTypeAdapter.setFormat(dateFormat);
    }

    /**
     * Gson TypeAdapter for java.sql.Date type
     * If the dateFormat is null, a simple "yyyy-MM-dd" format will be used
     * (more efficient than SimpleDateFormat).
     */
    public static class SqlDateTypeAdapter extends TypeAdapter<java.sql.Date> {

        private DateFormat dateFormat;

        public SqlDateTypeAdapter() {}

        public SqlDateTypeAdapter(DateFormat dateFormat) {
            this.dateFormat = dateFormat;
        }

        public void setFormat(DateFormat dateFormat) {
            this.dateFormat = dateFormat;
        }

        @Override
        public void write(JsonWriter out, java.sql.Date date) throws IOException {
            if (date == null) {
                out.nullValue();
            } else {
                String value;
                if (dateFormat != null) {
                    value = dateFormat.format(date);
                } else {
                    value = date.toString();
                }
                out.value(value);
            }
        }

        @Override
        public java.sql.Date read(JsonReader in) throws IOException {
            switch (in.peek()) {
                case NULL:
                    in.nextNull();
                    return null;
                default:
                    String date = in.nextString();
                    try {
                        if (dateFormat != null) {
                            return new java.sql.Date(dateFormat.parse(date).getTime());
                        }
                        return new java.sql.Date(ISO8601Utils.parse(date, new ParsePosition(0)).getTime());
                    } catch (ParseException e) {
                        throw new JsonParseException(e);
                    }
            }
        }
    }

    /**
     * Gson TypeAdapter for java.util.Date type
     * If the dateFormat is null, ISO8601Utils will be used.
     */
    public static class DateTypeAdapter extends TypeAdapter<Date> {

        private DateFormat dateFormat;

        public DateTypeAdapter() {}

        public DateTypeAdapter(DateFormat dateFormat) {
            this.dateFormat = dateFormat;
        }

        public void setFormat(DateFormat dateFormat) {
            this.dateFormat = dateFormat;
        }

        @Override
        public void write(JsonWriter out, Date date) throws IOException {
            if (date == null) {
                out.nullValue();
            } else {
                String value;
                if (dateFormat != null) {
                    value = dateFormat.format(date);
                } else {
                    value = ISO8601Utils.format(date, true);
                }
                out.value(value);
            }
        }

        @Override
        public Date read(JsonReader in) throws IOException {
            try {
                switch (in.peek()) {
                    case NULL:
                        in.nextNull();
                        return null;
                    default:
                        String date = in.nextString();
                        try {
                            if (dateFormat != null) {
                                return dateFormat.parse(date);
                            }
                            return ISO8601Utils.parse(date, new ParsePosition(0));
                        } catch (ParseException e) {
                            throw new JsonParseException(e);
                        }
                }
            } catch (IllegalArgumentException e) {
                throw new JsonParseException(e);
            }
        }
    }

    public static void setDateFormat(DateFormat dateFormat) {
        dateTypeAdapter.setFormat(dateFormat);
    }

    public static void setSqlDateFormat(DateFormat dateFormat) {
        sqlDateTypeAdapter.setFormat(dateFormat);
    }
}
