/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import java.util.Objects;
import java.util.Arrays;
import com.google.gson.annotations.SerializedName;

import java.io.IOException;
import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;

/**
 * The type of the contact of the terminal.
 */
@JsonAdapter(TerminalContactType.Adapter.class)
public enum TerminalContactType {
  
  PRIMARYCONTACT("primaryContact"),
  
  TECHNICALCONTACT("technicalContact");

  private String value;

  TerminalContactType(String value) {
    this.value = value;
  }

  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  public static TerminalContactType fromValue(String value) {
    for (TerminalContactType b : TerminalContactType.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }

  public static class Adapter extends TypeAdapter<TerminalContactType> {
    @Override
    public void write(final JsonWriter jsonWriter, final TerminalContactType enumeration) throws IOException {
      jsonWriter.value(enumeration.getValue());
    }

    @Override
    public TerminalContactType read(final JsonReader jsonReader) throws IOException {
      String value = jsonReader.nextString();
      return TerminalContactType.fromValue(value);
    }
  }
}

