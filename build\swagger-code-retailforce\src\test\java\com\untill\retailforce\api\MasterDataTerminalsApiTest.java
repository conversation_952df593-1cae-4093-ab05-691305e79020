/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.api;

import com.untill.retailforce.ApiException;
import com.untill.retailforce.model.DownloadLinkPageResultModel;
import com.untill.retailforce.model.GuidEntityVersionPageResultModel;
import com.untill.retailforce.model.GuidSimpleObjectPageResultModel;
import com.untill.retailforce.model.IdentificationType;
import java.time.OffsetDateTime;
import com.untill.retailforce.model.Terminal;
import com.untill.retailforce.model.TerminalInsightModel;
import com.untill.retailforce.model.TerminalModel;
import com.untill.retailforce.model.TerminalModelPageResultModel;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for MasterDataTerminalsApi
 */
@Disabled
public class MasterDataTerminalsApiTest {

    private final MasterDataTerminalsApi api = new MasterDataTerminalsApi();

    /**
     * Returns all terminals for the requested store for the authenticated user.
     *
     * At least one of organizationId, companyId or storeId parameter must have a value.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsGetTest() throws ApiException {
        UUID storeId = null;
        UUID organizationId = null;
        UUID companyId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        TerminalModelPageResultModel response = api.apiV10MasterdataTerminalsGet(storeId, organizationId, companyId, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Creates a terminal in the cloud store.
     *
     * If RetailForce.Cloud.Model.Terminal.TerminalId set to System.Guid.Empty, then the terminal id will be generated by the service.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsPostTest() throws ApiException {
        Terminal terminal = null;
        TerminalModel response = api.apiV10MasterdataTerminalsPost(terminal);
        // TODO: test validations
    }

    /**
     * Returns all terminals as RetailForce.Cloud.Model.Helper.SimpleObject&#x60;1 for the requested store for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsSimpleGetTest() throws ApiException {
        UUID storeId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        String searchString = null;
        GuidSimpleObjectPageResultModel response = api.apiV10MasterdataTerminalsSimpleGet(storeId, pageOffset, pageSize, searchString);
        // TODO: test validations
    }

    /**
     * Deactivates and archives the given terminal (cannot be undone).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdArchivePostTest() throws ApiException {
        UUID terminalId = null;
        Boolean reactivate = null;
        TerminalModel response = api.apiV10MasterdataTerminalsTerminalIdArchivePost(terminalId, reactivate);
        // TODO: test validations
    }

    /**
     * Deactivates a terminal temporarly (season functionality).
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdDeactivatePutTest() throws ApiException {
        UUID terminalId = null;
        TerminalModel response = api.apiV10MasterdataTerminalsTerminalIdDeactivatePut(terminalId);
        // TODO: test validations
    }

    /**
     * Set date of decommissioning for selected terminal in the cloud database.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdDecommissionDatePostTest() throws ApiException {
        UUID terminalId = null;
        OffsetDateTime response = api.apiV10MasterdataTerminalsTerminalIdDecommissionDatePost(terminalId);
        // TODO: test validations
    }

    /**
     * Deletes a terminal from the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdDeleteTest() throws ApiException {
        UUID terminalId = null;
        api.apiV10MasterdataTerminalsTerminalIdDelete(terminalId);
        // TODO: test validations
    }

    /**
     * Deletes the cloud storage data of the test terminal. If called on a productive terminal exception / error will be raised.
     *
     * The terminal itself will not be deleted.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdFiscaldataDeleteTest() throws ApiException {
        UUID terminalId = null;
        api.apiV10MasterdataTerminalsTerminalIdFiscaldataDelete(terminalId);
        // TODO: test validations
    }

    /**
     * Returns the requested terminal for the authenticated user.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdGetTest() throws ApiException {
        UUID terminalId = null;
        String entityVersion = null;
        TerminalModel response = api.apiV10MasterdataTerminalsTerminalIdGet(terminalId, entityVersion);
        // TODO: test validations
    }

    /**
     * Returns the terminal id for the requested parameter.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdGet_0Test() throws ApiException {
        IdentificationType identificationType = null;
        String identification = null;
        String storeNumber = null;
        String terminalNumber = null;
        UUID response = api.apiV10MasterdataTerminalsTerminalIdGet_0(identificationType, identification, storeNumber, terminalNumber);
        // TODO: test validations
    }

    /**
     * Generates a new or returns the already existing global short id for the terminal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdGlobalShortIdPostTest() throws ApiException {
        UUID terminalId = null;
        String response = api.apiV10MasterdataTerminalsTerminalIdGlobalShortIdPost(terminalId);
        // TODO: test validations
    }

    /**
     * Test if access to terminal is allowed.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdHeadTest() throws ApiException {
        UUID terminalId = null;
        api.apiV10MasterdataTerminalsTerminalIdHead(terminalId);
        // TODO: test validations
    }

    /**
     * Returns terminal insights
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdInsightsGetTest() throws ApiException {
        UUID terminalId = null;
        TerminalInsightModel response = api.apiV10MasterdataTerminalsTerminalIdInsightsGet(terminalId);
        // TODO: test validations
    }

    /**
     * Generates a new or returns the already existing encrypted iot access key for the terminal.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdIotAccessKeyGetTest() throws ApiException {
        UUID terminalId = null;
        String response = api.apiV10MasterdataTerminalsTerminalIdIotAccessKeyGet(terminalId);
        // TODO: test validations
    }

    /**
     * Updates the terminal in the cloud store.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdPutTest() throws ApiException {
        UUID terminalId = null;
        Terminal terminal = null;
        TerminalModel response = api.apiV10MasterdataTerminalsTerminalIdPut(terminalId, terminal);
        // TODO: test validations
    }

    /**
     * Returns the last 15 support packages if available.
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdSupportPackagesGetTest() throws ApiException {
        UUID terminalId = null;
        DownloadLinkPageResultModel response = api.apiV10MasterdataTerminalsTerminalIdSupportPackagesGet(terminalId);
        // TODO: test validations
    }

    /**
     * Get terminal versions
     *
     * @throws ApiException if the Api call fails
     */
    @Test
    public void apiV10MasterdataTerminalsTerminalIdVersionsGetTest() throws ApiException {
        UUID terminalId = null;
        Integer pageOffset = null;
        Integer pageSize = null;
        GuidEntityVersionPageResultModel response = api.apiV10MasterdataTerminalsTerminalIdVersionsGet(terminalId, pageOffset, pageSize);
        // TODO: test validations
    }

}
