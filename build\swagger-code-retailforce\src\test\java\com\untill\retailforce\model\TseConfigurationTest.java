/*
 * RetailForce Cloud v1.0
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v1.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package com.untill.retailforce.model;

import com.google.gson.TypeAdapter;
import com.google.gson.annotations.JsonAdapter;
import com.google.gson.annotations.SerializedName;
import com.google.gson.stream.JsonReader;
import com.google.gson.stream.JsonWriter;
import com.untill.retailforce.model.ConfigurationParameter;
import com.untill.retailforce.model.TseDriver;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;


/**
 * Model tests for TseConfiguration
 */
public class TseConfigurationTest {
    private final TseConfiguration model = new TseConfiguration();

    /**
     * Model tests for TseConfiguration
     */
    @Test
    public void testTseConfiguration() {
        // TODO: test TseConfiguration
    }

    /**
     * Test the property 'tseDriver'
     */
    @Test
    public void tseDriverTest() {
        // TODO: test tseDriver
    }

    /**
     * Test the property 'tseId'
     */
    @Test
    public void tseIdTest() {
        // TODO: test tseId
    }

    /**
     * Test the property 'tseGuid'
     */
    @Test
    public void tseGuidTest() {
        // TODO: test tseGuid
    }

    /**
     * Test the property 'useTseGuid'
     */
    @Test
    public void useTseGuidTest() {
        // TODO: test useTseGuid
    }

    /**
     * Test the property 'tseParameter'
     */
    @Test
    public void tseParameterTest() {
        // TODO: test tseParameter
    }

}
